package com.zxy.product.train.service.support;

import static com.zxy.product.train.jooq.tables.ClassDetail.CLASS_DETAIL;
import static com.zxy.product.train.jooq.tables.ClassInfo.CLASS_INFO;
import static com.zxy.product.train.jooq.tables.ClassSignupInfo.CLASS_SIGNUP_INFO;
import static com.zxy.product.train.jooq.tables.ConfigurationValue.CONFIGURATION_VALUE;
import static com.zxy.product.train.jooq.tables.Project.PROJECT;
import static com.zxy.product.train.jooq.tables.Trainee.TRAINEE;

import java.util.Optional;

import org.jooq.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.entity.KnowledgeInfo;
import com.zxy.product.train.api.ClassOfflineCourseService;
import com.zxy.product.train.api.ClassSignupInfoService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.ClassDetail;
import com.zxy.product.train.entity.ClassInfo;
import com.zxy.product.train.entity.ClassSignupInfo;
import com.zxy.product.train.entity.OfflineCourse;
import com.zxy.product.train.entity.Trainee;
import com.zxy.product.train.util.StringUtils;

/**
 * <AUTHOR> 班级报名
 */
@Service
public class ClassSignupServiceSupport implements ClassSignupInfoService {

  private CommonDao<ClassSignupInfo> signupDao;

  private MessageSender sender;

  private CommonDao<ClassInfo> classDao;

  private ClassOfflineCourseService offlineCourseService;

  private CommonDao<OfflineCourse> offlineDao;

  private CommonDao<Trainee> traineeDao;

  private CommonDao<ClassDetail> classDetailDao;

  @Autowired
  public void setTraineeDao(CommonDao<Trainee> traineeDao) {
    this.traineeDao = traineeDao;
  }

  @Autowired
  public void setOfflineDao(CommonDao<OfflineCourse> offlineDao) {
    this.offlineDao = offlineDao;
  }

  @Autowired
  public void setOfflineCourseService(ClassOfflineCourseService offlineCourseService) {
    this.offlineCourseService = offlineCourseService;
  }

  @Autowired
  public void setClassDao(CommonDao<ClassInfo> classDao) {
    this.classDao = classDao;
  }

  @Autowired
  public void setSender(MessageSender sender) {
    this.sender = sender;
  }

  @Autowired
  public void setSignupDao(CommonDao<ClassSignupInfo> signupDao) {
    this.signupDao = signupDao;
  }

  @Autowired
  public void setClassDetailDao(CommonDao<ClassDetail> classDetailDao) {
    this.classDetailDao = classDetailDao;
  }

  @Override
  public ClassSignupInfo insert(String classId, Integer isOpen, Optional<Long> startTime,
                                Optional<Long> endTime,
                                Optional<String> signupCode, Optional<Integer> usingTwoBrings, Optional<String> question1,
                                Optional<String> question2, Optional<String> signupUrl, Optional<String> createMember,
                                Optional<Integer> notice) {
      ClassInfo classInfo = classDao.get(classId);
      notice.ifPresent(classInfo::setNotice);
      classDao.update(classInfo);
      Optional<ClassSignupInfo> classSignupInfo = this.findByClassId(classId);
      if (classSignupInfo.isPresent()) {
          return classSignupInfo.get();
      }
      ClassSignupInfo signupInfo = new ClassSignupInfo();
      signupInfo.forInsert();
      signupInfo.setClassId(classId);
      signupInfo.setIsOpen(isOpen);
      startTime.ifPresent(signupInfo::setStartTime);
      Long end = endTime.isPresent() ? endTime.get() + 86399999 : 0L;
      signupInfo.setEndTime(end);
      signupCode.ifPresent(signupInfo::setSignupCode);
      usingTwoBrings.ifPresent(signupInfo::setUsingTwoBrings);
      signupInfo.setQuestion1(question1.orElse(null));
      signupInfo.setQuestion2(question2.orElse(null));
      signupUrl.ifPresent(signupInfo::setSignupUrl);
      createMember.ifPresent(signupInfo::setCreateMember);
      // 报名名验证重复
      String code = "";
      boolean isBreak = true;
      while (isBreak) {
          code = ClassSignupInfo.CODE_PREFIX + StringUtils.random(5);
          Optional<ClassSignupInfo> signInfo = this.findBySignupCode(code, Optional.ofNullable(null));
          if (!signInfo.isPresent()) {
              isBreak = false;
              break;
          }
      }
      signupInfo.setSignupCode(code);
      signupInfo.setSignupUrl(ClassSignupInfo.SIGN_URL);
      signupDao.insert(signupInfo);
      // 发消息,发报名通知、生成四度评估问卷(已屏蔽)
      sender.send(MessageTypeContent.TRAIN_NOTICE_SEND, MessageHeaderContent.CLASSID, classId);
      sender.send(MessageTypeContent.TRAIN_CLASS_NOTICE_FOR_ACTIVITY, MessageHeaderContent.ID, classId);
      //线下课程附件同步知识
      sender.send(com.zxy.product.course.content.MessageTypeContent.KNOWLEDGE_INFO_SYNC,
              com.zxy.product.course.content.MessageHeaderContent.ID, classId,
              com.zxy.product.course.content.MessageHeaderContent.BUSINESS_TYPE,
              KnowledgeInfo.BUSINESS_TYPE_CLASS + "");
      return signupInfo;
  }

  @Override
  public ClassSignupInfo update(String id, Integer isOpen, Optional<Long> startTime,
                                Optional<Long> endTime,
                                Optional<String> signupCode, Optional<Integer> usingTwoBrings, Optional<String> question1,
                                Optional<String> question2, Optional<String> signupUrl, Optional<Integer> faceAttendance) {
      Optional<ClassSignupInfo> singupInfo = signupDao.getOptional(id);
      Long end = endTime.isPresent() ? endTime.get() + 86399999 : 0l;
      singupInfo.ifPresent(x -> {
          x.setIsOpen(isOpen);
          startTime.ifPresent(x::setStartTime);
          x.setEndTime(end);
          faceAttendance.ifPresent(x::setFaceAttendance);
          if (!startTime.isPresent()) {
              x.setStartTime(Long.parseLong("0"));
          }
          if (!endTime.isPresent()) {
              x.setEndTime(Long.parseLong("0"));
          }
          signupCode.ifPresent(x::setSignupCode);
          usingTwoBrings.ifPresent(x::setUsingTwoBrings);
          if (usingTwoBrings.isPresent() && usingTwoBrings.get()
                  .equals(ClassSignupInfo.TOWBRINGS_TRUE)) {
              question1.ifPresent(x::setQuestion1);
              question2.ifPresent(x::setQuestion2);
          } else {
              x.setQuestion1("");
              x.setQuestion2("");
          }
          signupUrl.ifPresent(x::setSignupUrl);
          signupDao.update(x);
          sender.send(MessageTypeContent.TRAIN_CLASS_NOTICE_FOR_ACTIVITY, MessageHeaderContent.ID,
                  x.getClassId());
      });
      return singupInfo.isPresent() ? singupInfo.get() : null;
  }

  @Override
  public Optional<ClassSignupInfo> get(String id) {
    return signupDao.getOptional(id);
  }

  @Override
  public Optional<ClassSignupInfo> findByClassId(String classId) {
    return signupDao.fetchOne(CLASS_SIGNUP_INFO.CLASS_ID.eq(classId));
  }

  @Override
  public Optional<ClassSignupInfo> findBySignupCode(String code, Optional<String> memberId) {
    Optional<ClassSignupInfo> classSignupInfo = signupDao
        .execute(x -> x
            .selectDistinct(Fields.start()
                .add(CLASS_SIGNUP_INFO.ID, CLASS_SIGNUP_INFO.CLASS_ID, CLASS_SIGNUP_INFO.IS_OPEN,
                    CLASS_SIGNUP_INFO.START_TIME, CLASS_SIGNUP_INFO.END_TIME,
                    CLASS_SIGNUP_INFO.SIGNUP_CODE, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.RETURN_DATE,
                    CLASS_DETAIL.BANNER_ID)
                .end())
            .from(CLASS_SIGNUP_INFO).leftJoin(CLASS_INFO)
            .on(CLASS_INFO.ID.eq(CLASS_SIGNUP_INFO.CLASS_ID))
            .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID)).leftJoin(CLASS_DETAIL)
            .on(CLASS_DETAIL.CLASS_ID.eq(CLASS_SIGNUP_INFO.CLASS_ID))
            .where(CLASS_SIGNUP_INFO.SIGNUP_CODE.eq(code)))
        .fetchOptional(r -> {
          ClassSignupInfo csi = new ClassSignupInfo();
          csi.setId(r.getValue(CLASS_SIGNUP_INFO.ID));
          csi.setClassId(r.getValue(CLASS_SIGNUP_INFO.CLASS_ID));
          csi.setIsOpen(r.getValue(CLASS_SIGNUP_INFO.IS_OPEN));
          csi.setStartTime(r.getValue(CLASS_SIGNUP_INFO.START_TIME));
          csi.setEndTime(r.getValue(CLASS_SIGNUP_INFO.END_TIME));
          csi.setSignupCode(r.getValue(CLASS_SIGNUP_INFO.SIGNUP_CODE));
          ClassInfo ci = new ClassInfo();
          ci.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
          ci.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
          ci.setBannerId(r.getValue(CLASS_DETAIL.BANNER_ID));
          csi.setClassInfo(ci);
          return csi;
        });
    if (classSignupInfo.isPresent() && memberId.isPresent()) {
      Optional<Trainee> fetchOptional = traineeDao.execute(
          x -> x.select(Fields.start().add(TRAINEE).end())
              .from(TRAINEE)
              .where(TRAINEE.CLASS_ID.eq(classSignupInfo.get().getClassId()))
              .and(TRAINEE.MEMBER_ID.eq(memberId.get()))
              .and(TRAINEE.DELETE_FLAG.eq(0)))
          .fetchOptional(b -> {
            return b.into(Trainee.class);
          });
      if (fetchOptional.isPresent() && fetchOptional.get().getType() == 1) {
        classSignupInfo.get().setTraineeType(1);
      }
    }
    return classSignupInfo;
  }

  @Override
  public Optional<ClassSignupInfo> findDetailByClassId(String classId) {
    Field<String> projectName = PROJECT.NAME.as("projectName");
    Optional<ClassDetail> classDetail = classDetailDao
        .execute(x -> x.select(CLASS_DETAIL.COVER_ID, CLASS_DETAIL.COVER_PATH).from(CLASS_DETAIL))
        .where(CLASS_DETAIL.CLASS_ID.eq(classId)).limit(1)
        .fetchOptional(r -> r.into(ClassDetail.class));
    return signupDao
        .execute(x -> x
            .selectDistinct(Fields.start()
                .add(CLASS_SIGNUP_INFO.ID, CLASS_SIGNUP_INFO.CLASS_ID, CLASS_SIGNUP_INFO.IS_OPEN,
                    CLASS_SIGNUP_INFO.START_TIME, CLASS_SIGNUP_INFO.END_TIME,
                    CLASS_SIGNUP_INFO.SIGNUP_CODE, CLASS_INFO.ARRIVE_DATE, CLASS_INFO.STATUS,
                    CLASS_INFO.RETURN_DATE,
                    CONFIGURATION_VALUE.NAME, projectName)
                .end())
            .from(CLASS_SIGNUP_INFO).leftJoin(CLASS_INFO)
            .on(CLASS_INFO.ID.eq(CLASS_SIGNUP_INFO.CLASS_ID))
            .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
            .leftJoin(CONFIGURATION_VALUE)
            .on(PROJECT.ADDRESS.eq(CONFIGURATION_VALUE.ID))
            .where(CLASS_SIGNUP_INFO.CLASS_ID.eq(classId)))
        .fetchOptional(r -> {
          ClassSignupInfo csi = new ClassSignupInfo();
          csi.setId(r.getValue(CLASS_SIGNUP_INFO.ID));
          csi.setClassId(r.getValue(CLASS_SIGNUP_INFO.CLASS_ID));
          csi.setIsOpen(r.getValue(CLASS_SIGNUP_INFO.IS_OPEN));
          csi.setStartTime(r.getValue(CLASS_SIGNUP_INFO.START_TIME));
          csi.setEndTime(r.getValue(CLASS_SIGNUP_INFO.END_TIME));
          csi.setSignupCode(r.getValue(CLASS_SIGNUP_INFO.SIGNUP_CODE));
          ClassInfo ci = new ClassInfo();
          ci.setAddress(r.getValue(CONFIGURATION_VALUE.NAME));
          ci.setArriveDate(r.getValue(CLASS_INFO.ARRIVE_DATE));
          ci.setReturnDate(r.getValue(CLASS_INFO.RETURN_DATE));
//					ci.setBannerId(r.getValue(CLASS_DETAIL.BANNER_ID));
          ci.setStatus(r.getValue(CLASS_INFO.STATUS));
          ci.setClassName(r.getValue(projectName));
          classDetail.ifPresent(x -> {
            ci.setCoverPath(x.getCoverPath());
          });
          csi.setClassInfo(ci);
          return csi;
        });
  }

}
