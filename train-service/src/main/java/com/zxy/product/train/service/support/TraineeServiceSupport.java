package com.zxy.product.train.service.support;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.train.api.*;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.*;
import com.zxy.product.train.service.util.DateUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.train.entity.Trainee.*;
import static com.zxy.product.train.jooq.Tables.*;
import static com.zxy.product.train.jooq.tables.ClassRequired.CLASS_REQUIRED;
import static com.zxy.product.train.jooq.tables.Member.MEMBER;
import static org.jooq.impl.DSL.count;

/**
 * Created by 田聪 on 2017/2/12
 */
@Service
public class TraineeServiceSupport implements TraineeService {

	private CommonDao<Trainee> dao;
	private ClassQuotaService classQuotaService;
	private ClassQuotaDetailService classQuotaDetailService;
	private OrganizationDetailService organizationDetailService;
	private ClassOnlineCourseService onlineCourseService;
	private MemberService memberService;
	private CommonDao<MemberConfig> memberConfigCommonDao;
	private TraineeService traineeService;
	private ClassInfoService classInfoService;
	private CommonDao<ResearchQuestionary> researchQuestionaryDao;
	private CommonDao<ResearchAnswerRecord> researchAnswerRecordDao;
	private CommonDao<ResearchRecord> researchRecordDao;
	private CommonDao<ClassInfo> classInfoDao;
	private MessageSender messageSender;
	private SignDetailService signDetailService;
	private CommonDao<MessageRecord> messageRecordCommonDao;
	private CommonDao<ClassRequired> classRequiredDao;
	private CommonDao<Organization> orgDao;
	private CommonDao<GroupConfigurationValue> groupConfigurationValueDao;
	private CommonDao<DeleteDataTrain> deleteDataTrainCommonDao;
	private TrainChatGroupService trainChatGroupService;

	@Autowired
	public void setEnvironment(CommonDao<DeleteDataTrain> deleteDataTrainCommonDao) {
		this.deleteDataTrainCommonDao = deleteDataTrainCommonDao;
	}
	@Autowired
	public void setGroupConfigurationValueDao(CommonDao<GroupConfigurationValue> groupConfigurationValueDao) {
		this.groupConfigurationValueDao = groupConfigurationValueDao;
	}
	@Autowired
	public void setOrgDao(CommonDao<Organization> orgDao) {
		this.orgDao = orgDao;
	}
	@Autowired
	public void setMessageRecordCommonDao(CommonDao<MessageRecord> messageRecordCommonDao) {
		this.messageRecordCommonDao = messageRecordCommonDao;
	}
	@Autowired
	public void setMemberConfigCommonDao(CommonDao<MemberConfig> memberConfigCommonDao) {
		this.memberConfigCommonDao = memberConfigCommonDao;
	}

	@Autowired
	public void setClassRequiredDao(CommonDao<ClassRequired> classRequiredDao) {
		this.classRequiredDao = classRequiredDao;
	}
	@Autowired
	public void setTraineeService(TraineeService traineeService) {
		this.traineeService = traineeService;
	}
	@Autowired
	public void setClassInfoService(ClassInfoService classInfoService) {
		this.classInfoService = classInfoService;
	}

	@Autowired
	public void setSignDetailService(SignDetailService signDetailService) {
		this.signDetailService = signDetailService;
	}

	@Autowired
	public void setClassInfoDao(CommonDao<ClassInfo> classInfoDao) {
		this.classInfoDao = classInfoDao;
	}

	@Autowired
	public void setResearchQuestionaryDao(CommonDao<ResearchQuestionary> researchQuestionaryDao) {
		this.researchQuestionaryDao = researchQuestionaryDao;
	}

	@Autowired
	public void setResearchAnswerRecordDao(CommonDao<ResearchAnswerRecord> researchAnswerRecordDao) {
		this.researchAnswerRecordDao = researchAnswerRecordDao;
	}

	@Autowired
	public void setResearchRecordDao(CommonDao<ResearchRecord> researchRecordDao) {
		this.researchRecordDao = researchRecordDao;
	}

	@Autowired
	public void setOnlineCourseService(ClassOnlineCourseService onlineCourseService) {
		this.onlineCourseService = onlineCourseService;
	}

	@Autowired
	public void setDao(CommonDao<Trainee> dao) {
		this.dao = dao;
	}

	@Autowired
	public void setClassQuotaDetailService(ClassQuotaDetailService classQuotaDetailService) {
		this.classQuotaDetailService = classQuotaDetailService;
	}

	@Autowired
	public void setClassQuotaService(ClassQuotaService classQuotaService) {
		this.classQuotaService = classQuotaService;
	}

	@Autowired
	public void setOrganizationDetailService(OrganizationDetailService organizationDetailService) {
		this.organizationDetailService = organizationDetailService;
	}

	@Autowired
	public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}


	@Autowired
	public void setMessageSender(MessageSender messageSender) {
		this.messageSender = messageSender;
	}

	@Autowired
	public void setTrainChatGroupService(TrainChatGroupService trainChatGroupService) {
		this.trainChatGroupService = trainChatGroupService;
	}

	@Override
	public PagedResult<Trainee> find(int page, int pageSize, String classId, Optional<Integer> type,
									 Optional<String> memberName, Optional<String> memberFullName, Optional<String> organizationName,
									 Optional<String> groupId, Optional<String> organizationId, Optional<Integer> audtiStatus,
									 Optional<Integer> commitQuestionary, Optional<Integer> source,Optional<Integer> register,
									 Optional<Integer> sortType) {
		// 重命名组织表
		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
		com.zxy.product.train.jooq.tables.Job ihrJobTable = JOB.as("ihrJobTable");
		Field<String> mbName = MEMBER.NAME.as("mbName");
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		Field<String> orgId = ORGANIZATION.ID.as("orgId");
		Field<String> org2Name = org2.NAME.as("org2Name");
		Field<String> jobName = JOB.NAME.as("jobName");

		com.zxy.product.train.jooq.tables.Organization ORG_LEVEL1 = ORGANIZATION.as("org_level1");
		com.zxy.product.train.jooq.tables.Organization ORG_LEVEL2 = ORGANIZATION.as("org_level2");
		com.zxy.product.train.jooq.tables.Organization ORG_LEVEL3 = ORGANIZATION.as("org_level3");
		com.zxy.product.train.jooq.tables.Organization ORG_LEVEL4 = ORGANIZATION.as("org_level4");


		// 构建语句
		SelectOnConditionStep<Record> step = dao.execute(x -> x
				.selectDistinct(Fields.start().add(TRAINEE.ID, TRAINEE.JOB, MEMBER.FULL_NAME,
												   MEMBER.HEAD_PORTRAIT, MEMBER.HEAD_PORTRAIT_PATH,
												   mbName, orgId, orgName, ORGANIZATION.LEVEL,
												   ihrJobTable.NAME, jobName, TRAINEE.SEX,
												   TRAINEE.PHONE_NUMBER, TRAINEE.EMAIL, TRAINEE.AUDIT_STATUS,
												   TRAINEE.MEMBER_ID, TRAINEE.COMMIT_QUESTIONARY,
												   TRAINEE.SORT, TRAINEE.LEVEL_ID, TRAINEE.NATION,
												   TRAINEE.TRAINEE_GROUP_ID,org2Name,
												   ORGANIZATION.PATH,
												   TRAINEE.REMARK, ORGANIZATION.ORDER,
												   TRAINEE.SORT_FOR_GROUP, TRAINEE.NEW_COMPANY,
												   TRAINEE.NEW_ORGANIZATION, TRAINEE.REGISTER,
												   TRAINEE.SORT_NEW).end())
				.from(TRAINEE)
				.leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
				.leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
				.leftJoin(ORG_LEVEL1).on(
						ORG_LEVEL1.ID.eq(
								substringIndex(
										substringIndex(ORGANIZATION.PATH, ",", 1),
										",",
										-1
								)
						)
				).leftJoin(ORG_LEVEL2).on(
						ORG_LEVEL2.ID.eq(
								substringIndex(
										substringIndex(ORGANIZATION.PATH, ",", 2),
										",",
										-1
								)
						)
				)
				.leftJoin(ORG_LEVEL3).on(
						ORG_LEVEL3.ID.eq(
								substringIndex(
										substringIndex(ORGANIZATION.PATH, ",", 3),
										",",
										-1
								)
						)
				)
				.leftJoin(ORG_LEVEL4).on(
						ORG_LEVEL4.ID.eq(
								substringIndex(
										substringIndex(ORGANIZATION.PATH, ",", 4),
										",",
										-1
								)
						)
				)
				.leftJoin(org2).on(ORGANIZATION.COMPANY_ID.eq(org2.ID))
				.leftJoin(POSITION).on(MEMBER.MAJOR_POSITION_ID.eq(POSITION.ID))
				.leftJoin(JOB).on(POSITION.JOB_ID.eq(JOB.ID))
				.leftJoin(ihrJobTable).on(ihrJobTable.ID.eq(MEMBER.JOB_ID))
				.leftJoin(ORGANIZATION_DETAIL).on(TRAINEE.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB)));

		List<Condition> collect = Stream.of(Optional.of(classId).map(TRAINEE.CLASS_ID::eq),
				type.map(TRAINEE.TYPE::eq), memberName.map(MEMBER.NAME::contains),
				memberFullName.map(MEMBER.FULL_NAME::contains), organizationName.map(TRAINEE.NEW_COMPANY::contains),
				groupId.map(TRAINEE.TRAINEE_GROUP_ID::eq), organizationId.map(ORGANIZATION_DETAIL.ROOT::eq),
				audtiStatus.map(TRAINEE.AUDIT_STATUS::eq), Optional.of(DELETE_FLASE).map(TRAINEE.DELETE_FLAG::eq),
				commitQuestionary.map(TRAINEE.COMMIT_QUESTIONARY::eq), source.map(TRAINEE.SOURCE::eq))
				.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());


		if(register.isPresent()){
			Integer registerStatus = register.get();
			if (registerStatus == 1){
				collect.add(TRAINEE.REGISTER.eq(1));

			}else{
				collect.add(TRAINEE.REGISTER.eq(0).or(TRAINEE.REGISTER.isNull()));
			}
		}

		// 获取行数
		Integer count = dao.execute(x -> x.fetchCount(step.where(collect)));

		// 客户强烈要求的排序规则
		// 学员列表排序
		if (sortType.isPresent()) {
			step.orderBy(TRAINEE.SORT_NEW.asc().nullsLast(),
						 DSL.coalesce(ORG_LEVEL1.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL1.CREATE_TIME.asc(),
						 DSL.coalesce(ORG_LEVEL2.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL2.CREATE_TIME.asc(),
						 DSL.coalesce(ORG_LEVEL3.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL3.CREATE_TIME.asc(),
						 DSL.coalesce(ORG_LEVEL4.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL4.CREATE_TIME.asc(),
//						 getOrgOrderCondition(org2),
//						 getOrgCreateTimeCondition(org2),
						 getMemberNameCondition());
		} else {
			// 报名列表排序 以及 其他调用该方法使用的默认排序规则
			step.orderBy(getAuditStatusCondition(),
						 DSL.coalesce(ORG_LEVEL1.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL1.CREATE_TIME.asc(),
						 DSL.coalesce(ORG_LEVEL2.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL2.CREATE_TIME.asc(),
						 DSL.coalesce(ORG_LEVEL3.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL3.CREATE_TIME.asc(),
						 DSL.coalesce(ORG_LEVEL4.ORDER, Integer.MAX_VALUE).asc(),
						 ORG_LEVEL4.CREATE_TIME.asc(),
//						 getOrgOrderCondition(org2),
//						 getOrgCreateTimeCondition(org2),
						 getMemberNameCondition());
		}


		// 获取列表
		List<Trainee> list = step.limit((page - 1) * pageSize, pageSize).fetch(r -> {
			Trainee trainee = new Trainee();
			trainee.setId(r.getValue(TRAINEE.ID));
			Member mb = new Member();
			mb.setName(r.getValue(mbName));
			mb.setFullName(r.getValue(MEMBER.FULL_NAME));
			if (r.getValue(TRAINEE.JOB) != null &&  !"".equals(r.getValue(TRAINEE.JOB))) {
				mb.setJobName(r.getValue(TRAINEE.JOB));
			} else if (r.getValue(ihrJobTable.NAME) != null && !"".equals(r.getValue(ihrJobTable.NAME))) {
			    mb.setJobName(r.getValue(ihrJobTable.NAME));
			}else {
				mb.setJobName(r.getValue(jobName));
			}
			mb.setHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT));
			mb.setHeadPortraitPath(r.getValue(MEMBER.HEAD_PORTRAIT_PATH));
			trainee.setMember(mb);
			trainee.setTraineeGroupId(r.getValue(TRAINEE.TRAINEE_GROUP_ID));
			trainee.setSex(r.getValue(TRAINEE.SEX));
			trainee.setOrganizationId(r.getValue(orgId));
			trainee.setOrganizationName(r.getValue(orgName));
			trainee.setOrganizationLevel(r.getValue(ORGANIZATION.LEVEL));
			trainee.setPath(r.getValue(ORGANIZATION.PATH));
			trainee.setCompanyName(r.getValue(org2Name));
			trainee.setPhoneNumber(r.getValue(TRAINEE.PHONE_NUMBER));
			trainee.setLevelId(r.getValue(TRAINEE.LEVEL_ID));
			trainee.setMemberId(r.getValue(TRAINEE.MEMBER_ID));
			trainee.setNation(r.getValue(TRAINEE.NATION));
			trainee.setRemark(r.getValue(TRAINEE.REMARK));
			trainee.setEmail(r.getValue(TRAINEE.EMAIL));
			trainee.setAuditStatus(r.getValue(TRAINEE.AUDIT_STATUS));
			trainee.setSort(r.getValue(TRAINEE.SORT));
			trainee.setSortForGroup(r.getValue(TRAINEE.SORT_FOR_GROUP));
			trainee.setOrganizationOrder(r.getValue(ORGANIZATION.ORDER));
			trainee.setCommitQuestionary(r.getValue(TRAINEE.COMMIT_QUESTIONARY));
			trainee.setNewCompany(r.getValue(TRAINEE.NEW_COMPANY));
			trainee.setNewOrganization(r.getValue(TRAINEE.NEW_ORGANIZATION));
			trainee.setRegister(r.getValue(TRAINEE.REGISTER) == null ? NO_SHOW :r.getValue(TRAINEE.REGISTER));
			trainee.setSortNew(r.getValue(TRAINEE.SORT_NEW));
			return trainee;
		});

		return PagedResult.create(count, list);
	}

	Field<String> substringIndex(Field<String> field, String delimiter, int count) {
		return DSL.field(
				"SUBSTRING_INDEX({0}, {1}, {2})",
				SQLDataType.VARCHAR,
				field,
				DSL.val(delimiter),
				DSL.val(count)
		);
	}

	private  SortField<Integer> getAuditStatusCondition() {
		return DSL.when(TRAINEE.AUDIT_STATUS.eq(0), 0)
				  .when(TRAINEE.AUDIT_STATUS.eq(2), 1)
				  .when(TRAINEE.AUDIT_STATUS.eq(1), 2)
				  .otherwise(3)
				  .asc();
	}

	private SortField<Integer> getMemberNameCondition() {
		return DSL.when(MEMBER.NAME.like("E%"), 0)
				  .otherwise(1)
				  .asc();
	}

	private SortField<Long> getOrgCreateTimeCondition(com.zxy.product.train.jooq.tables.Organization org) {
		return org.CREATE_TIME.asc();
	}

	private SortField<Integer> getOrgOrderCondition(com.zxy.product.train.jooq.tables.Organization org) {
		   return DSL.when(org.ORDER.isNotNull(), org.ORDER)
						   .otherwise(Integer.MAX_VALUE) // NULL 排在最后
						   .asc();
	}

	@Override
	public PagedResult<Trainee> findForResponseCenter(int page, int pageSize, String classId,
													  Optional<String> memberFullName, Optional<String> organizationId, Optional<Integer> audtiStatus) {
		// 重命名组织表
		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
		com.zxy.product.train.jooq.tables.MemberConfig mc2 = MEMBER_CONFIG.as("mc2");
		com.zxy.product.train.jooq.tables.Job ihrJobTable = JOB.as("ihrJobTable");
		Field<String> mbName = MEMBER.NAME.as("mbName");
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		Field<String> orgId = ORGANIZATION.ID.as("orgId");
		Field<String> org2Name = org2.NAME.as("organizationName");
		Field<String> companyId = org2.ID.as("companyId");
		Field<String> jobName = JOB.NAME.as("jobName");
		Field<String> levelName = mc2.VALUE.as("levelName");

		// 构建语句
		SelectConditionStep<Record> step = dao.execute(x -> x
				.selectDistinct(Fields.start().add(TRAINEE.ID,TRAINEE.JOB, MEMBER.FULL_NAME, mbName, orgId,ORGANIZATION.LEVEL, orgName, companyId,
						jobName, ihrJobTable.NAME, levelName, TRAINEE.SEX, TRAINEE.PHONE_NUMBER, TRAINEE.EMAIL, TRAINEE.AUDIT_STATUS,
						TRAINEE.MEMBER_ID, TRAINEE.COMMIT_QUESTIONARY, TRAINEE.SORT, TRAINEE.LEVEL_ID, TRAINEE.NATION,TRAINEE.NEW_COMPANY,
						TRAINEE.REMARK, ORGANIZATION.ORDER, TRAINEE.SORT_FOR_GROUP, MEMBER_CONFIG.VALUE).end())
				.from(TRAINEE)
				.leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
				.leftJoin(ORGANIZATION).on(TRAINEE.ORGANIZATION_ID.eq(ORGANIZATION.ID))
				.leftJoin(org2).on(ORGANIZATION.COMPANY_ID.eq(org2.ID))
				.leftJoin(POSITION).on(MEMBER.MAJOR_POSITION_ID.eq(POSITION.ID))
				.leftJoin(JOB).on(POSITION.JOB_ID.eq(JOB.ID))
				.leftJoin(ihrJobTable).on(ihrJobTable.ID.eq(MEMBER.JOB_ID))
				.leftJoin(MEMBER_CONFIG).on(MEMBER_CONFIG.ID.eq(TRAINEE.NATION))
				.leftJoin(mc2).on(TRAINEE.LEVEL_ID.eq(mc2.ID))
				.leftJoin(ORGANIZATION_DETAIL).on(TRAINEE.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB))
				.where(TRAINEE.CLASS_ID.eq(classId))
				.and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL)).and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)));
		// 追加条件
		memberFullName.ifPresent(x -> {
			step.and(MEMBER.FULL_NAME.contains(x));
		});
		organizationId.ifPresent(x -> {
			step.and(ORGANIZATION_DETAIL.ROOT.eq(x));
		});
		if (audtiStatus.isPresent()) {
			step.and(TRAINEE.AUDIT_STATUS.eq(audtiStatus.get()));
		} else {
			step.and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE).or(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_WAIT)));
		}
		// 获取行数
		Integer count = dao.execute(x -> x.fetchCount(step));
		// 获取列表
		List<Trainee> list = step.orderBy(TRAINEE.SORT).limit((page - 1) * pageSize, pageSize)
				.fetch(r -> {
					Trainee trainee = new Trainee();
					trainee.setId(r.getValue(TRAINEE.ID));
					Member mb = new Member();
					mb.setName(r.getValue(mbName));
					mb.setFullName(r.getValue(MEMBER.FULL_NAME));
					if (r.getValue(TRAINEE.JOB) != null &&  !"".equals(r.getValue(TRAINEE.JOB))) {
		                mb.setJobName(r.getValue(TRAINEE.JOB));
		            } else if (r.getValue(ihrJobTable.NAME) != null && !"".equals(r.getValue(ihrJobTable.NAME))) {
		                mb.setJobName(r.getValue(ihrJobTable.NAME));
		            } else {
		                mb.setJobName(r.getValue(jobName));
		            }
					trainee.setMember(mb);
					trainee.setSex(r.getValue(TRAINEE.SEX));
					trainee.setOrganizationId(r.getValue(orgId));
					trainee.setOrganizationName(r.getValue(orgName));
					trainee.setOrganizationLevel(r.getValue(ORGANIZATION.LEVEL));
					trainee.setCompanyName(r.getValue(TRAINEE.NEW_COMPANY));
					trainee.setCompanyId(r.getValue(companyId));
					trainee.setPhoneNumber(r.getValue(TRAINEE.PHONE_NUMBER));
					trainee.setLevelId(r.getValue(TRAINEE.LEVEL_ID));
					trainee.setMemberId(r.getValue(TRAINEE.MEMBER_ID));
					trainee.setNation(r.getValue(TRAINEE.NATION));
					trainee.setRemark(r.getValue(TRAINEE.REMARK));
					trainee.setEmail(r.getValue(TRAINEE.EMAIL));
					trainee.setLevelName(r.getValue(levelName));
					trainee.setAuditStatus(r.getValue(TRAINEE.AUDIT_STATUS));
					trainee.setSort(r.getValue(TRAINEE.SORT));
					trainee.setSortForGroup(r.getValue(TRAINEE.SORT_FOR_GROUP));
					trainee.setNationName(r.getValue(MEMBER_CONFIG.VALUE));
					trainee.setOrganizationOrder(r.getValue(ORGANIZATION.ORDER));
					trainee.setCommitQuestionary(r.getValue(TRAINEE.COMMIT_QUESTIONARY));
					return trainee;
				});
		return PagedResult.create(count, list);
	}

	@Override
	public PagedResult<Trainee> findWaitGroupTrainees(int page, int pageSize, String classId, String groupId,
													  Optional<String> memberFullName, Optional<String> memberName, Optional<String> organizationName) {
		// 重命名
		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
		com.zxy.product.train.jooq.tables.Job ihrJobTable = JOB.as("ihrJobTable");
		Field<String> mbName = MEMBER.NAME.as("mbName");
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		Field<String> orgId = ORGANIZATION.ID.as("orgId");
//		Field<String> org2Name = org2.NAME.as("organizationName");
		Field<String> jobName = JOB.NAME.as("jobName");

		// 构建语句
		SelectConditionStep<Record> step = dao
				.execute(x -> x
						.selectDistinct(Fields.start()
								.add(TRAINEE.ID,TRAINEE.JOB, MEMBER.FULL_NAME, mbName, orgId, orgName,ORGANIZATION.LEVEL, TRAINEE.NEW_COMPANY, jobName,
										TRAINEE.SEX, TRAINEE.PHONE_NUMBER, TRAINEE.EMAIL, TRAINEE.AUDIT_STATUS, ihrJobTable.NAME,
										TRAINEE.SORT, ORGANIZATION.ORDER, TRAINEE.SORT_FOR_GROUP, MEMBER_CONFIG.VALUE)
								.end())
						.from(TRAINEE).leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID)).leftJoin(ORGANIZATION)
						.on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(org2)
						.on(ORGANIZATION.COMPANY_ID.eq(org2.ID)).leftJoin(POSITION)
						.on(MEMBER.MAJOR_POSITION_ID.eq(POSITION.ID)).leftJoin(JOB).on(POSITION.JOB_ID.eq(JOB.ID))
						.leftJoin(MEMBER_CONFIG).on(MEMBER_CONFIG.ID.eq(TRAINEE.NATION)))
				.leftJoin(ihrJobTable).on(ihrJobTable.ID.eq(MEMBER.JOB_ID))
				.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
				.and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
				.and(memberFullName.map(MEMBER.FULL_NAME::contains).orElse(DSL.trueCondition()))
				.and(memberName.map(MEMBER.NAME::contains).orElse(DSL.trueCondition()))
				.and(organizationName.map(TRAINEE.NEW_COMPANY::contains).orElse(DSL.trueCondition()))
				.and(TRAINEE.TRAINEE_GROUP_ID.eq(groupId).or(TRAINEE.TRAINEE_GROUP_ID.eq("0")));

		// 获取行数
		Integer count = dao.execute(x -> x.fetchCount(step));

		// 获取列表
		List<Trainee> list = step.orderBy(TRAINEE.SORT).limit((page - 1) * pageSize, pageSize)
				.fetch(r -> {
					Trainee trainee = new Trainee();
					trainee.setId(r.getValue(TRAINEE.ID));
					Member mb = new Member();
					mb.setName(r.getValue(mbName));
					mb.setFullName(r.getValue(MEMBER.FULL_NAME));
					if (r.getValue(TRAINEE.JOB) != null &&  !"".equals(r.getValue(TRAINEE.JOB))) {
		                mb.setJobName(r.getValue(TRAINEE.JOB));
		            } else if (r.getValue(ihrJobTable.NAME) != null && !"".equals(r.getValue(ihrJobTable.NAME))) {
		                mb.setJobName(r.getValue(ihrJobTable.NAME));
		            } else {
		                mb.setJobName(r.getValue(jobName));
		            }
					trainee.setMember(mb);
					trainee.setSex(r.getValue(TRAINEE.SEX));
					trainee.setOrganizationId(r.getValue(orgId));
					trainee.setOrganizationName(r.getValue(orgName));
					trainee.setOrganizationLevel(r.getValue(ORGANIZATION.LEVEL));
					trainee.setCompanyName(r.getValue(TRAINEE.NEW_COMPANY));
					trainee.setPhoneNumber(r.getValue(TRAINEE.PHONE_NUMBER));
					trainee.setEmail(r.getValue(TRAINEE.EMAIL));
					trainee.setAuditStatus(r.getValue(TRAINEE.AUDIT_STATUS));
					trainee.setSort(r.getValue(TRAINEE.SORT));
					trainee.setSortForGroup(r.getValue(TRAINEE.SORT_FOR_GROUP));
					trainee.setNationName(r.getValue(MEMBER_CONFIG.VALUE));
					trainee.setOrganizationOrder(r.getValue(ORGANIZATION.ORDER));
					return trainee;
				});
		return PagedResult.create(count, list);

	}

	@Override
	public Trainee updateStatus(String id, int auditStatus, Optional<String> auditOpinion, int quotaType) {
		Trainee trainee = dao.get(id);
		trainee.setAuditStatus(auditStatus);
		auditOpinion.ifPresent(trainee::setAuditOpinion);
		trainee.setSort(Trainee.DEFAULT_SORT);
		String classId = trainee.getClassId();
		// 如果审核为同意，进行判断
		if (auditStatus == AUDIT_AGREE) {
			// 查找当前班级审核方式和计划人数
			Optional<ClassQuota> OPclassQuota = classQuotaService.findByClassId(classId);
			if (OPclassQuota.isPresent()) {
				ClassQuota cq = OPclassQuota.get();
				// 判断配额是否已满
				if (this.haveQuota(quotaType, trainee.getOrganizationId(), trainee.getClassId(), cq.getAmount())) {
					trainee.setModifyDate(null);
					Trainee trainee2 = dao.update(trainee);
					// add by acong 20180227
					trainee2 = changeSort(trainee2);
					updateReleatedOrgOrder(Trainee.ADD, trainee2.getId(), trainee2.getSort(), classId);
					//报名审核通过，推送智慧校园学员数据
					messageSender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_INSERT,
							MessageHeaderContent.CLASSID, classId,
							MessageHeaderContent.MEMBERID, trainee.getMemberId());

					//报名审核通过，推送IM群聊
					messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_ADD,
							MessageHeaderContent.CLASSID, classId,
							MessageHeaderContent.MEMBERID, trainee.getMemberId());
					return trainee2;
				}
			}
		} else {
			trainee.setModifyDate(null);
			return dao.update(trainee);
		}
		return null;
	}

	// 通过班级id获取报名人数
	@Override
	public int getCountByClassId(String classId) {
		return dao
				.execute(x -> x.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
						.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE)))
				.fetchOne(TRAINEE.ID.count());
	}
	// 通过班级id获取报名人数
	@Override
	public int getCountByClassIdApp(String classId) {
		return dao
				.execute(x -> x.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
						.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)).and(TRAINEE.AUDIT_STATUS.isNull()))
				.fetchOne(TRAINEE.ID.count());
	}

	@Override
	public Map<String, Integer> getCountByOrgIds(String classId, Set<String> orgIds) {
		return dao
				.execute(x -> x.selectDistinct(Fields.start().add(ORGANIZATION_DETAIL.ROOT, TRAINEE.ID.count()).end())
						.from(ORGANIZATION_DETAIL).leftJoin(MEMBER)
						.on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB)).leftJoin(TRAINEE)
						.on(TRAINEE.MEMBER_ID.eq(MEMBER.ID).and(TRAINEE.CLASS_ID.eq(classId))
								.and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE)).and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE))
								.and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL)))
						.where(ORGANIZATION_DETAIL.ROOT.in(orgIds)))
				.groupBy(ORGANIZATION_DETAIL.ROOT).fetchMap(ORGANIZATION_DETAIL.ROOT, TRAINEE.ID.count());
	}

	// 分省配额下添加正式学员
	@Override
	public Trainee innerDesperSave(Trainee trainee) {
		boolean flag = true;
		// 根据当前学员对象的orgId获取到所有的父orgIds
		List<OrganizationDetail> organizationDetails = organizationDetailService.find(trainee.getOrganizationId());
		Set<String> orgIds = new HashSet<>();
		organizationDetails.forEach(x -> {
			orgIds.add(x.getRoot());
		});

		// 根据班级id和组织ids获取到对应组织的配额详情
		List<ClassQuotaDetail> classQuotaDetails = classQuotaDetailService.findByOrgIds(trainee.getClassId(), orgIds);
		if (classQuotaDetails.size() > 0 && classQuotaDetails != null) {
			Set<String> orgs = new HashSet<>();
			classQuotaDetails.forEach(x -> {
				orgs.add(x.getOrganizationId());
			});

			// 获取对应组织已存在的学员数
			Map<String, Integer> map = getCountByOrgIds(trainee.getClassId(), orgs);
			// 如果配额数小于或等于已存在学员数，那么添加失败
			for (ClassQuotaDetail cqd : classQuotaDetails) {
				if (cqd.getQuantity() <= map.get(cqd.getOrganizationId())) {
					flag = false;
				}
			}
		} else {
			flag = false;
		}
		// 如果还有配额成功添加学员
		if (flag) {
			return dao.insert(trainee);
		}
		return null;
	}
	public String shortNameMethod(List<GroupConfigurationValue> glist,Optional<Organization> org){
		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
		Organization organization = orgDao.execute(r->r.select(Fields.start().add(org2.NAME).add(ORGANIZATION).end()).from(ORGANIZATION).leftJoin(org2).on(ORGANIZATION.COMPANY_ID.eq(org2.ID)).where(ORGANIZATION.ID.eq(org.get().getId())).fetchOne(record -> {
			Organization o = record.into(ORGANIZATION).into(Organization.class);
			o.setCompanyName(record.getValue(org2.NAME));
			return o;
		}));
		String newName = null;
		if(org.isPresent()&&org.get().getPath()!=null){
			String path = org.get().getPath();
			newName = organization.getCompanyName();
			if (glist != null && glist.size() > 0) {
				for (int i = 0; i < glist.size(); i++) {
					if (path!=null&&path.contains(glist.get(i).getPath())) {
						newName = glist.get(i).getShortName();
						return newName;
					}
				}
			}
		}
		return newName;
	}
	@Override
	public Trainee updateSort(String id, Optional<Integer> sort, Optional<Integer> sortForGroup) {
		Trainee trainee = dao.get(id);
		Integer oldOrder = trainee.getSort();
//		(int operType,Optional<Integer> order, String classId)
		trainee.setManualSorting(1);
		trainee.setSort(getOrgOrder(Trainee.UPDATE, sort, trainee.getClassId()));
		trainee.setModifyDate(null);
		Trainee t = dao.update(trainee);
		updateReleatedOrgOrder(Trainee.UPDATE, id, oldOrder, trainee.getClassId());
		return t;
	}

	/**
	 * 个人中心我的档案 -- 班级
	 * modify by acong date 2018/02/05
	 */
	@Override
	public PagedResult<Trainee> personPageCount(Integer page, Integer pageSize, Optional<Long> startTime, Optional<Long> endTime, List<String> currentUserId) {

		return dao.execute(e->{
			Collection<Field<?>> aliasFields = Fields.start()
					.add(DSL.field("id"))
					.add(DSL.field("tableType"))
					.add(DSL.field("organizationName"))
					.add(DSL.field("projectName"))
					.add(DSL.field("startTime"))
					.add(DSL.field("endTime"))
					.add(DSL.field("auditStatus"))
					.add(DSL.field("classStatus"))
					.end();

			Collection<Field<?>> trainFields = Fields.start()
					.add(TRAINEE.ID.as("id"))
					.add(DSL.val(0).as("tableType"))
					.add(ORGANIZATION.NAME.as("organizationName"))
					.add(PROJECT.NAME.as("projectName"))
					.add(CLASS_INFO.ARRIVE_DATE.as("startTime"))
					.add(CLASS_INFO.RETURN_DATE.as("endTime"))
					.add(TRAINEE.AUDIT_STATUS.as("auditStatus"))
					.add(CLASS_INFO.STATUS.as("classStatus"))
					.end();

			Collection<Field<?>> trainHistoryFields = Fields.start()
					.add(STUDENT_HISTORY.ID.as("id"))
					.add(DSL.val(1).as("tableType"))
					.add(ORGANIZATION.NAME.as("organizationName"))
					.add(PROJECT_HISTORY.NAME.as("projectName"))
					.add(CLASS_HISTORY.ARRIVE_DATE.as("startTime"))
					.add(CLASS_HISTORY.RETURN_DATE.as("endTime"))
					.add(STUDENT_HISTORY.STATUS.as("auditStatus"))
					.add(DSL.val(1).as("classStatus"))
					.end();

			Stream<Optional<Condition>> conditionsA = Stream.of(
					Optional.of(Trainee.DELETE_FLASE).map(TRAINEE.DELETE_FLAG::eq),
					Optional.of(ClassInfo.DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq),
					Optional.of(Project.DELETE_FLASE).map(PROJECT.DELETE_FLAG::eq),
					Optional.of(Trainee.TRAINEE_TYPE_FORMAL).map(TRAINEE.TYPE::eq),
					startTime.map(CLASS_INFO.ARRIVE_DATE::ge),
					endTime.map(CLASS_INFO.RETURN_DATE::le),
					Optional.of(currentUserId).map(TRAINEE.MEMBER_ID::in),
					Optional.of(1).map(CLASS_INFO.NOTICE::eq));

			Condition condA = conditionsA.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
					.orElse(DSL.trueCondition());

			SelectConditionStep<Record> stepA = e.selectDistinct(trainFields)
					.from(TRAINEE)
					.leftJoin(CLASS_INFO).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
					.leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
					.leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(condA);

			SelectConditionStep<Record> stepB = e.selectDistinct(trainHistoryFields).from(STUDENT_HISTORY)
					.leftJoin(CLASS_HISTORY).on(CLASS_HISTORY.ID.eq(STUDENT_HISTORY.CLASS_ID))
					.leftJoin(PROJECT_HISTORY).on(CLASS_HISTORY.PROJECT_ID.eq(PROJECT_HISTORY.ID))
					.leftJoin(ORGANIZATION).on(PROJECT_HISTORY.ORGANIZATION_ID.eq(ORGANIZATION.ID))
					.where(startTime.map(CLASS_HISTORY.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
					.and(endTime.map(CLASS_HISTORY.RETURN_DATE::le).orElse(DSL.trueCondition()))
					.and(STUDENT_HISTORY.PERSON_ID.in(currentUserId));

			int count = e.fetchCount(e.selectDistinct(aliasFields).from(stepA.unionAll(stepB)));

			List<Trainee> list = new ArrayList<>();

			return PagedResult.create(count,list);
		});

	}

	/**
	 * 个人中心我的档案 -- 班级
	 * modify by acong date 2018/02/05
	 */
	@Override
	public Map<String,Object> personPage(Integer page, Integer pageSize, Optional<Long> startTime, Optional<Long> endTime, List<String> currentUserId,boolean pageSwitch) {

		return dao.execute(e->{
			Collection<Field<?>> aliasFields = Fields.start()
					.add(DSL.field("id"))
					.add(DSL.field("tableType"))
					.add(DSL.field("organizationName"))
					.add(DSL.field("projectName"))
					.add(DSL.field("startTime"))
					.add(DSL.field("endTime"))
					.add(DSL.field("auditStatus"))
					.add(DSL.field("classStatus"))
					.end();

			Collection<Field<?>> trainFields = Fields.start()
					.add(TRAINEE.ID.as("id"))
					.add(DSL.val(0).as("tableType"))
					.add(ORGANIZATION.NAME.as("organizationName"))
					.add(PROJECT.NAME.as("projectName"))
					.add(CLASS_INFO.ARRIVE_DATE.as("startTime"))
					.add(CLASS_INFO.RETURN_DATE.as("endTime"))
					.add(TRAINEE.AUDIT_STATUS.as("auditStatus"))
					.add(CLASS_INFO.STATUS.as("classStatus"))
					.end();

			Collection<Field<?>> trainHistoryFields = Fields.start()
					.add(STUDENT_HISTORY.ID.as("id"))
					.add(DSL.val(1).as("tableType"))
					.add(ORGANIZATION.NAME.as("organizationName"))
					.add(PROJECT_HISTORY.NAME.as("projectName"))
					.add(CLASS_HISTORY.ARRIVE_DATE.as("startTime"))
					.add(CLASS_HISTORY.RETURN_DATE.as("endTime"))
					.add(STUDENT_HISTORY.STATUS.as("auditStatus"))
					.add(DSL.val(1).as("classStatus"))
					.end();

			Stream<Optional<Condition>> conditionsA = Stream.of(
					Optional.of(Trainee.DELETE_FLASE).map(TRAINEE.DELETE_FLAG::eq),
					Optional.of(ClassInfo.DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq),
					Optional.of(Project.DELETE_FLASE).map(PROJECT.DELETE_FLAG::eq),
					Optional.of(Trainee.TRAINEE_TYPE_FORMAL).map(TRAINEE.TYPE::eq),
					startTime.map(CLASS_INFO.ARRIVE_DATE::ge),
					endTime.map(CLASS_INFO.RETURN_DATE::le),
					Optional.of(currentUserId).map(TRAINEE.MEMBER_ID::in),
					Optional.of(1).map(CLASS_INFO.NOTICE::eq));

			Condition condA = conditionsA.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
					.orElse(DSL.trueCondition());

			SelectConditionStep<Record> stepA = e.selectDistinct(trainFields)
					.from(TRAINEE)
					.leftJoin(CLASS_INFO).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
					.leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
					.leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(condA);

			SelectConditionStep<Record> stepB = e.selectDistinct(trainHistoryFields).from(STUDENT_HISTORY)
					.leftJoin(CLASS_HISTORY).on(CLASS_HISTORY.ID.eq(STUDENT_HISTORY.CLASS_ID))
					.leftJoin(PROJECT_HISTORY).on(CLASS_HISTORY.PROJECT_ID.eq(PROJECT_HISTORY.ID))
					.leftJoin(ORGANIZATION).on(PROJECT_HISTORY.ORGANIZATION_ID.eq(ORGANIZATION.ID))
					.where(startTime.map(CLASS_HISTORY.ARRIVE_DATE::ge).orElse(DSL.trueCondition()))
					.and(endTime.map(CLASS_HISTORY.RETURN_DATE::le).orElse(DSL.trueCondition()))
					.and(STUDENT_HISTORY.PERSON_ID.in(currentUserId));

			int count = 0;
			if(pageSwitch){
				count = e.fetchCount(e.selectDistinct(aliasFields).from(stepA.unionAll(stepB)));
			}
			Result<Record> result = e.selectDistinct(aliasFields).from(stepA.unionAll(stepB))
					.orderBy(DSL.field("startTime").desc(),DSL.field("endTime").desc()).limit((page - 1) * pageSize, pageSize + 1).fetch();

			List<Trainee> resultList = result.stream().map(r -> {
				Trainee trainee = new Trainee();
				trainee.setId(r.getValue(DSL.field("id")).toString());
				if (r.getValue(DSL.field("organizationName")) != null) {
					trainee.setOrganizationName(r.getValue(DSL.field("organizationName")).toString());
				}
				if (r.getValue(DSL.field("projectName")) != null) {
					trainee.setProjectName(r.getValue(DSL.field("projectName")).toString());
				}
				if (r.getValue(DSL.field("startTime")) != null) {
					trainee.setStartTime(Long.parseLong(r.getValue(DSL.field("startTime")).toString()));
				}
				if (r.getValue(DSL.field("endTime")) != null) {
					trainee.setEndTime(Long.parseLong(r.getValue(DSL.field("endTime")).toString()));
				}
				if (r.getValue(DSL.field("classStatus")) != null) {
					trainee.setClassStatus(Integer.parseInt(r.getValue(DSL.field("classStatus")).toString()));
				}
				String auditStatus = r.getValue(DSL.field("auditStatus")).toString();
				if ("1".equals(r.getValue(DSL.field("tableType")).toString())) {
					if ("1".equals(auditStatus)) {
						trainee.setAuditStatus(AUDIT_REFUSE);
					} else if ("2".equals(auditStatus)) {
						trainee.setAuditStatus(AUDIT_WAIT);
					} else if ("3".equals(auditStatus)) {
						trainee.setAuditStatus(AUDIT_AGREE);
					}
				} else {
					trainee.setAuditStatus(Integer.parseInt(auditStatus));
				}
				return trainee;
			}).collect(Collectors.toList());

			Integer more = 0;
			if (!CollectionUtils.isEmpty(resultList) && resultList.size() > pageSize){
				resultList.remove(pageSize);
				more = 1;
			}

			Map<String,Object> resultMap = new HashMap<>();
			resultMap.put("items",resultList);
			resultMap.put("more",more);
			if (pageSwitch){
				resultMap.put("recordCount",count);
			}
			return resultMap;
		});

	}

	/**
	 * 个人中心我的档案 -- 查所有，用于导出
	 */
	@Override
	public List<Trainee> personAll(String currentUserId,Optional<Long> startTime, Optional<Long> endTime) {
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		Field<String> projectName = PROJECT.NAME.as("projectName");
		// 构建语句
		SelectOnConditionStep<Record> step = dao.execute(x -> x
				.selectDistinct(
						Fields.start()
								.add(TRAINEE.ID)
								.add(TRAINEE.AUDIT_STATUS)
								.add(orgName)
								.add(projectName)
								.add(CLASS_INFO.ARRIVE_DATE)
								.add(CLASS_INFO.RETURN_DATE)
								.add(CLASS_INFO.STATUS)
								.end())
				.from(TRAINEE)
				.leftJoin(CLASS_INFO).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
				.leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
				.leftJoin(ORGANIZATION).on(PROJECT.ORGANIZATION_ID.eq(ORGANIZATION.ID)));
		// 组合条件
		Stream<Optional<Condition>> conditions = Stream.of(
				Optional.of(currentUserId).map(TRAINEE.MEMBER_ID::eq),
				Optional.of(Trainee.DELETE_FLASE).map(TRAINEE.DELETE_FLAG::eq),
				Optional.of(Trainee.AUDIT_WAIT).map(TRAINEE.AUDIT_STATUS::ne),
				Optional.of(ClassInfo.DELETE_FLASE).map(CLASS_INFO.DELETE_FLAG::eq),
				Optional.of(Project.DELETE_FLASE).map(PROJECT.DELETE_FLAG::eq),
				Optional.of(Trainee.TRAINEE_TYPE_FORMAL).map(TRAINEE.TYPE::eq),
				startTime.map(CLASS_INFO.ARRIVE_DATE::ge),
				endTime.map(CLASS_INFO.ARRIVE_DATE::le));
		// 合并条件
		Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
				.orElse(DSL.trueCondition());
		Long date = System.currentTimeMillis();
		// 获取列表
		List<Trainee> list = step.where(c).orderBy(CLASS_INFO.ARRIVE_DATE.desc()).fetch(r -> {
			Trainee trainee = new Trainee();
			trainee.setId(r.getValue(TRAINEE.ID));
			trainee.setOrganizationName(r.getValue(orgName));
			trainee.setProjectName(r.getValue(projectName));
			trainee.setStartTime(r.getValue(CLASS_INFO.ARRIVE_DATE));
			trainee.setEndTime(r.getValue(CLASS_INFO.RETURN_DATE));
			trainee.setClassStatus(r.getValue(CLASS_INFO.STATUS));
			trainee.setAuditStatus(r.getValue(TRAINEE.AUDIT_STATUS));
			if (date < trainee.getStartTime()&& trainee.getAuditStatus().equals(1)) {
				trainee.setClassStatus(1);
				trainee.setNum(1);
			} else if (date >= trainee.getStartTime() && date <= trainee.getEndTime()) {
				trainee.setClassStatus(2);
			} else {
				trainee.setClassStatus(3);
			}
			if(trainee.getAuditStatus().equals(2)||trainee.getAuditStatus().equals(8)){
				trainee.setNum(2);
			}else if(date > trainee.getStartTime()&&trainee.getAuditStatus().equals(1)){
				trainee.setNum(3);
			}
			return trainee;
		});
		return list;
	}
	public String dateTime(){
		long dateTime = System.currentTimeMillis();
		String date = DateUtil.dateLongToString(dateTime,"yyyy-MM-dd HH:mm:ss");
		String updateTime = date.split(" ")[0].substring(0,7);
		return updateTime;
	}
	public String dateTimeMonth(){
		long dateTime = System.currentTimeMillis();
		String date = DateUtil.dateLongToString(dateTime,"yyyy-MM-dd HH:mm:ss");
		String updateTime = date.split(" ")[0].substring(5,7);
		return updateTime;
	}
	@Override
	public int delete(Trainee trainee,String id,boolean flag) {
		int num = Trainee.CONSTANT_ZERO;
		updateReleatedOrgOrder(Trainee.DELETE, id, trainee.getSort(), trainee.getClassId());
		//如果未提交结算数据就进行删除，反之进行纠偏
		if(flag){
			//如果是提交结算数据之后后添加的数据就进行直接删除，如果不是，就进行纠偏，
			if(trainee.getFinance()!=null&&Trainee.FINANCE_ONE.equals(trainee.getFinance())){
				num = dao.delete(id);
				deleteDataTrainCommonDao.insert(DeleteDataTrain.getDeleteData(TRAINEE.getName(), id,""));

				deleteDataTrainCommonDao.insert(DeleteDataTrain
						.getDeleteData(DeleteDataTrain.TRAINEE,id,""));
			}else {
				String updateTime = this.dateTime();
				trainee.setUpdateMonth(this.dateTimeMonth());
				trainee.setUpdateTime(updateTime);
				trainee.setDeleteFlag(trainee.DELETE_TRUE);
				trainee.setFinance(trainee.FINANCE_TWO);
				trainee.setModifyDate(null);
				dao.update(trainee);
			}
			num = trainee.CONSTANT_ONE;
		}else {
			num = dao.delete(id);
			deleteDataTrainCommonDao.insert(DeleteDataTrain.getDeleteData(TRAINEE.getName(), id,""));

			deleteDataTrainCommonDao.insert(DeleteDataTrain
					.getDeleteData(DeleteDataTrain.TRAINEE,id,""));
		}
		Integer praiseNumm = dao.execute(y->y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
        		.where(TRAINEE.CLASS_ID.eq(trainee.getClassId())).and(TRAINEE.COMMIT_QUESTIONARY.eq(1))
        		.and(TRAINEE.TYPE.eq(0))
        		.and(TRAINEE.DELETE_FLAG.eq(0))
        		).fetchOne(TRAINEE.ID.count());
        Integer praiseNums = dao.execute(y->y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
        		.where(TRAINEE.CLASS_ID.eq(trainee.getClassId())).and(TRAINEE.COMMIT_FOUR_DEGREES.eq(1))
        		.and(TRAINEE.TYPE.eq(0))
        		.and(TRAINEE.DELETE_FLAG.eq(0))
        		).fetchOne(TRAINEE.ID.count());
        Integer praiseNumn = dao.execute(y->y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
        		.where(TRAINEE.CLASS_ID.eq(trainee.getClassId())).and(TRAINEE.COMMIT_ABILITY.eq(1))
        		.and(TRAINEE.TYPE.eq(0))
        		.and(TRAINEE.DELETE_FLAG.eq(0))
        		).fetchOne(TRAINEE.ID.count());
        Integer praiseNuml = dao.execute(y->y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
        		.where(TRAINEE.CLASS_ID.eq(trainee.getClassId())).and(TRAINEE.COMMIT_SUPERIOR_LEADERSHIP.eq(1))
        		.and(TRAINEE.TYPE.eq(0))
        		.and(TRAINEE.DELETE_FLAG.eq(0))
        		).fetchOne(TRAINEE.ID.count());
		Optional<ClassInfo> classInfo = classInfoDao.getOptional(trainee.getClassId());
		if (classInfo.isPresent()) {
			ClassInfo c = classInfo.get();
			c.setSubmitNum(praiseNumm);
			c.setAbilitySubmitNum(praiseNumn);
			c.setFourDegreesSubmitNum(praiseNums);
			c.setSuperiorLeadershipSubmitNum(praiseNuml);
			classInfoDao.update(c);
		}

		// 如果班主任在IM群聊中，则标记为删除
		messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_DELETE,
						   MessageHeaderContent.CLASSID, trainee.getClassId(),
						   MessageHeaderContent.MEMBERID, trainee.getMemberId());

		messageSender.send(MessageTypeContent.CLASS_DELETE_TRAINEE, MessageHeaderContent.ID, trainee.getId(),
				MessageHeaderContent.CLASSID, trainee.getClassId(), MessageHeaderContent.MEMBERID, trainee.getMemberId());

		messageSender.send(MessageTypeContent.TRAIN_STUDY_PLAN_CONFIG_REVOKE,
				MessageHeaderContent.BUSINESS_ID, trainee.getClassId(),
				MessageHeaderContent.MEMBERID, trainee.getMemberId());//学习计划

		messageSender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_DELETE,
				MessageHeaderContent.CLASSID, trainee.getClassId(),
				MessageHeaderContent.MEMBERID, trainee.getMemberId());

		return num;

	}

	@Override
	public int delete(String classId, Integer type) {
		List<String> trainIds = dao.execute(e -> e.select(TRAINEE.ID).from(TRAINEE)
				.where(TRAINEE.CLASS_ID.eq(classId))
				.and(TRAINEE.AUDIT_STATUS.eq(8))
				.fetch(TRAINEE.ID));
		int count = dao.execute(x -> x.delete(TRAINEE)
						.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.AUDIT_STATUS.eq(8))).execute();
		trainIds.forEach(id -> deleteDataTrainCommonDao.insert(DeleteDataTrain
				.getDeleteData(DeleteDataTrain.TRAINEE,id,"")));
		return count;
	}

	// 学员报名
	@Override
	public Trainee signUp(String memberId, String phoneNumber, String email, Integer sex, Optional<String> levelId, String nation,
						  String classId, String organizationId,Optional<String> settleOrganizationId,
						  String createMemberId, Optional<String> remark, Optional<String> jobName,
						  Optional<Integer> mainlandPersonnel) {
		List<GroupConfigurationValue> glist = groupConfigurationValueDao.execute(e -> {
			List<GroupConfigurationValue> all = e.selectFrom(GROUP_CONFIGURATION_VALUE)
					.fetch()
					.into(GroupConfigurationValue.class);
			return all;
		});
		Optional<Organization> org = orgDao.getOptional(organizationId);


		// 封装学员数据
		Trainee trainee = new Trainee();
		trainee.forInsert();
		trainee.setMemberId(memberId);
		trainee.setType(Trainee.TRAINEE_TYPE_FORMAL);
		trainee.setPhoneNumber(phoneNumber);
		trainee.setEmail(email);
		trainee.setSex(sex);
		trainee.setNewCompany(this.shortNameMethod(glist,org));
		levelId.ifPresent(trainee::setLevelId);
		trainee.setNation(nation);
		remark.ifPresent(trainee::setRemark);
		jobName.ifPresent(trainee::setJob);
		trainee.setClassId(classId);
		trainee.setTraineeGroupId(Trainee.WAIT_GROUP);
		trainee.setOrganizationId(organizationId);
		settleOrganizationId.ifPresent(trainee::setSettleOrganizationId);
		trainee.setCommitQuestionary(Trainee.NO_COMMIT_QUESTIONARY);
		trainee.setCommitAbility(0);
		trainee.setCommitFourDegrees(0);;
		trainee.setCommitSuperiorLeadership(0);;
		trainee.setCreateMemberId(createMemberId);
		trainee.setDeleteFlag(Trainee.DELETE_FLASE);
		trainee.setSource(Trainee.SOURCE_SIGNUP);
		mainlandPersonnel.ifPresent(trainee::setMainlandPersonnel);

		// 查找是否存在该学员
		Optional<Trainee> OPtrainee = findByMemberId(memberId, Trainee.TRAINEE_TYPE_FORMAL, classId, Optional.empty());
		// 如果学员存在,做修改操作
		if (OPtrainee.isPresent()) {
			Trainee t = OPtrainee.get();
			t.setNewCompany(this.shortNameMethod(glist,org));
			// 如果学员存在且状态为通过或待审核，返回该学员
			if (t.getAuditStatus() == Trainee.AUDIT_AGREE || t.getAuditStatus() == Trainee.AUDIT_WAIT) {
				return OPtrainee.get();
			} else {
				// 查找当前班级审核方式
				Optional<ClassQuota> OPclassQuota = classQuotaService.findByClassId(classId);
				if (OPclassQuota.isPresent()) {
					ClassQuota cq = OPclassQuota.get();
					// 如果审核方式为手动审核，那么修改状态为待审核
					if (cq.getIsAutoApprove() == ClassQuota.MANUAL_APPROVE) {
						t.setAuditStatus(Trainee.AUDIT_WAIT);
						t.setSource(Trainee.SOURCE_SIGNUP);
						t.setPhoneNumber(phoneNumber);
						t.setEmail(email);
						t.setSex(sex);
						levelId.ifPresent(t::setLevelId);
						t.setNation(nation);
						jobName.ifPresent(t::setJob);
						remark.ifPresent(t::setRemark);
						t.setOrganizationId(organizationId);
						settleOrganizationId.ifPresent(t::setSettleOrganizationId);
//				        t.setJobName(jobName);
						t.setModifyDate(null);
						return dao.update(t);
					} else {
						// 如果审核方式为自动审核，那么进行判断配额
						boolean flag = this.haveQuota(cq.getType(), organizationId, classId, cq.getAmount());
						if (flag) {
							t.setAuditStatus(Trainee.AUDIT_AGREE);
							t.setSource(Trainee.SOURCE_SIGNUP);
							t.setPhoneNumber(phoneNumber);
							t.setEmail(email);
							t.setSex(sex);
							levelId.ifPresent(t::setLevelId);
							t.setNation(nation);
							remark.ifPresent(t::setRemark);
							t.setOrganizationId(organizationId);
							settleOrganizationId.ifPresent(t::setSettleOrganizationId);
							t.setSort(Trainee.DEFAULT_SORT);
							t.setModifyDate(null);
							Trainee tt = dao.update(t);
							// add by acong 20180227
							tt = changeSort(tt);
							updateReleatedOrgOrder(Trainee.ADD, tt.getId(), tt.getSort(), classId);
//	                        t.setJobName(jobName);
							messageSender.send(MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
									MessageHeaderContent.CLASSID, classId,
									MessageHeaderContent.MEMBERID, tt.getMemberId());

							messageSender.send(MessageTypeContent.TRAIN_STUDY_PLAN_CONFIG_INSERT,
									MessageHeaderContent.BUSINESS_ID, classId,
									MessageHeaderContent.MEMBERID, tt.getMemberId());//学习计划

							messageSender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_INSERT,
									MessageHeaderContent.CLASSID, classId,
									MessageHeaderContent.MEMBERID, trainee.getMemberId());

							// 如果存在IM群聊，则同步新增到群聊列表
							messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_ADD,
											   MessageHeaderContent.CLASSID, classId,
											   MessageHeaderContent.MEMBERID, trainee.getMemberId());

							return tt;
						}else{
							t.setAuditStatus(8);
						}
						return t;
					}
				}
			}
		} else {
			// 查找当前班级审核方式
			Optional<ClassQuota> OPclassQuota = classQuotaService.findByClassId(classId);
			if (OPclassQuota.isPresent()) {
				ClassQuota cq = OPclassQuota.get();
				// 如果审核方式为手动审核，那么直接新增学员数据
				if (cq.getIsAutoApprove() == ClassQuota.MANUAL_APPROVE) {
					trainee.setAuditStatus(Trainee.AUDIT_WAIT);// 设置字段为待审核
					try {
						return dao.insert(trainee);
					}catch (RuntimeException e) {
						return null;
					}
				} else {
					// 如果审核方式为自动审核，那么判断配额方式
					boolean flag = this.haveQuota(cq.getType(), organizationId, classId, cq.getAmount());
					if (flag) {
						// 如果有剩余名额，审核通过
						trainee.setSort(Trainee.DEFAULT_SORT);
						trainee.setAuditStatus(Trainee.AUDIT_AGREE);
					} else {
						// 如果无剩余名额，审核未通过
						trainee.setAuditStatus(8);
					}
					Trainee insert =  new Trainee();
					try {
						insert = dao.insert(trainee);
						messageSender.send(MessageTypeContent.TRAIN_STUDY_PLAN_CONFIG_INSERT,
								MessageHeaderContent.BUSINESS_ID, classId,
								MessageHeaderContent.MEMBERID, trainee.getMemberId());//学习计划
						if (trainee.getAuditStatus() != 8){
							messageSender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_INSERT,
									MessageHeaderContent.CLASSID, classId,
									MessageHeaderContent.MEMBERID, trainee.getMemberId());

							// 如果存在IM群聊，则同步新增到群聊列表
							messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_ADD,
											   MessageHeaderContent.CLASSID, classId,
											   MessageHeaderContent.MEMBERID, trainee.getMemberId());
						}

					}catch (RuntimeException e) {
						return null;
					}
					String[] ids = new String[1];
					ids[0] = trainee.getMemberId();
					Integer num = traineeService.countTrainee(classId);
					classInfoService.updateTraineeNum(classId, num,false,false,false);
					signDetailService.insertDetail(classId,ids);
					if (insert!=null&&insert.getAuditStatus() == Trainee.AUDIT_AGREE) {
						// add by acong 20180227
						insert = changeSort(insert);
						updateReleatedOrgOrder(Trainee.ADD, insert.getId(), insert.getSort(), classId);
						messageSender.send(MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
								MessageHeaderContent.CLASSID, classId,
								MessageHeaderContent.MEMBERID, insert.getMemberId());
					}
					return insert;
				}
			}
		}
		return null;
	}

	@Override
	public int addTrainee(String classId, Integer type, String memberId, String userId,Optional<Integer> flag,Optional<Integer> mainlandPersonnel) {
		// 查找学员是否存在
		List<GroupConfigurationValue> glist = groupConfigurationValueDao.execute(e -> {
			List<GroupConfigurationValue> all = e.selectFrom(GROUP_CONFIGURATION_VALUE)
					.fetch()
					.into(GroupConfigurationValue.class);
			return all;
		});
		boolean deleteFlag = false;
		if(flag.isPresent()){
			Optional<Trainee> trainDelete = trainAddDelete(classId,memberId);
			if(trainDelete.isPresent()){
				deleteFlag = true;
				return this.quotaExtract(classId,trainDelete.get(),flag,deleteFlag);
			}
		}
		Optional<Trainee> OPtrainee = findByMemberId(memberId, type, classId, Optional.empty());
		if (OPtrainee.isPresent()) {
			Trainee t = OPtrainee.get();
			if (t.getAuditStatus() == AUDIT_AGREE) {
				return 888; // 学员已存在
			}
			Optional<Organization> org = orgDao.getOptional(t.getOrganizationId());
			t.setNewCompany(this.shortNameMethod(glist,org));
			// 判断学员类型
			if (type == TRAINEE_TYPE_FORMAL) { // 添加正式学员
				return this.quotaExtract(classId,t,flag,deleteFlag);
			} else {
				// 如果是非正式成员，直接修改
				t.setAuditStatus(AUDIT_AGREE);
				messageSender.send(MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
						MessageHeaderContent.CLASSID, classId,
						MessageHeaderContent.MEMBERID, t.getMemberId());
				messageSender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_INSERT,
						MessageHeaderContent.CLASSID, classId,
						MessageHeaderContent.MEMBERID, t.getMemberId());
				t.setModifyDate(null);
				dao.update(t);
				return 1;
			}
		} else {
			Optional<Member> member = memberService.findDetailById(memberId);
			if(member.isPresent()){
				Optional<Organization> org = orgDao.getOptional(member.get().getOrganizationId());
				String id = member.get().getId();
				Trainee trainee = new Trainee();
				trainee.forInsert();
				trainee.setNewCompany(this.shortNameMethod(glist,org));
				trainee.setMemberId(id);
				trainee.setType(type);
				flag.ifPresent(trainee::setFinance);
				trainee.setPhoneNumber(SM4Utils.decryptDataCBC(member.get().getPhoneNumber()));
				trainee.setEmail(SM4Utils.decryptDataCBC(member.get().getEmail()));
				trainee.setSex(member.get().getSex());
				trainee.setNation(member.get().getEthnicityId());
				trainee.setAuditStatus(AUDIT_AGREE);
				trainee.setClassId(classId);
				trainee.setDeleteFlag(DELETE_FLASE);
				if(flag.isPresent()){
					flag.ifPresent(trainee::setFinance);
					trainee.setUpdateTime(this.dateTime());
					trainee.setUpdateMonth(this.dateTimeMonth());
				}
				trainee.setOrganizationId(member.get().getOrganizationId());
				trainee.setSettleOrganizationId(member.get().getCompanyId());
				trainee.setJob(member.get().getJobName());
				trainee.setCreateMemberId(userId);
				trainee.setTraineeGroupId(Trainee.WAIT_GROUP);
				trainee.setSortForGroup(Trainee.DEFAULT_SORT);
				trainee.setSort(Trainee.DEFAULT_SORT);
				trainee.setCommitQuestionary(Trainee.NO_COMMIT_QUESTIONARY);
				trainee.setCommitAbility(0);//能力习得问卷提交状态0未提交
				trainee.setCommitFourDegrees(0);//四度问卷提交状态0未提交
				trainee.setCommitSuperiorLeadership(0);//领导问卷提交状态0未提交

				mainlandPersonnel.ifPresent(trainee::setMainlandPersonnel);

				// 判断学员类型
				if (type == TRAINEE_TYPE_FORMAL) { // 添加正式学员
					Optional<ClassQuota> OPclassQuota = classQuotaService.findByClassId(classId);
					if (OPclassQuota.isPresent()) {
						ClassQuota cq = OPclassQuota.get();
						if (this.haveQuota(cq.getType(), trainee.getOrganizationId(), classId, cq.getAmount())) {
							// 如果有配额，添加学员
							trainee.setAuditStatus(AUDIT_AGREE);
							trainee.setSource(Trainee.SOURCE_MANUAL);

							dao.insert(trainee);

							// 如果存在IM群聊，则同步新增到群聊列表
							messageSender.send(MessageTypeContent.CHAT_GROUP_MEMBER_ADD,
											   MessageHeaderContent.CLASSID, classId,
											   MessageHeaderContent.MEMBERID, id);


							messageSender.send(MessageTypeContent.TRAIN_STUDY_PLAN_CONFIG_INSERT,
									MessageHeaderContent.BUSINESS_ID, classId,
									MessageHeaderContent.MEMBERID, trainee.getMemberId());//学习计划

							// add by acong 20180227
							trainee = changeSort(trainee);
							updateReleatedOrgOrder(Trainee.ADD, trainee.getId(), trainee.getSort(), classId);
							messageSender.send(MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
									MessageHeaderContent.CLASSID, classId,
									MessageHeaderContent.MEMBERID, trainee.getMemberId());

							messageSender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_INSERT,
									MessageHeaderContent.CLASSID, classId,
									MessageHeaderContent.MEMBERID, trainee.getMemberId());
							return 1;
						} else {
							return 999;
						}
					} else {
						return 777;
					}
				} else {
					// 如果是非正式成员，直接添加
					trainee.setAuditStatus(AUDIT_AGREE);

					messageSender.send(MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
							MessageHeaderContent.CLASSID, classId,
							MessageHeaderContent.MEMBERID, trainee.getMemberId());
					dao.insert(trainee);
					return 1;
				}
			}
			return 999;
		}
	}
	@Override
	public Optional<Trainee> findByMemberId(String memberId, Integer type, String classId,
											Optional<Integer> auditStatus) {

		// 通过membeid获取已存在学员
		SelectConditionStep<Record> step = dao.execute(x -> x.select(Fields.start().add(TRAINEE).end())
				.from(TRAINEE).where(TRAINEE.MEMBER_ID.eq(memberId)).and(TRAINEE.TYPE.eq(type))
				.and(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)));
		// 判断追加条件
		auditStatus.ifPresent(x -> step.and(TRAINEE.AUDIT_STATUS.eq(x)));

		return step.fetchOptional(r -> r.into(Trainee.class));
	}

	/**
	 * 查询提交结算数据的人
	 * @param classId
	 * @param memberId
	 * @return
	 */
	public Optional<Trainee> trainAddDelete(String classId,String memberId){
		// 通过membeid获取已存在学员
		SelectConditionStep<Record> step = dao.execute(x -> x.select(Fields.start().add(TRAINEE).end())
				.from(TRAINEE).where(TRAINEE.MEMBER_ID.eq(memberId)).and(TRAINEE.TYPE.eq(0))
				.and(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.DELETE_FLAG.eq(DELETE_TRUE)));
		return step.fetchOptional(r -> r.into(Trainee.class));
	}

	/**
	 * 配额提取公共方法
	 * @param classId
	 */
	public int quotaExtract(String classId,Trainee t,Optional<Integer> flag,boolean deleteFlag){
		// 获取配额信息
		Optional<ClassQuota> OPclassQuota = classQuotaService.findByClassId(classId);
		if (OPclassQuota.isPresent()) {
			ClassQuota cq = OPclassQuota.get();
			if (this.haveQuota(cq.getType(), t.getOrganizationId(), classId, cq.getAmount())) {
				// 如果有配额，修改状态返回成功
				t.setSort(Trainee.DEFAULT_SORT);
				t.setAuditStatus(AUDIT_AGREE);
				t.setSource(Trainee.SOURCE_MANUAL);
				if(flag.isPresent()){
					if(deleteFlag){
						t.setDeleteFlag(0);
						t.setUpdateTime(this.dateTime());
						t.setUpdateMonth(this.dateTimeMonth());
						t.setFinance(1);
					}else{
						t.setUpdateTime(null);
						t.setUpdateMonth(null);
						t.setFinance(null);
					}
				}
				t.setModifyDate(null);
				dao.update(t);
				// add by acong 20180227
				t = changeSort(t);
				updateReleatedOrgOrder(Trainee.ADD, t.getId(), t.getSort(), classId);
				messageSender.send(MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
						MessageHeaderContent.CLASSID, classId,
						MessageHeaderContent.MEMBERID, t.getMemberId());
				return 1;
			} else {
				return 999;
			}
		} else {
			return 777; // 尚未配额
		}
	}


	@Override
	public Trainee updateGroup(String id) {
		Trainee trainee = dao.get(id);
		trainee.setTraineeGroupId("0");
		trainee.setModifyDate(null);
		Trainee t = dao.update(trainee);
		return t;
	}

	@Override
	public List<Trainee> findByGroup(String classId, String groupId) {
//		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		// 构建语句
		return dao
				.execute(x -> x
						.selectDistinct(Fields.start()
								.add(TRAINEE.ID, MEMBER.FULL_NAME, MEMBER.NAME, MEMBER.SEX, TRAINEE.SORT,TRAINEE.NEW_COMPANY,
										TRAINEE.SORT_FOR_GROUP, ORGANIZATION.ORDER)
								.end())
						.from(TRAINEE).leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID)).leftJoin(ORGANIZATION)
						.on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID)))
				.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.TRAINEE_GROUP_ID.eq(groupId))
				.and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE)).and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE))
				.and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL)).orderBy(TRAINEE.SORT_FOR_GROUP, ORGANIZATION.ORDER)
				.fetch(r -> {
					Trainee trainee = new Trainee();
					trainee.setId(r.getValue(TRAINEE.ID));
					trainee.setSort(r.getValue(TRAINEE.SORT));
					trainee.setSortForGroup(r.getValue(TRAINEE.SORT_FOR_GROUP));
					trainee.setOrganizationOrder(r.getValue(ORGANIZATION.ORDER));
					trainee.setOrganizationName(r.getValue(TRAINEE.NEW_COMPANY));
					Member member = new Member();
					member.setSex(r.getValue(MEMBER.SEX));
					member.setFullName(r.getValue(MEMBER.FULL_NAME));
					member.setName(r.getValue(MEMBER.NAME));
					trainee.setMember(member);
					return trainee;
				});
	}

	@Override
	public boolean findByClassGroup(String classId,String memberId) {

		ClassInfo classInfo = classInfoDao.get(classId);
		// 构建语句
		List<Trainee> list =  classInfoDao
				.execute(x -> x
						.select(Fields.start()
								.add(TRAINEE.ID)
								.end())
						.from(CLASS_INFO).innerJoin(TRAINEE).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID)).and(TRAINEE.MEMBER_ID.eq(memberId))
						.and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE)).and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE))
						.and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL)))
				.where(CLASS_INFO.GROUP_ID.eq(classInfo.getGroupId()))
				.fetch(r -> {
					Trainee trainee = new Trainee();
					trainee.setId(r.getValue(TRAINEE.ID));
					return trainee;
				});
		return list != null && list.size() > 0;
	}

	@Override
	public String[] findMemberIds(String classId, Integer type) {
		return dao.execute(x -> x.selectDistinct(Fields.start().add(TRAINEE.MEMBER_ID).end()).from(TRAINEE)
				.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE))
				.and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE)).and(TRAINEE.TYPE.eq(type))).fetch(r -> {
			return r.getValue(TRAINEE.MEMBER_ID);
		}).toArray(new String[0]);
	}

	@Override
	public String[] findMemberIds(String classId, String[] memberIds) {
		return dao.execute(x -> x.selectDistinct(Fields.start().add(TRAINEE.MEMBER_ID).end()).from(TRAINEE)
				.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE))
				.and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE)).and(TRAINEE.TYPE.eq(0)).and(TRAINEE.MEMBER_ID.in(memberIds))).fetch(r -> {
			return r.getValue(TRAINEE.MEMBER_ID);
		}).toArray(new String[0]);
	}

	@Override
	public int saveGroupManage(String classId, String groupId, Optional<String> ids, Optional<String> fmTrainees) {
		dao.execute(x -> x.update(TRAINEE).set(TRAINEE.TRAINEE_GROUP_ID, "0").where(TRAINEE.CLASS_ID.eq(classId))
				.and(TRAINEE.TRAINEE_GROUP_ID.eq(groupId)).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
				.and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)).and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))).execute();
		if (ids.isPresent()) {
			//传回来的list集合
			List<Trainee> traineeList = com.alibaba.fastjson.JSON.parseArray(fmTrainees.get(), Trainee.class);
			String[] idsArr = ids.get().split(",");
			//根据ids查询出来的集合
			List<Trainee> list = dao.fetch(TRAINEE.ID.in(idsArr).and(TRAINEE.CLASS_ID.eq(classId)));
			List<Trainee> newList = new ArrayList<>();
			for (Trainee trainee : traineeList) {
				for (Trainee t : list) {
					if (t.getId().equals(trainee.getId())) {
						t.setTraineeGroupId(groupId);
						t.setSortForGroup(trainee.getSortForGroup());
						t.setModifyDate(null);
						newList.add(t);
					}
				}
			}
			dao.update(newList);
			return newList.size();
//			return dao
//					.execute(x -> x.update(TRAINEE).set(TRAINEE.TRAINEE_GROUP_ID, groupId).where(TRAINEE.ID.in(idsArr)))
//					.execute();
		}
		return 0;
	}

	@Override
	public Trainee updateSettleOrganizationName(String organizationId, String id) {
		Trainee trainee = dao.get(id);
		trainee.setSettleOrganizationId(organizationId);
		trainee.setModifyDate(null);
		dao.update(trainee);
		return trainee;
	}

	@Override
	public Trainee getCourseStudyProgress(String classId) {
		Trainee t = new Trainee();
		StringBuffer tIds = new StringBuffer();
		String cIds1 = null;
		List<ClassOnlineCourse> cList = onlineCourseService.findByClassId(classId, Optional.empty(),true);
		if (cList != null && cList.size() > 0) {
			List<String> list1  = cList.stream().map(ClassOnlineCourse::getResourceId).collect(Collectors.toList());
			cIds1 = String.join(",", list1);
		}
		List<ClassRequired> required = classRequiredDao.execute(x -> {
			return x.select(Fields.start().add(CLASS_REQUIRED.THEME).end()).from(CLASS_REQUIRED)
					.where(CLASS_REQUIRED.CLASS_ID.eq(classId).and(CLASS_REQUIRED.DELETE_FLAG.eq(ClassRequired.DELETE_FLASE)))
					.groupBy(CLASS_REQUIRED.THEME)
					.fetch().into(ClassRequired.class);
		});
		String cIds2 = null;
		if (required != null && required.size() > 0) {
			// List<String> list2  = required.stream().map(ClassRequired::getTheme).collect(Collectors.toList());

			List<ClassRequired> required2 = classRequiredDao.execute(x -> {
				return x.select(Fields.start().add(CLASS_REQUIRED.COURSE_ID).end()).from(CLASS_REQUIRED)
						.where(CLASS_REQUIRED.THEME.in(required.stream().map(ClassRequired::getTheme).collect(Collectors.toList())).and(CLASS_REQUIRED.DELETE_FLAG.eq(ClassRequired.DELETE_FLASE)))
						.fetch().into(ClassRequired.class);
			});
			if (required2 != null && required2.size() > 0) {
				List<String> list3  = required2.stream().map(ClassRequired::getCourseId).collect(Collectors.toList());
				cIds2 = String.join(",", list3);
			}
		}
		if (cIds1 != null && cIds2 == null) {
			t.setCourseIds(cIds1);
		} else if (cIds1 == null && cIds2 != null) {
			t.setCourseIds(cIds2);
		} else if (cIds1 != null && cIds2 != null) {
			String[] split1 = cIds1.split(",");
			String[] split2 = cIds2.split(",");
			String[] array = Stream.concat(Stream.of(split1), Stream.of(split2)) //合并
					.distinct()     //去重
					.toArray(String[]::new);

			List<String> strList= Arrays.asList(array);
			List<String> strListNew=new ArrayList<>();
			for (int i = 0; i <strList.size(); i++) {
				if (strList.get(i) != null && !strList.get(i).equals("") && !strList.get(i).equals("null")){
					strListNew.add(strList.get(i));
				}
			}
			String[] strNewArray = strListNew.toArray(new String[strListNew.size()]);
			String join = String.join(",", strNewArray);
			t.setCourseIds(join);
		} else {
			t.setCourseIds(null);
		}

		List<Trainee> tList = dao
				.execute(x -> x.select(TRAINEE.MEMBER_ID).from(TRAINEE).where(TRAINEE.CLASS_ID.eq(classId))
						.and(TRAINEE.AUDIT_STATUS.eq(1)).and(TRAINEE.TYPE.eq(0)).and(TRAINEE.DELETE_FLAG.eq(0)))
				.fetchInto(Trainee.class);
		for (int i = 0; i < tList.size(); i++) {
			tIds.append(tList.get(i).getMemberId()).append(",");
		}
		if(tIds.length() > 1) {
			tIds.deleteCharAt(tIds.length() - 1);
		}

		t.setMemberIds(tIds.toString());
		return t;

	}



	@Override
	public boolean haveQuota(int quotaType, String organizationId, String classId, int amount) {
		boolean flag = false;
		// 整体配额
		if (quotaType == ClassQuota.TYPE_WHOLE) {
			// 如果现有学员数小于计划人数，返回true
			if (this.getCountByClassId(classId) < amount) {
				flag = true;
			}
		} else {
			flag = true;
			List<ClassQuotaDetail> cqdList = classQuotaDetailService.findDetailByClassIdAndOrgIdServer(classId,
					Optional.of(organizationId));
			// 如果其父组织配额若其中一个已满，则无法通过
			if (cqdList.size() != 0) {
				for (ClassQuotaDetail cqd : cqdList) {
					if (cqd.getQuantity() <= cqd.getSignUpNumber()) {
						flag = false;
						break;
					}
				}
			} else {
				flag = false;
			}
		}
		return flag;
	}
	@Override
	public Trainee update(String id, Integer sex, Optional<String> newCompany,Optional<String> newOrganization,Optional<String> levelId,String nation,String phoneNumber,String email,Optional<String> remark,Optional<String> jobName) {
		Trainee trainee = dao.get(id);
//		trainee.setLevelId(levelId);
		levelId.ifPresent(trainee::setLevelId);
		newCompany.ifPresent(trainee::setNewCompany);
		newOrganization.ifPresent(trainee::setNewOrganization);
		trainee.setSex(sex);
		trainee.setNation(nation);
		trainee.setPhoneNumber(phoneNumber);
		trainee.setEmail(email);
		trainee.setJob(jobName.orElse(null));
		remark.ifPresent(trainee::setRemark);
		trainee.setModifyDate(null);
		Trainee result = dao.update(trainee);
		messageSender.send(MessageTypeContent.TRAIN_CLASS_STUDENT_UPDATE,
				MessageHeaderContent.CLASSID, trainee.getClassId(),
				MessageHeaderContent.MEMBERID, trainee.getMemberId());
		return result;
	}
	/**
	 * 为不签到或者没有签到的情况下提供实训人数的接口
	 * @param classId
	 * @return
	 */
	@Override
	public Integer countTrainee(String classId) {
		Integer num = dao.execute(r->r.select(Fields.start().add(TRAINEE.MEMBER_ID.count()).end()).from(TRAINEE).where(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
				.and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE).and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE)).and(TRAINEE.CLASS_ID.eq(classId)))
				.fetchOne(TRAINEE.MEMBER_ID.count()));
		return num;
	}



	@Override
	public Trainee findTraineeByClassId(String classId, String memberId) {
		List<Trainee> list = dao.execute( x -> x.select(Fields.start().add(TRAINEE).end())
				.from(TRAINEE)
				.where(TRAINEE.MEMBER_ID.eq(memberId)
						.and(TRAINEE.CLASS_ID.eq(classId))
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))))
				.fetch(r -> {
					Trainee t = r.into(Trainee.class);
					return t;
				});
		if (list != null && list.size() > 1) {
			for (Trainee t : list) {
				if (t.getType() == 0) {
					return t;
				}
			}
		} else if (list != null && list.size() == 1) {
			return list.get(0);
		}
		return null;
	}
	/**
	 * 为班级问卷提供是否提交满意度问卷 <AUTHOR>
	 * @param classId
	 * @return
	 */
	@Override
	public Integer findTraineeBySubmitQuestion(String classId) {
		Select<Record> step = dao.execute(x -> x.select(Fields.start().add(TRAINEE).end())
				.from(TRAINEE)
				.where(TRAINEE.COMMIT_QUESTIONARY.eq(Trainee.YES_COMMIT_QUESTIONARY)
						.and(TRAINEE.CLASS_ID.eq(classId))
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))));
		Integer count = dao.execute(x -> x.fetchCount(step));
		return count;
	}

	@Override
	public Trainee findTraineeByAssessment(String classId) {
		Trainee trainee = dao.execute(x -> x.select(Fields.start().add(TRAINEE.COMMIT_FOUR_DEGREES.sum(),TRAINEE.COMMIT_ABILITY.sum(),TRAINEE.COMMIT_SUPERIOR_LEADERSHIP.sum()).end())
				.from(TRAINEE)
				.where((TRAINEE.CLASS_ID.eq(classId))
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))).fetchOne(r->{
					Trainee tr = r.into(TRAINEE).into(Trainee.class);
					Integer aaq = null;
					Integer fd = null;
					Integer slq = null;
					if(r.getValue(TRAINEE.COMMIT_ABILITY.sum())!=null){
						aaq = r.getValue(TRAINEE.COMMIT_ABILITY.sum()).intValue();
					}
					if(r.getValue(TRAINEE.COMMIT_FOUR_DEGREES.sum())!=null){
						fd = r.getValue(TRAINEE.COMMIT_FOUR_DEGREES.sum()).intValue();
					}
					if(r.getValue(TRAINEE.COMMIT_SUPERIOR_LEADERSHIP.sum())!=null){
						slq = r.getValue(TRAINEE.COMMIT_SUPERIOR_LEADERSHIP.sum()).intValue();
					}
					tr.setAaqSum(aaq);
					tr.setFdqSum(fd);
					tr.setSlqSum(slq);
					return tr;
				}));
		return trainee;
	}

	@Override
	public List<Trainee> findFormalTraineeAndCount(String classId) {
		List<Trainee> list = dao.execute(x -> x.select(
				count(ORGANIZATION_DETAIL.ROOT),
				ORGANIZATION_DETAIL.ROOT,
				ORGANIZATION.DEPTH,
				ORGANIZATION.NAME
		).from(TRAINEE)
				.leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
				.leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(MEMBER.ORGANIZATION_ID))
				.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.ROOT))
				.where(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL)
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))
						.and(TRAINEE.CLASS_ID.eq(classId))
						.and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE))
						.and(ORGANIZATION.DEPTH.eq(3).or(ORGANIZATION.DEPTH.eq(1)))
						.and(ORGANIZATION.LEVEL.le(3)))
				.groupBy(ORGANIZATION_DETAIL.ROOT,ORGANIZATION.DEPTH))
				.fetch(r -> {
					Trainee t = new Trainee();
					t.setOrganizationId(r.getValue(ORGANIZATION_DETAIL.ROOT));
					t.setMemberNum(r.getValue(count(ORGANIZATION_DETAIL.ROOT)));
					t.setNum(r.getValue(ORGANIZATION.DEPTH));
					t.setOrganizationName(r.getValue(ORGANIZATION.NAME));
					return t;
				});
		return list;
	}

	/**
	 * <AUTHOR>
	 *查看报名人员信息
	 * @param classId
	 * @return
	 */
	@Override
	public List<Trainee> findBySignPersonnel(String classId) {
		Field<String> jobName = JOB.NAME.as("jobName");
		Field<String> mbName = MEMBER.NAME.as("mbName");
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		com.zxy.product.train.jooq.tables.Organization organization2 = ORGANIZATION.as("organization2");
		Field<String> orgName2 = organization2.NAME.as("orgName");
		Field<String> orgId = ORGANIZATION.ID.as("orgId");
		com.zxy.product.train.jooq.tables.Job ihrJobTable = JOB.as("ihrJobTable");
		List<Trainee> trainees = dao.execute(x -> x.select(Fields.start().add(TRAINEE.ID,TRAINEE.JOB, MEMBER.FULL_NAME, mbName, orgId, orgName,
				jobName, ihrJobTable.NAME, TRAINEE.SEX, TRAINEE.PHONE_NUMBER, TRAINEE.EMAIL, TRAINEE.AUDIT_STATUS,TRAINEE.NEW_COMPANY,
				TRAINEE.MEMBER_ID, TRAINEE.COMMIT_QUESTIONARY, TRAINEE.SORT, TRAINEE.LEVEL_ID, TRAINEE.NATION,
				TRAINEE.TRAINEE_GROUP_ID,orgName2,
				TRAINEE.REMARK, TRAINEE.SORT_FOR_GROUP).end()).from(TRAINEE)
				.leftJoin(MEMBER).on(MEMBER.ID.eq(TRAINEE.MEMBER_ID)).leftJoin(POSITION).on(MEMBER.MAJOR_POSITION_ID.eq(POSITION.ID))
				.leftJoin(JOB).on(POSITION.JOB_ID.eq(JOB.ID))
				.leftJoin(ihrJobTable).on(ihrJobTable.ID.eq(MEMBER.JOB_ID))
//				.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
//				.leftJoin(organization2).on(ORGANIZATION.COMPANY_ID.eq(organization2.ID))
//				.leftJoin(GROUP_CONFIGURATION_VALUE).on(ORGANIZATION.COMPANY_ID.eq(GROUP_CONFIGURATION_VALUE.ORGANIZATION_ID))
				.leftJoin(organization2).on(MEMBER.ORGANIZATION_ID.eq(organization2.ID))
				.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
				.where(TRAINEE.CLASS_ID.eq(classId)
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))
						.and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.AUDIT_STATUS.notEqual(Trainee.AUDIT_REFUSE)))).fetch(r->{
			Trainee trainee = r.into(TRAINEE).into(Trainee.class);
			Member member = new Member();
			String gvName = r.getValue(TRAINEE.NEW_COMPANY);
			if (gvName != null && gvName != "") {
				trainee.setOrganizationName(gvName);
			} else {
				trainee.setOrganizationName(r.getValue(orgName));
			}
			trainee.setCompanyName(r.getValue(orgName2));
			member.setName(r.getValue(mbName));
			member.setFullName(r.getValue(MEMBER.FULL_NAME));
			if (trainee.getJob() != null && trainee.getJob() != "") {
				member.setJobName(trainee.getJob());
			} else if (r.getValue(ihrJobTable.NAME) != null && !"".equals(r.getValue(ihrJobTable.NAME))) {
			    member.setJobName(r.getValue(ihrJobTable.NAME));
            } else {
				member.setJobName(r.getValue(jobName));
			}
			trainee.setMember(member);
			return trainee;
		});
		Set<String> familySet = new HashSet<>();
		Set<String> leveSet = new HashSet<>();
		trainees.forEach(r -> {
			familySet.add(r.getNation());
			leveSet.add(r.getLevelId());
		});

		Map<String, MemberConfig> LevelMap = memberConfigCommonDao.execute(e -> e.select(Fields.start().add(MEMBER_CONFIG).end())
				.from(MEMBER_CONFIG).where(MEMBER_CONFIG.ID.in(leveSet)).fetch(record -> {
					MemberConfig memberConfig = record.into(MEMBER_CONFIG).into(MemberConfig.class);
					return memberConfig;
				}).stream().collect(Collectors.toMap(MemberConfig::getId, p -> p)));
		trainees.forEach(r -> {
			r.setLevelName(LevelMap.get(r.getLevelId()) == null ? "" : LevelMap.get(r.getLevelId()).getValue());
		});

		//民族
		Map<String, MemberConfig> memberConfigMap = memberConfigCommonDao.execute(e -> e.select(Fields.start().add(MEMBER_CONFIG).end())
				.from(MEMBER_CONFIG).where(MEMBER_CONFIG.ID.in(familySet)).fetch(record -> {
					MemberConfig memberConfig = record.into(MEMBER_CONFIG).into(MemberConfig.class);
					return memberConfig;
				}).stream().collect(Collectors.toMap(MemberConfig::getId, p -> p)));
		trainees.forEach(r -> {
			r.setNationName(memberConfigMap.get(r.getNation()) == null ? "" : memberConfigMap.get(r.getNation()).getValue());
		});
		return trainees;
	}



	@Override
	public Integer countFormalTrainee(String classId, String[] ids) {
		SelectConditionStep<Record> record = dao.execute(x -> x.select(Fields.start().add(TRAINEE).end())
				.from(TRAINEE)
				.leftJoin(MEMBER).on(MEMBER.ID.eq(TRAINEE.MEMBER_ID))
				.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
				.leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(ORGANIZATION.ID))
				.where(TRAINEE.CLASS_ID.eq(classId)
						.and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))
						.and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE))
						.and(ORGANIZATION_DETAIL.ROOT.in(ids))
				));
		Integer count = dao.execute(x -> x.fetchCount(record));
		return count;
	}

	@Override
	public Trainee getTrainee(String id) {
		Trainee trainee = dao.get(id);
		return trainee;
	}

	/**
	 * 删除学员满意度问卷作答记录
	 * @return
	 */
	public int updataResearchRecord(String classId,String memberId) {
		Optional<ResearchQuestionary> researchQuestionary = researchQuestionaryDao.fetchOne(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId), RESEARCH_QUESTIONARY.TYPE.eq(4));
		if (researchQuestionary.isPresent()) {
			Optional<ResearchRecord> researchRecord = researchRecordDao.fetchOne(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchQuestionary.get().getId()), RESEARCH_RECORD.MEMBER_ID.eq(memberId));
			if (researchRecord.isPresent()) {
				List<ResearchAnswerRecord> researchAnswerRecordList = researchAnswerRecordDao.execute(x -> {
					return x.selectFrom(RESEARCH_ANSWER_RECORD).where(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(researchRecord.get().getId()))
							.fetchInto(ResearchAnswerRecord.class);
				});
				if (researchAnswerRecordList.size() > 0 && researchAnswerRecordList != null) {
					List<String> ids = researchAnswerRecordList.stream().map(x -> {
						return x.getId();
					}).collect(Collectors.toList());
					researchAnswerRecordDao.delete(ids);
				}
				researchRecordDao.delete(researchRecord.get().getId());
				Integer praiseNum = dao.execute(y->y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
						.where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_QUESTIONARY.eq(1))
						.and(TRAINEE.TYPE.eq(0))
						.and(TRAINEE.DELETE_FLAG.eq(0))
				).fetchOne(TRAINEE.ID.count());
				ClassInfo classInfo = classInfoDao.get(classId);
				if (classInfo != null) {
					classInfo.setSubmitNum(praiseNum);
					classInfoDao.update(classInfo);
				}
			}
		}
		return 0;
	}

	@Override
	public Integer countFormalTraineeByClassId(String classId) {
		SelectConditionStep<Record> record = dao.execute(x -> x.select(Fields.start().add(TRAINEE.CLASS_ID).end())
				.from(TRAINEE))
				.where(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)
						.and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.AUDIT_STATUS.eq(1)));
		Integer count = dao.execute(r -> r.fetchCount(record));
		return count;
	}

	@Override
	public void deleteTraineesByClassId(String classId) {
		List<String> ids = dao.execute(x -> x.select(TRAINEE.ID).from(TRAINEE)
				.where(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE).and(TRAINEE.CLASS_ID.eq(classId))))
				.fetch(r -> r.into(Trainee.class)).stream().map(a -> a.getId()).collect(Collectors.toList());
		if (ids != null && ids.size() > 0) {
			dao.execute(x -> x.delete(TRAINEE).where(TRAINEE.ID.in(ids))).execute();
			ids.forEach( id-> deleteDataTrainCommonDao.insert(DeleteDataTrain
					.getDeleteData(DeleteDataTrain.TRAINEE,id,"")));
		}
	}


	/**
	 * 更新学员的order
	 */
	private void updateReleatedOrgOrder(int operType,String id, Integer order, String classId) {
		if (operType == Trainee.ADD || operType == Trainee.DELETE) {
			// 找到当前组织同级的,顺序比它大的组织
			List<String> orgIds = getOrgIds(order, Optional.empty(), classId);
			orgIds.remove(id);

			// 更新被影响的组织顺序
			updateOrgOrder(orgIds, operType, classId);
		} else if (operType == Trainee.UPDATE) {
			Trainee s = dao.get(id);
			int addBase = Integer.compare(order, s.getSort());
			int max = order - s.getSort() > 0 ? order : s.getSort();
			int min = order - s.getSort() < 0 ? order : s.getSort();
			List<String> orgIds = getOrgIds(min, Optional.of(max), classId);
			orgIds.remove(id);

			// 更新被影响的组织顺序
			updateOrgOrder(orgIds, addBase, classId);
		}

	}


	/**
	 * 获取指定范围的学员的id
	 */
	private List<String> getOrgIds(int order, Optional<Integer> maxOrder, String classId) {
		return maxOrder.map(max -> {
			return dao.execute(d -> d.select(TRAINEE.ID)
					.from(TRAINEE).where(TRAINEE.SORT.between(order, max)
							.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))
							.and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
							.and(TRAINEE.CLASS_ID.eq(classId))).fetch(TRAINEE.ID));
		}).orElseGet(() -> {
			return dao.execute(d -> d.select(TRAINEE.ID).from(TRAINEE).where(TRAINEE.SORT.ge(order)
					.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE))
					.and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
					.and(TRAINEE.CLASS_ID.eq(classId))).fetch(TRAINEE.ID));
		});
	}

	/**
	 * 更新结算单位顺序
	 */
	private void updateOrgOrder(List<String> orgIds, int addBase, String classId) {
		if (!orgIds.isEmpty() && addBase != 0) {
			if (addBase > 0) {
				dao.execute(d -> d.update(TRAINEE).set(TRAINEE.SORT, TRAINEE.SORT.add(addBase)).where(TRAINEE.ID.in(orgIds).and(TRAINEE.CLASS_ID.eq(classId))).execute());
			} else {
				dao.execute(d -> d.update(TRAINEE).set(TRAINEE.SORT, TRAINEE.SORT.add(addBase)).where(TRAINEE.ID.in(orgIds), TRAINEE.SORT.gt(0), TRAINEE.CLASS_ID.eq(classId)).execute());
			}
		}
	}


	//    /**
//     * 获取结算单位顺序
//     */
	private Integer getOrgOrder(int operType,Optional<Integer> order, String classId) {
		// 获取最大order
		Integer maxOrder = dao.execute(d -> {
			return d.select(DSL.max(TRAINEE.SORT)).from(TRAINEE)
					.where(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE), TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL), TRAINEE.CLASS_ID.eq(classId)).fetchOne(DSL.max(TRAINEE.SORT));
		});


		if (maxOrder == null) {
			return 1;
		} else {
			return order.map(o -> {
				if (operType == Trainee.ADD) {
					return Integer.compare(o, maxOrder) > 0 ? maxOrder + 1 : o;
				} else {
					return Integer.compare(o, maxOrder) > 0 ? maxOrder : o;
				}
			}).orElseGet(() -> {
				return maxOrder + 1;
			});
		}
	}
	@Override
	public Member getMember(String id) {
		return memberService.getMember(id);
	}


	/**
	 * 参训数据
	 */
	@Override
	public Map<String, Map<String, String>> findTrainee(Optional<Long> lastMonthFirstDay, Optional<Long> monthFirstDay) {
		return dao.execute(d -> {
			Map<String, Map<String, String>> trainMap = new HashMap<>();
			d.select(
					CLASS_INFO.PROJECT_ID,
					MEMBER.NAME,
					MEMBER.FULL_NAME,
					F2F_COURSE.COURSE_DURATION,
					SIGN.TYPE)
					.from(TRAINEE)
					.leftJoin(MEMBER).on(MEMBER.ID.eq(TRAINEE.MEMBER_ID))
					.leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(TRAINEE.CLASS_ID))
					.leftJoin(F2F_COURSE).on(F2F_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
					.leftJoin(SIGN).on(SIGN.CLASS_ID.eq(CLASS_INFO.ID))
					.where(CLASS_INFO.STATUS.eq(3))
					.and(lastMonthFirstDay.map(TRAINEE.CREATE_TIME::ge).orElse(DSL.trueCondition()))
					.and(monthFirstDay.map(TRAINEE.CREATE_TIME::lt).orElse(DSL.trueCondition()))
					.fetch().forEach(r -> {
				Map<String, String> infoMap = new HashMap<>();
				infoMap.put("course_id", r.getValue(CLASS_INFO.PROJECT_ID, String.class));
				infoMap.put("member_name", r.getValue(MEMBER.NAME, String.class));
				infoMap.put("member_full_name", r.getValue(MEMBER.FULL_NAME, String.class));
				Float value = r.getValue(F2F_COURSE.COURSE_DURATION, Float.class);
				if (null != value) {
					infoMap.put("course_time", String.valueOf(value * 60 * 60));
				} else {
					infoMap.put("course_time", "0");
				}
				infoMap.put("sign_type", r.getValue(SIGN.TYPE, String.class));
				trainMap.put(r.getValue(MEMBER.NAME, String.class), infoMap);
			});
			return trainMap;
		});
	}
	@Override
	public List<MessageRecord> messageMemberIds(String classId) {
		Select<Record>  records = messageRecordCommonDao.execute(d ->
				d.selectDistinct(Fields.start().add(MESSAGE_RECORD.RECEIVER).end()).from(MESSAGE_RECORD).where(MESSAGE_RECORD.CLASS_ID.eq(classId)));
		List<MessageRecord> list = records.fetch(r->{
			MessageRecord messageRecord = new MessageRecord();
			messageRecord.setReceiver(r.getValue(MESSAGE_RECORD.RECEIVER));
			return messageRecord;
		});
		return list;
	}
	@Override
	public List<Trainee> findTepTrainees(String classId, String[] mIds) {
		return dao.execute(x->x.select().from(TRAINEE).where(TRAINEE.MEMBER_ID.in(mIds).and(TRAINEE.CLASS_ID.eq(classId))
				.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)).and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))).fetch(r->{
			Trainee traine = r.into(TRAINEE).into(Trainee.class);
			return traine;
		}));
	}

	@Override
	public List<Trainee> findTraineesPhoneNumber(String classId, String[] mIds) {
		return dao.execute(x->x.select().from(TRAINEE).where(TRAINEE.MEMBER_ID.in(mIds).and(TRAINEE.CLASS_ID.eq(classId))
																			  .and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)))).fetch(r->{
			Trainee traine = r.into(TRAINEE).into(Trainee.class);
			return traine;
		});
	}

	@Override
	public String[] findTepIds(String classId, String[] mIds) {
		return dao.execute(x->x.select().from(TRAINEE).where(TRAINEE.MEMBER_ID.in(mIds).and(TRAINEE.CLASS_ID.eq(classId))
				.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)).and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))).fetchArray(TRAINEE.PHONE_NUMBER));
	}
	@Override
	public Integer getSortByRule(String id, String classId) {

		com.zxy.product.train.jooq.tables.GroupConfigurationValue gcv2 = GROUP_CONFIGURATION_VALUE.as("gcv2");
		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");

		List<Trainee> list = dao.execute(x->x.selectDistinct(TRAINEE.ID,TRAINEE.SORT).from(TRAINEE)
				.leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
				.leftJoin(GROUP_CONFIGURATION_VALUE).on(GROUP_CONFIGURATION_VALUE.ORGANIZATION_ID.eq(
						x.select(ORGANIZATION.ID).from(ORGANIZATION).where(ORGANIZATION.ID.in(
								x.select(ORGANIZATION_DETAIL.ROOT).from(ORGANIZATION_DETAIL)
										.innerJoin(gcv2).on(ORGANIZATION_DETAIL.ROOT.eq(gcv2.ORGANIZATION_ID))
										.where(ORGANIZATION_DETAIL.SUB.eq(MEMBER.ORGANIZATION_ID)))).orderBy(ORGANIZATION.DEPTH.desc()).limit(1)))
				.leftJoin(GROUP_CONFIGURATION).on(GROUP_CONFIGURATION.ID.eq(GROUP_CONFIGURATION_VALUE.GROUP_ID))
				.leftJoin(org2).on(MEMBER.ORGANIZATION_ID.eq(org2.ID))
				.where(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE)).and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
				.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)).and(TRAINEE.CLASS_ID.eq(classId))
				.and((TRAINEE.MANUAL_SORTING.ne(1)).or(TRAINEE.MANUAL_SORTING.isNull()))
				.orderBy(GROUP_CONFIGURATION.CREATE_TIME.neg().desc(),GROUP_CONFIGURATION_VALUE.SORT.asc(),MEMBER.NAME.cast(Integer.class).asc())).fetchInto(Trainee.class);
//				.orderBy(GROUP_CONFIGURATION.CREATE_TIME.neg().desc(),GROUP_CONFIGURATION_VALUE.SORT.asc(),org2.DEPTH.asc(),MEMBER.NAME.cast(Integer.class).asc())).fetchInto(Trainee.class);
		// 获取当前学员在集合中的下标
		int i;
		for (i = 0; i < list.size(); i++) {
			if(list.get(i).getId().equals(id)){
				break;
			}
		}
		if(list.size()==1) {
			return 1;
		}
		// 如果排最后一位,取当前最大排序+1
		if(i==list.size()-1){
			int max = list.get(0).getSort();
			for (int j = 1; j < list.size(); j++) {
				if(list.get(j).getSort()>list.get(j-1).getSort()){
					max = list.get(j).getSort();
				}
			}
			return max+1;
		} else {
			// 如果非最后一位, 取其下面一位学员的sort
			return list.get(i+1).getSort();
		}
	}

	@Override
	public Trainee changeSort(Trainee trainee){
		int sort = getSortByRule(trainee.getId(), trainee.getClassId());
		if(sort==trainee.getSort()){
			return trainee;
		}
		trainee.setSort(sort);
		trainee.setModifyDate(null);
		return dao.update(trainee);
	}
	@Override
	public List<Trainee> trainees(String classId) {
		// TODO Auto-generated method stub
		return dao.fetch(TRAINEE.CLASS_ID.eq(classId),TRAINEE.TYPE.eq(0),TRAINEE.DELETE_FLAG.eq(0),TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE));
	}

	@Override
	public boolean getTrainee(String classId, String memberId) {
		List<Trainee> traineelList = dao.fetch(TRAINEE.CLASS_ID.eq(classId),
				TRAINEE.MEMBER_ID.eq(memberId),
				TRAINEE.DELETE_FLAG.eq(0),
				TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE));
		return traineelList != null && traineelList.size() > 0;
	}

	/**
	 * 获取用户所在班级的ID。
	 *
	 * @param classIds 指定的培训班ID列表。
	 * @param memberId 当前登录用户的成员ID。
	 * @return 返回用户所在班级的第一个ID。如果没有找到，则抛出异常。
	 * @throws UnprocessableException 如果当前用户不在指定的培训班列表中，则抛出此异常。
	 */
	@Override
	public String getClassId(List<String> classIds, String memberId) {
		// 根据指定的班级ID列表和成员ID，查询该成员是否在对应的培训班中
		List<String> targetIds = dao.execute(x -> x.select(TRAINEE.CLASS_ID).from(TRAINEE))
								.leftJoin(CLASS_INFO).on(TRAINEE.CLASS_ID.eq(CLASS_INFO.ID))
								.where(TRAINEE.CLASS_ID.in(classIds)
													   .and(TRAINEE.MEMBER_ID.eq(memberId))
													   .and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
													   .and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE)))
								.orderBy(CLASS_INFO.ARRIVE_DATE.asc())
								.limit(1)
								.fetch(TRAINEE.CLASS_ID);

		// 如果查询结果为空，表示当前用户不在指定的培训班中，抛出异常
		if (CollectionUtils.isEmpty(targetIds)) {
			throw new UnprocessableException(com.zxy.product.system.content.ErrorCode.UserIsNotPermitted);
		}

		// 返回查询到的第一个班级ID
		return targetIds.get(0);
	}


	@Override
	public StringBuffer findByName(String classId, List phones) {
		StringBuffer buffer = new StringBuffer();
		String[] names = dao.execute(x->x.select(MEMBER.FULL_NAME).from(TRAINEE).innerJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID)).where(TRAINEE.PHONE_NUMBER.in(phones).and(TRAINEE.CLASS_ID.eq(classId))
				.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)).and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))).fetchArray(MEMBER.FULL_NAME));
		for(int i = 0;i<names.length;i++){
			buffer.append(names[i]);
			if(i!=names.length-1){
				buffer.append(",");
			}
		}
		return buffer;
	}
	@Override
	public int getNoGrouping(String classId) {
		// TODO Auto-generated method stub
		return dao.execute(x -> x.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
						.where(TRAINEE.CLASS_ID.eq(classId))
						.and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.DELETE_FLAG.eq(DELETE_FLASE))
						.and(TRAINEE.AUDIT_STATUS.eq(AUDIT_AGREE))
						.and(TRAINEE.TRAINEE_GROUP_ID.eq("0")))
				.fetchOne(TRAINEE.ID.count());
	}
	@Override
	public List<Trainee> findTraineeByClassId(String classId){
		return  dao.execute(x -> x.select(Fields.start().add(TRAINEE).add(TRAINEE.MEMBER_ID,MEMBER.FULL_NAME,MEMBER.NAME).end()).from(TRAINEE)
				.leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID)).where(TRAINEE.CLASS_ID.eq(classId).and(TRAINEE.TYPE.eq(0))
						.and(TRAINEE.DELETE_FLAG.eq(0)).and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE)))).fetch(record -> {
			Trainee trainee = record.into(Trainee.class);
			trainee.setMemberName(record.getValue(MEMBER.NAME));
			trainee.setMemberFullName(record.getValue(MEMBER.FULL_NAME));
			return  trainee;
		});
	}

	@Override
	public List<Trainee> getTraineeByClassId(String classId) {
		return getTraineeByClassIdAndName(classId, Optional.empty(), Optional.empty(), Optional.empty());
	}

	/**
	 * 通过classId查询班级正式成员的相关信息，用于学习详情
	 *
	 * @param classId          班级id
	 * @param name             员工编号
	 * @param fullName         员工姓名
	 * @param organizationName 单位
	 * @return 学员列表
	 */
	@Override
	public List<Trainee> getTraineeByClassIdAndName(String classId, Optional<String> name, Optional<String> fullName, Optional<String> organizationName) {
		Field<String> jobName = JOB.NAME.as("jobName");
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		com.zxy.product.train.jooq.tables.Job ihrJobTable = JOB.as("ihrJobTable");
		Stream<Condition> conditions = Stream.of(
				name.map(MEMBER.NAME::contains),
				fullName.map(MEMBER.FULL_NAME::contains),
				organizationName.map(TRAINEE.NEW_COMPANY::contains)
		).filter(Optional::isPresent).map(Optional::get);
		Condition condition = conditions.reduce(Condition::and).orElse(DSL.trueCondition());
		return  dao.execute(x -> x.select(Fields.start().add(TRAINEE.ID,TRAINEE.MEMBER_ID,orgName,TRAINEE.NEW_ORGANIZATION,TRAINEE.JOB,TRAINEE.NEW_COMPANY,TRAINEE.PHONE_NUMBER,jobName,ihrJobTable.NAME).add(MEMBER.FULL_NAME,MEMBER.NAME).end()).from(TRAINEE)
				.leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
				.leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
				.leftJoin(POSITION).on(MEMBER.MAJOR_POSITION_ID.eq(POSITION.ID))
				.leftJoin(JOB).on(POSITION.JOB_ID.eq(JOB.ID))
				.leftJoin(ihrJobTable).on(ihrJobTable.ID.eq(MEMBER.JOB_ID))
				.where(TRAINEE.CLASS_ID.eq(classId).and(TRAINEE.TYPE.eq(Trainee.TRAINEE_TYPE_FORMAL))
						.and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)).and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE)).and(condition))).orderBy(TRAINEE.SORT).fetch(r -> {
			Trainee trainee = new Trainee();
			trainee.setId(r.getValue(TRAINEE.ID));
			String org  = r.getValue(TRAINEE.NEW_ORGANIZATION)!=null?r.getValue(TRAINEE.NEW_ORGANIZATION):r.getValue(orgName);
			if (r.getValue(TRAINEE.JOB) != null &&  !"".equals(r.getValue(TRAINEE.JOB))) {
			    trainee.setJobName(r.getValue(TRAINEE.JOB));
            } else if (r.getValue(ihrJobTable.NAME) != null && !"".equals(r.getValue(ihrJobTable.NAME))) {
                trainee.setJobName(r.getValue(ihrJobTable.NAME));
            }else {
                trainee.setJobName(r.getValue(jobName));
            }
			trainee.setMemberId(r.getValue(TRAINEE.MEMBER_ID));
			trainee.setPhoneNumber(r.getValue(TRAINEE.PHONE_NUMBER));
			trainee.setNewCompany(r.getValue(TRAINEE.NEW_COMPANY));
			trainee.setNewOrganization(org);
			trainee.setMemberName(r.getValue(MEMBER.NAME));
			trainee.setMemberFullName(r.getValue(MEMBER.FULL_NAME));
			return  trainee;
		});
	}

	@Override
	public List<String> getTraineeList(Integer start, Integer limit) {
		return dao.execute(dslContext -> {
			return dslContext.selectDistinct(
					Fields.start().add(TRAINEE.MEMBER_ID).end())
					.from(TRAINEE)
					.where(TRAINEE.TYPE.eq(0)
							.and(TRAINEE.DELETE_FLAG.eq(0))
							.and(TRAINEE.AUDIT_STATUS.eq(1))).limit(start,limit)
					.fetch(TRAINEE.MEMBER_ID);
		});
	}

	@Override
	public Optional<Trainee> getClassFormalTrainee(String classId,String memberId) {
		return  dao.execute(d ->
				d.select(TRAINEE.fields())
						.from(TRAINEE)
						.where(TRAINEE.CLASS_ID.eq(classId)
								.and(TRAINEE.TYPE.eq(TRAINEE_TYPE_FORMAL)).and(TRAINEE.MEMBER_ID.eq(memberId)))
						.limit(1)
						.fetchOptionalInto(Trainee.class));
	}

	@Override
	public Trainee updateTrainee(Trainee trainee) {
		trainee.setModifyDate(null);
		return dao.update(trainee);
	}

	/**
	 * 根据培训班编号以及用户编号,查询该用户在该培训班是否存在
	 * @param classId 培训班编号
	 * @param userId 用户主键
	 * @return
	 */
	@Override
	public Integer findByClassIdAndUserNameExists(String classId,String userId) {
		return dao.execute(e -> e
				.select(TRAINEE.ID.count()).
						from(TRAINEE).
						where(TRAINEE.CLASS_ID.eq(classId)).
						and(TRAINEE.MEMBER_ID.eq(userId)).
						and(TRAINEE.AUDIT_STATUS.eq(Trainee.AUDIT_AGREE)).
						and(TRAINEE.DELETE_FLAG.eq(Trainee.DELETE_FLASE)).
						fetchOne(TRAINEE.ID.count(),Integer.class));
	}

	@Override
	public void updateSort(List<Trainee> traineeList) {
		dao.execute(e -> e.batch(updateSort(e, traineeList)).execute());
	}

	@Override
	public PagedResult<Trainee> findGroupMember(int page, int pageSize,
												String classId, Optional<Integer> type, Optional<String> groupId,
												Optional<Integer> auditStatus) {

		// 重命名组织表
		com.zxy.product.train.jooq.tables.Organization org2 = ORGANIZATION.as("org2");
		com.zxy.product.train.jooq.tables.Job ihrJobTable = JOB.as("ihrJobTable");
		Field<String> mbName = MEMBER.NAME.as("mbName");
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		Field<String> orgId = ORGANIZATION.ID.as("orgId");
		Field<String> org2Name = org2.NAME.as("org2Name");
		Field<String> jobName = JOB.NAME.as("jobName");
		//查深度为3的组织
		List<String> orgIds = new ArrayList<String>();
		// 构建语句
		SelectOnConditionStep<Record> step = dao.execute(x -> x
				.selectDistinct(Fields.start().add(TRAINEE.ID,TRAINEE.JOB, MEMBER.FULL_NAME,MEMBER.HEAD_PORTRAIT,MEMBER.HEAD_PORTRAIT_PATH, mbName, orgId, orgName,ORGANIZATION.LEVEL,
												   org2Name,
												   ihrJobTable.NAME,
												   jobName, TRAINEE.SEX, TRAINEE.PHONE_NUMBER, TRAINEE.EMAIL, TRAINEE.AUDIT_STATUS,
												   TRAINEE.MEMBER_ID, TRAINEE.COMMIT_QUESTIONARY, TRAINEE.SORT, TRAINEE.LEVEL_ID, TRAINEE.NATION,
												   TRAINEE.TRAINEE_GROUP_ID,
												   ORGANIZATION.PATH,
												   TRAINEE.REMARK, ORGANIZATION.ORDER, TRAINEE.SORT_FOR_GROUP,TRAINEE.NEW_COMPANY,
												   TRAINEE.NEW_ORGANIZATION,TRAINEE.REGISTER, TRAINEE.SORT_NEW,MEMBER_CONFIG.VALUE).end())
				.from(TRAINEE)
				.leftJoin(MEMBER).on(TRAINEE.MEMBER_ID.eq(MEMBER.ID))
				.leftJoin(MEMBER_CONFIG).on(TRAINEE.NATION.eq(MEMBER_CONFIG.ID))
				.leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
				.leftJoin(org2).on(ORGANIZATION.COMPANY_ID.eq(org2.ID))
				.leftJoin(POSITION).on(MEMBER.MAJOR_POSITION_ID.eq(POSITION.ID))
				.leftJoin(JOB).on(POSITION.JOB_ID.eq(JOB.ID))
				.leftJoin(ihrJobTable).on(ihrJobTable.ID.eq(MEMBER.JOB_ID))
				.leftJoin(ORGANIZATION_DETAIL).on(TRAINEE.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB)));

		List<Condition> collect = Stream.of(Optional.of(classId).map(TRAINEE.CLASS_ID::eq),
											type.map(TRAINEE.TYPE::eq), groupId.map(TRAINEE.TRAINEE_GROUP_ID::eq),
											auditStatus.map(TRAINEE.AUDIT_STATUS::eq), Optional.of(DELETE_FLASE).map(TRAINEE.DELETE_FLAG::eq))
										.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

		// 获取行数
		Integer count = dao.execute(x -> x.fetchCount(step.where(collect)));

		step.orderBy(TRAINEE.SORT_FOR_GROUP.asc());

		// 获取列表
		List<Trainee> list = step.limit((page - 1) * pageSize, pageSize).fetch(r -> {
			Trainee trainee = new Trainee();
			trainee.setId(r.getValue(TRAINEE.ID));
			Member mb = new Member();
			mb.setName(r.getValue(mbName));
			mb.setFullName(r.getValue(MEMBER.FULL_NAME));
			if (r.getValue(TRAINEE.JOB) != null &&  !"".equals(r.getValue(TRAINEE.JOB))) {
				mb.setJobName(r.getValue(TRAINEE.JOB));
			} else if (r.getValue(ihrJobTable.NAME) != null && !"".equals(r.getValue(ihrJobTable.NAME))) {
				mb.setJobName(r.getValue(ihrJobTable.NAME));
			}else {
				mb.setJobName(r.getValue(jobName));
			}
			mb.setHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT));
			mb.setHeadPortraitPath(r.getValue(MEMBER.HEAD_PORTRAIT_PATH));
			trainee.setMember(mb);
			trainee.setTraineeGroupId(r.getValue(TRAINEE.TRAINEE_GROUP_ID));
			trainee.setSex(r.getValue(TRAINEE.SEX));
			trainee.setOrganizationId(r.getValue(orgId));
			trainee.setOrganizationName(r.getValue(orgName));
			trainee.setOrganizationLevel(r.getValue(ORGANIZATION.LEVEL));
			trainee.setPath(r.getValue(ORGANIZATION.PATH));
			trainee.setCompanyName(r.getValue(org2Name));
			trainee.setPhoneNumber(r.getValue(TRAINEE.PHONE_NUMBER));
			trainee.setLevelId(r.getValue(TRAINEE.LEVEL_ID));
			trainee.setMemberId(r.getValue(TRAINEE.MEMBER_ID));
			trainee.setNation(r.getValue(TRAINEE.NATION));
			trainee.setNationName(r.getValue(MEMBER_CONFIG.VALUE));
			trainee.setRemark(r.getValue(TRAINEE.REMARK));
			trainee.setEmail(r.getValue(TRAINEE.EMAIL));
			trainee.setAuditStatus(r.getValue(TRAINEE.AUDIT_STATUS));
			trainee.setSort(r.getValue(TRAINEE.SORT));
			trainee.setSortForGroup(r.getValue(TRAINEE.SORT_FOR_GROUP));
			trainee.setOrganizationOrder(r.getValue(ORGANIZATION.ORDER));
			trainee.setCommitQuestionary(r.getValue(TRAINEE.COMMIT_QUESTIONARY));
			trainee.setNewCompany(r.getValue(TRAINEE.NEW_COMPANY));
			trainee.setNewOrganization(r.getValue(TRAINEE.NEW_ORGANIZATION));
			trainee.setRegister(r.getValue(TRAINEE.REGISTER) == null ? NO_SHOW :r.getValue(TRAINEE.REGISTER));
			trainee.setSortNew(r.getValue(TRAINEE.SORT_NEW));
			return trainee;
		});

		return PagedResult.create(count, list);
	}

	@Override
	public void updateGroupSort(String id, Integer sortForGroup) {
		dao.execute(d -> d.update(TRAINEE).set(TRAINEE.SORT_FOR_GROUP,sortForGroup)
						  .where(TRAINEE.ID.eq(id))
						  .execute());
	}

	public List<Query> updateSort(DSLContext context,List<Trainee> traineeList) {
		List<Query> updates = new ArrayList<>();
		traineeList.stream().forEach(t -> {
			updates.add(context.update(TRAINEE).set(TRAINEE.SORT_NEW, t.getSortNew()).where(TRAINEE.ID.eq(t.getId())));
		});
		return updates;
	}

}
