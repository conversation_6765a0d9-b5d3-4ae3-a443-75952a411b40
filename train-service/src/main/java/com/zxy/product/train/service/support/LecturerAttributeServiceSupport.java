package com.zxy.product.train.service.support;

import static com.zxy.product.train.jooq.Tables.LECTURER;
import static com.zxy.product.train.jooq.Tables.LECTURER_ATTRIBUTE;
import static com.zxy.product.train.jooq.Tables.MEMBER;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.jooq.Condition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.LecturerAttributeService;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.entity.Lecturer;
import com.zxy.product.train.entity.LecturerAttribute;

/**
 * 讲师属性配置
 * <AUTHOR>
 *
 */
@Service
public class LecturerAttributeServiceSupport implements LecturerAttributeService {
	
    private CommonDao<LecturerAttribute> lecturerAttributeDao;
	
    private CommonDao<Lecturer> lecturerDao;

    @Autowired
	public void setLecturerAttributeDao(CommonDao<LecturerAttribute> lecturerAttributeDao) {
		this.lecturerAttributeDao = lecturerAttributeDao;
	}

    @Autowired
	public void setLecturerDao(CommonDao<Lecturer> lecturerDao) {
		this.lecturerDao = lecturerDao;
	}

	@Override
	public PagedResult<LecturerAttribute> findTypeForAll(Integer typeId) {
		if (typeId >= 0) {
			return findType(Optional.of(typeId));
		}else {
			List<LecturerAttribute> items = lecturerAttributeDao.execute(dao -> {
				return dao.select(Fields.start().add(LECTURER_ATTRIBUTE).end())
				.from(LECTURER_ATTRIBUTE)
				.orderBy(LECTURER_ATTRIBUTE.CREATE_TIME.desc())
				.fetch().into(LecturerAttribute.class);
			});
			int count = lecturerAttributeDao.count();
			return PagedResult.create(count, items);
		}
	}

	@Override
	public PagedResult<LecturerAttribute> find(Integer page, Integer pageSize) {
		return lecturerAttributeDao.execute(dao -> {
			List<LecturerAttribute> list = dao.select(Fields.start().add(LECTURER_ATTRIBUTE.ID,
											LECTURER_ATTRIBUTE.ATTRIBUTE_NAME,
											LECTURER_ATTRIBUTE.CREATE_MEMBER_ID,
											LECTURER_ATTRIBUTE.CREATE_TIME,
											LECTURER_ATTRIBUTE.TYPE_ID,
											LECTURER_ATTRIBUTE.ORGANIZATION_ID,
											MEMBER.FULL_NAME).end())
			.from(LECTURER_ATTRIBUTE)
			.leftJoin(MEMBER).on(LECTURER_ATTRIBUTE.CREATE_MEMBER_ID.eq(MEMBER.ID))
			.orderBy(LECTURER_ATTRIBUTE.CREATE_TIME.desc())
			.limit((page - 1) * pageSize, pageSize)
			.fetch(items -> {
				LecturerAttribute lecturerAttribute = items.into(LecturerAttribute.class);
				lecturerAttribute.setFullName(items.get(MEMBER.FULL_NAME));
				return lecturerAttribute;
			});
			int count = dao.fetchCount(LECTURER_ATTRIBUTE);
			return PagedResult.create(count, list);
		});
	}

	@Override
	public PagedResult<LecturerAttribute> findType(Optional<Integer> typeId) {
		return lecturerAttributeDao.execute(dao -> {
			List<LecturerAttribute> list = dao.select(Fields.start().add(LECTURER_ATTRIBUTE).end())
			.from(LECTURER_ATTRIBUTE)
			.where(LECTURER_ATTRIBUTE.TYPE_ID.eq(typeId.get()))
			.orderBy(LECTURER_ATTRIBUTE.CREATE_TIME.desc())
			.fetch().into(LecturerAttribute.class);
			int count = dao.fetchCount(LECTURER_ATTRIBUTE, LECTURER_ATTRIBUTE.TYPE_ID.eq(typeId.get()));
			return PagedResult.create(count, list);
		});
	}

	@Override
	public LecturerAttribute findInfo(Optional<String> id) {
		return lecturerAttributeDao.get(id.get());
	}

	@Override
	public LecturerAttribute insert(Optional<String> attributeName, Optional<Integer> typeId, String memberId) {
		if(this.checkUnique(attributeName.get(), Optional.empty()) > 0)
			throw new UnprocessableException(ErrorCode.LecturerAttributeNameAlreadyExists);
		LecturerAttribute lecturerAttribute = new LecturerAttribute();
		lecturerAttribute.setAttributeName(attributeName.get());
		lecturerAttribute.setCreateMemberId(memberId);
		lecturerAttribute.setOrganizationId("1");
		lecturerAttribute.setTypeId(typeId.get());
		lecturerAttribute.forInsert();
		return lecturerAttributeDao.insert(lecturerAttribute);
	}

	@Override
	public LecturerAttribute update(Optional<String> id, Optional<String> attributeName, Optional<Integer> typeId, String memberId) {
		if(this.checkUnique(attributeName.get(), id) > 0)
			throw new UnprocessableException(ErrorCode.LecturerAttributeNameAlreadyExists);
		LecturerAttribute lecturerAttribute = new LecturerAttribute();
		lecturerAttribute.setAttributeName(attributeName.get());
		lecturerAttribute.setExitMemberId(memberId);
		lecturerAttribute.setUpdateTime(System.currentTimeMillis());
		lecturerAttribute.setTypeId(typeId.get());
		lecturerAttribute.setId(id.get());
		return lecturerAttributeDao.update(lecturerAttribute);
	}

	@Override
	public Map<String, Object> delete(Optional<String> id) {
		Optional<Lecturer> lecturer = lecturerDao.fetchOne(LECTURER.ATTRIBUTE_ID.eq(id.get()));
		int num = 0;
		Map<String,Object> jsonObject = new HashMap<String,Object>();
		if (!lecturer.isPresent()) {
			num = lecturerAttributeDao.delete(id.get());
		}
		jsonObject.put("success", num >= 1);
		jsonObject.put("msg", num >= 1 ? "删除成功" : "讲师属性已经被关联，不能删除");
		jsonObject.put("num", num);
		return jsonObject;
	}

	@Override
	public void save(List<LecturerAttribute> list) {
		lecturerAttributeDao.insert(list);
	}

	@Override
	public Map<String, String> findAll(Integer type) {
		return lecturerAttributeDao.execute(d -> d.select(
				LECTURER_ATTRIBUTE.ID,
				LECTURER_ATTRIBUTE.ATTRIBUTE_NAME)
			.from(LECTURER_ATTRIBUTE)
			.where(LECTURER_ATTRIBUTE.TYPE_ID.eq(type))
			.orderBy(LECTURER_ATTRIBUTE.CREATE_TIME)
			.fetchMap(LECTURER_ATTRIBUTE.ID, LECTURER_ATTRIBUTE.ATTRIBUTE_NAME));
	}

	@Override
	public Map<String, String> findAll() {
		return lecturerAttributeDao.execute(d -> d.select(
				LECTURER_ATTRIBUTE.ID,
				LECTURER_ATTRIBUTE.ATTRIBUTE_NAME)
			.from(LECTURER_ATTRIBUTE)
			.orderBy(LECTURER_ATTRIBUTE.CREATE_TIME)
			.fetchMap(LECTURER_ATTRIBUTE.ID, LECTURER_ATTRIBUTE.ATTRIBUTE_NAME));
	}

	@Override
	public Integer checkUnique(String attributeName, Optional<String> id) {
		List<Condition> c = Stream.of(
				Optional.of(attributeName).map(LECTURER_ATTRIBUTE.ATTRIBUTE_NAME::eq),
				id.map(LECTURER_ATTRIBUTE.ID::ne)
				).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
		return lecturerAttributeDao.execute(d -> d.select(LECTURER_ATTRIBUTE.ID.count()).from(LECTURER_ATTRIBUTE)
				.where(c).fetchOne(LECTURER_ATTRIBUTE.ID.count()));
	}

	@Override
	public List<LecturerAttribute> findAllInfo() {
		return lecturerAttributeDao.fetch();
	}

}
