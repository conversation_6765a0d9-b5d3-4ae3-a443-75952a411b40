package com.zxy.product.train.service.support;

import static com.zxy.product.train.jooq.Tables.CLASS_INFO;
import static com.zxy.product.train.jooq.Tables.COLLEGE_TEACHING;
import static com.zxy.product.train.jooq.Tables.COURSE_ATTACH;
import static com.zxy.product.train.jooq.Tables.COURSE_ATTRIBUTE;
import static com.zxy.product.train.jooq.Tables.F2F_COURSE_LIBRARY;
import static com.zxy.product.train.jooq.Tables.LECTURER;
import static com.zxy.product.train.jooq.Tables.MEMBER;
import static com.zxy.product.train.jooq.Tables.ORGANIZATION;
import static com.zxy.product.train.jooq.Tables.PROJECT;
import static com.zxy.product.train.jooq.Tables.LECTURER_COURSE_CONFIG;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.CollegeTeachingService;
import com.zxy.product.train.entity.CollegeTeaching;
import com.zxy.product.train.entity.CourseAttach;

/**
 * 学院授课记录
 * <AUTHOR>
 *
 */
@Service
public class CollegeTeachingServiceSupport implements CollegeTeachingService{

	private CommonDao<CollegeTeaching> collegeTeachingDao;
	private CommonDao<CourseAttach> courseAttachDao;
	
	@Autowired
	public void setCourseAttachDao(CommonDao<CourseAttach> courseAttachDao) {
		this.courseAttachDao = courseAttachDao;
	}
	
	@Autowired
	public void setCollegeTeachingDao(CommonDao<CollegeTeaching> collegeTeachingDao) {
		this.collegeTeachingDao = collegeTeachingDao;
	}

	@Override
	public List<CollegeTeaching> findByCourseId(String courseId) {
		// TODO Auto-generated method stub
		List<CollegeTeaching> collegeTeachingList = collegeTeachingDao.fetch(COLLEGE_TEACHING.COURSE_ID.eq(courseId));
		return collegeTeachingList;
	}

	@Override
	public CollegeTeaching insert(String courseId, String lecturerId, String lecturerPost, String classId,
			Long courseDate, String organizationName, String className, String classMember, Double courseDuration,
			String obj, Integer objNum, Optional<Double> tax, Optional<Double> pay, Optional<Double> courseReward,
			Optional<Double> satisfiedDegree) {
		// TODO Auto-generated method stub
		CollegeTeaching ct = new CollegeTeaching();
		ct.forInsert();
		ct.setCourseId(courseId);
		ct.setLecturerId(lecturerId);
		ct.setLecturerPost(lecturerPost);
		ct.setCourseDate(courseDate);
		ct.setClassId(classId);
		ct.setOrganizationName(organizationName);
		ct.setClassName(className);
		ct.setClassMember(classMember);
		ct.setCourseDuration(courseDuration);
		ct.setObj(obj);
		ct.setObjNum(objNum);
		tax.ifPresent(ct::setTax);
		pay.ifPresent(ct::setPay);
		courseReward.ifPresent(ct::setCourseReward);
		satisfiedDegree.ifPresent(ct::setSatisfiedDegree);
		collegeTeachingDao.insert(ct);
		return ct;
	}

	@Override
	public CollegeTeaching update(String id, Optional<String> courseDuration, Optional<String> obj,
			Optional<String> objNum, Optional<String> satisfiedDegree,List<CourseAttach> courseAttachmentList) {
		CollegeTeaching ct = collegeTeachingDao.get(id);
		ct.setObj(obj.orElse(null));
		if (courseDuration.isPresent() && !"".equals(courseDuration.get())) {
			ct.setCourseDuration(Double.valueOf(courseDuration.get()));
		}
		if (objNum.isPresent() && !"".equals(objNum.get())) {
			ct.setObjNum(Integer.valueOf(objNum.get()));
		}
//		if (pay.isPresent() && !"".equals(pay.get())) {
//			ct.setPay(Double.valueOf(pay.get()));
//		}
//		if (tax.isPresent() && !"".equals(tax.get())) {
//			ct.setTax(Double.valueOf(tax.get()));
//		}
//		if (courseReward.isPresent() && !"".equals(courseReward.get())) {
//			ct.setCourseReward(Double.valueOf(courseReward.get()));
//		}
		if (satisfiedDegree.isPresent() && !"".equals(satisfiedDegree.get())) {
			ct.setSatisfiedDegree(Double.valueOf(satisfiedDegree.get()));
		}
		collegeTeachingDao.update(ct);
		updateCourseAttach(id, courseAttachmentList); // 更新附件
		return ct;
	}

	private void updateCourseAttach(String id, List<CourseAttach> newCourseAttachList) {
		if (newCourseAttachList.size() == 0) { // 附件处理
			courseAttachDao.delete(COURSE_ATTACH.COURSE_ID.eq(id));
		} else {
			Map<String, CourseAttach> oldCourseAttachList = courseAttachDao.fetch(COURSE_ATTACH.COURSE_ID.eq(id)).stream().collect(Collectors.toMap(CourseAttach :: getId, a -> a, (p, q) -> q));
			newCourseAttachList.forEach(a -> {
				a.forInsert();
				a.setCourseId(id);
				if (null != oldCourseAttachList.get(a.getId())) a.setAttachType(oldCourseAttachList.get(a.getId()).getAttachType());	
			});
			courseAttachDao.delete(COURSE_ATTACH.COURSE_ID.eq(id));
			courseAttachDao.insert(newCourseAttachList);
		}
	}
	
	@Override
	public Optional<CollegeTeaching> get(String id) {
		return collegeTeachingDao.fetchOne(COLLEGE_TEACHING.CLASS_ID.eq(id));
	}

	@Override
	public List<CollegeTeaching> findByLecturerId(List<String> lecturerIds, List<String> organizationIds, 
			Optional<String> courseName, Optional<String> className, Long startTime, Long endTime, 
			Double startSatisfied, Double endSatisfied) {	
		 List<CollegeTeaching>  list = collegeTeachingDao.execute(x -> x
					.selectDistinct(Fields.start().add(COLLEGE_TEACHING)
							.add(
							LECTURER.NAME,
							LECTURER.JOB_NAME,
							LECTURER.UNIT,
							LECTURER.TYPE,
							COURSE_ATTRIBUTE.ID,
							COURSE_ATTRIBUTE.ATTRIBUTE_NAME,
							ORGANIZATION.as("class_org").NAME,
							F2F_COURSE_LIBRARY.NAME,
							F2F_COURSE_LIBRARY.INSTITUTION_ID,
							F2F_COURSE_LIBRARY.ORGANIZATION_ID,
							F2F_COURSE_LIBRARY.IS_SHARE,
							LECTURER_COURSE_CONFIG.NAME,
							PROJECT.NAME,
							PROJECT.OBJECT,
							MEMBER.FULL_NAME,
							PROJECT.ID,
							CLASS_INFO.TRAINEE_NUM,
							CLASS_INFO.COURSE_SATISFIED
							).end())
					.from(LECTURER)
					.innerJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID))
					.innerJoin(F2F_COURSE_LIBRARY).on(COLLEGE_TEACHING.COURSE_ID.eq(F2F_COURSE_LIBRARY.ID))
					.leftJoin(CLASS_INFO).on(COLLEGE_TEACHING.CLASS_ID.eq(CLASS_INFO.ID))
					.leftJoin(ORGANIZATION.as("class_org")).on(CLASS_INFO.ORGANIZATION_ID.eq(ORGANIZATION.as("class_org").ID))
					.leftJoin(MEMBER).on(CLASS_INFO.CLASS_TEACHER.eq(MEMBER.ID))
					.leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
					.leftJoin(COURSE_ATTRIBUTE).on(F2F_COURSE_LIBRARY.COURSE_ATTRIBUTES.eq(COURSE_ATTRIBUTE.ID))
					.leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER_COURSE_CONFIG.ID.eq(F2F_COURSE_LIBRARY.SEQUENCE))
					.where(LECTURER.ID.in(lecturerIds))

					.and(courseName.isPresent() ? F2F_COURSE_LIBRARY.NAME.contains(courseName.get()) : DSL.trueCondition())
					.and(className.isPresent() ? PROJECT.NAME.contains(className.get()) : DSL.trueCondition())
					.and(startTime > 0 ? COLLEGE_TEACHING.COURSE_DATE.ge(startTime) : DSL.trueCondition())
					.and(endTime > 0 ? COLLEGE_TEACHING.COURSE_DATE.le(endTime) : DSL.trueCondition())
					.and(startSatisfied > 0 ? COLLEGE_TEACHING.SATISFIED_DEGREE.ge(startSatisfied) : DSL.trueCondition())
					.and(endSatisfied > 0 ? COLLEGE_TEACHING.SATISFIED_DEGREE.le(endSatisfied) : DSL.trueCondition())
					.orderBy(DSL.field(("CONVERT(" + F2F_COURSE_LIBRARY.NAME + " USING GBK)").replaceAll("\"", "`")).asc(),F2F_COURSE_LIBRARY.CREATE_TIME.desc())
					.fetch(r -> {
						CollegeTeaching cl = r.into(CollegeTeaching.class);
						cl.setClassName(r.getValue(PROJECT.NAME));
						cl.setInstitutionId(r.getValue(F2F_COURSE_LIBRARY.INSTITUTION_ID));
						cl.setIsShare(r.getValue(F2F_COURSE_LIBRARY.IS_SHARE));
						cl.setCourseOrganizationId(r.getValue(F2F_COURSE_LIBRARY.ORGANIZATION_ID));
						cl.setProjectId(r.getValue(PROJECT.ID));
						cl.setObj(r.getValue(PROJECT.OBJECT));
						cl.setClassMember(r.getValue(MEMBER.FULL_NAME));
						cl.setLecturerName(r.getValue(LECTURER.NAME));
						cl.setLecturerPost(r.getValue(LECTURER.JOB_NAME));
						cl.setLecturerOrganization(r.getValue(LECTURER.UNIT));
						cl.setOrganizationName(r.getValue(ORGANIZATION.as("class_org").NAME));
						cl.setLecturerType(r.getValue(LECTURER.TYPE));
						cl.setAttributeId(r.getValue(COURSE_ATTRIBUTE.ID));
						cl.setAttributeName(r.getValue(COURSE_ATTRIBUTE.ATTRIBUTE_NAME));
						cl.setCourseName(r.getValue(F2F_COURSE_LIBRARY.NAME));
						cl.setObjNum(r.getValue(CLASS_INFO.TRAINEE_NUM));
						cl.setSatisfiedDegree(r.getValue(CLASS_INFO.COURSE_SATISFIED));
						cl.setCourseSequenceName(r.getValue(LECTURER_COURSE_CONFIG.NAME));
						return cl;
				    }));
		 Map<String,String> map = new HashMap();
		 list.forEach(ct -> {
			 List<CourseAttach> ac = courseAttachDao.fetch(COURSE_ATTACH.COURSE_ID.eq(ct.getId()));
			 ct.setCourseAttachList(ac);
			 map.put(ct.getId(),ct.getCourseName());
		 });	
		 
		 List<String> ids = list.stream().map(CollegeTeaching :: getId).collect(Collectors.toList());
		 
		 List<CollegeTeaching> collegeTeachingList = collegeTeachingDao.fetch(COLLEGE_TEACHING.ID.in(ids));
		 Map<String, CollegeTeaching> collegeTeachingMap = collegeTeachingList.stream().collect(Collectors.toMap(CollegeTeaching :: getId, a -> a, (p, q) -> q));
		 
		 list.forEach(l -> {
			 CollegeTeaching c = collegeTeachingMap.get(l.getId());
			 if (null != c.getSatisfiedDegree()) {
				l.setSatisfiedDegree(c.getSatisfiedDegree());
			 }
			 if (null != c.getObjNum()) {
					l.setObjNum(c.getObjNum());
		     }
			 if (null != c.getObj()) {
					l.setObj(c.getObj());
		     }
			 if (null == c.getSatisfiedDegree() && null == l.getSatisfiedDegree()) {
					l.setSatisfiedDegree(0.0);
			 } 
			 l.setCourseName(map.get(l.getId()));
			 l.setIsGrant(organizationIds.contains(l.getCourseOrganizationId()) ? true : false);
		 });
		 
		return list;
	}
	
	
	@Override
	public CollegeTeaching detail(String id){
		return collegeTeachingDao.getOptional(id).map(ct -> {
			List<CourseAttach> courseAttach = courseAttachDao.fetch(COURSE_ATTACH.COURSE_ID.eq(id));
			ct.setCourseAttachList(courseAttach);
			return ct;
		}).orElse(null);
	}

	@Override
	public boolean countCollegeTeachingByClassId(String classId) {
		List list = collegeTeachingDao.fetch(COLLEGE_TEACHING.CLASS_ID.eq(classId));
		boolean flag = list.size()>0?false:true;
		return flag;
	}

	@Override
	public List<CollegeTeaching> findCourseByCourseId(List<String> courseIds, List<String> organizationIds ) {
		 List<CollegeTeaching>  list = collegeTeachingDao.execute(x -> x
					.selectDistinct(Fields.start().add(COLLEGE_TEACHING)
							.add(
							LECTURER.NAME,
							LECTURER.JOB_NAME,
							LECTURER.UNIT,
							LECTURER.TYPE,
							LECTURER.ASCRIPTION_ORGANIZATION_ID,
							MEMBER.FULL_NAME,
							CLASS_INFO.TRAINEE_NUM,
							CLASS_INFO.COURSE_SATISFIED,
							PROJECT.OBJECT,
							ORGANIZATION.as("class_org").NAME,
							F2F_COURSE_LIBRARY.NAME,
							F2F_COURSE_LIBRARY.INSTITUTION_ID,
							PROJECT.ID,
							PROJECT.NAME
							).end())
					.from(F2F_COURSE_LIBRARY)
					.innerJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.COURSE_ID.eq(F2F_COURSE_LIBRARY.ID))
					.innerJoin(LECTURER).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID))
					.leftJoin(CLASS_INFO).on(COLLEGE_TEACHING.CLASS_ID.eq(CLASS_INFO.ID))
					.leftJoin(MEMBER).on(CLASS_INFO.CLASS_TEACHER.eq(MEMBER.ID))
					.leftJoin(ORGANIZATION.as("class_org")).on(CLASS_INFO.ORGANIZATION_ID.eq(ORGANIZATION.as("class_org").ID))
					.leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
					.where(F2F_COURSE_LIBRARY.ID.in(courseIds))
					.orderBy(DSL.field(("CONVERT(" + LECTURER.NAME + " USING GBK)").replaceAll("\"", "`")).asc(),LECTURER.CREATE_TIME.desc())
					.fetch(r -> {
						CollegeTeaching cl = r.into(CollegeTeaching.class);
						cl.setLecturerOrganization(r.getValue(LECTURER.UNIT));
						cl.setLecturerOrganizationId(r.getValue(LECTURER.ASCRIPTION_ORGANIZATION_ID));
				        cl.setLecturerType(r.getValue(LECTURER.TYPE));
				        cl.setInstitutionId(r.getValue(F2F_COURSE_LIBRARY.INSTITUTION_ID));
						cl.setClassName(r.getValue(PROJECT.NAME));
						cl.setProjectId(r.getValue(PROJECT.ID));
						cl.setObj(r.getValue(PROJECT.OBJECT));
						cl.setLecturerName(r.getValue(LECTURER.NAME));
						cl.setLecturerPost(r.getValue(LECTURER.JOB_NAME));
						cl.setClassMember(r.getValue(MEMBER.FULL_NAME));
						cl.setOrganizationName(r.getValue(ORGANIZATION.as("class_org").NAME));
						cl.setObjNum(r.getValue(CLASS_INFO.TRAINEE_NUM));
						cl.setSatisfiedDegree(r.getValue(CLASS_INFO.COURSE_SATISFIED));
						cl.setCourseName(r.getValue(F2F_COURSE_LIBRARY.NAME));
						return cl;
				    }));
		
		 list.forEach(ct -> {
			 List<CourseAttach> ac = courseAttachDao.fetch(COURSE_ATTACH.COURSE_ID.eq(ct.getId()));
			 ct.setCourseAttachList(ac);
		 });
		 
		 List<String> ids = list.stream().map(CollegeTeaching :: getId).collect(Collectors.toList());
		 
		 List<CollegeTeaching> collegeTeachingList = collegeTeachingDao.fetch(COLLEGE_TEACHING.ID.in(ids));
		 Map<String, CollegeTeaching> collegeTeachingMap = collegeTeachingList.stream().collect(Collectors.toMap(CollegeTeaching :: getId, a -> a, (p, q) -> q));
		 
		 list.forEach(l -> {
			 CollegeTeaching c = collegeTeachingMap.get(l.getId());
			 if (null != c.getSatisfiedDegree()) {
				l.setSatisfiedDegree(c.getSatisfiedDegree());
			 }
			 if (null != c.getObjNum()) {
					l.setObjNum(c.getObjNum());
		     }
			 if (null != c.getObj()) {
					l.setObj(c.getObj());
		     }
			 if (null == c.getSatisfiedDegree() && null == l.getSatisfiedDegree()) {
					l.setSatisfiedDegree(0.0);
			 } 
			 l.setIsGrant(organizationIds.contains(l.getLecturerOrganizationId()) ? true : false);
		 });
		  
		return list;
	}

	@Override
	public List<CollegeTeaching> findCourseIdsByInfo(List<String> courseIds) {
		return collegeTeachingDao.fetch(COLLEGE_TEACHING.COURSE_ID.in(courseIds));
	}

	@Override
	public List<CollegeTeaching> findLecturerIdsByInfo(List<String> lecturerIds) {
		return collegeTeachingDao.execute(dao -> {
			return dao.select(Fields.start().add(COLLEGE_TEACHING)
					.add(F2F_COURSE_LIBRARY.NAME,
						 COURSE_ATTRIBUTE.ATTRIBUTE_NAME,
						 LECTURER_COURSE_CONFIG.NAME).end())
			.from(COLLEGE_TEACHING)
			.leftJoin(F2F_COURSE_LIBRARY).on(COLLEGE_TEACHING.COURSE_ID.eq(F2F_COURSE_LIBRARY.ID))
			.leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER_COURSE_CONFIG.ID.eq(F2F_COURSE_LIBRARY.SEQUENCE))
			.leftJoin(COURSE_ATTRIBUTE).on(COURSE_ATTRIBUTE.ID.eq(F2F_COURSE_LIBRARY.COURSE_ATTRIBUTES))
			.where(COLLEGE_TEACHING.LECTURER_ID.in(lecturerIds))
			.fetch(item -> {
				CollegeTeaching ct = item.into(CollegeTeaching.class);
				ct.setCourseName(item.get(F2F_COURSE_LIBRARY.NAME));
				ct.setCourseSequenceName(item.getValue(LECTURER_COURSE_CONFIG.NAME));
				ct.setAttributeName(item.get(COURSE_ATTRIBUTE.ATTRIBUTE_NAME));
				return ct;
			});
		});
	}
}
