package com.zxy.product.train.service.support;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.train.api.*;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.*;
import com.zxy.product.train.service.util.DesensitizationUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.tools.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.train.jooq.Tables.*;

/**
 * Created by chengzhi on 17/2/15.
 */
@Service
public class LecturerServiceSupport implements LecturerService {

	private CommonDao<ClassOfflineCourse> offlineCourseDao;
	private CommonDao<Organization> organizationCommonDao;
	private MessageSender sender;
	private CommonDao<Lecturer> lecturerDao;
	private CommonDao<LecturerLabel> lecturerLabelDao;
	private CommonDao<Label> labelDao;
	private CommonDao<Project> projectDao;
	private CommonDao<Level> levelDao;
	private CommonDao<Member> memberDao;
	private CommonDao<ClassInfo> classInfoDao;
	private CommonDao<Position> positionDao;
	private CommonDao<Job> jobDao;
	private CommonDao<F2fCourseLibrary> f2fCourseLibraryDao;
	private LecturerCourseConfigService lecturerCourseConfigService;
	private CommonDao<LecturerAdeptCourse> lecturerAdeptCourseDao;
	private OrganizationTeachingService organizationTeachingService;
	private OtherTeachingService otherTeachingService;
	private CollegeTeachingService collegeTeachingService;
	private CommonDao<OfflineCourseLibrary> offlineCourseLibraryDao;
	private LecturerAttributeService lecturerAttributeService;
	private OfflineCourseLibraryService offlineCourseLibraryService;
	private CommonDao<OrganizationTeaching> organizationTeachingDao;
	private CommonDao<OtherTeaching> otherTeachingDao;
	private CommonDao<CollegeTeaching> collegeTeachingDao;
	private CommonDao<HomeLecturer> homeLecturerDao;
	private LecturerThumbsUpService lecturerThumbsUpService;
	private LecturerAdeptCourseService lecturerAdeptCourseService;
	@Resource
	private CommonDao<DeleteDataTrain> deleteDataTrainCommonDao;

	@Autowired
	public void setPositionDao(CommonDao<Position> positionDao) {
		this.positionDao = positionDao;
	}
	@Autowired
	public void setJobDao(CommonDao<Job> jobDao) {
		this.jobDao = jobDao;
	}
	@Autowired
	public void setClassInfoDao(CommonDao<ClassInfo> classInfoDao) {
		this.classInfoDao = classInfoDao;
	}
	@Autowired
	public void setOrganizationCommonDao(CommonDao<Organization> organizationCommonDao) {
		this.organizationCommonDao = organizationCommonDao;
	}
	@Autowired
	public void setOfflineCourseDao(CommonDao<ClassOfflineCourse> offlineCourseDao) {
		this.offlineCourseDao = offlineCourseDao;
	}

	@Autowired
	public void setLabelDao(CommonDao<Label> labelDao) {
		this.labelDao = labelDao;
	}

	@Autowired
	public void setLecturerDao(CommonDao<Lecturer> lecturerDao) {
		this.lecturerDao = lecturerDao;
	}
	@Autowired
	public void setSender(MessageSender sender) {
		this.sender = sender;
	}

	@Autowired
	public void setLecturerLabelDao(CommonDao<LecturerLabel> lecturerLabelDao) {
		this.lecturerLabelDao = lecturerLabelDao;
	}
	@Autowired
	public void setProjectDao(CommonDao<Project> projectDao) {
		this.projectDao = projectDao;
	}

	@Autowired
	public void setMemberDao(CommonDao<Member> memberDao) {
		this.memberDao = memberDao;
	}

	@Autowired
    public void setLevelDao(CommonDao<Level> levelDao) {
        this.levelDao = levelDao;
    }

	@Autowired
	public void setHomeLecturerDao(CommonDao<HomeLecturer> homeLecturerDao) {
		this.homeLecturerDao = homeLecturerDao;
	}

	@Autowired
    public void setF2fCourseLibraryDao(CommonDao<F2fCourseLibrary> f2fCourseLibraryDao) {
		this.f2fCourseLibraryDao = f2fCourseLibraryDao;
	}

    @Autowired
    public void setLecturerCourseConfigService(LecturerCourseConfigService lecturerCourseConfigService) {
		this.lecturerCourseConfigService = lecturerCourseConfigService;
	}
    @Autowired
    public void setLecturerAdeptCourseDao(CommonDao<LecturerAdeptCourse> lecturerAdeptCourseDao) {
		this.lecturerAdeptCourseDao = lecturerAdeptCourseDao;
	}
    @Autowired
	public void setOrganizationTeachingService(OrganizationTeachingService organizationTeachingService) {
		this.organizationTeachingService = organizationTeachingService;
	}
    @Autowired
	public void setOtherTeachingService(OtherTeachingService otherTeachingService) {
		this.otherTeachingService = otherTeachingService;
	}

    @Autowired
	public void setCollegeTeachingService(CollegeTeachingService collegeTeachingService) {
		this.collegeTeachingService = collegeTeachingService;
	}
	@Autowired
	public void setOfflineCourseLibraryDao(CommonDao<OfflineCourseLibrary> offlineCourseLibraryDao) {
		this.offlineCourseLibraryDao = offlineCourseLibraryDao;
	}

	@Autowired
	public void setLecturerAttributeService(LecturerAttributeService lecturerAttributeService) {
		this.lecturerAttributeService = lecturerAttributeService;
	}
	@Autowired
	public void setOfflineCourseLibraryService(OfflineCourseLibraryService offlineCourseLibraryService) {
		this.offlineCourseLibraryService = offlineCourseLibraryService;
	}

	@Autowired
	public void setOrganizationTeachingDao(CommonDao<OrganizationTeaching> organizationTeachingDao) {
		this.organizationTeachingDao = organizationTeachingDao;
	}
	@Autowired
	public void setOtherTeachingDao(CommonDao<OtherTeaching> otherTeachingDao) {
		this.otherTeachingDao = otherTeachingDao;
	}
	@Autowired
	public void setCollegeTeachingDao(CommonDao<CollegeTeaching> collegeTeachingDao) {
		this.collegeTeachingDao = collegeTeachingDao;
	}
	@Autowired
	public void setLecturerThumbsUpService(LecturerThumbsUpService lecturerThumbsUpService) {
		this.lecturerThumbsUpService = lecturerThumbsUpService;
	}

	@Autowired
	public void setLecturerAdeptCourseService(LecturerAdeptCourseService lecturerAdeptCourseService) {
		this.lecturerAdeptCourseService = lecturerAdeptCourseService;
	}
	@Override
    public PagedResult<Lecturer> find(int page, int pageSize, String currentMemberId, Optional<String> organizationId, Optional<String> name,
                                      Optional<Integer> type,Optional<Integer> status, Optional<String> levelId, Optional<String> className,
                                       Optional<String> courseName, Optional<String> parentId,
                                       Optional<String> sequenceId, Optional<String> label, Optional<String> attributeId,
                                       Optional<String> unit, Optional<String> courseType, Optional<String> courseId,
                                       Optional<List<String>> organizationIds) {
    	// 命名字段别名
//        Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
//        Field<String> levelName = LEVEL.NAME.as("levelName");
        //关联的级别IDS，讲师ids，组织ids
        List<String> levelIds = new ArrayList<>();
        List<String> memberIds = new ArrayList<>();
        Set<String> orgIds = new HashSet<>();
        List<String> pIds = new ArrayList<>();
        List<String> jIds = new ArrayList<>();
        //获取面授课程库课程列表
        List<String> courseList = new ArrayList<String>();
        Condition courseIdsDefalut = DSL.trueCondition();
        if (courseName.isPresent() || courseType.isPresent()) {
        	courseList = f2fCourseLibraryDao.execute(dao -> {
        		Condition conditions = F2F_COURSE_LIBRARY.IS_USE.eq(F2fCourseLibrary.IS_USE_TRUE)
        				.and(courseType.map(F2F_COURSE_LIBRARY.SEQUENCE::eq).orElse(DSL.trueCondition()))
        				.and(courseName.map(F2F_COURSE_LIBRARY.NAME::contains).orElse(DSL.trueCondition()));
        		return dao.select(Fields.start().add(F2F_COURSE_LIBRARY.ID).end())
		        		.from(F2F_COURSE_LIBRARY)
		        		.where(conditions)
		        		.fetch(item -> {
		        			return String.valueOf(item.get(F2F_COURSE_LIBRARY.ID));
		        		});
        	});
        	courseIdsDefalut = courseList.size() > 0 ?
        			LECTURER_ADEPT_COURSE.COURSE_ID.in(courseList)
        			.or(COLLEGE_TEACHING.COURSE_ID.in(courseList))
        			.or(ORGANIZATION_TEACHING.COURSE_ID.in(courseList)
        					.and(ORGANIZATION_TEACHING.APPROVAL_STATUS.eq(OrganizationTeaching.APPROVAL_STATUS_PASS))
    					)
        			: DSL.falseCondition();
		}
        Condition courseIds = courseId.isPresent() ? courseIdsDefalut.and(
	        		COLLEGE_TEACHING.COURSE_ID.eq(courseId.get())
	        		.or(ORGANIZATION_TEACHING.COURSE_ID.eq(courseId.get()))
        		) : courseIdsDefalut;
		Condition unitCent =  unit.isPresent() ? courseIds.and(LECTURER.UNIT.contains(unit.get()).or(LECTURER.INSTITUTIONS.contains(unit.get()))) : courseIds;
		List<String> lectureIds = new ArrayList<String>();
        List<Lecturer> lectureList = lecturerDao.execute(d -> {
            Table<Record1<String>> basic = (d.select(LECTURER.ID)
                    .from(LECTURER)
                    .leftJoin(CLASS_OFFLINE_COURSE).on(LECTURER.ID.eq(CLASS_OFFLINE_COURSE.TEACHER_ID))
                    .leftJoin(CLASS_INFO).on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
                    .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))

                    .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
                    .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
                    .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))

                    .leftJoin(LECTURER_ADEPT_COURSE).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
                    .leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID))
                    .leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.ID))

                    .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
                    .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
                    .and(className.map(
                    		c ->
                    			CLASS_INFO.PROJECT_ID.in(projectDao.fetch(PROJECT.NAME.contains(c)).stream().map(Project::getId).collect(Collectors.toList()))
                    			.or(ORGANIZATION_TEACHING.CLASS_NAME.contains(c))
                    			)
                    		.orElse(DSL.trueCondition())
                		)
                    .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
                    .and(status.map(LECTURER.STATUS::eq).orElse(DSL.trueCondition()))

                    .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
                    .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
                    .and(label.isPresent()?LABEL.NAME.contains(label.get()):DSL.trueCondition())
                    .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))

                    .and(unitCent)

                    // 系统权限限制
//                    .and(MEMBER.ORGANIZATION_ID.in(organizationIds.get())
//                    .or(LECTURER.TYPE.eq(1)))
                    // 归属权限限制
                    .and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
                    .groupBy(LECTURER.ID)
//                    .orderBy(LECTURER.CREATE_TIME.desc(),LECTURER.MOBILE.desc())
//                    .limit((page - 1) * pageSize, pageSize)
  			).asTable("b");
            Field<String> sequenceName = LECTURER_COURSE_CONFIG.NAME.as("sequenceName");
    		Field<String> organizationCompanyId = ORGANIZATION.COMPANY_ID.as("organizationCompanyId");
  			return d.select(Fields.start()
  					.add(LECTURER).add(sequenceName).add(organizationCompanyId)
  					.end())
  					.from(LECTURER)
  					.innerJoin(basic).on(basic.field(LECTURER.ID).eq(LECTURER.ID))
                    .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(LECTURER.ORGANIZATION_ID))
  					.orderBy(LECTURER.STATUS.asc(),LECTURER.CREATE_TIME.desc(),LECTURER.MOBILE.desc())
  					.limit((page - 1) * pageSize, pageSize)
  					.fetch(r -> {
  						Lecturer l = r.into(Lecturer.class);
  						lectureIds.add(l.getId());
  						l.setSequenceName(r.getValue(sequenceName));
                        if (r.getValue(LECTURER.LEVEL_ID) != null && r.getValue(LECTURER.LEVEL_ID) != "") {
  							levelIds.add(r.getValue(LECTURER.LEVEL_ID));
  						}
  						memberIds.add(r.getValue(LECTURER.MEMBER_ID));
//                        if (r.getValue(LECTURER.MEMBER_ID) != null && r.getValue(LECTURER.MEMBER_ID) != "") {
//                        }
  						orgIds.add(r.getValue(LECTURER.ORGANIZATION_ID));
  						orgIds.add(r.getValue(LECTURER.ASCRIPTION_ORGANIZATION_ID));
  						orgIds.add(r.getValue(organizationCompanyId));
//                        if (r.getValue(LECTURER.ORGANIZATION_ID) != null && r.getValue(LECTURER.ORGANIZATION_ID) != "") {
//                        }
                        return l;
                    });
        });
        //获取行数
        Integer count = 0;
        if (!className.isPresent() && !courseName.isPresent() && !courseType.isPresent()) {
            count = lecturerDao.execute(x -> x.select(LECTURER.ID.countDistinct()).from(LECTURER)
                    .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID)))
                    .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
                    .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
                    .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))
                    .leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID))
                    .leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.ID))
                    .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
                    .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
                    .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
                    .and(status.map(LECTURER.STATUS::eq).orElse(DSL.trueCondition()))
//                    .and(adeptCourse.map(LECTURER.ADEPT_COURSE::contains).orElse(DSL.trueCondition()))
//                    .and(MEMBER.COMPANY_ID.in(organizationIds.get())
                    .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
                    .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
                    .and(label.isPresent()?LABEL.NAME.contains(label.get()):DSL.trueCondition())
                    .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))
                    .and(unitCent)
//                    .and(MEMBER.ORGANIZATION_ID.in(organizationIds.get())
//                    .or(LECTURER.TYPE.eq(1)))
					.and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
                    .fetchOne(LECTURER.ID.count());
        } else {
            count = lecturerDao.execute(x -> x.select(LECTURER.ID.countDistinct()).from(LECTURER))
            .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))
            .leftJoin(CLASS_OFFLINE_COURSE).on(CLASS_OFFLINE_COURSE.TEACHER_ID.eq(LECTURER.ID))
            .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(CLASS_OFFLINE_COURSE.CLASS_ID))
            .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
            .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
            .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))
            .leftJoin(LECTURER_ADEPT_COURSE).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
            .leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.MEMBER_ID))
            .leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.ID))
            .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
            .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
            .and(className.map(c -> CLASS_INFO.PROJECT_ID.in(projectDao.fetch(PROJECT.NAME.contains(c)).stream().map(Project::getId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
//            .and(courseName.map(CLASS_OFFLINE_COURSE.NAME::contains).orElse(DSL.trueCondition()))
//            .and(adeptCourse.map(LECTURER.ADEPT_COURSE::contains).orElse(DSL.trueCondition()))
            .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
            .and(status.map(LECTURER.STATUS::eq).orElse(DSL.trueCondition()))
//            .and(organizationId.map(o -> LECTURER.ORGANIZATION_ID.in(organizationCommonDao.fetch(ORGANIZATION.PATH.contains(o)).stream().map(Organization::getId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
//            .and(MEMBER.COMPANY_ID.in(organizationIds.get())
            .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
            .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
            .and(label.isPresent()?LABEL.NAME.contains(label.get()):DSL.trueCondition())
            .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))
            .and(unitCent)
//            .and(MEMBER.ORGANIZATION_ID.in(organizationIds.get())
//            .or(LECTURER.TYPE.eq(1)))
            .and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
            .fetchOne(LECTURER.ID.count());
        }
        //最新授课时间
		Field<Long> latelyOrgRecordTime = COLLEGE_TEACHING.COURSE_DATE.max().as("latelyOrgRecordTime");
		Map<String, Long> recordMap = collegeTeachingDao.execute(dao -> {
			Map<String, Long> map = new HashMap<String, Long>();
			dao.select(COLLEGE_TEACHING.LECTURER_ID,latelyOrgRecordTime)
			.from(COLLEGE_TEACHING)
			.where(COLLEGE_TEACHING.LECTURER_ID.in(lectureIds))
			.groupBy(COLLEGE_TEACHING.LECTURER_ID)
			.fetch(item -> {
				map.put(item.getValue(COLLEGE_TEACHING.LECTURER_ID),Long.valueOf(item.getValue(latelyOrgRecordTime)));
				return null;
			});
			return map;
		});

        //level
        Map<String, Level> levelMap = levelDao.execute(x -> x.select(LEVEL.ID, LEVEL.NAME).from(LEVEL)).where(LEVEL.ID.in(levelIds))
                .fetch(r -> r.into(Level.class)).stream()
        .collect(Collectors.toMap(Level::getId, l -> l));
        //组织
        Map<String, Organization> orgMap = organizationCommonDao.execute(x -> x.select(ORGANIZATION.NAME, ORGANIZATION.ID, ORGANIZATION.COMPANY_ID).from(ORGANIZATION)).where(ORGANIZATION.ID.in(orgIds))
                .fetch(r -> r.into(Organization.class)).stream()
                .collect(Collectors.toMap(Organization::getId, c -> c));
        //人员
        Map<String, Member> memberMap = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.NAME,MEMBER.MAJOR_POSITION_ID).from(MEMBER)).where(MEMBER.ID.in(memberIds))
                .fetch(r -> {
                	Member m =  r.into(Member.class);
                	pIds.add(r.getValue(MEMBER.MAJOR_POSITION_ID));
                	return m;
                }).stream()
                .collect(Collectors.toMap(Member::getId, l -> l));

        Map<String, Position> positionMap = positionDao.execute(x -> x.select(POSITION.ID,POSITION.JOB_ID).from(POSITION)).where(POSITION.ID.in(pIds))
                .fetch(r ->{
                	Position p = r.into(Position.class);
                	jIds.add(r.getValue(POSITION.JOB_ID));
                	return p;
                }).stream()
                .collect(Collectors.toMap(Position::getId, l -> l));
        //讲师属性分类
        Map<String, Integer> attributeTypeMap = lecturerAttributeService.findAllInfo().stream()
        		.collect(Collectors.toMap(LecturerAttribute::getId, LecturerAttribute::getTypeId));

        Map<String, Job> jobMap = jobDao.execute(x -> x.select(JOB.ID, JOB.NAME).from(JOB)).where(JOB.ID.in(jIds))
                .fetch(r -> r.into(Job.class)).stream()
                .collect(Collectors.toMap(Job::getId, l -> l));
        Map<String, String> attributeMap = lecturerAttributeService.findAll();
        lectureList.forEach(f -> {
//                String level = f.getLevelId();
                String memberId = f.getMemberId();
                String orgId = f.getOrganizationId();
                String aorgId = f.getAscriptionOrganizationId();
                String attributeIds = f.getAttributeId();
                String pId = null;
                String jId = null;
                if(levelMap.get(f.getLevelId()) != null && Lecturer.TYPE_INSIDE.equals(f.getType())){
                	f.setLevelName(levelMap.get(f.getLevelId()).getName());
                }
                if (memberMap.get(memberId) != null) {
                    f.setMemberName(memberMap.get(memberId).getFullName());
                    f.setNum(memberMap.get(memberId).getName());
                    pId = memberMap.get(memberId).getMajorPositionId();
                }
                if (pId != null && positionMap.get(pId) != null) {
                	jId = positionMap.get(pId).getJobId();
                }
                if (jId != null && jobMap.get(jId) != null) {
                	f.setJobName(jobMap.get(jId).getName());
                }
                if (orgMap.get(orgId) != null) {
                    f.setOrganizationName(orgMap.get(orgId).getName());
                    if (orgMap.get(orgMap.get(orgId).getCompanyId()).getId() != null) {
                    	f.setUnit(orgMap.get(orgMap.get(orgId).getCompanyId()).getName());
					}
                }
                if (orgMap.get(aorgId) != null) {
                    f.setAscriptionOrganizationName(orgMap.get(aorgId).getName());
                }
                if (attributeIds != null) {
                	f.setAttribute(attributeMap.get(attributeIds));
				}
                f.setType(attributeTypeMap.get(attributeIds));
                if (recordMap.get(f.getId()) != null) {
                	f.setLatelyOrgRecordTime(recordMap.get(f.getId()));
				}
        });

//        lectureList.sort((lec1, lec2) -> lec2.getCreateTime().compareTo(lec1.getCreateTime()));

		return PagedResult.create(count, lectureList);

	}

	@Override
	public List<Lecturer> findMemberToOrgIds(Optional<Integer> type,Optional<String> attributeId,Optional<List<String>> organizationIds) {
		List<Condition> where = Stream.of(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()),
				attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()),
				organizationIds.map(LECTURER.ASCRIPTION_ORGANIZATION_ID::in).orElse(DSL.trueCondition())
				).collect(Collectors.toList());
		return lecturerDao.execute(dao -> {
				return dao.select(Fields.start()
							.add(LECTURER.ID)
							.add(LECTURER.NAME)
							.add(LECTURER.ASCRIPTION_ORGANIZATION_ID)
							.add(LECTURER.TYPE)
							.add(LECTURER.ATTRIBUTE_ID)
							.add(LECTURER.MOBILE)
							.add(LECTURER.BANK_IDENTITY)
						.end())
					.from(LECTURER).where(where)
					.fetch().into(Lecturer.class);
			});
	}

	@Override
	public Lecturer get(String id, String source) {
		// 命名字段别名
		Field<String> memberName = MEMBER.NAME.as("memberName");
		Field<String> memberPhoneNumber = MEMBER.PHONE_NUMBER.as("memberPhoneNumber");
		Field<String> memberEmail = MEMBER.EMAIL.as("memberEmail");
		Field<Long> memberEntryDate = MEMBER.ENTRY_DATE.as("memberEntryDate");
		Field<String> levelName = LEVEL.NAME.as("levelName");
		Field<String> levelId = LEVEL.ID.as("levelId");
//		Field<String> memberHeadPortrait = MEMBER.HEAD_PORTRAIT.as("memberHeadPortrait");
		Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
		Field<String> organizationCompanyId = ORGANIZATION.COMPANY_ID.as("organizationCompanyId");
		Lecturer l = lecturerDao
				.execute(e -> e
						.select(Fields.start().add(LECTURER)
								.add(memberName, levelId, levelName, organizationName,
										MEMBER.FULL_NAME,memberPhoneNumber,memberEmail,memberEntryDate,
										organizationCompanyId)
								.end())
						.from(LECTURER).leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID)).leftJoin(ORGANIZATION)
						.on(LECTURER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(LEVEL)
						.on(LECTURER.LEVEL_ID.eq(LEVEL.ID)).where(LECTURER.ID.eq(id)).fetchOne(r -> {
							Lecturer lecturer = r.into(Lecturer.class);
							lecturer.setLevelName(r.getValue(levelName));
							lecturer.setLevelId(r.getValue(levelId));
			                lecturer.setOrganizationName(r.getValue(lecturer.getType() == 1?LECTURER.ORGANIZATION_ID:organizationName));
//							lecturer.setMemberHeadPortrait(r.getValue(memberHeadPortrait));
							lecturer.setMemberName(r.getValue(MEMBER.FULL_NAME));
							if (SM4Utils.decryptDataCBC(r.getValue(memberEmail)) != null && !"".equals(SM4Utils.decryptDataCBC(r.getValue(memberEmail)))) {
								lecturer.setEmail(SM4Utils.decryptDataCBC(r.getValue(memberEmail)));
							}
							if (SM4Utils.decryptDataCBC(r.getValue(memberPhoneNumber)) != null && !"".equals(SM4Utils.decryptDataCBC(r.getValue(memberPhoneNumber)))) {
								//脱敏
								lecturer.setMobile(DesensitizationUtil.desensitizeMobile(Optional.of(SM4Utils.decryptDataCBC(r.getValue(memberPhoneNumber)))));
							}
							if (String.valueOf(r.getValue(memberEntryDate)) != null && !"".equals(r.getValue(memberEntryDate))) {
								lecturer.setWorkStartYear(String.valueOf(r.getValue(memberEntryDate)));
							}
							if (r.getValue(organizationCompanyId) != null && !"".equals(r.getValue(organizationCompanyId))) {
								Organization organization = organizationCommonDao.get(r.getValue(organizationCompanyId));
								lecturer.setUnit(organization.getName());
							}
							return lecturer;
						}));
		if (l != null && l.getMemberId() != null) {
			/*memberDao.execute(x -> x.select(MEMBER.ID,MEMBER.MAJOR_POSITION_ID).from(MEMBER)).where(MEMBER.ID.eq(l.getMemberId()))
			String pid = memberMap.get(l.getMemberId()).getMajorPositionId();

			Map<String, Position> positionMap = positionDao.execute(x -> x.select(POSITION.ID,POSITION.JOB_ID).from(POSITION)).where(POSITION.ID.eq(pid))
					.fetch(r ->r.into(Position.class)).stream()
					.collect(Collectors.toMap(Position::getId, c -> c));
			String jid = positionMap.get(pid).getJobId();

			Map<String, Job> jobMap = jobDao.execute(x -> x.select(JOB.ID, JOB.NAME).from(JOB)).where(JOB.ID.eq(jid))
					.fetch(r -> r.into(Job.class)).stream()
					.collect(Collectors.toMap(Job::getId, c -> c));*/

			Optional<Job> fetchOptional = jobDao.execute(
					x -> x.select(Fields.start().add(JOB.NAME).end()).from(JOB)
							.leftJoin(POSITION)
							.on(JOB.ID.eq(POSITION.JOB_ID))
							.leftJoin(MEMBER)
							.on(POSITION.ID.eq(MEMBER.MAJOR_POSITION_ID))
							.where(MEMBER.ID.eq(l.getMemberId())))
					.fetchOptional(b -> {
						return b.into(Job.class);
					});
			if (fetchOptional.isPresent()) {
				l.setJobName(fetchOptional.get().getName());
			}
		}



		List<Label> labelList = labelDao.execute(
				x -> x.select(Fields.start().add(LABEL).add(LECTURER_LABEL).end()).from(LABEL).leftJoin(LECTURER_LABEL)
						.on(LABEL.ID.eq(LECTURER_LABEL.LABEL_ID)).where(LECTURER_LABEL.LECTURER_ID.eq(id)))
				.fetch(b -> {
					return b.into(Label.class);
				});

		/**
		List<String> cIds = new ArrayList<>();
		List<String> pIds = new ArrayList<>();
		List<String> oIds = new ArrayList<>();
		List<ClassOfflineCourse> list = offlineCourseDao.execute(d -> {
			Table<Record1<String>> basic = (d.select(CLASS_OFFLINE_COURSE.ID)
					.from(CLASS_OFFLINE_COURSE)
					.leftJoin(COURSE_SALARY)
					.on(CLASS_OFFLINE_COURSE.ID.eq(COURSE_SALARY.COURSE_ID))
					.where(COURSE_SALARY.LECTURE_ID.eq(id)
							.and(CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(0)))
			).asTable("b");

			return d.select(Fields.start()
					.add(CLASS_OFFLINE_COURSE.CREATE_TIME,CLASS_OFFLINE_COURSE.TEACHER_TITLE,CLASS_OFFLINE_COURSE.TEACHER_ORGANIZATION,
							CLASS_OFFLINE_COURSE.NAME,CLASS_OFFLINE_COURSE.CLASS_ID,
							CLASS_OFFLINE_COURSE.COURSE_DATE, CLASS_OFFLINE_COURSE.START_TIME,
							CLASS_OFFLINE_COURSE.END_TIME, CLASS_OFFLINE_COURSE.COURSE_SATISFY,
							COURSE_SALARY.LECTURER_BANK_CARD, COURSE_SALARY.PAID_PAY, COURSE_SALARY.PAY, COURSE_SALARY.TAX)
					.end())
					.from(CLASS_OFFLINE_COURSE)
					.leftJoin(COURSE_SALARY)
					.on(CLASS_OFFLINE_COURSE.ID.eq(COURSE_SALARY.COURSE_ID))
					.innerJoin(basic).on(basic.field(CLASS_OFFLINE_COURSE.ID).eq(CLASS_OFFLINE_COURSE.ID))
					.orderBy(CLASS_OFFLINE_COURSE.CREATE_TIME.desc(), CLASS_OFFLINE_COURSE.COURSE_DATE.desc(), CLASS_OFFLINE_COURSE.END_TIME.desc())
					.fetch( b -> {
						ClassOfflineCourse classOfflineCourse = b.into(ClassOfflineCourse.class);
						Double value = b.getValue(CLASS_OFFLINE_COURSE.COURSE_SATISFY);
						if (value != null) {
							BigDecimal bd = new BigDecimal(value);
							//保留2位小数
							double result = bd.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
							classOfflineCourse.setCourseSatisfy(result);
						} else {
							classOfflineCourse.setCourseSatisfy(value);
						}
						classOfflineCourse.setCreateTime(b.getValue(CLASS_OFFLINE_COURSE.CREATE_TIME));
						classOfflineCourse.setLecturerBankCard(b.getValue(COURSE_SALARY.LECTURER_BANK_CARD));
						classOfflineCourse.setPaidPayD(b.getValue(COURSE_SALARY.PAID_PAY));
						classOfflineCourse.setPayD(b.getValue(COURSE_SALARY.PAY));
						classOfflineCourse.setTaxD(b.getValue(COURSE_SALARY.TAX));
						classOfflineCourse.setTeacherTitle(b.getValue(CLASS_OFFLINE_COURSE.TEACHER_TITLE));
						classOfflineCourse.setTeacherOrganization(b.getValue(CLASS_OFFLINE_COURSE.TEACHER_ORGANIZATION));
						String endTime = b.getValue(CLASS_OFFLINE_COURSE.END_TIME);
						String startTime = b.getValue(CLASS_OFFLINE_COURSE.START_TIME);
						if ((endTime != null && !endTime.equals("")) && startTime != null && !startTime.equals("")) {
							String endS[] = endTime.split(":");
							Double branch = 0.0;
							Integer endB = Integer.parseInt(endS[1]);
							String startS[] = startTime.split(":");
							Integer startB = Integer.parseInt(startS[1]);
							Integer hour = (Integer.parseInt(endS[0]) - Integer.parseInt(startS[0]));
							Integer numTime = 0;
							if (endB > startB) {
								numTime = endB - startB;
								branch = 0.5;
								if (numTime > 30) {
									branch = 1.0;
								}
							} else if (endB < startB) {
								numTime = startB - endB;
								if (numTime >= 30) {
									branch = -0.5;
								}
							}
							Double aa = branch + hour;
							classOfflineCourse.setTime(aa);
						}
						cIds.add(b.getValue(CLASS_OFFLINE_COURSE.CLASS_ID));
						return classOfflineCourse;
					});
		});


		Map<String, ClassInfo> classInfoMap = classInfoDao.execute(x -> x.select(CLASS_INFO.ID,CLASS_INFO.PROJECT_ID)
				.from(CLASS_INFO)).where(CLASS_INFO.ID.in(cIds))
				.fetch(r ->{
					ClassInfo cr =  r.into(ClassInfo.class);
					pIds.add(r.getValue(CLASS_INFO.PROJECT_ID));
					return cr;

				}).stream()
				.collect(Collectors.toMap(ClassInfo::getId, c -> c));

		Map<String, Project> projectMap = projectDao.execute(x -> x.select(PROJECT.ID,PROJECT.ORGANIZATION_ID,PROJECT.NAME,PROJECT.CODE,PROJECT.ADDRESS,CONFIGURATION_VALUE.NAME)
				.from(PROJECT).leftJoin(CONFIGURATION_VALUE).on(CONFIGURATION_VALUE.ID.eq(PROJECT.ADDRESS))).where(PROJECT.ID.in(pIds))
				.fetch(r ->{
					Project cr =  r.into(Project.class);
					oIds.add(r.getValue(PROJECT.ORGANIZATION_ID));
					cr.setAddressName(r.get(CONFIGURATION_VALUE.NAME));
					return cr;

				}).stream()
				.collect(Collectors.toMap(Project::getId, c -> c));

		Map<String, Organization> organizationMap = organizationCommonDao.execute(x -> x.select(ORGANIZATION.ID,ORGANIZATION.NAME)
				.from(ORGANIZATION)).where(ORGANIZATION.ID.in(oIds))
				.fetch(r ->r.into(Organization.class)).stream()
				.collect(Collectors.toMap(Organization::getId, c -> c));

		list.stream().map(r -> {
			String cId = r.getClassId();
			String pId = null;
			String oId = null;
			if (classInfoMap.get(cId) != null) {
				pId = classInfoMap.get(cId).getProjectId();
			}
			if (pId != null && projectMap.get(pId) != null) {
				r.setClassName(projectMap.get(pId).getName());
				r.setProjectCode(projectMap.get(pId).getCode());
				r.setAddress(projectMap.get(pId).getAddressName());
				oId = projectMap.get(pId).getOrganizationId();
			}

			if (oId != null && organizationMap.get(oId) != null) {
				r.setOrganizationName(organizationMap.get(oId).getName());
			}
			return r;
		}).collect(Collectors.toList());
		list.sort((lec1, lec2) -> lec2.getCreateTime().compareTo(lec1.getCreateTime()));
		*/
		if (l.getSequenceId() != null && !"".equals(l.getSequenceId())) {
			LecturerCourseConfig lecturerCourseConfig = lecturerCourseConfigService.findInfo(Optional.of(l.getSequenceId()));
			if (lecturerCourseConfig != null) {
				l.setSequenceName(lecturerCourseConfig.getName());
				l.setParentId(lecturerCourseConfig.getParentId());
			}
		}
		if (l != null && l.getAscriptionOrganizationId() != null) {
			Organization organization = organizationCommonDao.get(l.getAscriptionOrganizationId());
			l.setAscriptionOrganizationName(organization != null ? organization.getName() : null);
		}
		if (l != null) {
//			l.setOfflineCourseList(list);
			l.setLabelList(labelList);
			LecturerAttribute lecturerAttribute = lecturerAttributeService.findInfo(Optional.of(l.getAttributeId()));
			l.setType(lecturerAttribute.getTypeId());
			Member meberName = null;
			if (l.getCreateMemberId() != null) {
				meberName = memberDao.get(l.getCreateMemberId());
				l.setCreateMemberName(meberName.getFullName());
			}
			if (l.getModifyMemberId() != null) {
				meberName = memberDao.get(l.getModifyMemberId());
				l.setModifyMemberName(meberName.getFullName());
			}
			String bankIdentity = l.getBankIdentity();
			if (bankIdentity != null && !"edit".equals(source)) {
				if (bankIdentity.length() <= 4) {
					l.setBankIdentity(bankIdentity.replaceAll(".", "*"));
				} else {
					l.setBankIdentity(bankIdentity.substring(0, 6) + "********" + bankIdentity.substring(14));
				}
			}else {
				l.setBankIdentity(com.zxy.product.train.util.DesensitizationUtil.desensitizeEmployeeId(l.getBankIdentity()));
			}
			l.setBankCard(DesensitizationUtil.desensitizeIdCart(Optional.ofNullable(l.getBankCard())));
		}
		return l;
	}

	@Override
	public Lecturer insert(Optional<Integer> sourceType, Optional<String> memberId, Optional<String> headPortrait,
						   Optional<String> headPortraitPath,String name, Optional<String> levelId, Optional<String> labelIds, String mobile, Integer sex,
						   Optional<String> organizationId, Optional<String> jobName, Optional<String> email, Integer type,
						   Optional<String> description, Optional<String> adeptCourse, Optional<String> attachId, Optional<String> attachName, Optional<String> bankUser, Optional<String> bank,
						   Optional<String> bankIdentity, Optional<String> bankCard, Optional<String> remark, Optional<String> unit,
						   Optional<Integer> cooperationType, Optional<String> institutions, Optional<String> linkman,
						   Optional<String> linkmanNo) {
		Lecturer lecturer = new Lecturer();
		lecturer.forInsert();
		memberId.ifPresent(lecturer::setMemberId);
		headPortrait.ifPresent(lecturer::setHeadPortrait);
		headPortraitPath.ifPresent(lecturer::setCoverPath);
		levelId.ifPresent(lecturer::setLevelId);
		lecturer.setName(name);
		lecturer.setMobile(mobile);
		lecturer.setSex(sex);
		lecturer.setStatus(0);
		jobName.ifPresent(lecturer::setJobName);
		email.ifPresent(lecturer::setEmail);
		lecturer.setType(type);
		if (type == 1) {
			lecturer.setOrganizationId("1");
		} else {
			organizationId.ifPresent(lecturer::setOrganizationId);
		}
		description.ifPresent(lecturer::setDescription);
		adeptCourse.ifPresent(lecturer::setAdeptCourse);
		attachId.ifPresent(lecturer::setAttachmentId);
		attachName.ifPresent(lecturer::setAttachmentName);
		bankUser.ifPresent(lecturer::setBankUser);
		bank.ifPresent(lecturer::setBank);
		bankIdentity.ifPresent(lecturer::setBankIdentity);
		bankCard.ifPresent(lecturer::setBankCard);
		remark.ifPresent(lecturer::setRemark);
		unit.ifPresent(lecturer::setUnit);
		cooperationType.ifPresent(lecturer::setCooperationType);
		institutions.ifPresent(lecturer::setInstitutions);
		linkman.ifPresent(lecturer::setLinkman);
		linkmanNo.ifPresent(lecturer::setLinkmanNo);
		if (sourceType.isPresent()) {
			lecturer.setSourceType(sourceType.get());
		} else {
			lecturer.setSourceType(Lecturer.SOURCES);
		}
		if (labelIds.isPresent()) {
			List<Label> execute = labelDao.execute(e -> e.select(Fields.start().add(LABEL).end()).from(LABEL)
					.where(LABEL.ID.in(labelIds.get().split(","))).fetch().into(Label.class));
			StringBuffer names = new StringBuffer();
			for (int i = 0; i < execute.size(); i++) {
				names.append(execute.get(i).getName()).append(",");
			}
			if (names.length() > 1) {
				names.deleteCharAt(names.length() - 1);
				lecturer.setLabelNames(names.toString());
			}
		}
		lecturerDao.insert(lecturer);
		if(type==0 && memberId.isPresent()){
			Organization organization = organizationCommonDao.get(organizationId.get());
			String orgName = organization!=null?organization.getName():null;
			sender.send(MessageTypeContent.HR_MEMBER_IDENTY_LOG_INSERT,MessageHeaderContent.MEMBER_ID,memberId.get(),
					MessageHeaderContent.IDENTY_ID,"3",
					MessageHeaderContent.EMPLOYER_NAME,orgName);
		}
		labelIds.ifPresent(ll -> Arrays.stream(ll.split(",")).forEach(r -> {
			LecturerLabel lecturerLabel = new LecturerLabel();
			lecturerLabel.forInsert();
			lecturerLabel.setLabelId(r);
			lecturerLabel.setLecturerId(lecturer.getId());
			lecturerLabelDao.insert(lecturerLabel);
		}));
		return lecturer;
	}

	@Override
	public Lecturer insertNew(Optional<Integer> sourceType, Optional<String> memberId, Optional<String> headPortrait,
			Optional<String> headPortraitPath,String name, Optional<String> levelId, Optional<String> labelIds, String mobile, Integer sex,
			Optional<String> organizationId, Optional<String> jobName, Optional<String> email, Integer type,
			Optional<String> description, Optional<String> adeptCourse, Optional<String> attachId, Optional<String> attachName,Optional<String> attachPath,
			Optional<String> bankUser, Optional<String> bank,
			Optional<String> bankIdentity, Optional<String> bankCard, Optional<String> remark, Optional<String> unit,
			Optional<Integer> cooperationType, Optional<String> institutions, Optional<String> linkman,
			Optional<String> linkmanNo,Optional<String> attributeId,Optional<String> workStartYear,
            Optional<String> certificationYear,Optional<String> sequenceId,Optional<String> lectureExperience,
            Optional<String> ascriptionOrganizationId,Optional<String> adeptCourseList,Optional<String> unitrecordList,
            Optional<String> otherrecordList,Optional<String> organizationName,Integer releaseStatus,String createMemberId) {
		Lecturer lecturer = new Lecturer();
		lecturer.forInsert();
		memberId.ifPresent(lecturer::setMemberId);
		headPortrait.ifPresent(lecturer::setHeadPortrait);
		headPortraitPath.ifPresent(lecturer::setCoverPath);
		levelId.ifPresent(lecturer::setLevelId);
		lecturer.setName(name);
		lecturer.setMobile(mobile);
		lecturer.setSex(sex);
		lecturer.setStatus(Lecturer.STATUS_YES);
		jobName.ifPresent(lecturer::setJobName);
		email.ifPresent(lecturer::setEmail);
		lecturer.setType(type);
		lecturer.setReleaseStatus(releaseStatus);
		if (type == 1) {
			organizationName.ifPresent(lecturer::setOrganizationId);
		} else {
			organizationId.ifPresent(lecturer::setOrganizationId);
		}
		description.ifPresent(lecturer::setDescription);
		adeptCourse.ifPresent(lecturer::setAdeptCourse);
		attachId.ifPresent(lecturer::setAttachmentId);
		attachName.ifPresent(lecturer::setAttachmentName);
		attachPath.ifPresent(lecturer::setAttachmentPath);
		bankUser.ifPresent(lecturer::setBankUser);
		bank.ifPresent(lecturer::setBank);
		bankIdentity.ifPresent(lecturer::setBankIdentity);
		bankCard.ifPresent(lecturer::setBankCard);
		remark.ifPresent(lecturer::setRemark);
		unit.ifPresent(lecturer::setUnit);
		cooperationType.ifPresent(lecturer::setCooperationType);
		institutions.ifPresent(lecturer::setInstitutions);
		linkman.ifPresent(lecturer::setLinkman);
		linkmanNo.ifPresent(lecturer::setLinkmanNo);
		if (sourceType.isPresent()) {
			lecturer.setSourceType(sourceType.get());
		} else {
			lecturer.setSourceType(Lecturer.SOURCES);
		}
		if (labelIds.isPresent()) {
			List<Label> execute = labelDao.execute(e -> e.select(Fields.start().add(LABEL).end()).from(LABEL)
					.where(LABEL.ID.in(labelIds.get().split(","))).fetch().into(Label.class));
			StringBuffer names = new StringBuffer();
			for (int i = 0; i < execute.size(); i++) {
				names.append(execute.get(i).getName()).append(",");
			}
			if (names.length() > 1) {
				names.deleteCharAt(names.length() - 1);
				lecturer.setLabelNames(names.toString());
			}
		}
		lecturer.setAttributeId(attributeId.get());
		workStartYear.ifPresent(lecturer::setWorkStartYear);
		certificationYear.ifPresent(lecturer::setCertificationYear);
		lectureExperience.ifPresent(lecturer::setLectureExperience);
		lecturer.setSequenceId(sequenceId.get());
		lecturer.setAscriptionOrganizationId(ascriptionOrganizationId.get());
		lecturer.setCreateMemberId(createMemberId);
		lecturer.setModifyTime(System.currentTimeMillis());
		lecturer.setModifyMemberId(createMemberId);
		lecturerDao.insert(lecturer);
		if (adeptCourseList.isPresent()) {
			List<LecturerAdeptCourse> list=com.alibaba.fastjson.JSON.parseArray(adeptCourseList.get() ,LecturerAdeptCourse.class);
			for (LecturerAdeptCourse lecturerAdeptCourse : list) {
				if(lecturerAdeptCourse.getCourseId() == lecturerAdeptCourse.getCId()) continue;
				if(lecturerAdeptCourse.getCId() != null){//防止选择的课程只有cId，没有courseId
					lecturerAdeptCourse.setCourseId(lecturerAdeptCourse.getCId());
				}
				lecturerAdeptCourse.forInsert();
				lecturerAdeptCourse.setLecturerId(lecturer.getId());
				lecturerAdeptCourse.setCreateMemberId(createMemberId);
				lecturerAdeptCourse.setOrganizationId("1");
				lecturerAdeptCourseDao.insert(lecturerAdeptCourse);
			}
		}
 		if (unitrecordList.isPresent()) {
 			List<OrganizationTeaching> list=com.alibaba.fastjson.JSON.parseArray(unitrecordList.get() ,OrganizationTeaching.class);
 			for (OrganizationTeaching organizationTeaching : list) {
 				organizationTeachingInsert(organizationTeaching, lecturer.getId(),createMemberId);
 			}
 		}
 		if (otherrecordList.isPresent()) {
 			List<OtherTeaching> list=com.alibaba.fastjson.JSON.parseArray(otherrecordList.get() ,OtherTeaching.class);
 			for (OtherTeaching otherTeaching : list) {
 				otherTeachingInsert(otherTeaching, lecturer, ascriptionOrganizationId);
 			}
 		}
		if(type==0 && memberId.isPresent()){
			Organization organization = organizationCommonDao.get(organizationId.get());
			String orgName = organization!=null?organization.getName():null;
			sender.send(MessageTypeContent.HR_MEMBER_IDENTY_LOG_INSERT,MessageHeaderContent.MEMBER_ID,memberId.get(),
					MessageHeaderContent.IDENTY_ID,"3",
					MessageHeaderContent.EMPLOYER_NAME,orgName);
		}
		labelIds.ifPresent(ll -> Arrays.stream(ll.split(",")).forEach(r -> {
			LecturerLabel lecturerLabel = new LecturerLabel();
			lecturerLabel.forInsert();
			lecturerLabel.setLabelId(r);
			lecturerLabel.setLecturerId(lecturer.getId());
			lecturerLabelDao.insert(lecturerLabel);
		}));
		sender.send(MessageTypeContent.LECTURER_LECTURES_OF_RECORDS,
				MessageHeaderContent.LECTURER_ID,lecturer.getId());
		return lecturer;
	}

	@Override
	public Lecturer update(String id, Optional<String> memberId, Optional<String> headPortrait,Optional<String> headPortraitPath, Optional<String> name,
			Optional<String> levelId, Optional<String> labelIds, Optional<String> mobile, Integer sex,
			Integer type,
			Optional<String> organizationId, Optional<String> jobName, Optional<String> email,
			Optional<String> description, Optional<String> adeptCourse, Optional<String> attachmentId,
			Optional<String> attachmentName, Optional<String> attachmentPath, Optional<String> bankUser, Optional<String> bank,
			Optional<String> bankIdentity, Optional<String> bankCard, Optional<String> remark, Optional<String> unit,
			Optional<Integer> cooperationType, Optional<String> institutions, Optional<String> linkman,
			Optional<String> linkmanNo,Optional<String> attributeId,Optional<String> workStartYear,
            Optional<String> certificationYear,Optional<String> sequenceId,Optional<String> lectureExperience,
            Optional<String> ascriptionOrganizationId,Optional<String> collegerecordList,Optional<String> unitrecordList,
            Optional<String> otherrecordList,Optional<String> organizationName,String createMemberId) {
		Lecturer lecturer = lecturerDao.get(id);
		String  mId = lecturer.getMemberId();
		lecturer.setMemberId(memberId.orElse(null));
		lecturer.setHeadPortrait(headPortrait.orElse(null));
		lecturer.setCoverPath(headPortraitPath.orElse(null));
		lecturer.setLevelId(levelId.orElse(null));
		lecturer.setName(name.orElse(null));
		lecturer.setMobile(mobile.orElse(null));
		lecturer.setSex(sex);
//		lecturer.setType(type);
//		if (type == 1) {
//			lecturer.setOrganizationId("1");
//		} else {
//			lecturer.setOrganizationId(organizationId.orElse(null));
//		}
		if (type == 1) {
			Lecturer lecturer2 = getLecturerDetailByIDCard(bankIdentity.get(), 1);
			if (lecturer2 != null && !lecturer.getId().equals(lecturer2.getId()))
				throw new UnprocessableException(ErrorCode.MemberCardFormatError);
			organizationName.ifPresent(lecturer::setOrganizationId);
		} else {
			organizationId.ifPresent(lecturer::setOrganizationId);
		}
		lecturer.setJobName(jobName.orElse(null));
		lecturer.setEmail(email.orElse(null));
		lecturer.setDescription(description.orElse(null));
		lecturer.setAdeptCourse(adeptCourse.orElse(null));
		lecturer.setAttachmentId(attachmentId.orElse(null));
		lecturer.setAttachmentName(attachmentName.orElse(null));
		lecturer.setAttachmentPath(attachmentPath.orElse(null));
		lecturer.setBankUser(bankUser.orElse(null));
		lecturer.setBank(bank.orElse(null));
		lecturer.setBankIdentity(bankIdentity.orElse(null));
		lecturer.setBankCard(bankCard.orElse(null));
		lecturer.setRemark(remark.orElse(null));
		lecturer.setUnit(unit.orElse(null));
		lecturer.setCooperationType(cooperationType.orElse(null));
		lecturer.setInstitutions(institutions.orElse(null));
		lecturer.setLinkman(linkman.orElse(null));
		lecturer.setLinkmanNo(linkmanNo.orElse(null));
		if (labelIds.isPresent()) {
			List<Label> execute = labelDao.execute(e -> e.selectDistinct(Fields.start().add(LABEL).end()).from(LABEL)
					.where(LABEL.ID.in(labelIds.get().split(","))).fetch().into(Label.class));
			StringBuffer names = new StringBuffer();
			for (int i = 0; i < execute.size(); i++) {
				names.append(execute.get(i).getName()).append(",");
			}
			if (names.length() > 1) {
				names.deleteCharAt(names.length() - 1);
				lecturer.setLabelNames(names.toString());
			}
			lecturerLabelDao.delete(LECTURER_LABEL.LECTURER_ID.eq(id));
			if (mId != null && mId != " ") {
				sender.send(MessageTypeContent.HR_MEMBER_IDENTY_LOG_UPDATE,MessageHeaderContent.MEMBER_ID,mId,
						MessageHeaderContent.IDENTY_ID,"3");
			}
			labelIds.ifPresent(ll -> Arrays.stream(ll.split(",")).forEach(r -> {
				LecturerLabel lecturerLabel = new LecturerLabel();
				lecturerLabel.forInsert();
				lecturerLabel.setLabelId(r);
				lecturerLabel.setLecturerId(id);
				lecturerLabelDao.insert(lecturerLabel);
			}));
		} else {
			lecturerLabelDao.delete(LECTURER_LABEL.LECTURER_ID.eq(id));
		}
		lecturer.setAttributeId(attributeId.get());
		lecturer.setWorkStartYear(workStartYear.orElse(null));
		lecturer.setCertificationYear(certificationYear.orElse(null));
		lecturer.setLectureExperience(lectureExperience.orElse(null));
		lecturer.setSequenceId(sequenceId.get());
		lecturer.setAscriptionOrganizationId(ascriptionOrganizationId.get());
		lecturer.setModifyMemberId(createMemberId);
		lecturer.setModifyTime(System.currentTimeMillis());
		lecturer.setModifyDate(null);
		lecturerDao.update(lecturer);
		if (collegerecordList.isPresent()) {
			List<CollegeTeaching> list=com.alibaba.fastjson.JSON.parseArray(collegerecordList.get() ,CollegeTeaching.class);
			for (CollegeTeaching collegeTeaching : list) {
				collegeTeachingUpdate(collegeTeaching,null);
			}
		}
		if (unitrecordList.isPresent()) {
			List<OrganizationTeaching> list=com.alibaba.fastjson.JSON.parseArray(unitrecordList.get() ,OrganizationTeaching.class);
			List<String> notDelList = new ArrayList<String>();
			for (OrganizationTeaching organizationTeaching : list) {
				if (organizationTeaching.getId() == null || "".equals(organizationTeaching.getId()) || organizationTeaching.getId().indexOf("record") == 0) {
					organizationTeaching = organizationTeachingInsert(organizationTeaching, lecturer.getId(),createMemberId);
					notDelList.add(organizationTeaching.getId());
				}else{
					notDelList.add(organizationTeaching.getId());
					organizationTeachingUpdate(organizationTeaching);
				}
			}
			if (notDelList.size() > 0) {
				organizationTeachingDao.delete(ORGANIZATION_TEACHING.LECTURER_ID.eq(id)
												.and(ORGANIZATION_TEACHING.ID.notIn(notDelList)));
			} else {
				organizationTeachingDao.delete(ORGANIZATION_TEACHING.LECTURER_ID.eq(id));
			}
		}
		if (otherrecordList.isPresent()) {
			List<OtherTeaching> list=com.alibaba.fastjson.JSON.parseArray(otherrecordList.get() ,OtherTeaching.class);
			List<String> notDelList = new ArrayList<String>();
			for (OtherTeaching otherTeaching : list) {
				if (otherTeaching.getId() == null || "".equals(otherTeaching.getId()) || otherTeaching.getId().indexOf("record") == 0) {
					otherTeaching = otherTeachingInsert(otherTeaching, lecturer, ascriptionOrganizationId);
					notDelList.add(otherTeaching.getId());
				}else{
					notDelList.add(otherTeaching.getId());
					otherTeachingUpdate(otherTeaching, lecturer, ascriptionOrganizationId);
				}
			}
			if (notDelList.size() > 0) {
				otherTeachingDao.delete(OTHER_TEACHING.LECTURER_ID.eq(id)
						.and(OTHER_TEACHING.ID.notIn(notDelList)));
			} else {
				otherTeachingDao.delete(OTHER_TEACHING.LECTURER_ID.eq(id));
			}
		}
		if (memberId.isPresent() && organizationId.isPresent() ) {
			Organization organization = organizationCommonDao.get(organizationId.get());
			String orgName = organization!=null?organization.getName():null;
			sender.send(MessageTypeContent.HR_MEMBER_IDENTY_LOG_INSERT,MessageHeaderContent.MEMBER_ID,memberId.get(),
					MessageHeaderContent.IDENTY_ID,"3",
					MessageHeaderContent.EMPLOYER_NAME,orgName);
		}
		sender.send(MessageTypeContent.LECTURER_LECTURES_OF_RECORDS,
				MessageHeaderContent.LECTURER_ID,lecturer.getId());
		return lecturer;
	}

	private OfflineCourseLibrary insertOffline(
			String name,
			String courseAttributes,//课程属性
			String institutionId,//课程所属机构
			String coursePlan,//课程所属方案
			String sequence,//课程分类序列
			String organizationId,//归属部门
			String isShare,//是否共享
			Integer isUse,//是否启用
			Optional<String> obj,//对象
			Optional<String> keyword,//关键词
			Optional<String> summary,
			Optional<String> remark,
			Optional<String> target,
			Optional<String> outline,
			Optional<String> attachmentId,
			Optional<String> attachmentName,
			Optional<String> referenceTime,
			List<LecturerAdeptCourse> list,
			String createMember){
		return offlineCourseLibraryService.insert(
				name,
                courseAttributes,
                institutionId,
                coursePlan,
                sequence,
                organizationId,
                isShare,
                isUse,
                obj,
                keyword,
                summary,
                remark,
                target,
                outline,
                attachmentId,
                attachmentName,
                referenceTime,
                list,
                createMember
        		);
	}

	/**
	 * 添加各单位授课记录
	 * @param lecturerId
	 * @return
	 */
	private OrganizationTeaching organizationTeachingInsert(OrganizationTeaching ot,String lecturerId, String createMemberId){
		if ("".equals(ot.getCourseId()) || ot.getCourseId() == null) {
				OfflineCourseLibrary offline = insertOffline(
						ot.getCourseName(),
						CourseAttribute.COLLECT_MINING_ID,
						"",			//课程所属机构
						"",			//课程所属方案
						LecturerCourseConfig.SEQUENCE_OTHER,
						ot.getOrganizationId(),
						F2fCourseLibrary.IS_SHARE_TRUE,
						F2fCourseLibrary.IS_USE_TRUE,
						Optional.ofNullable(ot.getObject()),
						Optional.empty(),		//关键词
						Optional.empty(),	//简介
						Optional.empty(), //备注
						Optional.empty(),	//课程目标
						Optional.empty(),	//课程大纲
						Optional.empty(),	//附件ID
						Optional.empty(),	//附件名称
						Optional.ofNullable(ot.getCourseDuration() + ""),
						new ArrayList<LecturerAdeptCourse>(),
						createMemberId);
				ot.setCourseId(offline.getId());
		}
		return organizationTeachingService.insert(
				Optional.ofNullable(ot.getCourseId()),
				Optional.ofNullable(lecturerId),
//				Optional.ofNullable(ot.getReferenceDollars() == null ? null : String.valueOf(ot.getReferenceDollars())),//参考课酬
				Optional.ofNullable(ot.getCourseDuration() == null ? null : String.valueOf(ot.getCourseDuration())),//课酬时长
				Optional.ofNullable(ot.getOrganizationId()),//主办部门
				Optional.ofNullable(ot.getClassName()),//培训班名称
				Optional.ofNullable(ot.getClassMember()),//培训班联系人
				Optional.ofNullable(ot.getObject()),//授课对象
				Optional.ofNullable(ot.getMemberCount()),//授课人数
//				Optional.ofNullable(ot.getTax() == null ? null : String.valueOf(ot.getTax())),//税金
//				Optional.ofNullable(ot.getPay() == null ? null : String.valueOf(ot.getPay())),//实付
//				Optional.ofNullable(ot.getRemuneration() == null ? null : String.valueOf(ot.getRemuneration())),//酬金
				Optional.ofNullable(ot.getSatisfiedDegree() == null ? null : String.valueOf(ot.getSatisfiedDegree())),//平均满意度(十分制，小数点后保留一位)
				Optional.ofNullable(ot.getSatisfiedEvaluate()),//满意度评价
				Optional.ofNullable(ot.getStartDate()),//授课起止日期
				Optional.ofNullable(ot.getEndDate()),
				OrganizationTeaching.APPROVAL_STATUS_PASS,
				OrganizationTeaching.SOURCE_MANAGE,
				ot.getCourseAttachList());//授课结束日期
	}
	/**
	 * 修改学院授课记录
	 */
	private void collegeTeachingUpdate(CollegeTeaching collegeTeaching,List<CourseAttach> courseAttachmentList){
		collegeTeachingService.update(
				collegeTeaching.getId(),
				Optional.ofNullable(collegeTeaching.getCourseDuration() == null ? "" : String.valueOf(collegeTeaching.getCourseDuration())),
				Optional.ofNullable(collegeTeaching.getObj()),
				Optional.ofNullable(collegeTeaching.getObjNum() == null ? "" : String.valueOf(collegeTeaching.getObjNum())),
//				Optional.ofNullable(collegeTeaching.getPay() == null ? "" : String.valueOf(collegeTeaching.getPay())),
//				Optional.ofNullable(collegeTeaching.getTax() == null ? "" : String.valueOf(collegeTeaching.getTax())),
//				Optional.ofNullable(collegeTeaching.getCourseReward() == null ? "" : String.valueOf(collegeTeaching.getCourseReward())),
				Optional.ofNullable(collegeTeaching.getSatisfiedDegree() == null ? "" : String.valueOf(collegeTeaching.getSatisfiedDegree())),
				collegeTeaching.getCourseAttachList());
	}
	/**
	 * 修改各单位授课记录
	 * @param organizationTeaching
	 */
	private void organizationTeachingUpdate(OrganizationTeaching organizationTeaching){
		organizationTeachingService.update(
				organizationTeaching.getId(),
				Optional.ofNullable(String.valueOf(organizationTeaching.getOrganizationId())),
				Optional.ofNullable(String.valueOf(organizationTeaching.getClassName())),
				Optional.ofNullable(String.valueOf(organizationTeaching.getClassMember())),
				Optional.ofNullable(String.valueOf(organizationTeaching.getCourseDuration())),
				Optional.ofNullable(organizationTeaching.getStartDate()),
				Optional.ofNullable(organizationTeaching.getObject()),
				Optional.ofNullable(String.valueOf(organizationTeaching.getMemberCount() == null ? 0 : organizationTeaching.getMemberCount())),
//				Optional.ofNullable(String.valueOf(organizationTeaching.getPay())),//实付
//				Optional.ofNullable(String.valueOf(organizationTeaching.getTax())),//税金
//				Optional.ofNullable(String.valueOf(organizationTeaching.getRemuneration())),//酬金
				Optional.ofNullable(String.valueOf(organizationTeaching.getSatisfiedDegree())),//平均满意度(十分制，小数点后保留一位)
				Optional.ofNullable(organizationTeaching.getSatisfiedEvaluate()),
				-1,
				organizationTeaching.getCourseAttachList());
	}
	/**
	 * 批量添加各单位教学教研记录
	 * @param otherTeaching
	 * @param lecturer
	 * @param ascriptionOrganizationId
	 * @return
	 */
	private OtherTeaching otherTeachingInsert(OtherTeaching otherTeaching,Lecturer lecturer,Optional<String> ascriptionOrganizationId){
		return otherTeachingService.insert(
				otherTeaching.getCourseName(),
				lecturer.getId(),
				lecturer.getName(),
				otherTeaching.getTeachingType(),
				otherTeaching.getActivityLevel(),
				otherTeaching.getStartTime(),
				otherTeaching.getEndTime(),
				otherTeaching.getStatus(),
				Optional.ofNullable(otherTeaching.getSatisfiedDegree()),
				Optional.ofNullable(otherTeaching.getOtherInstructions()),
				Optional.ofNullable(otherTeaching.getSatisfiedEvaluate()),
				ascriptionOrganizationId,
				OtherTeaching.APPROVAL_STATUS_PASS,
				OtherTeaching.SOURCE_MANAGE,
				otherTeaching.getCourseType(),
				otherTeaching.getReferenceTime(),
				otherTeaching.getObjectOriented(),
				otherTeaching.getDescription(),
				otherTeaching.getCreateMember());
	}
	private void otherTeachingUpdate(OtherTeaching otherTeaching,Lecturer lecturer,Optional<String> ascriptionOrganizationId){
		otherTeachingService.update(
				otherTeaching.getId(),
				otherTeaching.getCourseName(),
				lecturer.getId(),
				lecturer.getName(),
				otherTeaching.getTeachingType(),
				otherTeaching.getActivityLevel(),
				otherTeaching.getStartTime(),
				otherTeaching.getEndTime(),
				otherTeaching.getStatus(),
				Optional.ofNullable(otherTeaching.getSatisfiedDegree()),
				Optional.ofNullable(otherTeaching.getOtherInstructions()),
				Optional.ofNullable(otherTeaching.getSatisfiedEvaluate()),
				ascriptionOrganizationId.get(),
				"",
				-1,
				"",
				otherTeaching.getCourseType(),
				otherTeaching.getReferenceTime(),
				otherTeaching.getObjectOriented(),
				otherTeaching.getDescription(),
				"");
	}


	@Override
	public List<Lecturer> getLecturerInsideList(List<String> phones) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.MOBILE.in(phones)).and(LECTURER.TYPE.eq(0)))
				.fetch(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public List<Lecturer> getLecturerExternal(List<String> idCard) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.BANK_IDENTITY.in(idCard)).and(LECTURER.TYPE.eq(1)))
				.fetch(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public Lecturer findLecturerByPhone(String phone) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.MOBILE.eq(phone)).and(LECTURER.TYPE.eq(Lecturer.TYPE_INSIDE)))
				.fetchOne(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public List<Lecturer> findLecturerBPhone(String phone) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.MOBILE.eq(phone)))
				.fetch(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public List<Lecturer> findLecturerByPhoneList(List<String> phone) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.MOBILE.in(phone)))
				.fetch(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public List<Lecturer> findLecturerByPhoneWai(String phone) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.MOBILE.eq(phone)).and(LECTURER.TYPE.eq(Lecturer.TYPE_EXTERNAL)))
				.fetch(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public int checkCode(String mobile, Optional<String> id) {
		// TODO Auto-generated method stub
		return lecturerDao.execute(d -> {
			return d.selectCount().from(LECTURER).where(LECTURER.MOBILE.eq(mobile))
					.and(id.map(LECTURER.ID::ne).orElse(DSL.trueCondition())).fetchOne(0, Integer.class);
		});
	}

	@Override
	public int checkMmeber(String memberId, Optional<String> id) {
		// TODO Auto-generated method stub
		return lecturerDao.execute(d -> {
			return d.selectCount().from(LECTURER).where(LECTURER.MEMBER_ID.eq(memberId))
					.and(id.map(LECTURER.ID::ne).orElse(DSL.trueCondition())).fetchOne(0, Integer.class);
		});
	}

	@Override
	public int checkCodeAndIDCard(String memberId, String idCard) {
		return lecturerDao.execute(d -> {
			return d.selectCount().from(LECTURER).where(LECTURER.MEMBER_ID.eq(memberId))
					.and(LECTURER.BANK_IDENTITY.eq(idCard)).fetchOne(0, Integer.class);
		});
	}

	@Override
	public List<Lecturer> findAll() {
		// TODO Auto-generated method stub
		return lecturerDao.execute(e -> {
			List<Lecturer> all = e.selectFrom(LECTURER).where(LECTURER.STATUS.eq(0)).orderBy(LECTURER.CREATE_TIME.desc()).fetch().into(Lecturer.class);
			return all;
		});
	}

	@Override
	public void save(List<Lecturer> lecturers) {
		for (Lecturer lecturer : lecturers) {
			if (lecturer.getLabelIds() != null) {
				List<LecturerLabel> entities = new ArrayList<LecturerLabel>();
				for (String labelId : lecturer.getLabelIds().split(",")) {
					LecturerLabel lecturerLabel = new LecturerLabel();
					lecturerLabel.forInsert();
					lecturerLabel.setLabelId(labelId);
					lecturerLabel.setLecturerId(lecturer.getId());
					entities.add(lecturerLabel);
				}
				lecturerLabelDao.insert(entities);
			}
		}
		lecturerDao.insert(lecturers);
	}

	@Override
	public void saveLecturerLabel(List<LecturerLabel> lecturerLabels) {
		// TODO Auto-generated method stub
		lecturerLabelDao.insert(lecturerLabels);
	}

	@Override
	public Integer findLecturer(String memberId) {
		// TODO Auto-generated method stub
		Optional<Lecturer> fetchOne = lecturerDao.fetchOne(LECTURER.MEMBER_ID.eq(memberId));
		if (fetchOne.isPresent()) {
			return 1;
		} else {
			return 0;
		}
	}

	@Override
	public Lecturer findFront(String memberId, Optional<Double> stratCourseDuration,
							  Optional<Double> endCourseDuration, Optional<String> className, Optional<Long> starttime,
							  Optional<Long> endtime, Optional<Double> startSatisfiedDegree, Optional<Double> endSatisfiedDegree) {
		// TODO Auto-generated method stub
		Field<String> memberName = MEMBER.NAME.as("memberName");
		Field<String> levelName = LEVEL.NAME.as("levelName");
		Field<String> levelId = LEVEL.ID.as("levelId");
		Field<String> memberHeadPortrait = MEMBER.HEAD_PORTRAIT.as("memberHeadPortrait");
		Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
		Lecturer l = lecturerDao
				.execute(e -> e
						.select(Fields.start().add(LECTURER)
								.add(memberName, memberHeadPortrait, levelId, levelName, organizationName,
										MEMBER.FULL_NAME)
								.end())
						.from(LECTURER).leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID)).leftJoin(ORGANIZATION)
						.on(LECTURER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(LEVEL)
						.on(LECTURER.LEVEL_ID.eq(LEVEL.ID)).where(LECTURER.MEMBER_ID.eq(memberId)).fetchOne(r -> {
							Lecturer lecturer = r.into(Lecturer.class);
							lecturer.setLevelName(r.getValue(levelName));
							lecturer.setLevelId(r.getValue(levelId));
							lecturer.setOrganizationName(r.getValue(organizationName));
							lecturer.setMemberHeadPortrait(r.getValue(memberHeadPortrait));
							lecturer.setMemberName(r.getValue(MEMBER.FULL_NAME));
							return lecturer;
						}));
		if (l != null) {
			List<Label> labelList = labelDao.execute(
					x -> x.select(Fields.start().add(LABEL).add(LECTURER_LABEL).end()).from(LABEL).leftJoin(LECTURER_LABEL)
							.on(LABEL.ID.eq(LECTURER_LABEL.LABEL_ID)).where(LECTURER_LABEL.LECTURER_ID.eq(l.getId())))
					.fetch(b -> {
						return b.into(Label.class);
					});


			List<String> cIds = new ArrayList<>();
			List<String> pIds = new ArrayList<>();

			List<ClassOfflineCourse> offlineCourseList = offlineCourseDao.execute(d -> {
				Table<Record1<String>> basic = (d.select(CLASS_OFFLINE_COURSE.ID)
						.from(CLASS_OFFLINE_COURSE)
						.leftJoin(F2F_COURSE)
						.on(F2F_COURSE.ID.eq(CLASS_OFFLINE_COURSE.ID))
						.leftJoin(COURSE_SALARY)
						.on(CLASS_OFFLINE_COURSE.ID.eq(COURSE_SALARY.COURSE_ID))
						.where(
								stratCourseDuration.map(F2F_COURSE.COURSE_DURATION::ge).orElse(DSL.trueCondition()))
						.and(endCourseDuration.map(F2F_COURSE.COURSE_DURATION::le).orElse(DSL.trueCondition()))
						.and(starttime.map(F2F_COURSE.COURSE_DATE::ge).orElse(DSL.trueCondition()))
						.and(endtime.map(F2F_COURSE.COURSE_DATE::le).orElse(DSL.trueCondition()))
						.and(startSatisfiedDegree.map(F2F_COURSE.SATISFIED_DEGREE::ge).orElse(DSL.trueCondition()))
						.and(endSatisfiedDegree.map(F2F_COURSE.SATISFIED_DEGREE::le).orElse(DSL.trueCondition()))
						.and(className.map(m -> {
							List<ClassInfo> classList = classInfoDao.execute(e -> {
								return e.select(CLASS_INFO.fields())
										.from(CLASS_INFO)
										.leftJoin(PROJECT)
										.on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
										.where(PROJECT.NAME.contains(m))
										.fetchInto(ClassInfo.class);
							});
							return F2F_COURSE.CLASS_ID.in(
									classList
											.stream().map(ClassInfo::getId)
											.collect(Collectors.toList()));
						}).orElse(DSL.trueCondition()))
						.and(CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(0))
						.and(COURSE_SALARY.LECTURE_ID.eq(l.getId()))
				).asTable("b");

				return d.select(Fields.start()
						.add(CLASS_OFFLINE_COURSE.NAME,CLASS_OFFLINE_COURSE.CREATE_TIME,CLASS_OFFLINE_COURSE.CLASS_ID,
								CLASS_OFFLINE_COURSE.COURSE_DATE, CLASS_OFFLINE_COURSE.START_TIME,
								CLASS_OFFLINE_COURSE.END_TIME, CLASS_OFFLINE_COURSE.COURSE_SATISFY)
						.end())
						.from(CLASS_OFFLINE_COURSE)
						.innerJoin(basic).on(basic.field(CLASS_OFFLINE_COURSE.ID).eq(CLASS_OFFLINE_COURSE.ID))
						.orderBy(CLASS_OFFLINE_COURSE.CREATE_TIME.desc(), CLASS_OFFLINE_COURSE.COURSE_DATE.desc(), CLASS_OFFLINE_COURSE.END_TIME.desc())
						.fetch( b -> {
							ClassOfflineCourse classOfflineCourse = b.into(ClassOfflineCourse.class);
							classOfflineCourse.setCreateTime(b.getValue(CLASS_OFFLINE_COURSE.CREATE_TIME));
							classOfflineCourse.setName(b.getValue(CLASS_OFFLINE_COURSE.NAME));
							classOfflineCourse.setCourseDate(b.getValue(CLASS_OFFLINE_COURSE.COURSE_DATE));
							Double value = b.getValue(CLASS_OFFLINE_COURSE.COURSE_SATISFY);
							if (value != null) {
								BigDecimal bd = new BigDecimal(value);
								//保留2位小数
								double result = bd.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
								classOfflineCourse.setCourseSatisfy(result*10);
							} else {
								classOfflineCourse.setCourseSatisfy(value);
							}
							classOfflineCourse.setStartTime(b.getValue(CLASS_OFFLINE_COURSE.START_TIME));
							String endTime = b.getValue(CLASS_OFFLINE_COURSE.END_TIME);
							String startTime = b.getValue(CLASS_OFFLINE_COURSE.START_TIME);
							if ((endTime != null && !endTime.equals("")) && startTime != null && !startTime.equals("")) {
								String endS[] = endTime.split(":");
								Double branch = 0.0;
								Integer endB = Integer.parseInt(endS[1]);
								String startS[] = startTime.split(":");
								Integer startB = Integer.parseInt(startS[1]);
								Integer hour = (Integer.parseInt(endS[0]) - Integer.parseInt(startS[0]));
								Integer numTime = 0;
								if (endB > startB) {
									numTime = endB - startB;
									branch = 0.5;
									if (numTime > 30) {
										branch = 1.0;
									}
								} else if (endB < startB) {
									numTime = startB - endB;
									if (numTime >= 30) {
										branch = -0.5;
									}
								}
								Double aa = branch + hour;
								classOfflineCourse.setTime(aa);
							}
							cIds.add(b.getValue(CLASS_OFFLINE_COURSE.CLASS_ID));
							return classOfflineCourse;
						});
			});


			Map<String, ClassInfo> classInfoMap = classInfoDao.execute(x -> x.select(CLASS_INFO.ID,CLASS_INFO.PROJECT_ID)
					.from(CLASS_INFO)).where(CLASS_INFO.ID.in(cIds))
					.fetch(r ->{
						ClassInfo cr =  r.into(ClassInfo.class);
						pIds.add(r.getValue(CLASS_INFO.PROJECT_ID));
						return cr;

					}).stream()
					.collect(Collectors.toMap(ClassInfo::getId, s -> s));

			Map<String, Project> projectMap = projectDao.execute(x -> x.select(PROJECT.ID,PROJECT.NAME)
					.from(PROJECT)).where(PROJECT.ID.in(pIds))
					.fetch(r -> r.into(Project.class)).stream()
					.collect(Collectors.toMap(Project::getId, f -> f));

			offlineCourseList.stream().map(r -> {
				String cId = r.getClassId();
				String pId = null;
				if (classInfoMap.get(cId) != null) {
					pId = classInfoMap.get(cId).getProjectId();
				}
				if (pId != null && projectMap.get(pId) != null) {
					r.setClassName(projectMap.get(pId).getName());
				}
				return r;
			}).collect(Collectors.toList());
			offlineCourseList.sort((lec1, lec2) -> lec2.getCreateTime().compareTo(lec1.getCreateTime()));
			l.setOfflineCourseList(offlineCourseList);
			l.setLabelList(labelList);
			if (offlineCourseList.size() > 0 && offlineCourseList != null) {
				Double d = 0.0d;
				Double count = 0.0d;
				for (int i = 0; i < offlineCourseList.size(); i++) {
					if (offlineCourseList.get(i).getCourseSatisfy() != null) {
						d += offlineCourseList.get(i).getCourseSatisfy();
					}
					count += offlineCourseList.get(i).getTime();
				}
				if (d != 0.0) {
					double result = d/offlineCourseList.size()/10;
					String format = String.format("%.1f", result);
//					BigDecimal bd = new BigDecimal(result);
//				    //保留2位小数
//					double result2 = bd.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
					DecimalFormat df1 = new DecimalFormat("0.0");
					String format2 = df1.format(Double.parseDouble(format));
					l.setSatisfy(format2);
				} else {
					l.setSatisfy("0.0");
				}
				l.setCount(count);
			} else {
				l.setSatisfy("0.0");
			}
		}
		return l;
	}

	@Override
	public Optional<Lecturer> findLecturerByUserId(String memberId) {
		// TODO Auto-generated method stub
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.MEMBER_ID.eq(memberId)))
				.fetchOptional(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public Lecturer updateHead(String id, Optional<String> headPortrait, Optional<String> headPortraitPath) {
		Lecturer lecturer = lecturerDao.get(id);
		headPortrait.ifPresent(lecturer::setHeadPortrait);
		headPortraitPath.ifPresent(lecturer::setCoverPath);
		lecturer.setModifyDate(null);
		lecturerDao.update(lecturer);
		return lecturer;
	}

	@Override
	public Optional<Lecturer> getOne(String id) {
		// TODO Auto-generated method stub
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.ID.eq(id)))
				.fetchOptional(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}
	@Override
	public PagedResult<Lecturer> findExternalLecturer(int page, int pageSize, Optional<String> name,
													  Optional<String> className, Optional<String> courseName) {
		List<Lecturer> lectureList = lecturerDao.execute(d -> {
			Table<Record1<String>> basic = (d.select(LECTURER.ID)
					.from(LECTURER)
					.leftJoin(CLASS_OFFLINE_COURSE).on(LECTURER.ID.eq(CLASS_OFFLINE_COURSE.TEACHER_ID))
					.leftJoin(CLASS_INFO).on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
					.leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))
					.where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
					.and(className.map(c -> CLASS_INFO.PROJECT_ID.in(projectDao.fetch(PROJECT.NAME.contains(c)).stream().map(Project::getId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
					.and(courseName.map(CLASS_OFFLINE_COURSE.NAME::contains).orElse(DSL.trueCondition()))
					.and(LECTURER.TYPE.eq(1))
					.and(LECTURER.STATUS.eq(0))
					.groupBy(LECTURER.ID)
//                    .orderBy(LECTURER.CREATE_TIME.desc())
//                    .limit((page - 1) * pageSize, pageSize)
			).asTable("b");

			return d.select(Fields.start()
					.add(LECTURER)
					.end())
					.from(LECTURER)
					.innerJoin(basic).on(basic.field(LECTURER.ID).eq(LECTURER.ID))
					.orderBy(LECTURER.CREATE_TIME.desc())
					.limit((page - 1) * pageSize, pageSize)
					.fetch(r -> {
						Lecturer l = r.into(Lecturer.class);
						return l;
					});
		});

		//获取行数
		Integer count = 0;
		if (!className.isPresent() && !courseName.isPresent()) {
			count = lecturerDao.execute(x -> x.select(LECTURER.ID.count()).from(LECTURER)
					.leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID)))
					.where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
					.and(LECTURER.TYPE.eq(1))
					.fetchOne(LECTURER.ID.count());
		} else {
			count = lecturerDao.execute(x -> x.select(LECTURER.ID.count()).from(LECTURER))
					.leftJoin(CLASS_OFFLINE_COURSE).on(CLASS_OFFLINE_COURSE.TEACHER_ID.eq(LECTURER.ID))
					.leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(CLASS_OFFLINE_COURSE.CLASS_ID))
					.where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
					.and(className.map(c -> CLASS_INFO.PROJECT_ID.in(projectDao.fetch(PROJECT.NAME.contains(c)).stream().map(Project::getId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
					.and(courseName.map(CLASS_OFFLINE_COURSE.NAME::contains).orElse(DSL.trueCondition()))
					.and(LECTURER.TYPE.eq(1))
					.fetchOne(LECTURER.ID.count());
		}

		lectureList.sort((lec1, lec2) -> lec2.getCreateTime().compareTo(lec1.getCreateTime()));

		return PagedResult.create(count, lectureList);
	}

	@Override
	public Lecturer getLecturerDetailByPhone(String phone) {
		//讲师职务
		Field<String> jobName = JOB.NAME.as("jobName");
		com.zxy.product.train.jooq.tables.Job ihrJobTable = JOB.as("ihrJobTable");
		//讲师单位
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		return lecturerDao.execute(x -> x.select(Fields.start()
				.add(LECTURER.ID, LECTURER.NAME, jobName, ihrJobTable.NAME, orgName)
				.add(LECTURER.BANK, LECTURER.BANK_IDENTITY, LECTURER.BANK_CARD, LECTURER.BANK_USER).end())
				.from(LECTURER)
				.leftJoin(ORGANIZATION).on(LECTURER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
				.leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))
				.leftJoin(POSITION).on(MEMBER.MAJOR_POSITION_ID.eq(POSITION.ID))
				.leftJoin(JOB).on(JOB.ID.eq(POSITION.JOB_ID))
				.leftJoin(ihrJobTable).on(ihrJobTable.ID.eq(MEMBER.JOB_ID))
				.where(LECTURER.MOBILE.eq(phone).and(LECTURER.TYPE.eq(0)).and(LECTURER.STATUS.eq(0))))
				.fetchOne(r -> {
					Lecturer lecturer = r.into(LECTURER).into(Lecturer.class);
					lecturer.setJobName(StringUtils.isEmpty(r.getValue(ihrJobTable.NAME)) ? r.getValue(jobName) : r.getValue(ihrJobTable.NAME));
					lecturer.setOrganizationName(r.getValue(orgName));
					return lecturer;
				});
	}

	@Override
	public Lecturer getLecturerDetailByIDCard(String idCard) {
		return getLecturerDetailByIDCard(idCard, 0);
	}

	@Override
	public Lecturer getLecturerByIDCard(String idCard) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.BANK_IDENTITY.eq(idCard)))
				.fetchOne(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public Lecturer getLecturerDetailByIDCard(String idCard, Integer type) {
		return lecturerDao
				.execute(x -> x
						.select(Fields.start()
								.add(LECTURER)
								.end())
						.from(LECTURER)
						.where(LECTURER.BANK_IDENTITY.eq(idCard)).and(LECTURER.TYPE.eq(type)))
				.limit(1)
				.fetchOne(r -> {
					Lecturer lecturer = r.into(Lecturer.class);
					return lecturer;
				});
	}

	@Override
	public Lecturer getLecturerByNameAndPhone(String phone, String name) {
		return  lecturerDao.execute(x -> x.select(Fields.start()
				.add(LECTURER).end())
				.from(LECTURER).where(LECTURER.NAME.eq(name)).and(LECTURER.MOBILE.eq(phone)).and(LECTURER.TYPE.eq(1))).fetchOne(r->{
			Lecturer lecturer = r.into(LECTURER).into(Lecturer.class);
			return  lecturer;
		});
	}

	@Override
	public Lecturer putStatus(String id, Integer status) {
		// TODO Auto-generated method stub
		Optional<Lecturer> optional = lecturerDao.getOptional(id);
		if(optional.isPresent()){
			Lecturer l = optional.get();
			l.setStatus(status);
			l.setModifyDate(null);
			lecturerDao.update(l);
			if (Lecturer.STATUS_NO.equals(status)) {
				releaseStatus(id, Lecturer.RELEASE_STATUS_NO);
			}
			return l;
		} else {
			return null;
		}
	}

	@Override
	public PagedResult<OfflineCourseLibrary> selectOfflineCourse(Integer page, Integer pageSize, Optional<String> name,
			Optional<String> lecturerId, Optional<String> courseAttributes, Optional<String> courseSequence,
			Optional<String> courseList, Optional<String> organizationId, List<String> organizationIds) {
		Set<String> courseIds = new HashSet<String>();
		lecturerId.ifPresent(id -> {
			lecturerAdeptCourseDao.fetch(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(id)).forEach(lecturerAdeptCourse -> {
				if(lecturerAdeptCourse.getCourseId() != null){
					courseIds.add(lecturerAdeptCourse.getCourseId());
				}
			});
		});
		courseList.ifPresent(courseId -> {
			if (courseId.length() > 2) {
				for (String id : courseId.split(",")) {
					if(!"null".equals(id) && !"NULL".equals(id)){
						courseIds.add(id);
					}
				}
			}
		});
		Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
		SelectOnConditionStep<Record> step = offlineCourseLibraryDao.execute(x -> x
				.selectDistinct(Fields.start().add(
						F2F_COURSE_LIBRARY.ID,
						F2F_COURSE_LIBRARY.NAME,
						F2F_COURSE_LIBRARY.COURSE_ATTRIBUTES,
						F2F_COURSE_LIBRARY.SEQUENCE,
						F2F_COURSE_LIBRARY.CREATE_TIME,
						F2F_COURSE_LIBRARY.UPDATE_TIME,
						F2F_COURSE_LIBRARY.IS_USE,
						F2F_COURSE_LIBRARY.INSTITUTION_ID,
						COURSE_ATTRIBUTE.ATTRIBUTE_NAME,
						organizationName
						).end())
				.from(F2F_COURSE_LIBRARY)
				.leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.COURSE_ID.eq(F2F_COURSE_LIBRARY.ID))
				.leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.COURSE_ID.eq(F2F_COURSE_LIBRARY.ID))
				.leftJoin(CLASS_INFO).on(COLLEGE_TEACHING.CLASS_ID.eq(CLASS_INFO.ID))
				.leftJoin(PROJECT).on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
				.leftJoin(LECTURER).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID).or(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.ID)))
				.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(F2F_COURSE_LIBRARY.ORGANIZATION_ID))
				.leftJoin(COURSE_ATTRIBUTE).on(F2F_COURSE_LIBRARY.COURSE_ATTRIBUTES.eq(COURSE_ATTRIBUTE.ID))
				);

		List<Condition> conditions = Stream.of(
				name.map(F2F_COURSE_LIBRARY.NAME::contains),
				courseAttributes.map(F2F_COURSE_LIBRARY.COURSE_ATTRIBUTES::eq),
				courseSequence.map(F2F_COURSE_LIBRARY.SEQUENCE::eq),
//				Optional.ofNullable(organizationIds).map(F2F_COURSE_LIBRARY.ORGANIZATION_ID::in),
				Optional.ofNullable(courseIds).map(F2F_COURSE_LIBRARY.ID::notIn),
				Optional.ofNullable(F2fCourseLibrary.IS_USE_TRUE).map(F2F_COURSE_LIBRARY.IS_USE::eq)
				)
				.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
		Condition wheres = organizationId.isPresent()
				? F2F_COURSE_LIBRARY.ORGANIZATION_ID.in(organizationIds)
				: F2F_COURSE_LIBRARY.ORGANIZATION_ID.in(organizationIds)
					.or(F2F_COURSE_LIBRARY.IS_SHARE.eq(OfflineCourseLibrary.SHARE_YES));
		// 获取行数
		Integer count = offlineCourseLibraryDao.execute(x -> x.fetchCount(step.where(conditions)
				.and(wheres)));

		List<OfflineCourseLibrary> list = step
				.where(conditions)
				.and(F2F_COURSE_LIBRARY.IS_SHARE.eq(OfflineCourseLibrary.SHARE_YES)
						.or(F2F_COURSE_LIBRARY.ORGANIZATION_ID.in(organizationIds)))
				.orderBy(F2F_COURSE_LIBRARY.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch(r -> {
			OfflineCourseLibrary ot = new OfflineCourseLibrary();
			ot.setId(r.getValue(F2F_COURSE_LIBRARY.ID));
			ot.setCourseAttributes(r.getValue(F2F_COURSE_LIBRARY.COURSE_ATTRIBUTES));
			ot.setName(r.getValue(F2F_COURSE_LIBRARY.NAME));
			ot.setCreateTime(r.getValue(F2F_COURSE_LIBRARY.CREATE_TIME));
			ot.setUpdateTime(r.getValue(F2F_COURSE_LIBRARY.UPDATE_TIME));
			ot.setSequence(r.getValue(F2F_COURSE_LIBRARY.SEQUENCE));
			ot.setIsUse(r.getValue(F2F_COURSE_LIBRARY.IS_USE));
			ot.setInstitutionId(r.getValue(F2F_COURSE_LIBRARY.INSTITUTION_ID));
			ot.setOrganizationName(r.getValue(organizationName));
			ot.setCourseAttributesName(r.getValue(COURSE_ATTRIBUTE.ATTRIBUTE_NAME));
			return ot;
		});
		return PagedResult.create(count, list);
	}

	@Override
	public PagedResult<Lecturer> selectFind(int page, int pageSize, String currentMemberId,
			Optional<String> organizationId, Optional<String> name, Optional<Integer> type, Optional<Integer> status,
			Optional<String> levelId, Optional<String> className, Optional<String> courseName,
			Optional<String> parentId, Optional<String> sequenceId, Optional<String> label,
			Optional<String> attributeId, Optional<String> unit, Optional<String> courseType,
			Optional<List<String>> organizationIds, Optional<List<String>> lecturerIds) {
	       List<String> levelIds = new ArrayList<>();
	        List<String> memberIds = new ArrayList<>();
	        List<String> orgIds = new ArrayList<>();
	        List<String> pIds = new ArrayList<>();
	        List<String> jIds = new ArrayList<>();
	        //获取面授课程库课程列表
	        List<String> courseList = new ArrayList<String>();
	        Condition courseIdsDefalut = DSL.trueCondition();
	        if (courseName.isPresent() || courseType.isPresent()) {
	        	courseList = f2fCourseLibraryDao.execute(dao -> {
	        		Condition conditions = F2F_COURSE_LIBRARY.IS_USE.eq(F2fCourseLibrary.IS_USE_TRUE)
	        				.and(courseType.map(F2F_COURSE_LIBRARY.SEQUENCE::eq).orElse(DSL.trueCondition()))
	        				.and(courseName.map(F2F_COURSE_LIBRARY.NAME::contains).orElse(DSL.trueCondition()));
	        		return dao.select(Fields.start().add(F2F_COURSE_LIBRARY.ID).end())
			        		.from(F2F_COURSE_LIBRARY)
			        		.where(conditions)
			        		.fetch().into(String.class);
	        	});
	        	courseIdsDefalut = courseList.size() > 0 ? LECTURER_ADEPT_COURSE.COURSE_ID.in(courseList)
	        			.or(COLLEGE_TEACHING.COURSE_ID.in(courseList)).or(ORGANIZATION_TEACHING.COURSE_ID.in(courseList))
	        			: DSL.falseCondition();
			}
	        Condition courseIds = courseIdsDefalut;
	        List<Lecturer> lectureList = lecturerDao.execute(d -> {
	            Table<Record1<String>> basic = (d.select(LECTURER.ID)
	                    .from(LECTURER)
	                    .leftJoin(CLASS_OFFLINE_COURSE).on(LECTURER.ID.eq(CLASS_OFFLINE_COURSE.TEACHER_ID))
	                    .leftJoin(CLASS_INFO).on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
	                    .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))

	                    .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
	                    .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
	                    .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))

	                    .leftJoin(LECTURER_ADEPT_COURSE).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
	                    .leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.MEMBER_ID))
	                    .leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.MEMBER_ID))

	                    .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
	                    .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
	                    .and(className.map(c -> CLASS_INFO.PROJECT_ID.in(projectDao.fetch(PROJECT.NAME.contains(c)).stream().map(Project::getId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
	                    .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
	                    .and(status.map(LECTURER.STATUS::eq).orElse(DSL.trueCondition()))

	                    .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
	                    .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
	                    .and(label.isPresent()?LABEL.NAME.contains(label.get()):DSL.trueCondition())
	                    .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))
	                    .and(unit.map(LECTURER.UNIT::contains).orElse(DSL.trueCondition()))

	                    .and(lecturerIds.map(LECTURER.ID :: notIn).orElse(DSL.trueCondition()))
	                    .and(courseIds)


	                    // 系统权限限制
//	                    .and(MEMBER.ORGANIZATION_ID.in(organizationIds.get())
//	                    .or(LECTURER.TYPE.eq(1)))
	                    // 归属权限限制
	                    .and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
	                    .groupBy(LECTURER.ID)
	                    .orderBy(LECTURER.CREATE_TIME.desc(),LECTURER.MOBILE.desc())
//	                    .limit((page - 1) * pageSize, pageSize)
				).asTable("b");
	            Field<String> sequenceName = LECTURER_COURSE_CONFIG.NAME.as("sequenceName");
				return d.select(Fields.start()
						.add(LECTURER).add(sequenceName)
						.end())
						.from(LECTURER)
						.innerJoin(basic).on(basic.field(LECTURER.ID).eq(LECTURER.ID))
	                    .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
						.orderBy(LECTURER.CREATE_TIME.desc(),LECTURER.MOBILE.desc())
						.limit((page - 1) * pageSize, pageSize)
						.fetch(r -> {
							Lecturer l = r.into(Lecturer.class);
							l.setSequenceName(r.getValue(sequenceName));
//	                        if (r.getValue(LECTURER.LEVEL_ID) != null && r.getValue(LECTURER.LEVEL_ID) != "") {
//	                        }
							levelIds.add(r.getValue(LECTURER.LEVEL_ID));
							memberIds.add(r.getValue(LECTURER.MEMBER_ID));
//	                        if (r.getValue(LECTURER.MEMBER_ID) != null && r.getValue(LECTURER.MEMBER_ID) != "") {
//	                        }
							orgIds.add(r.getValue(LECTURER.ORGANIZATION_ID));
//	                        if (r.getValue(LECTURER.ORGANIZATION_ID) != null && r.getValue(LECTURER.ORGANIZATION_ID) != "") {
//	                        }
	                        return l;
	                    });
	        });

	        //获取行数
	        Integer count = 0;
	        if (!className.isPresent() && !courseName.isPresent() && !courseType.isPresent()) {
	            count = lecturerDao.execute(x -> x.select(LECTURER.ID.countDistinct()).from(LECTURER)
	                    .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID)))
	                    .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
	                    .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
	                    .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))
	                    .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
	                    .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
	                    .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
	                    .and(status.map(LECTURER.STATUS::eq).orElse(DSL.trueCondition()))
//	                    .and(adeptCourse.map(LECTURER.ADEPT_COURSE::contains).orElse(DSL.trueCondition()))
//	                    .and(MEMBER.COMPANY_ID.in(organizationIds.get())
	                    .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
	                    .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
	                    .and(label.isPresent()?LABEL.NAME.contains(label.get()):DSL.trueCondition())
	                    .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))
	                    .and(unit.map(LECTURER.UNIT::contains).orElse(DSL.trueCondition()))
	                    .and(lecturerIds.map(LECTURER.ID :: notIn).orElse(DSL.trueCondition()))
//	                    .and(MEMBER.ORGANIZATION_ID.in(organizationIds.get())
//	                    .or(LECTURER.TYPE.eq(1)))
						.and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
	                    .fetchOne(LECTURER.ID.count());
	        } else {
	            count = lecturerDao.execute(x -> x.select(LECTURER.ID.countDistinct()).from(LECTURER))
	            .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))
	            .leftJoin(CLASS_OFFLINE_COURSE).on(CLASS_OFFLINE_COURSE.TEACHER_ID.eq(LECTURER.ID))
	            .leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(CLASS_OFFLINE_COURSE.CLASS_ID))
	            .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
	            .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
	            .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))
	            .leftJoin(LECTURER_ADEPT_COURSE).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
	            .leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.MEMBER_ID))
	            .leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.MEMBER_ID))
	            .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
	            .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
	            .and(className.map(c -> CLASS_INFO.PROJECT_ID.in(projectDao.fetch(PROJECT.NAME.contains(c)).stream().map(Project::getId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
//	            .and(courseName.map(CLASS_OFFLINE_COURSE.NAME::contains).orElse(DSL.trueCondition()))
//	            .and(adeptCourse.map(LECTURER.ADEPT_COURSE::contains).orElse(DSL.trueCondition()))
	            .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
	            .and(status.map(LECTURER.STATUS::eq).orElse(DSL.trueCondition()))
//	            .and(organizationId.map(o -> LECTURER.ORGANIZATION_ID.in(organizationCommonDao.fetch(ORGANIZATION.PATH.contains(o)).stream().map(Organization::getId).collect(Collectors.toList()))).orElse(DSL.trueCondition()))
//	            .and(MEMBER.COMPANY_ID.in(organizationIds.get())
	            .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
	            .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
	            .and(label.isPresent()?LABEL.NAME.contains(label.get()):DSL.trueCondition())
	            .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))
	            .and(unit.map(LECTURER.UNIT::contains).orElse(DSL.trueCondition()))
	            .and(lecturerIds.map(LECTURER.ID :: notIn).orElse(DSL.trueCondition()))
	            .and(courseIds)
//	            .and(MEMBER.ORGANIZATION_ID.in(organizationIds.get())
//	            .or(LECTURER.TYPE.eq(1)))
	            .and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
	            .fetchOne(LECTURER.ID.count());
	        }
	        //level
	        Map<String, Level> levelMap = levelDao.execute(x -> x.select(LEVEL.ID, LEVEL.NAME).from(LEVEL)).where(LEVEL.ID.in(levelIds))
	                .fetch(r -> r.into(Level.class)).stream()
	        .collect(Collectors.toMap(Level::getId, l -> l));
	        //组织
	        Map<String, String> orgMap = organizationCommonDao.execute(x -> x.select(ORGANIZATION.NAME, ORGANIZATION.ID).from(ORGANIZATION)).where(ORGANIZATION.ID.in(orgIds))
	                .fetch(r -> r.into(Organization.class)).stream()
	                .collect(Collectors.toMap(Organization::getId, Organization::getName));
	        //人员
	        Map<String, Member> memberMap = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.NAME,MEMBER.MAJOR_POSITION_ID).from(MEMBER)).where(MEMBER.ID.in(memberIds))
	                .fetch(r -> {
	                	Member m =  r.into(Member.class);
	                	pIds.add(r.getValue(MEMBER.MAJOR_POSITION_ID));
	                	return m;
	                }).stream()
	                .collect(Collectors.toMap(Member::getId, l -> l));

	        Map<String, Position> positionMap = positionDao.execute(x -> x.select(POSITION.ID,POSITION.JOB_ID).from(POSITION)).where(POSITION.ID.in(pIds))
	                .fetch(r ->{
	                	Position p = r.into(Position.class);
	                	jIds.add(r.getValue(POSITION.JOB_ID));
	                	return p;
	                }).stream()
	                .collect(Collectors.toMap(Position::getId, l -> l));

	        Map<String, Job> jobMap = jobDao.execute(x -> x.select(JOB.ID, JOB.NAME).from(JOB)).where(JOB.ID.in(jIds))
	                .fetch(r -> r.into(Job.class)).stream()
	                .collect(Collectors.toMap(Job::getId, l -> l));
	        Map<String, String> attributeMap = lecturerAttributeService.findAll();
	        lectureList.forEach(f -> {
	                String level = f.getLevelId();
	                String memberId = f.getMemberId();
	                String orgId = f.getOrganizationId();
	                String aorgId = f.getAscriptionOrganizationId();
	                Integer typeId = f.getType();
	                String attributeIds = f.getAttributeId();
	                String pId = null;
	                String jId = null;
	                if (levelMap.get(level) != null) {
	                    f.setLevelName(levelMap.get(level).getName());
	                }
	                if (memberMap.get(memberId) != null) {
	                    f.setMemberName(memberMap.get(memberId).getFullName());
	                    f.setNum(memberMap.get(memberId).getName());
	                    pId = memberMap.get(memberId).getMajorPositionId();
	                }
	                if (pId != null && positionMap.get(pId) != null) {
	                	jId = positionMap.get(pId).getJobId();
	                }
	                if (jId != null && jobMap.get(jId) != null) {
	                	f.setJobName(jobMap.get(jId).getName());
	                }
	                if (typeId == 1) {
	                    f.setOrganizationName(f.getOrganizationId());
					}else if (orgMap.get(orgId) != null) {
	                    f.setOrganizationName(orgMap.get(orgId));
	                }
	                f.setAscriptionOrganizationName(orgMap.get(aorgId));
	                if (attributeIds != null) {
	                	f.setAttribute(attributeMap.get(attributeIds));
					}
	        });

//	        lectureList.sort((lec1, lec2) -> lec2.getCreateTime().compareTo(lec1.getCreateTime()));

			return PagedResult.create(count, lectureList);
	}


	@Override
	public Lecturer getStudentFindId(String id, Integer type, String memberId) {
		// 命名字段别名
		Field<String> memberName = MEMBER.NAME.as("memberName");
		Field<String> memberPhoneNumber = MEMBER.PHONE_NUMBER.as("memberPhoneNumber");
		Field<String> memberEmail = MEMBER.EMAIL.as("memberEmail");
		Field<Long> memberEntryDate = MEMBER.ENTRY_DATE.as("memberEntryDate");
		Field<String> levelName = LEVEL.NAME.as("levelName");
		Field<String> levelId = LEVEL.ID.as("levelId");
		Field<String> organizationName = ORGANIZATION.NAME.as("organizationName");
		Field<String> organizationCompanyId = ORGANIZATION.COMPANY_ID.as("organizationCompanyId");
		List<Lecturer> lecturerList = lecturerDao
				.execute(e -> e
						.select(Fields.start().add(LECTURER)
								.add(memberName, levelId, levelName, organizationName,
										MEMBER.FULL_NAME,memberPhoneNumber,memberEmail,memberEntryDate,
										organizationCompanyId,LECTURER_COURSE_CONFIG.NAME,LECTURER_COURSE_CONFIG.PARENT_ID)
								.end())
						.from(LECTURER)
						.leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))
						.leftJoin(ORGANIZATION).on(LECTURER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
						.leftJoin(LEVEL).on(LECTURER.LEVEL_ID.eq(LEVEL.ID))
						.leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER_COURSE_CONFIG.ID.eq(LECTURER.SEQUENCE_ID))
						.where(
								type == 0 ? LECTURER.ID.eq(id) : LECTURER.MEMBER_ID.eq(id)
								.and(LECTURER.STATUS.eq(Lecturer.STATUS_YES))
								.and(LECTURER.RELEASE_STATUS.eq(Lecturer.RELEASE_STATUS_YES))
								)
						.limit(1)
						.fetch(r -> {
							Lecturer lecturer = r.into(Lecturer.class);
							lecturer.setLevelName(r.getValue(levelName));
							lecturer.setLevelId(r.getValue(levelId));
							lecturer.setSequenceName(r.getValue(LECTURER_COURSE_CONFIG.NAME));
							lecturer.setParentId(r.getValue(LECTURER_COURSE_CONFIG.PARENT_ID));
			                lecturer.setOrganizationName(r.getValue(lecturer.getType() == 1?LECTURER.ORGANIZATION_ID:organizationName));
							if (r.getValue(MEMBER.FULL_NAME) != null && !"".equals(r.getValue(MEMBER.FULL_NAME))) {
								lecturer.setMemberName(r.getValue(MEMBER.FULL_NAME));
							}
							if (SM4Utils.decryptDataCBC(r.getValue(memberEmail)) != null && !"".equals(SM4Utils.decryptDataCBC(r.getValue(memberEmail)))) {
								lecturer.setEmail(SM4Utils.decryptDataCBC(r.getValue(memberEmail)));
							}
							if (SM4Utils.decryptDataCBC(r.getValue(memberPhoneNumber)) != null && !"".equals(SM4Utils.decryptDataCBC(r.getValue(memberPhoneNumber)))) {
								lecturer.setMobile(SM4Utils.decryptDataCBC(r.getValue(memberPhoneNumber)));
							}
							if (String.valueOf(r.getValue(memberEntryDate)) != null && !"".equals(r.getValue(memberEntryDate))) {
								lecturer.setWorkStartYear(String.valueOf(r.getValue(memberEntryDate)));
							}
							if (r.getValue(organizationCompanyId) != null && !"".equals(r.getValue(organizationCompanyId))) {
								Organization organization = organizationCommonDao.get(r.getValue(organizationCompanyId));
								lecturer.setUnit(organization.getName());
							}
							return lecturer;
						}));
		Lecturer l = lecturerList.size() > 0 ? lecturerList.get(0) : null;
		if (l != null && l.getMemberId() != null) {
			Optional<Job> fetchOptional = jobDao.execute(
					x -> x.select(Fields.start().add(JOB.NAME).end()).from(JOB)
							.leftJoin(POSITION)
							.on(JOB.ID.eq(POSITION.JOB_ID))
							.leftJoin(MEMBER)
							.on(POSITION.ID.eq(MEMBER.MAJOR_POSITION_ID))
							.where(MEMBER.ID.eq(l.getMemberId())))
					.fetchOptional(b -> {
						return b.into(Job.class);
					});
			if (fetchOptional.isPresent()) {
				l.setJobName(fetchOptional.get().getName());
			}
		}
		if (l != null) {
			if (type == 0) {
				l.setThumbsUp(lecturerThumbsUpService.findMemberToLecturer(memberId, l.getId()).isPresent());
			}
			List<Label> labelList = labelDao.execute(
				x -> x.select(Fields.start().add(LABEL).add(LECTURER_LABEL).end()).from(LABEL).leftJoin(LECTURER_LABEL)
						.on(LABEL.ID.eq(LECTURER_LABEL.LABEL_ID)).where(LECTURER_LABEL.LECTURER_ID.eq(id)))
				.fetch(b -> {
					return b.into(Label.class);
				});

			l.setLabelList(labelList);
//			Organization organization = organizationCommonDao.get(l.getAscriptionOrganizationId());
//			l.setAscriptionOrganizationName(organization != null ? organization.getName() : null);
			LecturerAttribute lecturerAttribute = lecturerAttributeService.findInfo(Optional.of(l.getAttributeId()));
			l.setType(lecturerAttribute.getTypeId());
			l.setAttribute(lecturerAttribute.getAttributeName());
			l.setAdeptCourseList(lecturerAdeptCourseService.findAdeptLecturer(Arrays.asList(id)));
			if (l.getSequenceId() != null && !"".equals(l.getSequenceId())) {
				LecturerCourseConfig lecturerCourseConfig = lecturerCourseConfigService.findInfo(Optional.of(l.getParentId()));
				if (lecturerCourseConfig != null) {
					l.setParentName(lecturerCourseConfig.getName());
				}
			}
		}
		return l;
	}

	@Override
	public void addBrowse(String id) {
		Lecturer lecturer = lecturerDao.get(id);
		if (lecturer != null) {
			lecturer.setBrowseNumber(lecturer.getBrowseNumber() == null ? 1 : lecturer.getBrowseNumber() + 1);
			lecturer.setModifyDate(null);
			lecturerDao.update(lecturer);
		}
	}

	@Override
	public void addThumbsUp(String id) {
		Lecturer lecturer = lecturerDao.get(id);
		if (lecturer != null) {
			lecturer.setThumbsUpNumber(lecturer.getThumbsUpNumber() == null ? 1 : lecturer.getThumbsUpNumber() + 1);
			lecturer.setModifyDate(null);
			lecturerDao.update(lecturer);
		}
	}

	@Override
	public void deleteThumbsUp(String id) {
		Lecturer lecturer = lecturerDao.get(id);
		if (lecturer != null) {
			lecturer.setThumbsUpNumber(lecturer.getThumbsUpNumber() == null || lecturer.getThumbsUpNumber() <= 0 ? 0 : lecturer.getThumbsUpNumber() - 1);
			lecturer.setModifyDate(null);
			lecturerDao.update(lecturer);
		}
	}
	@Override
	public PagedResult<Lecturer> findStudent(Integer page, Integer pageSize, String currentUserId, Optional<String> name,
			Optional<Integer> type, Optional<String> levelId, Optional<String> courseName, Optional<String> parentId,
			Optional<String> sequenceId, Optional<String> labelName, Optional<String> labels,  Optional<String> attributeId,
			Optional<String> unit, Optional<String> courseType, List<String> organizationIds, Integer order, Optional<String> moduleHomeConfigId) {
//		List<String> levelIds = new ArrayList<>();
		List<String> memberIds = new ArrayList<>();
//		Set<String> orgIds = new HashSet<>();
		List<String> pIds = new ArrayList<>();
		List<String> jIds = new ArrayList<>();
		//获取面授课程库课程列表
		List<String> courseList = new ArrayList<String>();
		Condition courseIdsDefalut = DSL.trueCondition();
		if (moduleHomeConfigId.isPresent()) {
			List<String> lecturerList = homeLecturerDao.execute(dao -> {
				return dao.select(Fields.start().add(HOME_LECTURER.LECTURE_ID).end())
						.from(HOME_LECTURER)
						.where(HOME_LECTURER.MODULE_CONFIG_ID.eq(moduleHomeConfigId.get()))
						.fetch(item -> {
							return item.getValue(HOME_LECTURER.LECTURE_ID);
						});
			});
			courseIdsDefalut = (lecturerList != null && lecturerList.size() > 0 ) ?
					courseIdsDefalut.and(LECTURER.ID.notIn(lecturerList)) : courseIdsDefalut;
		}
        if (courseName.isPresent() || courseType.isPresent()) {
			courseList = f2fCourseLibraryDao.execute(dao -> {
				Condition conditions = F2F_COURSE_LIBRARY.IS_USE.eq(F2fCourseLibrary.IS_USE_TRUE)
    				.and(courseType.map(F2F_COURSE_LIBRARY.SEQUENCE::eq).orElse(DSL.trueCondition()))
      				.and(courseName.map(F2F_COURSE_LIBRARY.NAME::contains).orElse(DSL.trueCondition()));
      		return dao.select(Fields.start().add(F2F_COURSE_LIBRARY.ID).end())
		        		.from(F2F_COURSE_LIBRARY)
		        		.where(conditions)
		        		.fetch(item -> {
		        			return String.valueOf(item.get(F2F_COURSE_LIBRARY.ID));
		        		});
      	});
      	courseIdsDefalut = courseList.size() > 0 ?
      			courseIdsDefalut.and(LECTURER_ADEPT_COURSE.COURSE_ID.in(courseList))
      			: DSL.falseCondition();
		}
		List<String> listLecturerId = new ArrayList<String>();
		Condition label = DSL.trueCondition();
		if(labelName.isPresent() && labels.isPresent()){
			label = LABEL.NAME.contains(labelName.get()).or(LABEL.ID.in(labels.get().split(",")));
		} else if (labelName.isPresent()){
			label = LABEL.NAME.contains(labelName.get());
		} else if (labels.isPresent()){
			label = LABEL.ID.in(labels.get().split(","));
		}
		Condition unit_Institutions = DSL.trueCondition();
		if(unit.isPresent()){
			unit_Institutions = LECTURER.UNIT.contains(unit.get()).or(LECTURER.INSTITUTIONS.contains(unit.get()));
		}
		if(organizationIds.size() > 0){
			courseIdsDefalut = courseIdsDefalut.and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds));
		}
		Condition courseIds = courseIdsDefalut.and(label).and(unit_Institutions);
		List<Lecturer> lectureList = lecturerDao.execute(d -> {
			Table<Record1<String>> basic = (d.select(LECTURER.ID)
                  .from(LECTURER)
                  .leftJoin(CLASS_OFFLINE_COURSE).on(LECTURER.ID.eq(CLASS_OFFLINE_COURSE.TEACHER_ID))
                  .leftJoin(CLASS_INFO).on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
                  .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))

                  .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
                  .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
                  .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))

                  .leftJoin(LECTURER_ADEPT_COURSE).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
                  .leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID))
                  .leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.ID))

                  .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
                  .and(LECTURER.STATUS.eq(Lecturer.STATUS_YES))
                  .and(LECTURER.RELEASE_STATUS.eq(Lecturer.RELEASE_STATUS_YES))
                  .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
                  .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))

                  .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
                  .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
                  .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))

                  .and(courseIds)
                  // 归属权限限制
//                  .and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
                  .groupBy(LECTURER.ID)
					).asTable("b");
			Field<String> sequenceName = LECTURER_COURSE_CONFIG.NAME.as("sequenceName");
			Field<String> sequenceParentId = LECTURER_COURSE_CONFIG.PARENT_ID.as("parentId");
			Field<String> organizationCompanyId = ORGANIZATION.COMPANY_ID.as("organizationCompanyId");
			Field<String> organizationCompanyName = ORGANIZATION.NAME.as("organizationCompanyName");
			Field<String> levelName = LEVEL.NAME.as("levelName");
			SortField<?> sort = null;
			switch (order) {
				case 1: sort = LECTURER.BROWSE_NUMBER.desc(); break;
				case 2: sort = LECTURER.THUMBS_UP_NUMBER.desc(); break;
				default: sort = LECTURER.CREATE_TIME.desc(); break;
			}
			return d.select(Fields.start()
					.add(LECTURER).add(sequenceName).add(sequenceParentId).add(organizationCompanyId).add(organizationCompanyName)
					.add(levelName)
					.end())
					.from(LECTURER)
					.innerJoin(basic).on(basic.field(LECTURER.ID).eq(LECTURER.ID))
                  .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
                  .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(LECTURER.ASCRIPTION_ORGANIZATION_ID))
                  .leftJoin(LEVEL).on(LEVEL.ID.eq(LECTURER.LEVEL_ID))
					.orderBy(sort)
					.limit((page - 1) * pageSize, pageSize)
					.fetch(r -> {
						Lecturer l = r.into(Lecturer.class);
						l.setSequenceName(r.getValue(sequenceName));
						l.setParentId(r.getValue(sequenceParentId));
						l.setLevelName(r.getValue(levelName));
						l.setOrganizationName(r.getValue(organizationCompanyName));
//						levelIds.add(l.getLevelId());
						memberIds.add(l.getMemberId());
//						orgIds.add(r.getValue(LECTURER.ORGANIZATION_ID));
//						orgIds.add(r.getValue(LECTURER.ASCRIPTION_ORGANIZATION_ID));
//						orgIds.add(r.getValue(organizationCompanyId));
						listLecturerId.add(l.getId());
                      return l;
                  });
		});

		//获取行数
		Integer count = 0;
		if (!courseName.isPresent() && !courseType.isPresent()) {
			count = lecturerDao.execute(x -> x.select(LECTURER.ID.countDistinct()).from(LECTURER)
                  .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID)))
                  .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
                  .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
                  .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))
                  .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
                  .and(LECTURER.STATUS.eq(Lecturer.STATUS_YES))
                  .and(LECTURER.RELEASE_STATUS.eq(Lecturer.RELEASE_STATUS_YES))
                  .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
                  .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
                  .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
                  .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
//                  .and(
//                		  (labelName.isPresent()?LABEL.NAME.contains(labelName.get()):DSL.trueCondition())
//                		  .or(labels.isPresent()?LABEL.ID.in(labels.get().split(",")):DSL.trueCondition())
//            		  )
                  .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))
//                  .and(unit.map(LECTURER.UNIT::contains).orElse(DSL.trueCondition()))
                  .and(courseIds)
                  .fetchOne(LECTURER.ID.count());
		} else {
			count = lecturerDao.execute(x -> x.select(LECTURER.ID.countDistinct()).from(LECTURER))
			.leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))
			.leftJoin(CLASS_OFFLINE_COURSE).on(CLASS_OFFLINE_COURSE.TEACHER_ID.eq(LECTURER.ID))
			.leftJoin(CLASS_INFO).on(CLASS_INFO.ID.eq(CLASS_OFFLINE_COURSE.CLASS_ID))
			.leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
			.leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
			.leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))
			.leftJoin(LECTURER_ADEPT_COURSE).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
			.leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID))
			.leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.ID))
			.where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
            .and(LECTURER.STATUS.eq(Lecturer.STATUS_YES))
            .and(LECTURER.RELEASE_STATUS.eq(Lecturer.RELEASE_STATUS_YES))
			.and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
			.and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
			.and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
			.and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
//            .and(
//          		  (labelName.isPresent()?LABEL.NAME.contains(labelName.get()):DSL.trueCondition())
//          		  .or(labels.isPresent()?LABEL.ID.in(labels.get().split(",")):DSL.trueCondition())
//      		  )
			.and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))
//			.and(unit.map(LECTURER.UNIT::contains).orElse(DSL.trueCondition()))
			.and(courseIds)
			.fetchOne(LECTURER.ID.count());
		}
		//level
//		Map<String, Level> levelMap = levelDao.execute(x -> x.select(LEVEL.ID, LEVEL.NAME).from(LEVEL)).where(LEVEL.ID.in(levelIds))
//              .fetch(r -> r.into(Level.class)).stream()
//              .collect(Collectors.toMap(Level::getId, l -> l));
		//组织
//		Map<String, Organization> orgMap = organizationCommonDao.execute(x -> x.select(ORGANIZATION.NAME, ORGANIZATION.ID, ORGANIZATION.COMPANY_ID).from(ORGANIZATION)).where(ORGANIZATION.ID.in(orgIds))
//              .fetch(r -> r.into(Organization.class)).stream()
//              .collect(Collectors.toMap(Organization::getId, c -> c));
		//人员
		Map<String, Member> memberMap = memberDao.execute(x -> x.select(MEMBER.ID, MEMBER.FULL_NAME, MEMBER.NAME,MEMBER.MAJOR_POSITION_ID).from(MEMBER)).where(MEMBER.ID.in(memberIds))
              .fetch(r -> {
              	Member m =  r.into(Member.class);
              	pIds.add(r.getValue(MEMBER.MAJOR_POSITION_ID));
              	return m;
              }).stream()
              .collect(Collectors.toMap(Member::getId, l -> l));

		Map<String, Position> positionMap = positionDao.execute(x -> x.select(POSITION.ID,POSITION.JOB_ID).from(POSITION)).where(POSITION.ID.in(pIds))
              .fetch(r ->{
              	Position p = r.into(Position.class);
              	jIds.add(r.getValue(POSITION.JOB_ID));
              	return p;
              }).stream()
              .collect(Collectors.toMap(Position::getId, l -> l));
		List<String> thumbsList = lecturerThumbsUpService.findByMemberId(currentUserId);
		//讲师属性分类
		Map<String, Integer> attributeTypeMap = lecturerAttributeService.findAllInfo().stream()
      		.collect(Collectors.toMap(LecturerAttribute::getId, LecturerAttribute::getTypeId));
		// 讲师职位
		Map<String, Job> jobMap = jobDao.execute(x -> x.select(JOB.ID, JOB.NAME).from(JOB)).where(JOB.ID.in(jIds))
              .fetch(r -> r.into(Job.class)).stream()
              .collect(Collectors.toMap(Job::getId, l -> l));
        // 所有的顶级讲师专业序列
        Map<String, String> parentMap = lecturerCourseConfigService.findNextLevel(Optional.empty(), LecturerCourseConfig.TYPE_LECTURER)
        		.stream().collect(Collectors.toMap(LecturerCourseConfig::getId, LecturerCourseConfig::getName));
        // 所有的讲师属性
		Map<String, String> attributeMap = lecturerAttributeService.findAll();
		// 讲师擅长课程
		Map<String, List<LecturerAdeptCourse>> adeptCourseMap = new HashMap<String, List<LecturerAdeptCourse>>();
		lecturerAdeptCourseService.findAdeptLecturer(listLecturerId).forEach(lac -> {
			if (adeptCourseMap.get(lac.getLecturerId()) != null) {
				adeptCourseMap.get(lac.getLecturerId()).add(lac);
				adeptCourseMap.put(lac.getLecturerId(), adeptCourseMap.get(lac.getLecturerId()));
			} else {
				List<LecturerAdeptCourse> lists = new ArrayList<LecturerAdeptCourse>();
				lists.add(lac);
				adeptCourseMap.put(lac.getLecturerId(), lists);
			}
		});
		lectureList.forEach(f -> {
              String memberId = f.getMemberId();
              String attributeIds = f.getAttributeId();
              String pId = null;
              String jId = null;
//              String lId = f.getLevelId();
              if (memberMap.get(memberId) != null) {
                  f.setMemberName(memberMap.get(memberId).getFullName());
                  f.setNum(memberMap.get(memberId).getName());
                  pId = memberMap.get(memberId).getMajorPositionId();
              }
              if (pId != null && positionMap.get(pId) != null) {
            	  jId = positionMap.get(pId).getJobId();
              }
              if (jId != null && jobMap.get(jId) != null) {
            	  f.setJobName(jobMap.get(jId).getName());
              }
              if (attributeIds != null) {
            	  f.setAttribute(attributeMap.get(attributeIds));
              }
              f.setType(attributeTypeMap.get(attributeIds));
              f.setThumbsUp(thumbsList.contains(f.getId()));
              f.setAdeptCourseList(adeptCourseMap.get(f.getId()));
              f.setParentName(parentMap.get(f.getParentId()));
//              if (lId != null) {
//            	  f.setLevelName(levelMap.get(lId).getName());
//              }

		});
		return PagedResult.create(count, lectureList);

	}
	@Override
	public Lecturer updateLabel(String id, Optional<String> labelIds) {
		Lecturer lecturer = lecturerDao.get(id);
		if (labelIds.isPresent() && lecturer != null) {
			List<Label> execute = labelDao.execute(e -> e.selectDistinct(Fields.start().add(LABEL).end()).from(LABEL)
					.where(LABEL.ID.in(labelIds.get().split(","))).fetch().into(Label.class));
			StringBuffer names = new StringBuffer();
			for (int i = 0; i < execute.size(); i++) {
				names.append(execute.get(i).getName()).append(",");
			}
			if (names.length() > 1) {
				names.deleteCharAt(names.length() - 1);
				lecturer.setLabelNames(names.toString());
				lecturer.setModifyDate(null);
				lecturerDao.update(lecturer);
			}
			lecturerLabelDao.delete(LECTURER_LABEL.LECTURER_ID.eq(id));
			labelIds.ifPresent(ll -> Arrays.stream(ll.split(",")).forEach(r -> {
				LecturerLabel lecturerLabel = new LecturerLabel();
				lecturerLabel.forInsert();
				lecturerLabel.setLabelId(r);
				lecturerLabel.setLecturerId(id);
				lecturerLabelDao.insert(lecturerLabel);
			}));
		} else {
			lecturerLabelDao.delete(LECTURER_LABEL.LECTURER_ID.eq(id));
		}
		return lecturer;
	}
	@Override
	public Lecturer updateDescription(String id, Optional<String> description) {
		Lecturer lecturer = lecturerDao.get(id);
		lecturer.setDescription(description.orElse(null));
		lecturer.setModifyDate(null);
		lecturerDao.update(lecturer);
		return lecturer;
	}
	@Override
	public Lecturer updateExperience(String id, Optional<String> experience) {
		Lecturer lecturer = lecturerDao.get(id);
		lecturer.setLectureExperience(experience.orElse(null));
		lecturer.setModifyDate(null);
		lecturerDao.update(lecturer);
		return lecturer;
	}
	@Override
	public List<Lecturer> goldLecturer(Integer page, Integer pageSize) {

		Table<Record> orgRecord = organizationTeachingDao.execute(otd -> {
			return otd.select(Fields.start()
					.add(ORGANIZATION_TEACHING.LECTURER_ID)
					.add(ORGANIZATION_TEACHING.LECTURER_ID.count().as("orgCount"))
					.end())
				.from(ORGANIZATION_TEACHING)
				.where(ORGANIZATION_TEACHING.APPROVAL_STATUS.eq(OrganizationTeaching.APPROVAL_STATUS_PASS))
				.groupBy(ORGANIZATION_TEACHING.LECTURER_ID)
				.asTable("orgRecord");
		});
		Table<Record> otherRecord = otherTeachingDao.execute(otd -> {
			return otd.select(Fields.start()
					.add(OTHER_TEACHING.LECTURER_ID)
					.add(OTHER_TEACHING.LECTURER_ID.count().as("otherCount"))
					.end())
				.from(OTHER_TEACHING)
				.where(OTHER_TEACHING.APPROVAL_STATUS.eq(OtherTeaching.APPROVAL_STATUS_PASS))
				.groupBy(OTHER_TEACHING.LECTURER_ID)
				.asTable("otherRecord");
		});
		Table<Record> ctRecord = collegeTeachingDao.execute(otd -> {
			return otd.select(Fields.start()
					.add(COLLEGE_TEACHING.LECTURER_ID)
					.add(COLLEGE_TEACHING.LECTURER_ID.count().as("ctCount"))
					.end())
				.from(COLLEGE_TEACHING)
				.groupBy(COLLEGE_TEACHING.LECTURER_ID)
				.asTable("ctRecord");
		});
		Field<Integer> countColumn =
				DSL.ifnull(orgRecord.field(ORGANIZATION_TEACHING.LECTURER_ID.count().as("orgCount")), 0)
				.add(DSL.ifnull(otherRecord.field(ORGANIZATION_TEACHING.LECTURER_ID.count().as("otherCount")), 0))
				.add(DSL.ifnull(ctRecord.field(ORGANIZATION_TEACHING.LECTURER_ID.count().as("ctCount")), 0))
//				orgRecord.field(ORGANIZATION_TEACHING.LECTURER_ID.count().as("orgCount"))
//				.add(otherRecord.field(OTHER_TEACHING.LECTURER_ID.count().as("otherCount")))
//				.add(ctRecord.field(COLLEGE_TEACHING.LECTURER_ID.count().as("ctCount")))
				.as("count");
		return lecturerDao.execute(dao -> {
			return dao.select(Fields.start().add(
						LECTURER.ID,
						LECTURER.NAME,
						LECTURER.HEAD_PORTRAIT,
						LECTURER.COVER_PATH,
						LECTURER.TYPE,
						LECTURER.ATTRIBUTE_ID,
						LECTURER_ATTRIBUTE.ATTRIBUTE_NAME,
						countColumn
//						otherRecord.field(OTHER_TEACHING.LECTURER_ID.count().as("otherCount")),
//						ctRecord.field(COLLEGE_TEACHING.LECTURER_ID.count().as("ctCount"))
					).end())
			.from(LECTURER)
			.leftJoin(orgRecord).on(orgRecord.field(ORGANIZATION_TEACHING.LECTURER_ID).eq(LECTURER.ID))
			.leftJoin(otherRecord).on(otherRecord.field(OTHER_TEACHING.LECTURER_ID).eq(LECTURER.ID))
			.leftJoin(ctRecord).on(ctRecord.field(COLLEGE_TEACHING.LECTURER_ID).eq(LECTURER.ID))
			.leftJoin(LECTURER_ATTRIBUTE).on(LECTURER_ATTRIBUTE.ID.eq(LECTURER.ATTRIBUTE_ID))
			.where(LECTURER.RELEASE_STATUS.eq(Lecturer.RELEASE_STATUS_YES))
			.groupBy(LECTURER.ID)
			.orderBy(countColumn.desc(),LECTURER.CREATE_TIME.desc())
			.limit((page-1)*pageSize,pageSize)
			.fetch(item -> {
				Lecturer lecturer = item.into(Lecturer.class);
				lecturer.setLectureNumber(item.get(countColumn));
				lecturer.setAttribute(item.get(LECTURER_ATTRIBUTE.ATTRIBUTE_NAME));
				return lecturer;
			});
		});
	}
	@Override
	public Integer releaseStatus(String ids, Integer status) {
		return lecturerDao.execute(dao -> {
			//删除讲师榜讲师
			if (Lecturer.RELEASE_STATUS_NO.equals(status)) {
				homeLecturerDao.delete(HOME_LECTURER.LECTURE_ID.in(ids.split(",")));
				deleteDataTrainCommonDao.insert(DeleteDataTrain.getDeleteDataList(DeleteDataTrain.getTableName(HOME_LECTURER),new ArrayList<>(Arrays.asList(ids.split(","))),""));
			}
			return dao.update(LECTURER).set(LECTURER.RELEASE_STATUS, status)
				.where(LECTURER.ID.in(Arrays.asList(ids.split(","))))
				.execute();
		});
	}
	@Override
	public void updateLectureNumberToId(String lecturerId) {
		int count = organizationTeachingDao.count(ORGANIZATION_TEACHING.LECTURER_ID.eq(lecturerId))
					+ otherTeachingDao.count(OTHER_TEACHING.LECTURER_ID.eq(lecturerId))
					+ collegeTeachingDao.count(COLLEGE_TEACHING.LECTURER_ID.eq(lecturerId));
		lecturerDao.execute(dao -> {
			return dao.update(LECTURER)
					.set(LECTURER.LECTURE_NUMBER, count)
					.where(LECTURER.ID.eq(lecturerId))
					.execute();
		});
	}
	@Override
	public void updateLectureNumberToCourseId(String courseId) {
		List<OrganizationTeaching> lecturerList = organizationTeachingDao.fetch(ORGANIZATION_TEACHING.COURSE_ID.eq(courseId));
		if (lecturerList != null && lecturerList.size() > 0) {
			for (OrganizationTeaching lecturer:lecturerList) {
				updateLectureNumberToId(lecturer.getId());
			}
		}
	}
	@Override
	public Lecturer getBasics(String memberId) {
		try {
			return lecturerDao.fetchOne(LECTURER.MEMBER_ID.eq(memberId)).get();
		} catch (Exception e) {
			return null;
		}
	}
	@Override
	public List<Lecturer> findExportLecturer(String currentMemberId,
			Optional<String> organizationId, Optional<String> name, Optional<Integer> type, Optional<Integer> status,
			Optional<String> levelId, Optional<String> className, Optional<String> courseName,
			Optional<String> parentId, Optional<String> sequenceId, Optional<String> label,
			Optional<String> attributeId, Optional<String> unit, Optional<String> courseType, Optional<String> courseId,
			Optional<List<String>> organizationIds) {
      //获取面授课程库课程列表
      List<String> courseList = new ArrayList<String>();
      Condition courseIdsDefalut = DSL.trueCondition();
      if (courseName.isPresent() || courseType.isPresent()) {
      	courseList = f2fCourseLibraryDao.execute(dao -> {
      		Condition conditions = F2F_COURSE_LIBRARY.IS_USE.eq(F2fCourseLibrary.IS_USE_TRUE)
      				.and(courseType.map(F2F_COURSE_LIBRARY.SEQUENCE::eq).orElse(DSL.trueCondition()))
      				.and(courseName.map(F2F_COURSE_LIBRARY.NAME::contains).orElse(DSL.trueCondition()));
      		return dao.select(Fields.start().add(F2F_COURSE_LIBRARY.ID).end())
		        		.from(F2F_COURSE_LIBRARY)
		        		.where(conditions)
		        		.fetch(item -> {
		        			return String.valueOf(item.get(F2F_COURSE_LIBRARY.ID));
		        		});
      	});
      	courseIdsDefalut = courseList.size() > 0 ?
      			LECTURER_ADEPT_COURSE.COURSE_ID.in(courseList)
      			.or(COLLEGE_TEACHING.COURSE_ID.in(courseList))
      			.or(ORGANIZATION_TEACHING.COURSE_ID.in(courseList)
      					.and(ORGANIZATION_TEACHING.APPROVAL_STATUS.eq(OrganizationTeaching.APPROVAL_STATUS_PASS))
  					)
      			: DSL.falseCondition();
		}
      	Condition courseIds = courseId.isPresent() ? courseIdsDefalut.and(
      			LECTURER_ADEPT_COURSE.COURSE_ID.eq(courseId.get())
//	        		COLLEGE_TEACHING.COURSE_ID.eq(courseId.get())
//	        		.or(ORGANIZATION_TEACHING.COURSE_ID.eq(courseId.get()))
      		) : courseIdsDefalut;
		Condition unitCent = unit.isPresent() ? courseIds.and(LECTURER.UNIT.contains(unit.get()).or(LECTURER.INSTITUTIONS.contains(unit.get()))) : courseIds;
		return lecturerDao.execute(d -> {
          Table<Record1<String>> basic = (d.select(LECTURER.ID)
                  .from(LECTURER)
                  .leftJoin(CLASS_OFFLINE_COURSE).on(LECTURER.ID.eq(CLASS_OFFLINE_COURSE.TEACHER_ID))
                  .leftJoin(CLASS_INFO).on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID))
                  .leftJoin(MEMBER).on(LECTURER.MEMBER_ID.eq(MEMBER.ID))

                  .leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
                  .leftJoin(LECTURER_LABEL).on(LECTURER.ID.eq(LECTURER_LABEL.LECTURER_ID))
                  .leftJoin(LABEL).on(LECTURER_LABEL.LABEL_ID.eq(LABEL.ID))

                  .leftJoin(LECTURER_ADEPT_COURSE).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
                  .leftJoin(COLLEGE_TEACHING).on(COLLEGE_TEACHING.LECTURER_ID.eq(LECTURER.ID))
                  .leftJoin(ORGANIZATION_TEACHING).on(ORGANIZATION_TEACHING.LECTURER_ID.eq(LECTURER.ID))

                  .where(name.map(LECTURER.NAME::contains).orElse(DSL.trueCondition()))
                  .and(levelId.map(LECTURER.LEVEL_ID::eq).orElse(DSL.trueCondition()))
                  .and(className.map(
                  		c ->
                  			CLASS_INFO.PROJECT_ID.in(projectDao.fetch(PROJECT.NAME.contains(c)).stream().map(Project::getId).collect(Collectors.toList()))
                  			.or(ORGANIZATION_TEACHING.CLASS_NAME.contains(c))
                  			)
                  		.orElse(DSL.trueCondition())
              		)
                  .and(type.map(LECTURER.TYPE::eq).orElse(DSL.trueCondition()))
                  .and(status.map(LECTURER.STATUS::eq).orElse(DSL.trueCondition()))

                  .and(parentId.map(LECTURER_COURSE_CONFIG.PARENT_ID::eq).orElse(DSL.trueCondition()))
                  .and(sequenceId.map(LECTURER.SEQUENCE_ID::eq).orElse(DSL.trueCondition()))
                  .and(label.isPresent()?LABEL.NAME.contains(label.get()):DSL.trueCondition())
                  .and(attributeId.map(LECTURER.ATTRIBUTE_ID::eq).orElse(DSL.trueCondition()))

                  .and(unitCent)
                  // 归属权限限制
                  .and(LECTURER.ASCRIPTION_ORGANIZATION_ID.in(organizationIds.get()))
                  .groupBy(LECTURER.ID)
			).asTable("b");
          Field<String> sequenceName = LECTURER_COURSE_CONFIG.NAME.as("sequenceName");
          Field<String> attributeName = LECTURER_COURSE_CONFIG.NAME.as("attributeName");
  		Field<String> organizationCompanyId = ORGANIZATION.COMPANY_ID.as("organizationCompanyId");
        //组织
        Set<String> orgIds = new HashSet<String>();
      	Set<String> levelIds = new HashSet<String>();
		List<Lecturer> list = d.select(Fields.start()
					.add(LECTURER).add(sequenceName).add(organizationCompanyId)
					.add(attributeName)
					.end())
					.from(LECTURER)
					.innerJoin(basic).on(basic.field(LECTURER.ID).eq(LECTURER.ID))
					.leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER.SEQUENCE_ID.eq(LECTURER_COURSE_CONFIG.ID))
					.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(LECTURER.ORGANIZATION_ID))
					.leftJoin(LECTURER_ATTRIBUTE).on(LECTURER_ATTRIBUTE.ID.eq(LECTURER.ATTRIBUTE_ID))
					.orderBy(LECTURER.STATUS.asc(),LECTURER.CREATE_TIME.desc(),LECTURER.MOBILE.desc())
					.fetch(r -> {
						Lecturer l = r.into(Lecturer.class);
						l.setSequenceName(r.getValue(sequenceName));
						l.setOrganizationCompanyId(r.getValue(organizationCompanyId));
						l.setAttribute(r.getValue(attributeName));
   						orgIds.add(r.getValue(LECTURER.ASCRIPTION_ORGANIZATION_ID));
   						orgIds.add(r.getValue(organizationCompanyId));
   						if(Lecturer.TYPE_INSIDE == r.getValue(LECTURER.TYPE)){
   							levelIds.add(r.getValue(LECTURER.LEVEL_ID));
   							orgIds.add(r.getValue(LECTURER.ORGANIZATION_ID));
   						}
 		                return l;
 					});

 			//level
 	        Map<String, Level> levelMap = levelDao.execute(x -> x.select(LEVEL.ID, LEVEL.NAME).from(LEVEL)).where(LEVEL.ID.in(levelIds))
 	                .fetch(r -> r.into(Level.class)).stream()
 	        .collect(Collectors.toMap(Level::getId, l -> l));
 	        //组织
 			Map<String, Organization> orgMap = organizationCommonDao.execute(x -> x.select(ORGANIZATION.NAME, ORGANIZATION.ID, ORGANIZATION.COMPANY_ID).from(ORGANIZATION)).where(ORGANIZATION.ID.in(orgIds))
 					.fetch(r -> r.into(Organization.class)).stream()
 					.collect(Collectors.toMap(Organization::getId, c -> c));
 			list = list.stream().map(mapper -> {
 				String orgId = mapper.getOrganizationId();
 				String ascriptionOrgId = mapper.getAscriptionOrganizationId();
 				String level_Id = mapper.getLevelId();
 				if(levelMap.get(level_Id) != null){
 					mapper.setLevelName(levelMap.get(level_Id).getName());
 				}
                 if (orgMap.get(orgId) != null) {
                 	mapper.setOrganizationName(orgMap.get(orgId).getName());
                     if (orgMap.get(orgMap.get(orgId).getCompanyId()).getId() != null) {
                     	mapper.setUnit(orgMap.get(orgMap.get(orgId).getCompanyId()).getName());
 					}
                 }
                 mapper.setAscriptionOrganizationName(orgMap.get(ascriptionOrgId).getName());
 				return mapper;
 			}).collect(Collectors.toList());
 			return list;
 		});
	}

	@Override
	public Integer findLecturerRecordCount(List<String> lecturerIds) {

		Table<Record> orgRecord = organizationTeachingDao.execute(otd -> {
			return otd.select(Fields.start()
					.add(ORGANIZATION_TEACHING.LECTURER_ID)
					.add(ORGANIZATION_TEACHING.LECTURER_ID.count().as("orgCount"))
					.end())
				.from(ORGANIZATION_TEACHING)
				.where(ORGANIZATION_TEACHING.APPROVAL_STATUS.eq(OrganizationTeaching.APPROVAL_STATUS_PASS))
				.and(ORGANIZATION_TEACHING.LECTURER_ID.in(lecturerIds))
				.asTable("orgRecord");
		});
		Table<Record> otherRecord = otherTeachingDao.execute(otd -> {
			return otd.select(Fields.start()
					.add(OTHER_TEACHING.LECTURER_ID)
					.add(OTHER_TEACHING.LECTURER_ID.count().as("otherCount"))
					.end())
				.from(OTHER_TEACHING)
				.where(OTHER_TEACHING.APPROVAL_STATUS.eq(OtherTeaching.APPROVAL_STATUS_PASS))
				.and(OTHER_TEACHING.LECTURER_ID.in(lecturerIds))
				.asTable("otherRecord");
		});
		Table<Record> ctRecord = collegeTeachingDao.execute(otd -> {
			return otd.select(Fields.start()
					.add(COLLEGE_TEACHING.LECTURER_ID)
					.add(COLLEGE_TEACHING.LECTURER_ID.count().as("ctCount"))
					.end())
				.from(COLLEGE_TEACHING)
				.where(COLLEGE_TEACHING.LECTURER_ID.in(lecturerIds))
				.asTable("ctRecord");
		});
		Field<BigDecimal> countColumn = DSL.ifnull(orgRecord.field(ORGANIZATION_TEACHING.LECTURER_ID.count().as("orgCount")), 0)
		.add(DSL.ifnull(otherRecord.field(ORGANIZATION_TEACHING.LECTURER_ID.count().as("otherCount")), 0))
		.add(DSL.ifnull(ctRecord.field(ORGANIZATION_TEACHING.LECTURER_ID.count().as("ctCount")), 0))
		.sum().as("count");
		return lecturerDao.execute(dao -> {
			return dao.select(Fields.start().add(countColumn).end())
			.from(LECTURER)
			.leftJoin(orgRecord).on(orgRecord.field(ORGANIZATION_TEACHING.LECTURER_ID).eq(LECTURER.ID))
			.leftJoin(otherRecord).on(otherRecord.field(OTHER_TEACHING.LECTURER_ID).eq(LECTURER.ID))
			.leftJoin(ctRecord).on(ctRecord.field(COLLEGE_TEACHING.LECTURER_ID).eq(LECTURER.ID))
			.fetchOne(item -> {
				return item.getValue(countColumn).intValueExact();
			});
		});
	}
	@Override
	public List<Lecturer> findLecturerByIds(List<String> lecturerIds) {
		return lecturerDao.execute(dao -> {
 			Set<String> orgIds = new HashSet<String>();
 	        Set<String> levelIds = new HashSet<String>();
 	        Field<String> organizationCompanyId = ORGANIZATION.COMPANY_ID.as("organizationCompanyId");
 	        List<Lecturer> list = dao.select(Fields.start().add(
					LECTURER.ID,
					LECTURER.NAME,
					LECTURER.TYPE,
					LECTURER.LEVEL_ID,
					LECTURER.SEX,
					LECTURER.EMAIL,
					LECTURER.MOBILE,
					LECTURER.UNIT,
					LECTURER.JOB_NAME,
 					LECTURER.INSTITUTIONS,
 					LECTURER.ORGANIZATION_ID,
 					LECTURER.ASCRIPTION_ORGANIZATION_ID,
					ORGANIZATION.NAME,
 					organizationCompanyId,
					LECTURER_COURSE_CONFIG.NAME,
					MEMBER.NAME,
					MEMBER.EMAIL,
					MEMBER.SEX,
					MEMBER.PHONE_NUMBER
					).end())
			.from(LECTURER)
			.leftJoin(LECTURER_COURSE_CONFIG).on(LECTURER_COURSE_CONFIG.ID.eq(LECTURER.SEQUENCE_ID))
			.leftJoin(MEMBER).on(MEMBER.ID.eq(LECTURER.MEMBER_ID))
			.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
			.where(LECTURER.ID.in(lecturerIds))
			.fetch(item -> {
				Lecturer lecturer = item.into(Lecturer.class);
				lecturer.setSequenceName(item.getValue(LECTURER_COURSE_CONFIG.NAME));
 				orgIds.add(item.getValue(LECTURER.ASCRIPTION_ORGANIZATION_ID));
 				orgIds.add(item.getValue(organizationCompanyId));
				if(Lecturer.TYPE_INSIDE.equals(item.getValue(LECTURER.TYPE))){
 					levelIds.add(item.getValue(LECTURER.LEVEL_ID));
 					orgIds.add(item.getValue(LECTURER.ORGANIZATION_ID));
					lecturer.setName(item.getValue(MEMBER.NAME));
					lecturer.setEmail(SM4Utils.decryptDataCBC(item.getValue(MEMBER.EMAIL)));
					lecturer.setSex(item.getValue(MEMBER.SEX));
					lecturer.setMobile(SM4Utils.decryptDataCBC(item.getValue(MEMBER.PHONE_NUMBER)));
				}
 				if (Lecturer.TYPE_EXTERNAL.equals(item.getValue(LECTURER.TYPE))){
 					lecturer.setOrganizationName(item.getValue(ORGANIZATION.NAME));
 					lecturer.setUnit(item.getValue(LECTURER.INSTITUTIONS));
 				}
				return lecturer;
			});//level
 	        Map<String, Level> levelMap = levelDao.execute(x -> x.select(LEVEL.ID, LEVEL.NAME).from(LEVEL)).where(LEVEL.ID.in(levelIds))
 	                .fetch(r -> r.into(Level.class)).stream()
 	        .collect(Collectors.toMap(Level::getId, l -> l));
 	        //组织
 			Map<String, Organization> orgMap = organizationCommonDao.execute(x -> x.select(ORGANIZATION.NAME, ORGANIZATION.ID, ORGANIZATION.COMPANY_ID).from(ORGANIZATION)).where(ORGANIZATION.ID.in(orgIds))
 					.fetch(r -> r.into(Organization.class)).stream()
 					.collect(Collectors.toMap(Organization::getId, c -> c));
 			list = list.stream().map(mapper -> {
 				String orgId = mapper.getOrganizationId();
 				String ascriptionOrgId = mapper.getAscriptionOrganizationId();
 				String level_Id = mapper.getLevelId();
 				if(levelMap.get(level_Id) != null){
 					mapper.setLevelName(levelMap.get(level_Id).getName());
 				}
                 if (orgMap.get(orgId) != null && orgMap.get(orgMap.get(orgId).getCompanyId()) != null) {
                 	mapper.setOrganizationName(orgMap.get(orgId).getName());
                     if (orgMap.get(orgMap.get(orgId).getCompanyId()).getId() != null) {
                     	mapper.setUnit(orgMap.get(orgMap.get(orgId).getCompanyId()).getName());
 					}
                 }
                 mapper.setAscriptionOrganizationName(orgMap.get(ascriptionOrgId).getName());
 				return mapper;
 			}).collect(Collectors.toList());
 	        return list;
		});
	}

}
