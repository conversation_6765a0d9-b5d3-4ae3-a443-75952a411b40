package com.zxy.product.train.service.support;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.SelectOnConditionStep;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.ClassHistoryService;
import com.zxy.product.train.entity.ClassHistory;
import com.zxy.product.train.entity.ProjectHistory;
import com.zxy.product.train.entity.StudentHistory;

import static com.zxy.product.train.jooq.Tables.CLASS_HISTORY;
import static com.zxy.product.train.jooq.Tables.STUDENT_HISTORY;
import static com.zxy.product.train.jooq.Tables.PROJECT_HISTORY;
import static com.zxy.product.train.jooq.Tables.ORGANIZATION;
import static com.zxy.product.train.jooq.Tables.MEMBER;

/**
 * 历史班级api
 * 
 * @ClassName: ClassHistoryServiceSupport
 * @author: Acong
 * @date: 2018年1月26日
 */
@Service
public class ClassHistoryServiceSupport implements ClassHistoryService {

	private CommonDao<ClassHistory> dao;

	@Autowired
	public void setDao(CommonDao<ClassHistory> dao) {
		this.dao = dao;
	}

	@Override
	public PagedResult<ClassHistory> findByPersonId(List<String> personId, int page, int pageSize) {
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		SelectConditionStep<Record> step = dao.execute(x->x.selectDistinct(Fields.start().add(CLASS_HISTORY.ID,PROJECT_HISTORY.NAME,
				orgName,CLASS_HISTORY.ARRIVE_DATE,CLASS_HISTORY.RETURN_DATE).end()).from(STUDENT_HISTORY)
				.leftJoin(CLASS_HISTORY).on(CLASS_HISTORY.ID.eq(STUDENT_HISTORY.CLASS_ID))
				.leftJoin(PROJECT_HISTORY).on(CLASS_HISTORY.PROJECT_ID.eq(PROJECT_HISTORY.ID))
				.leftJoin(ORGANIZATION).on(PROJECT_HISTORY.ORGANIZATION_ID.eq(ORGANIZATION.ID))
				.where(STUDENT_HISTORY.PERSON_ID.in(personId))).and(STUDENT_HISTORY.STATUS.eq(StudentHistory.STATUS_YES));
		
		Integer count = dao.execute(x->x.fetchCount(step));
		
		List<ClassHistory> list = step.orderBy(CLASS_HISTORY.ARRIVE_DATE.desc()).limit((page - 1) * pageSize, pageSize).fetch(r->{
			ClassHistory ch = new ClassHistory();
			ch.setId(r.getValue(CLASS_HISTORY.ID));
			ch.setProjectName(r.getValue(PROJECT_HISTORY.NAME));
			ch.setArriveDate(r.getValue(CLASS_HISTORY.ARRIVE_DATE));
			ch.setReturnDate(r.getValue(CLASS_HISTORY.RETURN_DATE));
			ch.setOrganizationName(r.getValue(orgName));
			return ch;
		});
		
		return PagedResult.create(count, list);
	}

	@Override
	public List<ClassHistory> findByPersonId(List<String> personId) {
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");
		return dao.execute(x->x.selectDistinct(Fields.start().add(CLASS_HISTORY.ID,PROJECT_HISTORY.NAME,
				orgName,CLASS_HISTORY.ARRIVE_DATE,CLASS_HISTORY.RETURN_DATE).end()).from(STUDENT_HISTORY)
				.leftJoin(CLASS_HISTORY).on(CLASS_HISTORY.ID.eq(STUDENT_HISTORY.CLASS_ID))
				.leftJoin(PROJECT_HISTORY).on(CLASS_HISTORY.PROJECT_ID.eq(PROJECT_HISTORY.ID))
				.leftJoin(ORGANIZATION).on(PROJECT_HISTORY.ORGANIZATION_ID.eq(ORGANIZATION.ID))
				.where(STUDENT_HISTORY.PERSON_ID.in(personId))).and(STUDENT_HISTORY.STATUS.eq(StudentHistory.STATUS_YES))
				.orderBy(CLASS_HISTORY.ARRIVE_DATE.desc()).fetch(r->{
					ClassHistory ch = new ClassHistory();
					ch.setId(r.getValue(CLASS_HISTORY.ID));
					ch.setProjectName(r.getValue(PROJECT_HISTORY.NAME));
					ch.setArriveDate(r.getValue(CLASS_HISTORY.ARRIVE_DATE));
					ch.setReturnDate(r.getValue(CLASS_HISTORY.RETURN_DATE));
					ch.setOrganizationName(r.getValue(orgName));
					return ch;
				});
	}
	
	@Override
	public PagedResult<ClassHistory> list(int page, int pageSize, Optional<String> name, Optional<String> MIScode,
			Optional<String> memberName, Optional<String> memberFullName) {
		
		Field<String> projectId = PROJECT_HISTORY.ID.as("projectId");
		Field<String> orgName = ORGANIZATION.NAME.as("orgName");

		
		if(memberName.isPresent()||memberFullName.isPresent()){
			// 构建语句
			SelectOnConditionStep<Record> step = dao.execute(x->x.selectDistinct(
				Fields.start().add(CLASS_HISTORY.ID,CLASS_HISTORY.ARRIVE_DATE,CLASS_HISTORY.RETURN_DATE,
						CLASS_HISTORY.CLASS_TEACHER,projectId,PROJECT_HISTORY.NAME,PROJECT_HISTORY.CODE,
						orgName,PROJECT_HISTORY.CONTACT_MEMBER_ID).end()).from(MEMBER)
				.leftJoin(STUDENT_HISTORY).on(STUDENT_HISTORY.PERSON_ID.eq(MEMBER.ID))
				.leftJoin(CLASS_HISTORY).on(STUDENT_HISTORY.CLASS_ID.eq(CLASS_HISTORY.ID))
				.leftJoin(PROJECT_HISTORY).on(CLASS_HISTORY.PROJECT_ID.eq(PROJECT_HISTORY.ID))
				.leftJoin(ORGANIZATION).on(PROJECT_HISTORY.ORGANIZATION_ID.eq(ORGANIZATION.ID)));

			// 组合条件
			Stream<Optional<Condition>> conditions = Stream.of(
					name.map(PROJECT_HISTORY.NAME::contains),MIScode.map(PROJECT_HISTORY.CODE::contains),
					memberName.map(MEMBER.NAME::contains),memberFullName.map(MEMBER.FULL_NAME::contains));

			// 合并条件
			Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
					.orElse(DSL.trueCondition());

			// 获取行数
			Integer count = dao.execute(x->x.fetchCount(step.where(c)));
			
			// 获取列表
			List<ClassHistory> list = step.where(c).orderBy(CLASS_HISTORY.ARRIVE_DATE.desc()).limit((page - 1) * pageSize, pageSize).fetch(r->{
				ClassHistory ch = new ClassHistory();
				ch.setId(r.getValue(CLASS_HISTORY.ID));
				ch.setArriveDate(r.getValue(CLASS_HISTORY.ARRIVE_DATE));
				ch.setReturnDate(r.getValue(CLASS_HISTORY.RETURN_DATE));
				ch.setClassTeacher(r.getValue(CLASS_HISTORY.CLASS_TEACHER));
				ProjectHistory ph = new ProjectHistory();
				ph.setId(r.getValue(projectId));
				ph.setName(r.getValue(PROJECT_HISTORY.NAME));
				ph.setCode(r.getValue(PROJECT_HISTORY.CODE));
				ph.setOrganizationName(r.getValue(orgName));

				ph.setContactMemberFullName(r.getValue(PROJECT_HISTORY.CONTACT_MEMBER_ID));
				ch.setProjectHistory(ph);
				return ch;
			});
			
			return PagedResult.create(count, list);
		}else {
			// 构建语句
			SelectOnConditionStep<Record> step = dao.execute(x->x.selectDistinct(
				Fields.start().add(CLASS_HISTORY.ID,CLASS_HISTORY.ARRIVE_DATE,CLASS_HISTORY.RETURN_DATE,
				CLASS_HISTORY.CLASS_TEACHER,projectId,PROJECT_HISTORY.NAME,PROJECT_HISTORY.CODE,

				orgName,PROJECT_HISTORY.CONTACT_MEMBER_ID).end()).from(CLASS_HISTORY)
				.leftJoin(PROJECT_HISTORY).on(CLASS_HISTORY.PROJECT_ID.eq(PROJECT_HISTORY.ID))
				.leftJoin(ORGANIZATION).on(PROJECT_HISTORY.ORGANIZATION_ID.eq(ORGANIZATION.ID)));
			// 组合条件
			Stream<Optional<Condition>> conditions = Stream.of(
					name.map(PROJECT_HISTORY.NAME::contains),MIScode.map(PROJECT_HISTORY.CODE::contains));

			// 合并条件
			Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
					.orElse(DSL.trueCondition());

			// 获取行数
			Integer count = dao.execute(x->x.fetchCount(step.where(c)));
			
			// 获取列表
			List<ClassHistory> list = step.where(c).orderBy(CLASS_HISTORY.ARRIVE_DATE.desc()).limit((page - 1) * pageSize, pageSize).fetch(r->{
				ClassHistory ch = new ClassHistory();
				ch.setId(r.getValue(CLASS_HISTORY.ID));
				ch.setArriveDate(r.getValue(CLASS_HISTORY.ARRIVE_DATE));
				ch.setReturnDate(r.getValue(CLASS_HISTORY.RETURN_DATE));
				ch.setClassTeacher(r.getValue(CLASS_HISTORY.CLASS_TEACHER));
				ProjectHistory ph = new ProjectHistory();
				ph.setId(r.getValue(projectId));
				ph.setName(r.getValue(PROJECT_HISTORY.NAME));
				ph.setCode(r.getValue(PROJECT_HISTORY.CODE));
				ph.setOrganizationName(r.getValue(orgName));

				ph.setContactMemberFullName(r.getValue(PROJECT_HISTORY.CONTACT_MEMBER_ID));
				ch.setProjectHistory(ph);
				return ch;
			});
			
			return PagedResult.create(count, list);
		}
	}


}
