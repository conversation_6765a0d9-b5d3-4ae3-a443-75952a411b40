package com.zxy.product.train.service.support;

import static com.zxy.product.train.jooq.Tables.COURSE_ATTRIBUTE;
import static com.zxy.product.train.jooq.Tables.F2F_COURSE_LIBRARY;
import static com.zxy.product.train.jooq.Tables.LECTURER;
import static com.zxy.product.train.jooq.Tables.LECTURER_ADEPT_COURSE;
import static com.zxy.product.train.jooq.Tables.LECTURER_ATTRIBUTE;
import static com.zxy.product.train.jooq.Tables.LECTURER_COURSE_CONFIG;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.train.api.LecturerAdeptCourseService;
import com.zxy.product.train.entity.LecturerAdeptCourse;

/**
 * 讲师擅长课程列表
 * <AUTHOR>
 *
 */
@Service
public class LecturerAdeptCourseServiceSupport implements LecturerAdeptCourseService {
	
    private CommonDao<LecturerAdeptCourse> lecturerAdeptCourseDao;

    @Autowired
	public void setLecturerAdeptCourseDao(CommonDao<LecturerAdeptCourse> lecturerAdeptCourseDao) {
		this.lecturerAdeptCourseDao = lecturerAdeptCourseDao;
	}

	@Override
	public PagedResult<LecturerAdeptCourse> findToCourse(Optional<String> lecturerId,Optional<Integer> page,Optional<Integer> pageSize,List<String> organizationIds) {
		int start = page.get() <= 1 ? 0 : page.get() * pageSize.get();
		List<LecturerAdeptCourse> list = lecturerAdeptCourseDao.execute(dao -> {
			return dao.select(Fields.start().add(F2F_COURSE_LIBRARY.NAME,
										  LECTURER_COURSE_CONFIG.NAME,
										  LECTURER_ADEPT_COURSE.REFERENCE_TIME,
										  COURSE_ATTRIBUTE.ID,
										  COURSE_ATTRIBUTE.ATTRIBUTE_NAME,
										  LECTURER_ADEPT_COURSE.REFERENCE_REMUNERATION,
										  LECTURER_ADEPT_COURSE.COURSE_URL,
										  LECTURER_ADEPT_COURSE.ID,
										  LECTURER_ADEPT_COURSE.COURSE_ID,
										  LECTURER_ADEPT_COURSE.LECTURER_ID,
										  F2F_COURSE_LIBRARY.IS_SHARE,
										  F2F_COURSE_LIBRARY.ORGANIZATION_ID,
										  F2F_COURSE_LIBRARY.INSTITUTION_ID
										  ).end())
			.from(LECTURER_ADEPT_COURSE)
			.leftJoin(F2F_COURSE_LIBRARY).on(LECTURER_ADEPT_COURSE.COURSE_ID.eq(F2F_COURSE_LIBRARY.ID))
			.leftJoin(COURSE_ATTRIBUTE).on(F2F_COURSE_LIBRARY.COURSE_ATTRIBUTES.eq(COURSE_ATTRIBUTE.ID))
			.leftJoin(LECTURER_COURSE_CONFIG).on(F2F_COURSE_LIBRARY.SEQUENCE.eq(LECTURER_COURSE_CONFIG.ID))
			.where(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(lecturerId.get()))
			.orderBy(LECTURER_ADEPT_COURSE.CREATE_TIME.asc())
			.limit(start,pageSize.get())
			.fetch(item -> {
				LecturerAdeptCourse lecturerAdeptCourse = item.into(LecturerAdeptCourse.class);
				lecturerAdeptCourse.setCourseName(item.get(F2F_COURSE_LIBRARY.NAME));
				lecturerAdeptCourse.setCourseType(item.get(LECTURER_COURSE_CONFIG.NAME));
				lecturerAdeptCourse.setCourseAttribute(item.get(COURSE_ATTRIBUTE.ATTRIBUTE_NAME));
				lecturerAdeptCourse.setIsShare(item.get(F2F_COURSE_LIBRARY.IS_SHARE));
				lecturerAdeptCourse.setCourseAttributeId(item.get(COURSE_ATTRIBUTE.ID));
				lecturerAdeptCourse.setInstitutionId(item.get(F2F_COURSE_LIBRARY.INSTITUTION_ID));
				if (item.getValue(F2F_COURSE_LIBRARY.IS_SHARE) != null && item.getValue(F2F_COURSE_LIBRARY.IS_SHARE) == 0) {
					lecturerAdeptCourse.setIsLook(true);
				} else if(organizationIds.contains(item.getValue(F2F_COURSE_LIBRARY.ORGANIZATION_ID))){
					lecturerAdeptCourse.setIsLook(true);
				} else {
					lecturerAdeptCourse.setIsLook(false);
				}
				return lecturerAdeptCourse;
			});
		});
		int count = lecturerAdeptCourseDao.count(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(lecturerId.get()));
		return PagedResult.create(count, list);
	}


	@Override
	public PagedResult<LecturerAdeptCourse> findToLecturer(Optional<String> courseId,Optional<Integer> page,Optional<Integer> pageSize) {
		int start = page.get() <= 1 ? 0 : page.get() * pageSize.get();
		List<LecturerAdeptCourse> list = lecturerAdeptCourseDao.execute(dao -> {
			return dao.select(Fields.start().add(LECTURER.NAME,
										  LECTURER_ATTRIBUTE.ATTRIBUTE_NAME,
										  LECTURER_ADEPT_COURSE.REFERENCE_TIME,
										  LECTURER_ADEPT_COURSE.REFERENCE_REMUNERATION,
										  LECTURER_ADEPT_COURSE.COURSE_URL
										  ).end())
			.from(LECTURER_ADEPT_COURSE)
			.leftJoin(LECTURER).on(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(LECTURER.ID))
			.leftJoin(LECTURER_ATTRIBUTE).on(LECTURER.ATTRIBUTE_ID.eq(LECTURER_ATTRIBUTE.ID))
			.where(LECTURER_ADEPT_COURSE.COURSE_ID.eq(courseId.get()))
			.orderBy(LECTURER_ADEPT_COURSE.CREATE_TIME.asc())
			.limit(start,pageSize.get())
			.fetch(item -> {
				LecturerAdeptCourse LecturerAdeptCourse = item.into(LecturerAdeptCourse.class);
				LecturerAdeptCourse.setLecturerName(item.get(LECTURER.NAME));
				LecturerAdeptCourse.setLecturerType(item.get(LECTURER_ATTRIBUTE.ATTRIBUTE_NAME));
				return LecturerAdeptCourse;
			});
		});
		int count = lecturerAdeptCourseDao.count(LECTURER_ADEPT_COURSE.COURSE_ID.eq(courseId.get()));
		return PagedResult.create(count, list);
	}

	@Override
    public int checkCode(Optional<String> courseId,Optional<String> lecturerId) {
        return lecturerAdeptCourseDao.execute(d -> {
            return d.selectCount().from(LECTURER_ADEPT_COURSE)
            .where(LECTURER_ADEPT_COURSE.COURSE_ID.eq(courseId.get()))
            .and(LECTURER_ADEPT_COURSE.LECTURER_ID.eq(lecturerId.get()))
            .fetchOne(0, Integer.class);
        });
    }

	@Override
	public Map<String, Object> delete(Optional<String> id) {
		lecturerAdeptCourseDao.delete(id.get());
		Map<String,Object> jsonObject = new HashMap<String,Object>();
		jsonObject.put("success", true);
		jsonObject.put("num", 1);
		return jsonObject;
	}

	@Override
	public LecturerAdeptCourse choiceSave(Optional<String> lecturerId, Optional<String> courseId,
			Optional<String> referenceTime, Optional<String> referenceRemuneration, Optional<String> courseUrl,
			String attachmentId, String attachmentName, String memberId) {
		return saveAdeptRecord(lecturerId, courseId, referenceTime, referenceRemuneration, courseUrl, attachmentId, attachmentName, memberId);
	}
	
	private LecturerAdeptCourse saveAdeptRecord(Optional<String> lecturerId, Optional<String> courseId,
			Optional<String> referenceTime, Optional<String> referenceRemuneration, Optional<String> courseUrl,
			String attachmentId, String attachmentName, String memberId){
		LecturerAdeptCourse lecturerAdeptCourse = new LecturerAdeptCourse();
		lecturerAdeptCourse.setLecturerId(lecturerId.get());
		lecturerAdeptCourse.setCourseId(courseId.get());
		referenceTime.ifPresent(val -> {lecturerAdeptCourse.setReferenceTime(Double.valueOf(val));});
		referenceRemuneration.ifPresent(val -> {lecturerAdeptCourse.setReferenceRemuneration(Double.valueOf(val));});
		courseUrl.ifPresent(lecturerAdeptCourse::setCourseUrl);
		lecturerAdeptCourse.setCreateMemberId(memberId);
		lecturerAdeptCourse.setOrganizationId("1");
		lecturerAdeptCourse.forInsert();
		return lecturerAdeptCourseDao.insert(lecturerAdeptCourse);
	}

	@Override
	public LecturerAdeptCourse lecturerExit(Optional<String> id, Optional<String> referenceTime,
			Optional<String> referenceRemuneration, Optional<String> courseUrl, String attachmentId, String attachmentName) {
		return exitAdeptRecord(id, referenceTime, referenceRemuneration, courseUrl,attachmentId,attachmentName);
	}

	@Override
	public LecturerAdeptCourse courseExit(Optional<String> id, Optional<String> referenceTime, 
			Optional<String> referenceRemuneration, Optional<String> courseUrl, String attachmentId, String attachmentName) {
		return exitAdeptRecord(id, referenceTime, referenceRemuneration, courseUrl,attachmentId,attachmentName);
	}
	
	private  LecturerAdeptCourse exitAdeptRecord(Optional<String> id, Optional<String> referenceTime, 
			Optional<String> referenceRemuneration, Optional<String> courseUrl, String attachmentId, String attachmentName){
		LecturerAdeptCourse lecturerAdeptCourse = lecturerAdeptCourseDao.get(id.get());
		if(lecturerAdeptCourse == null) return null;
		if (referenceTime.isPresent()) {
			lecturerAdeptCourse.setReferenceTime(Double.valueOf(referenceTime.get()));
		} else {
			lecturerAdeptCourse.setReferenceTime(null);
		}
		if (referenceRemuneration.isPresent()) {
			lecturerAdeptCourse.setReferenceRemuneration(Double.valueOf(referenceRemuneration.get()));
		} else {
			lecturerAdeptCourse.setReferenceRemuneration(null);
		}
		lecturerAdeptCourse.setAttachmentId(attachmentId);
		lecturerAdeptCourse.setAttachmentName(attachmentName);
		lecturerAdeptCourse.setCourseUrl(courseUrl.orElse(""));
		lecturerAdeptCourse.setId(id.get());
		return lecturerAdeptCourseDao.update(lecturerAdeptCourse);
	}

	@Override
	public int choiceList(Optional<String> adeptCourseList, String memberId) {
		if(adeptCourseList.isPresent()) {
 			List<LecturerAdeptCourse> list = com.alibaba.fastjson.JSON.parseArray(adeptCourseList.get() ,LecturerAdeptCourse.class);
 			list = list.stream().filter(adept -> {
 				Optional<String> lId = Optional.ofNullable(adept.getLId());
 				Optional<String> cId = Optional.ofNullable(adept.getCId());
 				return (lId.isPresent() && cId.isPresent());
 			}).map(adept -> {
 				adept.setId("");
 				adept.setCourseId(adept.getCId());
 				adept.setLecturerId(adept.getLId());
 				adept.forInsert();
 				return adept;
 			}).collect(Collectors.toList());
 			if(list.size() > 0)
 				lecturerAdeptCourseDao.insert(list);
 			return list.size();
		}
		return 0;
	}

	@Override
	public LecturerAdeptCourse updateAdeptLecturer(Optional<String> courseId, Optional<String> lecturerId,
			Optional<String> referenceTime, Optional<String> referenceRemuneration, Optional<String> courseUrl, 
			String attachmentId, String attachmentName) {
		Optional<LecturerAdeptCourse> LecturerAdeptCourse = lecturerAdeptCourseDao.fetchOne(LECTURER_ADEPT_COURSE.COURSE_ID.eq(courseId.orElse(null)), LECTURER_ADEPT_COURSE.LECTURER_ID.eq(lecturerId.orElse(null)));
		LecturerAdeptCourse.ifPresent(c -> {
			if (referenceTime.isPresent()) {
				c.setReferenceTime(Double.valueOf(referenceTime.get()));
			} else {
				c.setReferenceTime(null);
			}
			if (referenceRemuneration.isPresent()) {
				c.setReferenceRemuneration(Double.valueOf(referenceRemuneration.get()));
			} else {
				c.setReferenceRemuneration(null);
			}
			c.setCourseUrl(courseUrl.orElse(null));
			c.setAttachmentId(attachmentId);
			c.setAttachmentName(attachmentName);
//			courseUrl.ifPresent(c :: setCourseUrl);
			lecturerAdeptCourseDao.update(c);
		});	
		return LecturerAdeptCourse.orElse(null);
	}
	
	@Override
	public List<LecturerAdeptCourse> batchAdeptLecturer(Optional<String> adeptCourseList, String currentUserId) {
		List<LecturerAdeptCourse> list = com.alibaba.fastjson.JSON.parseArray(adeptCourseList.orElseGet(null) ,LecturerAdeptCourse.class);
			list = list.stream().map(adept -> {
				adept.setCourseId(adept.getCId());
				adept.setLecturerId(adept.getLId());
				adept.forInsert();
				adept.setCreateMemberId(currentUserId);
				adept.setOrganizationId("1");
				return adept;
			}).collect(Collectors.toList());
			if(list.size() > 0) lecturerAdeptCourseDao.insert(list);
		return list;
	}

	@Override
	public List<LecturerAdeptCourse> findAdeptLecturer(List<String> lecturerList) {
		return lecturerAdeptCourseDao.execute(dao -> {
			return dao.select(Fields.start().add(
					LECTURER_ADEPT_COURSE.ID,
					LECTURER_ADEPT_COURSE.COURSE_ID,
					LECTURER_ADEPT_COURSE.LECTURER_ID,
					F2F_COURSE_LIBRARY.NAME
				).end())
			.from(LECTURER_ADEPT_COURSE)
			.leftJoin(F2F_COURSE_LIBRARY).on(F2F_COURSE_LIBRARY.ID.eq(LECTURER_ADEPT_COURSE.COURSE_ID))
			.where(LECTURER_ADEPT_COURSE.LECTURER_ID.in(lecturerList))
			.fetch(r -> {
				LecturerAdeptCourse lecturerAdeptCourse = r.into(LecturerAdeptCourse.class);
				lecturerAdeptCourse.setCourseName(r.get(F2F_COURSE_LIBRARY.NAME));
				return lecturerAdeptCourse;
			});
		});
	}

	@Override
	public void batchInsert(List<LecturerAdeptCourse> listAdept) {
		lecturerAdeptCourseDao.insert(listAdept);		
	}
}
