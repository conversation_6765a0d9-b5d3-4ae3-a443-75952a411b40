/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ProjectHistoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 历史培训计划表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProjectHistory extends TableImpl<ProjectHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_project_history</code>
     */
    public static final ProjectHistory PROJECT_HISTORY = new ProjectHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProjectHistoryRecord> getRecordType() {
        return ProjectHistoryRecord.class;
    }

    /**
     * The column <code>train.t_project_history.f_id</code>.
     */
    public final TableField<ProjectHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_project_history.f_name</code>. 计划名称
     */
    public final TableField<ProjectHistoryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "计划名称");

    /**
     * The column <code>train.t_project_history.f_code</code>. MIS编码
     */
    public final TableField<ProjectHistoryRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "MIS编码");

    /**
     * The column <code>train.t_project_history.f_contact_member_id</code>. 需求单位联系人
     */
    public final TableField<ProjectHistoryRecord, String> CONTACT_MEMBER_ID = createField("f_contact_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "需求单位联系人");

    /**
     * The column <code>train.t_project_history.f_organization_id</code>. 培训需求单位
     */
    public final TableField<ProjectHistoryRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训需求单位");

    /**
     * The column <code>train.t_project_history.f_contact_phone</code>. 联系电话
     */
    public final TableField<ProjectHistoryRecord, String> CONTACT_PHONE = createField("f_contact_phone", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "联系电话");

    /**
     * The column <code>train.t_project_history.f_contact_email</code>. 联系邮箱
     */
    public final TableField<ProjectHistoryRecord, String> CONTACT_EMAIL = createField("f_contact_email", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "联系邮箱");

    /**
     * The column <code>train.t_project_history.f_plan_year</code>. 计划年月
     */
    public final TableField<ProjectHistoryRecord, Long> PLAN_YEAR = createField("f_plan_year", org.jooq.impl.SQLDataType.BIGINT, this, "计划年月");

    /**
     * The column <code>train.t_project_history.f_amount</code>. 计划人数
     */
    public final TableField<ProjectHistoryRecord, Integer> AMOUNT = createField("f_amount", org.jooq.impl.SQLDataType.INTEGER, this, "计划人数");

    /**
     * The column <code>train.t_project_history.f_object</code>. 培训对象
     */
    public final TableField<ProjectHistoryRecord, String> OBJECT = createField("f_object", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "培训对象");

    /**
     * The column <code>train.t_project_history.f_days</code>. 计划培训天数
     */
    public final TableField<ProjectHistoryRecord, Integer> DAYS = createField("f_days", org.jooq.impl.SQLDataType.INTEGER, this, "计划培训天数");

    /**
     * The column <code>train.t_project_history.f_type_id</code>. 培训类型
     */
    public final TableField<ProjectHistoryRecord, String> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训类型");

    /**
     * The column <code>train.t_project_history.f_address</code>. 培训地点
     */
    public final TableField<ProjectHistoryRecord, String> ADDRESS = createField("f_address", org.jooq.impl.SQLDataType.VARCHAR.length(1000), this, "培训地点");

    /**
     * The column <code>train.t_project_history.f_status</code>. 状态
     */
    public final TableField<ProjectHistoryRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "状态");

    /**
     * Create a <code>train.t_project_history</code> table reference
     */
    public ProjectHistory() {
        this("t_project_history", null);
    }

    /**
     * Create an aliased <code>train.t_project_history</code> table reference
     */
    public ProjectHistory(String alias) {
        this(alias, PROJECT_HISTORY);
    }

    private ProjectHistory(String alias, Table<ProjectHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ProjectHistory(String alias, Table<ProjectHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "历史培训计划表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ProjectHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_PROJECT_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ProjectHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<ProjectHistoryRecord>>asList(Keys.KEY_T_PROJECT_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectHistory as(String alias) {
        return new ProjectHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ProjectHistory rename(String name) {
        return new ProjectHistory(name, null);
    }
}
