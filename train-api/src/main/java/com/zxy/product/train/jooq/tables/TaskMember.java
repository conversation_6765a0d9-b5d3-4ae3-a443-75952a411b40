/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TaskMemberRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 用户提交作业详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TaskMember extends TableImpl<TaskMemberRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_task_member</code>
     */
    public static final TaskMember TASK_MEMBER = new TaskMember();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TaskMemberRecord> getRecordType() {
        return TaskMemberRecord.class;
    }

    /**
     * The column <code>train.t_task_member.f_id</code>. 表ID
     */
    public final TableField<TaskMemberRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表ID");

    /**
     * The column <code>train.t_task_member.f_member_id</code>. 提交人
     */
    public final TableField<TaskMemberRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "提交人");

    /**
     * The column <code>train.t_task_member.f_create_time</code>. 提交作业时间
     */
    public final TableField<TaskMemberRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "提交作业时间");

    /**
     * The column <code>train.t_task_member.f_task_id</code>. 作业ID
     */
    public final TableField<TaskMemberRecord, String> TASK_ID = createField("f_task_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "作业ID");

    /**
     * The column <code>train.t_task_member.f_name</code>. 附件名称
     */
    public final TableField<TaskMemberRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "附件名称");

    /**
     * The column <code>train.t_task_member.f_description</code>. 附件描述
     */
    public final TableField<TaskMemberRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "附件描述");

    /**
     * The column <code>train.t_task_member.f_attachment_type</code>. 附件类型
     */
    public final TableField<TaskMemberRecord, String> ATTACHMENT_TYPE = createField("f_attachment_type", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "附件类型");

    /**
     * The column <code>train.t_task_member.f_attachment_id</code>. 附件ID
     */
    public final TableField<TaskMemberRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "附件ID");

    /**
     * The column <code>train.t_task_member.f_state</code>. 状态 1未提交 2审核中  3合格  4不合格
     */
    public final TableField<TaskMemberRecord, Integer> STATE = createField("f_state", org.jooq.impl.SQLDataType.INTEGER, this, "状态 1未提交 2审核中  3合格  4不合格");

    /**
     * Create a <code>train.t_task_member</code> table reference
     */
    public TaskMember() {
        this("t_task_member", null);
    }

    /**
     * Create an aliased <code>train.t_task_member</code> table reference
     */
    public TaskMember(String alias) {
        this(alias, TASK_MEMBER);
    }

    private TaskMember(String alias, Table<TaskMemberRecord> aliased) {
        this(alias, aliased, null);
    }

    private TaskMember(String alias, Table<TaskMemberRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "用户提交作业详情表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TaskMemberRecord> getPrimaryKey() {
        return Keys.KEY_T_TASK_MEMBER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TaskMemberRecord>> getKeys() {
        return Arrays.<UniqueKey<TaskMemberRecord>>asList(Keys.KEY_T_TASK_MEMBER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMember as(String alias) {
        return new TaskMember(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TaskMember rename(String name) {
        return new TaskMember(name, null);
    }
}
