/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.routines;


import com.zxy.product.train.jooq.Train;

import javax.annotation.Generated;

import org.jooq.impl.AbstractRoutine;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.5"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InsertTClassInfo extends AbstractRoutine<java.lang.Void> {

    private static final long serialVersionUID = -820073250;

    /**
     * Create a new routine call instance
     */
    public InsertTClassInfo() {
        super("insert_t_class_info", Train.TRAIN_SCHEMA);
    }
}
