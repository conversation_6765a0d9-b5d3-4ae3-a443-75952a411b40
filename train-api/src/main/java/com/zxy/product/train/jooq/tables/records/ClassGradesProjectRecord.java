/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassGradesProject;
import com.zxy.product.train.jooq.tables.interfaces.IClassGradesProject;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassGradesProjectRecord extends UpdatableRecordImpl<ClassGradesProjectRecord> implements Record6<String, String, String, Long, String, String>, IClassGradesProject {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_grades_project.f_id</code>. 表id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project.f_id</code>. 表id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_grades_project.f_name</code>. 项目名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project.f_name</code>. 项目名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_grades_project.f_status</code>. 学员端状态 0:隐藏 1:显示
     */
    @Override
    public void setStatus(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project.f_status</code>. 学员端状态 0:隐藏 1:显示
     */
    @Override
    public String getStatus() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_class_grades_project.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>train.t_class_grades_project.f_create_member</code>. 创建人
     */
    @Override
    public void setCreateMember(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project.f_create_member</code>. 创建人
     */
    @Override
    public String getCreateMember() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_class_grades_project.f_class_id</code>. 培训班id
     */
    @Override
    public void setClassId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project.f_class_id</code>. 培训班id
     */
    @Override
    public String getClassId() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, Long, String, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, Long, String, String> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassGradesProject.CLASS_GRADES_PROJECT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassGradesProject.CLASS_GRADES_PROJECT.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ClassGradesProject.CLASS_GRADES_PROJECT.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return ClassGradesProject.CLASS_GRADES_PROJECT.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ClassGradesProject.CLASS_GRADES_PROJECT.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return ClassGradesProject.CLASS_GRADES_PROJECT.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectRecord value3(String value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectRecord value5(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectRecord value6(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectRecord values(String value1, String value2, String value3, Long value4, String value5, String value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassGradesProject from) {
        setId(from.getId());
        setName(from.getName());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setClassId(from.getClassId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassGradesProject> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassGradesProjectRecord
     */
    public ClassGradesProjectRecord() {
        super(ClassGradesProject.CLASS_GRADES_PROJECT);
    }

    /**
     * Create a detached, initialised ClassGradesProjectRecord
     */
    public ClassGradesProjectRecord(String id, String name, String status, Long createTime, String createMember, String classId) {
        super(ClassGradesProject.CLASS_GRADES_PROJECT);

        set(0, id);
        set(1, name);
        set(2, status);
        set(3, createTime);
        set(4, createMember);
        set(5, classId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassGradesProjectEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassGradesProjectEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassGradesProjectEntity)source;
        pojo.into(this);
        return true;
    }
}
