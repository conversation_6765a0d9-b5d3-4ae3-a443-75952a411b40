/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassGroup extends Serializable {

    /**
     * Setter for <code>train.t_class_group.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_group.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_group.f_name</code>. 分组名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_class_group.f_name</code>. 分组名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_class_group.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_class_group.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_class_group.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_group.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_group.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_group.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassGroup
     */
    public void from(IClassGroup from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassGroup
     */
    public <E extends IClassGroup> E into(E into);
}
