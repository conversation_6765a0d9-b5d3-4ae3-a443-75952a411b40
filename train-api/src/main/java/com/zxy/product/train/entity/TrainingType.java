package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.TrainingTypeEntity;

/**
 * 培训类别
 */
public class TrainingType extends TrainingTypeEntity {
    private static final long serialVersionUID = 6607673831799944933L;
    /**
     * 大类  =0
     */
    public static final int TRAINING_TYPE_BIG = 0;

    /**
     * 小类 =1
     */
    public static final int TRAINING_TYPE_SMALL = 1;

    private String bigName;

    private String bigCode;

    private String bigTypeId;

    private Integer smallCount;

    public Integer getSmallCount() {
        return smallCount;
    }

    public void setSmallCount(Integer smallCount) {
        this.smallCount = smallCount;
    }

    public String getBigName() {
        return bigName;
    }

    public void setBigName(String bigName) {
        this.bigName = bigName;
    }

    public String getBigCode() {
        return bigCode;
    }

    public void setBigCode(String bigCode) {
        this.bigCode = bigCode;
    }

    public String getBigTypeId() {
        return bigTypeId;
    }

    public void setBigTypeId(String bigTypeId) {
        this.bigTypeId = bigTypeId;
    }
}
