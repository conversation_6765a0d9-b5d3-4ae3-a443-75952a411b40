/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassstaffConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassstaffConfiguration extends TableImpl<ClassstaffConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_classstaff_configuration</code>
     */
    public static final ClassstaffConfiguration CLASSSTAFF_CONFIGURATION = new ClassstaffConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassstaffConfigurationRecord> getRecordType() {
        return ClassstaffConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_classstaff_configuration.f_id</code>. 主键
     */
    public final TableField<ClassstaffConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_classstaff_configuration.f_configuration_id</code>. 关联配置表ID
     */
    public final TableField<ClassstaffConfigurationRecord, String> CONFIGURATION_ID = createField("f_configuration_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联配置表ID");

    /**
     * The column <code>train.t_classstaff_configuration.f_type_id</code>. 关联类型ID
     */
    public final TableField<ClassstaffConfigurationRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型ID");

    /**
     * The column <code>train.t_classstaff_configuration.f_member_id</code>. 人员ID
     */
    public final TableField<ClassstaffConfigurationRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "人员ID");

    /**
     * The column <code>train.t_classstaff_configuration.f_sort</code>. 排序
     */
    public final TableField<ClassstaffConfigurationRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_classstaff_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<ClassstaffConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_classstaff_configuration.f_create_member</code>. 创建人ID
     */
    public final TableField<ClassstaffConfigurationRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_classstaff_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassstaffConfigurationRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_classstaff_configuration</code> table reference
     */
    public ClassstaffConfiguration() {
        this("t_classstaff_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_classstaff_configuration</code> table reference
     */
    public ClassstaffConfiguration(String alias) {
        this(alias, CLASSSTAFF_CONFIGURATION);
    }

    private ClassstaffConfiguration(String alias, Table<ClassstaffConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassstaffConfiguration(String alias, Table<ClassstaffConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassstaffConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASSSTAFF_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassstaffConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassstaffConfigurationRecord>>asList(Keys.KEY_T_CLASSSTAFF_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfiguration as(String alias) {
        return new ClassstaffConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassstaffConfiguration rename(String name) {
        return new ClassstaffConfiguration(name, null);
    }
}
