/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 培训班群聊成员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITrainChatGroup extends Serializable {

    /**
     * Setter for <code>train.t_train_chat_group.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_train_chat_group.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_train_chat_group.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_train_chat_group.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_train_chat_group.f_chat_id</code>. 群聊ID
     */
    public void setChatId(String value);

    /**
     * Getter for <code>train.t_train_chat_group.f_chat_id</code>. 群聊ID
     */
    public String getChatId();

    /**
     * Setter for <code>train.t_train_chat_group.f_member_id</code>. 成员ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_train_chat_group.f_member_id</code>. 成员ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_train_chat_group.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_train_chat_group.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_train_chat_group.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_train_chat_group.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_train_chat_group.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_train_chat_group.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITrainChatGroup
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.ITrainChatGroup from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITrainChatGroup
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.ITrainChatGroup> E into(E into);
}
