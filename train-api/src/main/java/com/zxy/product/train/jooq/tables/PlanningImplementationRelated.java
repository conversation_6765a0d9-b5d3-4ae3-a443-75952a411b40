/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.PlanningImplementationRelatedRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 策划实施关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PlanningImplementationRelated extends TableImpl<PlanningImplementationRelatedRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_planning_implementation_related</code>
     */
    public static final PlanningImplementationRelated PLANNING_IMPLEMENTATION_RELATED = new PlanningImplementationRelated();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PlanningImplementationRelatedRecord> getRecordType() {
        return PlanningImplementationRelatedRecord.class;
    }

    /**
     * The column <code>train.t_planning_implementation_related.f_id</code>. 主键
     */
    public final TableField<PlanningImplementationRelatedRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_planning_implementation_related.f_business_id</code>. 关联业务id
     */
    public final TableField<PlanningImplementationRelatedRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "关联业务id");

    /**
     * The column <code>train.t_planning_implementation_related.f_organization_id</code>. 部门id
     */
    public final TableField<PlanningImplementationRelatedRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "部门id");

    /**
     * The column <code>train.t_planning_implementation_related.f_member_id</code>. 人员id
     */
    public final TableField<PlanningImplementationRelatedRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "人员id");

    /**
     * The column <code>train.t_planning_implementation_related.f_type</code>. 来源（2：策划 1：实施）
     */
    public final TableField<PlanningImplementationRelatedRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源（2：策划 1：实施）");

    /**
     * The column <code>train.t_planning_implementation_related.f_create_time</code>. 创建时间
     */
    public final TableField<PlanningImplementationRelatedRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_planning_implementation_related.f_modify_date</code>. 修改时间
     */
    public final TableField<PlanningImplementationRelatedRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>train.t_planning_implementation_related</code> table reference
     */
    public PlanningImplementationRelated() {
        this("t_planning_implementation_related", null);
    }

    /**
     * Create an aliased <code>train.t_planning_implementation_related</code> table reference
     */
    public PlanningImplementationRelated(String alias) {
        this(alias, PLANNING_IMPLEMENTATION_RELATED);
    }

    private PlanningImplementationRelated(String alias, Table<PlanningImplementationRelatedRecord> aliased) {
        this(alias, aliased, null);
    }

    private PlanningImplementationRelated(String alias, Table<PlanningImplementationRelatedRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "策划实施关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PlanningImplementationRelatedRecord> getPrimaryKey() {
        return Keys.KEY_T_PLANNING_IMPLEMENTATION_RELATED_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PlanningImplementationRelatedRecord>> getKeys() {
        return Arrays.<UniqueKey<PlanningImplementationRelatedRecord>>asList(Keys.KEY_T_PLANNING_IMPLEMENTATION_RELATED_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelated as(String alias) {
        return new PlanningImplementationRelated(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PlanningImplementationRelated rename(String name) {
        return new PlanningImplementationRelated(name, null);
    }
}
