package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.OrganizationTeachingEntity;

public class OrganizationTeaching extends OrganizationTeachingEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = -821729553491599211L;
	public static final Integer SOURCE_SELF = 0;
	public static final Integer SOURCE_MANAGE = 1;
	public static final Integer SOURCE_IMPORT = 2;

	/**
	 * 待审核
	 */
	public static final Integer APPROVAL_STATUS_WAIT = 0;
	/**
	 * 通过
	 */
	public static final Integer APPROVAL_STATUS_PASS = 1;
	/**
	 * 拒绝
	 */
	public static final Integer APPROVAL_STATUS_REFUSE = 2;
	/**
	 * 无状态
	 */
	public static final Integer APPROVAL_STATUS_NULL = 3;
	
	private String approvalMemberName;		//审核人
	private String lecturerOrganization;
	private CourseAttach courseAttach;
	private Integer lecturerType;
	private List<CourseAttach> courseAttachList;
	private String attributeId;	
	private String attributeName;
	private String institutionId;
	private String lecturerOrganizationId;
	private String courseOrganizationId;
	private Boolean isGrant;
	private Integer isShare;

	private String unit;

	public Integer getIsShare() {
		return isShare;
	}

	public void setIsShare(Integer isShare) {
		this.isShare = isShare;
	}
	
	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Boolean getIsGrant() {
		return isGrant;
	}

	public void setIsGrant(Boolean isGrant) {
		this.isGrant = isGrant;
	}
	

	public String getLecturerOrganizationId() {
		return lecturerOrganizationId;
	}

	public void setLecturerOrganizationId(String lecturerOrganizationId) {
		this.lecturerOrganizationId = lecturerOrganizationId;
	}

	public String getCourseOrganizationId() {
		return courseOrganizationId;
	}

	public void setCourseOrganizationId(String courseOrganizationId) {
		this.courseOrganizationId = courseOrganizationId;
	}
	
	
	public String getInstitutionId() {
		return institutionId;
	}

	public void setInstitutionId(String institutionId) {
		this.institutionId = institutionId;
	}
	
	
	public String getAttributeId() {
		return attributeId;
	}

	public void setAttributeId(String attributeId) {
		this.attributeId = attributeId;
	}

	public String getAttributeName() {
		return attributeName;
	}

	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}

	public List<CourseAttach> getCourseAttachList() {
		return courseAttachList;
	}

	public void setCourseAttachList(List<CourseAttach> courseAttachList) {
		this.courseAttachList = courseAttachList;
	}
	
	
	
	public Integer getLecturerType() {
		return lecturerType;
	}

	public void setLecturerType(Integer lecturerType) {
		this.lecturerType = lecturerType;
	}

	public CourseAttach getCourseAttach() {
		return courseAttach;
	}

	public void setCourseAttach(CourseAttach courseAttach) {
		this.courseAttach = courseAttach;
	}

	private String jobName;
	

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getLecturerOrganization() {
		return lecturerOrganization;
	}

	public void setLecturerOrganization(String lecturerOrganization) {
		this.lecturerOrganization = lecturerOrganization;
	}

	public String getApprovalMemberName() {
		return approvalMemberName;
	}

	public void setApprovalMemberName(String approvalMemberName) {
		this.approvalMemberName = approvalMemberName;
	}

}
