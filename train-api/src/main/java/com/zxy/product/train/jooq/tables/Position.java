/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.PositionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Position extends TableImpl<PositionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_position</code>
     */
    public static final Position POSITION = new Position();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PositionRecord> getRecordType() {
        return PositionRecord.class;
    }

    /**
     * The column <code>train.t_position.f_id</code>. ID
     */
    public final TableField<PositionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_position.f_name</code>. 职位名称
     */
    public final TableField<PositionRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "职位名称 ");

    /**
     * The column <code>train.t_position.f_code</code>. 职位编码
     */
    public final TableField<PositionRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "职位编码 ");

    /**
     * The column <code>train.t_position.f_organization_id</code>. 组织ID
     */
    public final TableField<PositionRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "组织ID");

    /**
     * The column <code>train.t_position.f_status</code>. 职位状态 1:活动  2：禁用
     */
    public final TableField<PositionRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "职位状态 1:活动  2：禁用");

    /**
     * The column <code>train.t_position.f_numbers</code>. 职位人数
     */
    public final TableField<PositionRecord, Integer> NUMBERS = createField("f_numbers", org.jooq.impl.SQLDataType.INTEGER, this, "职位人数");

    /**
     * The column <code>train.t_position.f_job_id</code>. 职务ID
     */
    public final TableField<PositionRecord, String> JOB_ID = createField("f_job_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "职务ID");

    /**
     * The column <code>train.t_position.f_instruction_id</code>. 职位说明书id(附件id)
     */
    public final TableField<PositionRecord, String> INSTRUCTION_ID = createField("f_instruction_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "职位说明书id(附件id)");

    /**
     * The column <code>train.t_position.f_desc</code>. 专业能力标准/资质模型
     */
    public final TableField<PositionRecord, String> DESC = createField("f_desc", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "专业能力标准/资质模型");

    /**
     * The column <code>train.t_position.f_root_name</code>. 主族
     */
    public final TableField<PositionRecord, String> ROOT_NAME = createField("f_root_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "主族");

    /**
     * The column <code>train.t_position.f_sub_name</code>. 子族
     */
    public final TableField<PositionRecord, String> SUB_NAME = createField("f_sub_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "子族");

    /**
     * The column <code>train.t_position.f_alias_id</code>. 条线(t_member_config)
     */
    public final TableField<PositionRecord, String> ALIAS_ID = createField("f_alias_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "条线(t_member_config)");

    /**
     * The column <code>train.t_position.f_level_id</code>. 职级(t_member_config)
     */
    public final TableField<PositionRecord, String> LEVEL_ID = createField("f_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "职级(t_member_config)");

    /**
     * The column <code>train.t_position.f_create_time</code>. 创建时间
     */
    public final TableField<PositionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_position.f_mis_code</code>. MIS省份简称
     */
    public final TableField<PositionRecord, String> MIS_CODE = createField("f_mis_code", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "MIS省份简称");

    /**
     * The column <code>train.t_position.f_mis_id</code>. MIS同步职位ID
     */
    public final TableField<PositionRecord, String> MIS_ID = createField("f_mis_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "MIS同步职位ID");

    /**
     * The column <code>train.t_position.f_type</code>. 职位类型 1细分职位，2自设职位，3集团标准职位 4其他
     */
    public final TableField<PositionRecord, String> TYPE = createField("f_type", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "职位类型 1细分职位，2自设职位，3集团标准职位 4其他");

    /**
     * The column <code>train.t_position.f_parent_id</code>. 集团标准职位编码
     */
    public final TableField<PositionRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "集团标准职位编码");

    /**
     * Create a <code>train.t_position</code> table reference
     */
    public Position() {
        this("t_position", null);
    }

    /**
     * Create an aliased <code>train.t_position</code> table reference
     */
    public Position(String alias) {
        this(alias, POSITION);
    }

    private Position(String alias, Table<PositionRecord> aliased) {
        this(alias, aliased, null);
    }

    private Position(String alias, Table<PositionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PositionRecord> getPrimaryKey() {
        return Keys.KEY_T_POSITION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PositionRecord>> getKeys() {
        return Arrays.<UniqueKey<PositionRecord>>asList(Keys.KEY_T_POSITION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Position as(String alias) {
        return new Position(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Position rename(String name) {
        return new Position(name, null);
    }
}
