package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassStatisticsEntity;

/**
 * <AUTHOR>
 * 满意度问卷统计
 *
 */
public class ClassStatistics extends ClassStatisticsEntity{

	private static final long serialVersionUID = 1177090062214185171L;
	
	private String questionContent; //题目
	private Integer manA;
	private Integer manB;
	private Integer manC;
	private Integer manD;
	private Integer manE;
	private Integer manF;
	private Integer manZ;
	private String manAF;
	private String manBF;
	private String manCF;
	private String manDF;
	private String manEF;
	private String manFF;
	private String manCMF;
	
	public String getQuestionContent() {
		return questionContent;
	}
	public void setQuestionContent(String questionContent) {
		this.questionContent = questionContent;
	}
	public Integer getManA() {
		return manA;
	}
	public void setManA(Integer manA) {
		this.manA = manA;
	}
	public Integer getManB() {
		return manB;
	}
	public void setManB(Integer manB) {
		this.manB = manB;
	}
	public Integer getManC() {
		return manC;
	}
	public void setManC(Integer manC) {
		this.manC = manC;
	}
	public Integer getManD() {
		return manD;
	}
	public void setManD(Integer manD) {
		this.manD = manD;
	}
	public Integer getManE() {
		return manE;
	}
	public void setManE(Integer manE) {
		this.manE = manE;
	}
	public Integer getManF() {
		return manF;
	}
	public void setManF(Integer manF) {
		this.manF = manF;
	}
	public Integer getManZ() {
		return manZ;
	}
	public void setManZ(Integer manZ) {
		this.manZ = manZ;
	}
	public String getManAF() {
		return manAF;
	}
	public void setManAF(String manAF) {
		this.manAF = manAF;
	}
	public String getManBF() {
		return manBF;
	}
	public void setManBF(String manBF) {
		this.manBF = manBF;
	}
	public String getManCF() {
		return manCF;
	}
	public void setManCF(String manCF) {
		this.manCF = manCF;
	}
	public String getManDF() {
		return manDF;
	}
	public void setManDF(String manDF) {
		this.manDF = manDF;
	}
	public String getManEF() {
		return manEF;
	}
	public void setManEF(String manEF) {
		this.manEF = manEF;
	}
	public String getManFF() {
		return manFF;
	}
	public void setManFF(String manFF) {
		this.manFF = manFF;
	}
	public String getManCMF() {
		return manCMF;
	}
	public void setManCMF(String manCMF) {
		this.manCMF = manCMF;
	}

}
