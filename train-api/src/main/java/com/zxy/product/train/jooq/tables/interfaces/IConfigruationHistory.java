/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 历史配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IConfigruationHistory extends Serializable {

    /**
     * Setter for <code>train.t_configruation_history.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_configruation_history.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_configruation_history.f_type</code>. 类别:1A,2B,3C,4G,5M,6O,7R,8T,9Z
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_configruation_history.f_type</code>. 类别:1A,2B,3C,4G,5M,6O,7R,8T,9Z
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_configruation_history.f_name</code>. 名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_configruation_history.f_name</code>. 名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_configruation_history.f_organization_id</code>. 归属机构
     */
    public void setOrganizationId(Long value);

    /**
     * Getter for <code>train.t_configruation_history.f_organization_id</code>. 归属机构
     */
    public Long getOrganizationId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IConfigruationHistory
     */
    public void from(IConfigruationHistory from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IConfigruationHistory
     */
    public <E extends IConfigruationHistory> E into(E into);
}
