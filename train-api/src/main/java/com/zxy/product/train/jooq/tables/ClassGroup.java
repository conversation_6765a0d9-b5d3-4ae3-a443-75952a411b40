/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassGroupRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassGroup extends TableImpl<ClassGroupRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_group</code>
     */
    public static final ClassGroup CLASS_GROUP = new ClassGroup();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassGroupRecord> getRecordType() {
        return ClassGroupRecord.class;
    }

    /**
     * The column <code>train.t_class_group.f_id</code>. 表id
     */
    public final TableField<ClassGroupRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_group.f_name</code>. 分组名称
     */
    public final TableField<ClassGroupRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "分组名称");

    /**
     * The column <code>train.t_class_group.f_organization_id</code>. 组织id
     */
    public final TableField<ClassGroupRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织id");

    /**
     * The column <code>train.t_class_group.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassGroupRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_group.f_create_time</code>. 创建时间
     */
    public final TableField<ClassGroupRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_class_group</code> table reference
     */
    public ClassGroup() {
        this("t_class_group", null);
    }

    /**
     * Create an aliased <code>train.t_class_group</code> table reference
     */
    public ClassGroup(String alias) {
        this(alias, CLASS_GROUP);
    }

    private ClassGroup(String alias, Table<ClassGroupRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassGroup(String alias, Table<ClassGroupRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassGroupRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_GROUP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassGroupRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassGroupRecord>>asList(Keys.KEY_T_CLASS_GROUP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGroup as(String alias) {
        return new ClassGroup(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassGroup rename(String name) {
        return new ClassGroup(name, null);
    }
}
