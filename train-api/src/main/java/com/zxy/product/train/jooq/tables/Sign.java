/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SignRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 签到表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Sign extends TableImpl<SignRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_sign</code>
     */
    public static final Sign SIGN = new Sign();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SignRecord> getRecordType() {
        return SignRecord.class;
    }

    /**
     * The column <code>train.t_sign.f_id</code>. 主键
     */
    public final TableField<SignRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_sign.f_name</code>. 签到名称
     */
    public final TableField<SignRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "签到名称");

    /**
     * The column <code>train.t_sign.f_start_time</code>. 签到起始时间
     */
    public final TableField<SignRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "签到起始时间");

    /**
     * The column <code>train.t_sign.f_end_time</code>. 签到结束时间
     */
    public final TableField<SignRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "签到结束时间");

    /**
     * The column <code>train.t_sign.f_place</code>. 签到地点
     */
    public final TableField<SignRecord, String> PLACE = createField("f_place", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "签到地点");

    /**
     * The column <code>train.t_sign.f_late_time</code>. 迟到时间设置
     */
    public final TableField<SignRecord, Long> LATE_TIME = createField("f_late_time", org.jooq.impl.SQLDataType.BIGINT, this, "迟到时间设置");

    /**
     * The column <code>train.t_sign.f_delete_flag</code>. 删除状态 0未删除(默认) 1已删除
     */
    public final TableField<SignRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除状态 0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_sign.f_create_time</code>. 创建时间
     */
    public final TableField<SignRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_sign.f_sign_num</code>. 签到人数
     */
    public final TableField<SignRecord, Integer> SIGN_NUM = createField("f_sign_num", org.jooq.impl.SQLDataType.INTEGER, this, "签到人数");

    /**
     * The column <code>train.t_sign.f_class_id</code>. 班级ID
     */
    public final TableField<SignRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_sign.f_create_mem</code>. 创建人ID
     */
    public final TableField<SignRecord, String> CREATE_MEM = createField("f_create_mem", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_sign.f_type</code>. 签到类型 1半天 2全天 3不签到
     */
    public final TableField<SignRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "签到类型 1半天 2全天 3不签到");

    /**
     * The column <code>train.t_sign.f_sign_in_type</code>. 签到方式（0扫码签到 1 考勤机签到）
     */
    public final TableField<SignRecord, Integer> SIGN_IN_TYPE = createField("f_sign_in_type", org.jooq.impl.SQLDataType.INTEGER, this, "签到方式（0扫码签到 1 考勤机签到）");

    /**
     * Create a <code>train.t_sign</code> table reference
     */
    public Sign() {
        this("t_sign", null);
    }

    /**
     * Create an aliased <code>train.t_sign</code> table reference
     */
    public Sign(String alias) {
        this(alias, SIGN);
    }

    private Sign(String alias, Table<SignRecord> aliased) {
        this(alias, aliased, null);
    }

    private Sign(String alias, Table<SignRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "签到表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SignRecord> getPrimaryKey() {
        return Keys.KEY_T_SIGN_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SignRecord>> getKeys() {
        return Arrays.<UniqueKey<SignRecord>>asList(Keys.KEY_T_SIGN_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Sign as(String alias) {
        return new Sign(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Sign rename(String name) {
        return new Sign(name, null);
    }
}
