/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 团队学习班-学习成果回复表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamAchievementReply extends Serializable {

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_parent_id</code>. 上级id
     */
    public void setParentId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_parent_id</code>. 上级id
     */
    public String getParentId();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_comment_id</code>. 评论id
     */
    public void setCommentId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_comment_id</code>. 评论id
     */
    public String getCommentId();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_member_id</code>. 用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_member_id</code>. 用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_to_member_id</code>. 被回复的用户id
     */
    public void setToMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_to_member_id</code>. 被回复的用户id
     */
    public String getToMemberId();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_content</code>. 回复内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_content</code>. 回复内容
     */
    public String getContent();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_hide</code>. 隐藏(1是，0否)
     */
    public void setHide(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_hide</code>. 隐藏(1是，0否)
     */
    public Integer getHide();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_praise_count</code>. 点赞数
     */
    public void setPraiseCount(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_praise_count</code>. 点赞数
     */
    public Integer getPraiseCount();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_organization_id</code>. 所属组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_organization_id</code>. 所属组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_level</code>. 1回复了讨论 2回复了回复
     */
    public void setLevel(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_level</code>. 1回复了讨论 2回复了回复
     */
    public Integer getLevel();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamAchievementReply
     */
    public void from(IStudyTeamAchievementReply from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamAchievementReply
     */
    public <E extends IStudyTeamAchievementReply> E into(E into);
}
