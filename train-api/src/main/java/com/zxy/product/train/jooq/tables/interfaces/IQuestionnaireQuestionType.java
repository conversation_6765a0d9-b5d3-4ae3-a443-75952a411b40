/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 培训班满意度问卷类型问题表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IQuestionnaireQuestionType extends Serializable {

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_id</code>. 系统ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_id</code>. 系统ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_questionnaire_code</code>. 问卷code码区分问卷
     */
    public void setQuestionnaireCode(String value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_questionnaire_code</code>. 问卷code码区分问卷
     */
    public String getQuestionnaireCode();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_questionnaire_name</code>. 问卷名称
     */
    public void setQuestionnaireName(String value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_questionnaire_name</code>. 问卷名称
     */
    public String getQuestionnaireName();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_group_name</code>. 分组名称
     */
    public void setGroupName(String value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_group_name</code>. 分组名称
     */
    public String getGroupName();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_question_name</code>. 问题名称
     */
    public void setQuestionName(String value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_question_name</code>. 问题名称
     */
    public String getQuestionName();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_level</code>. 分组等级【1：总体评估；2：课程师资评价；3：主观题】
     */
    public void setLevel(Short value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_level</code>. 分组等级【1：总体评估；2：课程师资评价；3：主观题】
     */
    public Short getLevel();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_type</code>. leve为2时有效；类型【
1 针对性：目标明确，满足需求；
2 实用性：紧贴实际，指导实践；
3 启发性：拓展视野，启发思考；
4 逻辑性：科学严谨，结构合理；
5 知识水平：底蕴深厚，见解独到；
6 实战经验：联系实际，解决问题；
7 授课技巧：形式多样，互动得当；
8 控场能力：掌控力强，张弛有度；
9 授课态度：认真敬业，关注学员；
10 过程控制：进度合理，重点突出；
11 对本课程有何优化意见建议
】
     */
    public void setType(Short value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_type</code>. leve为2时有效；类型【
1 针对性：目标明确，满足需求；
2 实用性：紧贴实际，指导实践；
3 启发性：拓展视野，启发思考；
4 逻辑性：科学严谨，结构合理；
5 知识水平：底蕴深厚，见解独到；
6 实战经验：联系实际，解决问题；
7 授课技巧：形式多样，互动得当；
8 控场能力：掌控力强，张弛有度；
9 授课态度：认真敬业，关注学员；
10 过程控制：进度合理，重点突出；
11 对本课程有何优化意见建议
】
     */
    public Short getType();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_type_branch</code>. leve为2时有效；【1：课程内容；2：师资表现；3：课程建议】
     */
    public void setTypeBranch(Short value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_type_branch</code>. leve为2时有效；【1：课程内容；2：师资表现；3：课程建议】
     */
    public Short getTypeBranch();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_order</code>. 排序
     */
    public void setOrder(Short value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_order</code>. 排序
     */
    public Short getOrder();

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IQuestionnaireQuestionType
     */
    public void from(IQuestionnaireQuestionType from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IQuestionnaireQuestionType
     */
    public <E extends IQuestionnaireQuestionType> E into(E into);
}
