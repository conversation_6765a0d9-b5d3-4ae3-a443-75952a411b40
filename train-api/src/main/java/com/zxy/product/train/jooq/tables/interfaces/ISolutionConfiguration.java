/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 智慧教务-配置情况
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISolutionConfiguration extends Serializable {

    /**
     * Setter for <code>train.t_solution_configuration.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_solution_configuration.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_solution_configuration.f_name</code>. 名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_solution_configuration.f_name</code>. 名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_solution_configuration.f_code</code>. 编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_solution_configuration.f_code</code>. 编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_solution_configuration.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_solution_configuration.f_order</code>. 排序
     */
    public Integer getOrder();

    /**
     * Setter for <code>train.t_solution_configuration.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_solution_configuration.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_solution_configuration.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_solution_configuration.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_solution_configuration.f_type</code>. 0=方案策划情况配置,1=实施方式配置
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_solution_configuration.f_type</code>. 0=方案策划情况配置,1=实施方式配置
     */
    public Integer getType();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISolutionConfiguration
     */
    public void from(ISolutionConfiguration from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISolutionConfiguration
     */
    public <E extends ISolutionConfiguration> E into(E into);
}
