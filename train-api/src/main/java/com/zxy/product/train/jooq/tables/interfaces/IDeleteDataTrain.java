/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 删除记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IDeleteDataTrain extends Serializable {

    /**
     * Setter for <code>train.t_delete_data_train.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_delete_data_train.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_delete_data_train.f_database_name</code>. 数据库名称-全程
     */
    public void setDatabaseName(String value);

    /**
     * Getter for <code>train.t_delete_data_train.f_database_name</code>. 数据库名称-全程
     */
    public String getDatabaseName();

    /**
     * Setter for <code>train.t_delete_data_train.f_table_name</code>. 表名称
     */
    public void setTableName(String value);

    /**
     * Getter for <code>train.t_delete_data_train.f_table_name</code>. 表名称
     */
    public String getTableName();

    /**
     * Setter for <code>train.t_delete_data_train.f_business_id</code>. 业务id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>train.t_delete_data_train.f_business_id</code>. 业务id
     */
    public String getBusinessId();

    /**
     * Setter for <code>train.t_delete_data_train.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_delete_data_train.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_delete_data_train.f_modify_date</code>. 更新时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_delete_data_train.f_modify_date</code>. 更新时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>train.t_delete_data_train.f_company_id</code>. 企业id
     */
    public void setCompanyId(String value);

    /**
     * Getter for <code>train.t_delete_data_train.f_company_id</code>. 企业id
     */
    public String getCompanyId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IDeleteDataTrain
     */
    public void from(IDeleteDataTrain from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IDeleteDataTrain
     */
    public <E extends IDeleteDataTrain> E into(E into);
}
