/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassDetail extends TableImpl<ClassDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_detail</code>
     */
    public static final ClassDetail CLASS_DETAIL = new ClassDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassDetailRecord> getRecordType() {
        return ClassDetailRecord.class;
    }

    /**
     * The column <code>train.t_class_detail.f_id</code>. 表id
     */
    public final TableField<ClassDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_detail.f_class_id</code>. 班级ID
     */
    public final TableField<ClassDetailRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_detail.f_cover_id</code>. 封面
     */
    public final TableField<ClassDetailRecord, String> COVER_ID = createField("f_cover_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "封面");

    /**
     * The column <code>train.t_class_detail.f_banner_id</code>. banner图片
     */
    public final TableField<ClassDetailRecord, String> BANNER_ID = createField("f_banner_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "banner图片");

    /**
     * The column <code>train.t_class_detail.f_attendance_type</code>. 签到规则：1按半天签到 2按全天签到 3不签到
     */
    public final TableField<ClassDetailRecord, Integer> ATTENDANCE_TYPE = createField("f_attendance_type", org.jooq.impl.SQLDataType.INTEGER, this, "签到规则：1按半天签到 2按全天签到 3不签到");

    /**
     * The column <code>train.t_class_detail.f_have_province_leader</code>. 是否有省公司二级经理参加
     */
    public final TableField<ClassDetailRecord, Integer> HAVE_PROVINCE_LEADER = createField("f_have_province_leader", org.jooq.impl.SQLDataType.INTEGER, this, "是否有省公司二级经理参加");

    /**
     * The column <code>train.t_class_detail.f_have_minister</code>. 是否有部长及以上领导参加
     */
    public final TableField<ClassDetailRecord, Integer> HAVE_MINISTER = createField("f_have_minister", org.jooq.impl.SQLDataType.INTEGER, this, "是否有部长及以上领导参加");

    /**
     * The column <code>train.t_class_detail.f_need_group_photo</code>. 是否合影
     */
    public final TableField<ClassDetailRecord, Integer> NEED_GROUP_PHOTO = createField("f_need_group_photo", org.jooq.impl.SQLDataType.INTEGER, this, "是否合影");

    /**
     * The column <code>train.t_class_detail.f_photo_time</code>. 合影时间
     */
    public final TableField<ClassDetailRecord, Long> PHOTO_TIME = createField("f_photo_time", org.jooq.impl.SQLDataType.BIGINT, this, "合影时间");

    /**
     * The column <code>train.t_class_detail.f_need_video</code>. 课程录像
     */
    public final TableField<ClassDetailRecord, Integer> NEED_VIDEO = createField("f_need_video", org.jooq.impl.SQLDataType.INTEGER, this, "课程录像");

    /**
     * The column <code>train.t_class_detail.f_video_requirement</code>. 录像需求
     */
    public final TableField<ClassDetailRecord, String> VIDEO_REQUIREMENT = createField("f_video_requirement", org.jooq.impl.SQLDataType.VARCHAR.length(1000), this, "录像需求");

    /**
     * The column <code>train.t_class_detail.f_need_make_course</code>. 课程制作
     */
    public final TableField<ClassDetailRecord, Integer> NEED_MAKE_COURSE = createField("f_need_make_course", org.jooq.impl.SQLDataType.INTEGER, this, "课程制作");

    /**
     * The column <code>train.t_class_detail.f_course_video_requirement</code>. 课程制作录像需求
     */
    public final TableField<ClassDetailRecord, String> COURSE_VIDEO_REQUIREMENT = createField("f_course_video_requirement", org.jooq.impl.SQLDataType.VARCHAR.length(1000), this, "课程制作录像需求");

    /**
     * The column <code>train.t_class_detail.f_need_net</code>. 是否需要网络
     */
    public final TableField<ClassDetailRecord, Integer> NEED_NET = createField("f_need_net", org.jooq.impl.SQLDataType.INTEGER, this, "是否需要网络");

    /**
     * The column <code>train.t_class_detail.f_table_type</code>. 教室桌形
     */
    public final TableField<ClassDetailRecord, String> TABLE_TYPE = createField("f_table_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "教室桌形");

    /**
     * The column <code>train.t_class_detail.f_other_requirement</code>. 其它需求
     */
    public final TableField<ClassDetailRecord, String> OTHER_REQUIREMENT = createField("f_other_requirement", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "其它需求");

    /**
     * The column <code>train.t_class_detail.f_show_ranking</code>. 是否显示排行
     */
    public final TableField<ClassDetailRecord, Integer> SHOW_RANKING = createField("f_show_ranking", org.jooq.impl.SQLDataType.INTEGER, this, "是否显示排行");

    /**
     * The column <code>train.t_class_detail.f_ranking_rule</code>. 显示规则
     */
    public final TableField<ClassDetailRecord, Integer> RANKING_RULE = createField("f_ranking_rule", org.jooq.impl.SQLDataType.INTEGER, this, "显示规则");

    /**
     * The column <code>train.t_class_detail.f_notice</code>. 公告
     */
    public final TableField<ClassDetailRecord, String> NOTICE = createField("f_notice", org.jooq.impl.SQLDataType.CLOB, this, "公告");

    /**
     * The column <code>train.t_class_detail.f_create_time</code>. 创建时间
     */
    public final TableField<ClassDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_detail.f_create_member</code>. 创建人
     */
    public final TableField<ClassDetailRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_detail.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassDetailRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_detail.f_path</code>. 班级图片路径
     */
    public final TableField<ClassDetailRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "班级图片路径");

    /**
     * The column <code>train.t_class_detail.f_cover_path</code>. 班级封面路径
     */
    public final TableField<ClassDetailRecord, String> COVER_PATH = createField("f_cover_path", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "班级封面路径");

    /**
     * The column <code>train.t_class_detail.f_notice_text</code>. 公告文本
     */
    public final TableField<ClassDetailRecord, String> NOTICE_TEXT = createField("f_notice_text", org.jooq.impl.SQLDataType.CLOB, this, "公告文本");

    /**
     * Create a <code>train.t_class_detail</code> table reference
     */
    public ClassDetail() {
        this("t_class_detail", null);
    }

    /**
     * Create an aliased <code>train.t_class_detail</code> table reference
     */
    public ClassDetail(String alias) {
        this(alias, CLASS_DETAIL);
    }

    private ClassDetail(String alias, Table<ClassDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassDetail(String alias, Table<ClassDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassDetailRecord>>asList(Keys.KEY_T_CLASS_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassDetail as(String alias) {
        return new ClassDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassDetail rename(String name) {
        return new ClassDetail(name, null);
    }
}
