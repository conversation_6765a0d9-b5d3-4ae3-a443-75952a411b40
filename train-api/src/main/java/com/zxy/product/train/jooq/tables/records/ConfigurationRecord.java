/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.Configuration;
import com.zxy.product.train.jooq.tables.interfaces.IConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConfigurationRecord extends UpdatableRecordImpl<ConfigurationRecord> implements Record8<String, String, Integer, String, Integer, Long, String, Integer>, IConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_configuration.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_configuration.f_configuration_name</code>. 配置项名称
     */
    @Override
    public void setConfigurationName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_configuration_name</code>. 配置项名称
     */
    @Override
    public String getConfigurationName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_configuration.f_type</code>. 配置类型（1费用类型2培训类型3培训补贴类型4人员类型5班级类型6教室信息7额度配置8常用版务人员9
     */
    @Override
    public void setType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_type</code>. 配置类型（1费用类型2培训类型3培训补贴类型4人员类型5班级类型6教室信息7额度配置8常用版务人员9
     */
    @Override
    public Integer getType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_configuration.f_organization_id</code>. 所属部门
     */
    @Override
    public void setOrganizationId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_organization_id</code>. 所属部门
     */
    @Override
    public String getOrganizationId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_configuration.f_configuration_amount</code>. 配置数
     */
    @Override
    public void setConfigurationAmount(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_configuration_amount</code>. 配置数
     */
    @Override
    public Integer getConfigurationAmount() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, Integer, String, Integer, Long, String, Integer> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, Integer, String, Integer, Long, String, Integer> valuesRow() {
        return (Row8) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return Configuration.CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return Configuration.CONFIGURATION.CONFIGURATION_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return Configuration.CONFIGURATION.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return Configuration.CONFIGURATION.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return Configuration.CONFIGURATION.CONFIGURATION_AMOUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return Configuration.CONFIGURATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return Configuration.CONFIGURATION.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return Configuration.CONFIGURATION.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getConfigurationName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getConfigurationAmount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value2(String value) {
        setConfigurationName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value3(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value4(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value5(Integer value) {
        setConfigurationAmount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value7(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord value8(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationRecord values(String value1, String value2, Integer value3, String value4, Integer value5, Long value6, String value7, Integer value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IConfiguration from) {
        setId(from.getId());
        setConfigurationName(from.getConfigurationName());
        setType(from.getType());
        setOrganizationId(from.getOrganizationId());
        setConfigurationAmount(from.getConfigurationAmount());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConfigurationRecord
     */
    public ConfigurationRecord() {
        super(Configuration.CONFIGURATION);
    }

    /**
     * Create a detached, initialised ConfigurationRecord
     */
    public ConfigurationRecord(String id, String configurationName, Integer type, String organizationId, Integer configurationAmount, Long createTime, String createMember, Integer deleteFlag) {
        super(Configuration.CONFIGURATION);

        set(0, id);
        set(1, configurationName);
        set(2, type);
        set(3, organizationId);
        set(4, configurationAmount);
        set(5, createTime);
        set(6, createMember);
        set(7, deleteFlag);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
