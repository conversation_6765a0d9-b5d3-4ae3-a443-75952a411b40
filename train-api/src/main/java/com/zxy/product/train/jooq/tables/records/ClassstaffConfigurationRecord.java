/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassstaffConfiguration;
import com.zxy.product.train.jooq.tables.interfaces.IClassstaffConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassstaffConfigurationRecord extends UpdatableRecordImpl<ClassstaffConfigurationRecord> implements Record8<String, String, Integer, String, Integer, Long, String, Integer>, IClassstaffConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_classstaff_configuration.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_classstaff_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public void setConfigurationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public String getConfigurationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_classstaff_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public void setTypeId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public Integer getTypeId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_classstaff_configuration.f_member_id</code>. 人员ID
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_member_id</code>. 人员ID
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_classstaff_configuration.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_classstaff_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_classstaff_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_classstaff_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_classstaff_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, Integer, String, Integer, Long, String, Integer> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, Integer, String, Integer, Long, String, Integer> valuesRow() {
        return (Row8) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.CONFIGURATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.TYPE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getConfigurationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getTypeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value2(String value) {
        setConfigurationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value3(Integer value) {
        setTypeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value5(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value7(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord value8(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffConfigurationRecord values(String value1, String value2, Integer value3, String value4, Integer value5, Long value6, String value7, Integer value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassstaffConfiguration from) {
        setId(from.getId());
        setConfigurationId(from.getConfigurationId());
        setTypeId(from.getTypeId());
        setMemberId(from.getMemberId());
        setSort(from.getSort());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassstaffConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassstaffConfigurationRecord
     */
    public ClassstaffConfigurationRecord() {
        super(ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION);
    }

    /**
     * Create a detached, initialised ClassstaffConfigurationRecord
     */
    public ClassstaffConfigurationRecord(String id, String configurationId, Integer typeId, String memberId, Integer sort, Long createTime, String createMember, Integer deleteFlag) {
        super(ClassstaffConfiguration.CLASSSTAFF_CONFIGURATION);

        set(0, id);
        set(1, configurationId);
        set(2, typeId);
        set(3, memberId);
        set(4, sort);
        set(5, createTime);
        set(6, createMember);
        set(7, deleteFlag);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassstaffConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassstaffConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassstaffConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
