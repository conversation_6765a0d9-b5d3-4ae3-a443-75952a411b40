/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassBusinessProgress extends Serializable {

    /**
     * Setter for <code>train.t_class_business_progress.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_business_progress.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_business_progress.f_member_id</code>. 用户ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_class_business_progress.f_member_id</code>. 用户ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_class_business_progress.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_business_progress.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_business_progress.f_class_business_id</code>. 调研，评估或考试的ID，
     */
    public void setClassBusinessId(String value);

    /**
     * Getter for <code>train.t_class_business_progress.f_class_business_id</code>. 调研，评估或考试的ID，
     */
    public String getClassBusinessId();

    /**
     * Setter for <code>train.t_class_business_progress.f_finish_status</code>. 参与状态：0--未参与，1--已完成，2--待评卷，3，未及格
     */
    public void setFinishStatus(Integer value);

    /**
     * Getter for <code>train.t_class_business_progress.f_finish_status</code>. 参与状态：0--未参与，1--已完成，2--待评卷，3，未及格
     */
    public Integer getFinishStatus();

    /**
     * Setter for <code>train.t_class_business_progress.f_score</code>. 考试得分
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>train.t_class_business_progress.f_score</code>. 考试得分
     */
    public Integer getScore();

    /**
     * Setter for <code>train.t_class_business_progress.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_business_progress.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassBusinessProgress
     */
    public void from(IClassBusinessProgress from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassBusinessProgress
     */
    public <E extends IClassBusinessProgress> E into(E into);
}
