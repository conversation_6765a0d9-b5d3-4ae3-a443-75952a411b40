/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 讲师发布权限人员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILecturerReleaseAuthority extends Serializable {

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_member_id</code>. 用户ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_member_id</code>. 用户ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_name</code>. 人员编号
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_name</code>. 人员编号
     */
    public String getName();

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Short value);

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Short getDeleteFlag();

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILecturerReleaseAuthority
     */
    public void from(ILecturerReleaseAuthority from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILecturerReleaseAuthority
     */
    public <E extends ILecturerReleaseAuthority> E into(E into);
}
