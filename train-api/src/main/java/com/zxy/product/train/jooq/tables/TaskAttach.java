/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TaskAttachRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 作业附件关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TaskAttach extends TableImpl<TaskAttachRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_task_attach</code>
     */
    public static final TaskAttach TASK_ATTACH = new TaskAttach();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TaskAttachRecord> getRecordType() {
        return TaskAttachRecord.class;
    }

    /**
     * The column <code>train.t_task_attach.f_id</code>. 主键
     */
    public final TableField<TaskAttachRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_task_attach.f_task_id</code>. 作业ID
     */
    public final TableField<TaskAttachRecord, String> TASK_ID = createField("f_task_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "作业ID");

    /**
     * The column <code>train.t_task_attach.f_attachment_id</code>. 附件ID
     */
    public final TableField<TaskAttachRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "附件ID");

    /**
     * The column <code>train.t_task_attach.f_attachment_name</code>. 附件名称
     */
    public final TableField<TaskAttachRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "附件名称");

    /**
     * The column <code>train.t_task_attach.f_attachment_type</code>. 类型
     */
    public final TableField<TaskAttachRecord, String> ATTACHMENT_TYPE = createField("f_attachment_type", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "类型");

    /**
     * The column <code>train.t_task_attach.f_create_member_id</code>. 创建人id
     */
    public final TableField<TaskAttachRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人id");

    /**
     * The column <code>train.t_task_attach.f_create_time</code>. 创建时间
     */
    public final TableField<TaskAttachRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_task_attach</code> table reference
     */
    public TaskAttach() {
        this("t_task_attach", null);
    }

    /**
     * Create an aliased <code>train.t_task_attach</code> table reference
     */
    public TaskAttach(String alias) {
        this(alias, TASK_ATTACH);
    }

    private TaskAttach(String alias, Table<TaskAttachRecord> aliased) {
        this(alias, aliased, null);
    }

    private TaskAttach(String alias, Table<TaskAttachRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "作业附件关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TaskAttachRecord> getPrimaryKey() {
        return Keys.KEY_T_TASK_ATTACH_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TaskAttachRecord>> getKeys() {
        return Arrays.<UniqueKey<TaskAttachRecord>>asList(Keys.KEY_T_TASK_ATTACH_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskAttach as(String alias) {
        return new TaskAttach(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TaskAttach rename(String name) {
        return new TaskAttach(name, null);
    }
}
