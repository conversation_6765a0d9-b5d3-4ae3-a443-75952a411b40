/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.F2fCourseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 面授课程库表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class F2fCourse extends TableImpl<F2fCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_f2f_course</code>
     */
    public static final F2fCourse F2F_COURSE = new F2fCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<F2fCourseRecord> getRecordType() {
        return F2fCourseRecord.class;
    }

    /**
     * The column <code>train.t_f2f_course.f_id</code>. 主键
     */
    public final TableField<F2fCourseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_f2f_course.f_name</code>. 课程名称
     */
    public final TableField<F2fCourseRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "课程名称");

    /**
     * The column <code>train.t_f2f_course.f_sequence</code>. 课程分类/序列
     */
    public final TableField<F2fCourseRecord, String> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程分类/序列");

    /**
     * The column <code>train.t_f2f_course.f_obj</code>. 授课对象
     */
    public final TableField<F2fCourseRecord, String> OBJ = createField("f_obj", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "授课对象");

    /**
     * The column <code>train.t_f2f_course.f_keyword</code>. 关键词
     */
    public final TableField<F2fCourseRecord, String> KEYWORD = createField("f_keyword", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "关键词");

    /**
     * The column <code>train.t_f2f_course.f_lecturer</code>. 课程讲师
     */
    public final TableField<F2fCourseRecord, String> LECTURER = createField("f_lecturer", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程讲师");

    /**
     * The column <code>train.t_f2f_course.f_course_duration</code>. 课程时长
     */
    public final TableField<F2fCourseRecord, Double> COURSE_DURATION = createField("f_course_duration", org.jooq.impl.SQLDataType.FLOAT, this, "课程时长");

    /**
     * The column <code>train.t_f2f_course.f_course_reward</code>. 课酬
     */
    public final TableField<F2fCourseRecord, Double> COURSE_REWARD = createField("f_course_reward", org.jooq.impl.SQLDataType.DOUBLE, this, "课酬");

    /**
     * The column <code>train.t_f2f_course.f_satisfied_degree</code>. 平均满意度
     */
    public final TableField<F2fCourseRecord, Double> SATISFIED_DEGREE = createField("f_satisfied_degree", org.jooq.impl.SQLDataType.FLOAT, this, "平均满意度");

    /**
     * The column <code>train.t_f2f_course.f_teach_method</code>. 教学方式
     */
    public final TableField<F2fCourseRecord, String> TEACH_METHOD = createField("f_teach_method", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "教学方式");

    /**
     * The column <code>train.t_f2f_course.f_trainee_number1</code>. 学员人数起始
     */
    public final TableField<F2fCourseRecord, Integer> TRAINEE_NUMBER1 = createField("f_trainee_number1", org.jooq.impl.SQLDataType.INTEGER, this, "学员人数起始");

    /**
     * The column <code>train.t_f2f_course.f_trainee_number2</code>. 学员人数结束
     */
    public final TableField<F2fCourseRecord, Integer> TRAINEE_NUMBER2 = createField("f_trainee_number2", org.jooq.impl.SQLDataType.INTEGER, this, "学员人数结束");

    /**
     * The column <code>train.t_f2f_course.f_summary</code>. 课程简介
     */
    public final TableField<F2fCourseRecord, String> SUMMARY = createField("f_summary", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "课程简介");

    /**
     * The column <code>train.t_f2f_course.f_remark</code>. 备注
     */
    public final TableField<F2fCourseRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "备注");

    /**
     * The column <code>train.t_f2f_course.f_target</code>. 课程目标
     */
    public final TableField<F2fCourseRecord, String> TARGET = createField("f_target", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "课程目标");

    /**
     * The column <code>train.t_f2f_course.f_outline</code>. 课程大纲
     */
    public final TableField<F2fCourseRecord, String> OUTLINE = createField("f_outline", org.jooq.impl.SQLDataType.VARCHAR.length(5000), this, "课程大纲");

    /**
     * The column <code>train.t_f2f_course.f_institution</code>. 课程所属机构
     */
    public final TableField<F2fCourseRecord, String> INSTITUTION = createField("f_institution", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "课程所属机构");

    /**
     * The column <code>train.t_f2f_course.f_contacts</code>. 机构联系人
     */
    public final TableField<F2fCourseRecord, String> CONTACTS = createField("f_contacts", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "机构联系人");

    /**
     * The column <code>train.t_f2f_course.f_contacts_tel</code>. 机构联系人电话
     */
    public final TableField<F2fCourseRecord, String> CONTACTS_TEL = createField("f_contacts_tel", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "机构联系人电话");

    /**
     * The column <code>train.t_f2f_course.f_contacts_email</code>. 机构联系人邮箱
     */
    public final TableField<F2fCourseRecord, String> CONTACTS_EMAIL = createField("f_contacts_email", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "机构联系人邮箱");

    /**
     * The column <code>train.t_f2f_course.f_create_time</code>. 创建时间
     */
    public final TableField<F2fCourseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_f2f_course.f_pay</code>. 实付
     */
    public final TableField<F2fCourseRecord, Double> PAY = createField("f_pay", org.jooq.impl.SQLDataType.DOUBLE, this, "实付");

    /**
     * The column <code>train.t_f2f_course.f_tax</code>. 税金
     */
    public final TableField<F2fCourseRecord, Double> TAX = createField("f_tax", org.jooq.impl.SQLDataType.DOUBLE, this, "税金");

    /**
     * The column <code>train.t_f2f_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<F2fCourseRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_f2f_course.f_create_member</code>. 创建人ID
     */
    public final TableField<F2fCourseRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_f2f_course.f_type</code>. 0自建 1班级
     */
    public final TableField<F2fCourseRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "0自建 1班级");

    /**
     * The column <code>train.t_f2f_course.f_class_id</code>. 班级id
     */
    public final TableField<F2fCourseRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级id");

    /**
     * The column <code>train.t_f2f_course.f_course_date</code>. 上课日期
     */
    public final TableField<F2fCourseRecord, Long> COURSE_DATE = createField("f_course_date", org.jooq.impl.SQLDataType.BIGINT, this, "上课日期");

    /**
     * The column <code>train.t_f2f_course.f_bank_user</code>. 开户人
     */
    public final TableField<F2fCourseRecord, String> BANK_USER = createField("f_bank_user", org.jooq.impl.SQLDataType.VARCHAR.length(20), this, "开户人");

    /**
     * The column <code>train.t_f2f_course.f_bank_identity</code>. 开户人身份证
     */
    public final TableField<F2fCourseRecord, String> BANK_IDENTITY = createField("f_bank_identity", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "开户人身份证");

    /**
     * The column <code>train.t_f2f_course.f_bank</code>. 开户行
     */
    public final TableField<F2fCourseRecord, String> BANK = createField("f_bank", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "开户行");

    /**
     * The column <code>train.t_f2f_course.f_bank_card</code>. 卡号
     */
    public final TableField<F2fCourseRecord, String> BANK_CARD = createField("f_bank_card", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "卡号");

    /**
     * The column <code>train.t_f2f_course.f_start_time</code>. 开始时间
     */
    public final TableField<F2fCourseRecord, String> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "开始时间");

    /**
     * The column <code>train.t_f2f_course.f_end_time</code>. 结束时间
     */
    public final TableField<F2fCourseRecord, String> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "结束时间");

    /**
     * The column <code>train.t_f2f_course.f_status</code>. 0在库 1退库
     */
    public final TableField<F2fCourseRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "0在库 1退库");

    /**
     * The column <code>train.t_f2f_course.f_attachment_id</code>. 附件id
     */
    public final TableField<F2fCourseRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "附件id");

    /**
     * The column <code>train.t_f2f_course.f_attachment_name</code>. 附件名称
     */
    public final TableField<F2fCourseRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "附件名称");

    /**
     * Create a <code>train.t_f2f_course</code> table reference
     */
    public F2fCourse() {
        this("t_f2f_course", null);
    }

    /**
     * Create an aliased <code>train.t_f2f_course</code> table reference
     */
    public F2fCourse(String alias) {
        this(alias, F2F_COURSE);
    }

    private F2fCourse(String alias, Table<F2fCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private F2fCourse(String alias, Table<F2fCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "面授课程库表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<F2fCourseRecord> getPrimaryKey() {
        return Keys.KEY_T_F2F_COURSE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<F2fCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<F2fCourseRecord>>asList(Keys.KEY_T_F2F_COURSE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public F2fCourse as(String alias) {
        return new F2fCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public F2fCourse rename(String name) {
        return new F2fCourse(name, null);
    }
}
