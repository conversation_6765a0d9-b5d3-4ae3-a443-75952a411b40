/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CollectingCourseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CollectingCourse extends TableImpl<CollectingCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_collecting_course</code>
     */
    public static final CollectingCourse COLLECTING_COURSE = new CollectingCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CollectingCourseRecord> getRecordType() {
        return CollectingCourseRecord.class;
    }

    /**
     * The column <code>train.t_collecting_course.f_id</code>.
     */
    public final TableField<CollectingCourseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_collecting_course.f_name</code>. 集采课程名称
     */
    public final TableField<CollectingCourseRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "集采课程名称");

    /**
     * The column <code>train.t_collecting_course.f_create_time</code>. 创建时间
     */
    public final TableField<CollectingCourseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_collecting_course</code> table reference
     */
    public CollectingCourse() {
        this("t_collecting_course", null);
    }

    /**
     * Create an aliased <code>train.t_collecting_course</code> table reference
     */
    public CollectingCourse(String alias) {
        this(alias, COLLECTING_COURSE);
    }

    private CollectingCourse(String alias, Table<CollectingCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private CollectingCourse(String alias, Table<CollectingCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CollectingCourseRecord> getPrimaryKey() {
        return Keys.KEY_T_COLLECTING_COURSE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CollectingCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<CollectingCourseRecord>>asList(Keys.KEY_T_COLLECTING_COURSE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectingCourse as(String alias) {
        return new CollectingCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CollectingCourse rename(String name) {
        return new CollectingCourse(name, null);
    }
}
