/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IGrantDetail;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GrantDetailEntity extends BaseEntity implements IGrantDetail {

    private static final long serialVersionUID = 1L;

    private String grantId;
    private String memberId;
    private String organizationId;
    private String uri;
    private String menuId;
    private String operatorTypes;

    public GrantDetailEntity() {}

    public GrantDetailEntity(GrantDetailEntity value) {
        this.grantId = value.grantId;
        this.memberId = value.memberId;
        this.organizationId = value.organizationId;
        this.uri = value.uri;
        this.menuId = value.menuId;
        this.operatorTypes = value.operatorTypes;
    }

    public GrantDetailEntity(
        String id,
        String grantId,
        String memberId,
        String organizationId,
        Long   createTime,
        String uri,
        String menuId,
        String operatorTypes
    ) {
        super.setId(id);
        this.grantId = grantId;
        this.memberId = memberId;
        this.organizationId = organizationId;
        super.setCreateTime(createTime);
        this.uri = uri;
        this.menuId = menuId;
        this.operatorTypes = operatorTypes;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getGrantId() {
        return this.grantId;
    }

    @Override
    public void setGrantId(String grantId) {
        this.grantId = grantId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getUri() {
        return this.uri;
    }

    @Override
    public void setUri(String uri) {
        this.uri = uri;
    }

    @Override
    public String getMenuId() {
        return this.menuId;
    }

    @Override
    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    @Override
    public String getOperatorTypes() {
        return this.operatorTypes;
    }

    @Override
    public void setOperatorTypes(String operatorTypes) {
        this.operatorTypes = operatorTypes;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("GrantDetailEntity (");

        sb.append(getId());
        sb.append(", ").append(grantId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(uri);
        sb.append(", ").append(menuId);
        sb.append(", ").append(operatorTypes);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IGrantDetail from) {
        setId(from.getId());
        setGrantId(from.getGrantId());
        setMemberId(from.getMemberId());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setUri(from.getUri());
        setMenuId(from.getMenuId());
        setOperatorTypes(from.getOperatorTypes());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IGrantDetail> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends GrantDetailEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.GrantDetailRecord r = new com.zxy.product.train.jooq.tables.records.GrantDetailRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.ID, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.GRANT_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.GRANT_ID, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.GRANT_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.MEMBER_ID, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.ORGANIZATION_ID, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.URI) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.URI, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.URI));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.MENU_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.MENU_ID, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.MENU_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.OPERATOR_TYPES) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.OPERATOR_TYPES, record.getValue(com.zxy.product.train.jooq.tables.GrantDetail.GRANT_DETAIL.OPERATOR_TYPES));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
