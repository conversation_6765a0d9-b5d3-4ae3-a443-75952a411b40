package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementReplyEntity;

public class StudyTeamAchievementReply extends StudyTeamAchievementReplyEntity {
    private static final long serialVersionUID = -6393155514577534096L;

    /**
     * 是否隐藏
     * 1.是 0.否
     */
    public static final Integer TOP_STATUS_HIDE = 1;
    public static final Integer TOP_STATUS_SHOW = 0;

    /**
     * 是否删除
     * 1.是 0.否
     */
    public static final int DELETE_FALSE = 0;
    public static final int DELETE_TRUE = 1;

    /**
     * 回复级别
     * 1.回复的评论 2.回复的回复
     */
    public static final int LEVEL_COMMENT = 1;
    public static final int LEVEL_REPLAY = 2;

    /**
     * 点赞id
     */
    private String praiseId;

    /**
     * 回复人信息以及被回复人信息
     */
    private String memberName;
    private String memberHeadPath;
    private String toMemberName;
    private String toMemberHeadPath;
    private String createTimeStr;

    public String getPraiseId() {
        return praiseId;
    }

    public void setPraiseId(String praiseId) {
        this.praiseId = praiseId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberHeadPath() {
        return memberHeadPath;
    }

    public void setMemberHeadPath(String memberHeadPath) {
        this.memberHeadPath = memberHeadPath;
    }

    public String getToMemberName() {
        return toMemberName;
    }

    public void setToMemberName(String toMemberName) {
        this.toMemberName = toMemberName;
    }

    public String getToMemberHeadPath() {
        return toMemberHeadPath;
    }

    public void setToMemberHeadPath(String toMemberHeadPath) {
        this.toMemberHeadPath = toMemberHeadPath;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }
}
