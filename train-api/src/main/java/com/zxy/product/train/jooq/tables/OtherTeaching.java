/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.OtherTeachingRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 其他教学教研记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OtherTeaching extends TableImpl<OtherTeachingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_other_teaching</code>
     */
    public static final OtherTeaching OTHER_TEACHING = new OtherTeaching();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OtherTeachingRecord> getRecordType() {
        return OtherTeachingRecord.class;
    }

    /**
     * The column <code>train.t_other_teaching.f_id</code>. 主键
     */
    public final TableField<OtherTeachingRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_other_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public final TableField<OtherTeachingRecord, String> LECTURER_ID = createField("f_lecturer_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程讲师ID");

    /**
     * The column <code>train.t_other_teaching.f_course_name</code>. 教研名称
     */
    public final TableField<OtherTeachingRecord, String> COURSE_NAME = createField("f_course_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "教研名称");

    /**
     * The column <code>train.t_other_teaching.f_teaching_type</code>. 教学教研类型1：授课；2：课程开发；3：电子课件开发；4：案例开发；5：业务专题研究；6：内训工作支撑
     */
    public final TableField<OtherTeachingRecord, Integer> TEACHING_TYPE = createField("f_teaching_type", org.jooq.impl.SQLDataType.INTEGER, this, "教学教研类型1：授课；2：课程开发；3：电子课件开发；4：案例开发；5：业务专题研究；6：内训工作支撑");

    /**
     * The column <code>train.t_other_teaching.f_activity_level</code>. 活动级别
     */
    public final TableField<OtherTeachingRecord, String> ACTIVITY_LEVEL = createField("f_activity_level", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "活动级别");

    /**
     * The column <code>train.t_other_teaching.f_start_time</code>. 参与开始时间
     */
    public final TableField<OtherTeachingRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "参与开始时间");

    /**
     * The column <code>train.t_other_teaching.f_end_time</code>. 参与结束时间
     */
    public final TableField<OtherTeachingRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "参与结束时间");

    /**
     * The column <code>train.t_other_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public final TableField<OtherTeachingRecord, Double> SATISFIED_DEGREE = createField("f_satisfied_degree", org.jooq.impl.SQLDataType.FLOAT, this, "平均满意度(十分制，小数点后保留一位)");

    /**
     * The column <code>train.t_other_teaching.f_status</code>. 是否通过0：是1：否
     */
    public final TableField<OtherTeachingRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "是否通过0：是1：否");

    /**
     * The column <code>train.t_other_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    public final TableField<OtherTeachingRecord, String> SATISFIED_EVALUATE = createField("f_satisfied_evaluate", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "满意度评价");

    /**
     * The column <code>train.t_other_teaching.f_other_instructions</code>. 其他情况说明
     */
    public final TableField<OtherTeachingRecord, String> OTHER_INSTRUCTIONS = createField("f_other_instructions", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "其他情况说明");

    /**
     * The column <code>train.t_other_teaching.f_create_time</code>. 创建时间
     */
    public final TableField<OtherTeachingRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_other_teaching.f_approval_time</code>. 审核时间
     */
    public final TableField<OtherTeachingRecord, Long> APPROVAL_TIME = createField("f_approval_time", org.jooq.impl.SQLDataType.BIGINT, this, "审核时间");

    /**
     * The column <code>train.t_other_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    public final TableField<OtherTeachingRecord, Integer> APPROVAL_STATUS = createField("f_approval_status", org.jooq.impl.SQLDataType.INTEGER, this, "是否通过0：待审核1：通过2：拒绝");

    /**
     * The column <code>train.t_other_teaching.f_organization_id</code>. 归属部门
     */
    public final TableField<OtherTeachingRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "归属部门");

    /**
     * The column <code>train.t_other_teaching.f_create_member</code>. 创建ID
     */
    public final TableField<OtherTeachingRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建ID");

    /**
     * The column <code>train.t_other_teaching.f_approval_member</code>. 审核人ID
     */
    public final TableField<OtherTeachingRecord, String> APPROVAL_MEMBER = createField("f_approval_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "审核人ID");

    /**
     * The column <code>train.t_other_teaching.f_remark</code>. 备注
     */
    public final TableField<OtherTeachingRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "备注");

    /**
     * The column <code>train.t_other_teaching.f_lecturer_name</code>. 讲师名称
     */
    public final TableField<OtherTeachingRecord, String> LECTURER_NAME = createField("f_lecturer_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "讲师名称");

    /**
     * The column <code>train.t_other_teaching.f_source</code>. 0：讲师自己申请 1：管理员添加
     */
    public final TableField<OtherTeachingRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER, this, "0：讲师自己申请 1：管理员添加");

    /**
     * The column <code>train.t_other_teaching.f_course_type</code>. 分类（同面授课程分类）
     */
    public final TableField<OtherTeachingRecord, String> COURSE_TYPE = createField("f_course_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "分类（同面授课程分类）");

    /**
     * The column <code>train.t_other_teaching.f_reference_time</code>. 时长(小时)
     */
    public final TableField<OtherTeachingRecord, Double> REFERENCE_TIME = createField("f_reference_time", org.jooq.impl.SQLDataType.DOUBLE, this, "时长(小时)");

    /**
     * The column <code>train.t_other_teaching.f_object_oriented</code>. 面向对象
     */
    public final TableField<OtherTeachingRecord, String> OBJECT_ORIENTED = createField("f_object_oriented", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "面向对象");

    /**
     * The column <code>train.t_other_teaching.f_description</code>. 简介
     */
    public final TableField<OtherTeachingRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "简介");

    /**
     * Create a <code>train.t_other_teaching</code> table reference
     */
    public OtherTeaching() {
        this("t_other_teaching", null);
    }

    /**
     * Create an aliased <code>train.t_other_teaching</code> table reference
     */
    public OtherTeaching(String alias) {
        this(alias, OTHER_TEACHING);
    }

    private OtherTeaching(String alias, Table<OtherTeachingRecord> aliased) {
        this(alias, aliased, null);
    }

    private OtherTeaching(String alias, Table<OtherTeachingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "其他教学教研记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OtherTeachingRecord> getPrimaryKey() {
        return Keys.KEY_T_OTHER_TEACHING_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OtherTeachingRecord>> getKeys() {
        return Arrays.<UniqueKey<OtherTeachingRecord>>asList(Keys.KEY_T_OTHER_TEACHING_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OtherTeaching as(String alias) {
        return new OtherTeaching(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public OtherTeaching rename(String name) {
        return new OtherTeaching(name, null);
    }
}
