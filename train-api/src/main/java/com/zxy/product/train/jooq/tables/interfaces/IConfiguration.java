/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IConfiguration extends Serializable {

    /**
     * Setter for <code>train.t_configuration.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_configuration.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_configuration.f_configuration_name</code>. 配置项名称
     */
    public void setConfigurationName(String value);

    /**
     * Getter for <code>train.t_configuration.f_configuration_name</code>. 配置项名称
     */
    public String getConfigurationName();

    /**
     * Setter for <code>train.t_configuration.f_type</code>. 配置类型（1费用类型2培训类型3培训补贴类型4人员类型5班级类型6教室信息7额度配置8常用版务人员9
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_configuration.f_type</code>. 配置类型（1费用类型2培训类型3培训补贴类型4人员类型5班级类型6教室信息7额度配置8常用版务人员9
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_configuration.f_organization_id</code>. 所属部门
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_configuration.f_organization_id</code>. 所属部门
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_configuration.f_configuration_amount</code>. 配置数
     */
    public void setConfigurationAmount(Integer value);

    /**
     * Getter for <code>train.t_configuration.f_configuration_amount</code>. 配置数
     */
    public Integer getConfigurationAmount();

    /**
     * Setter for <code>train.t_configuration.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_configuration.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_configuration.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_configuration.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IConfiguration
     */
    public void from(IConfiguration from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IConfiguration
     */
    public <E extends IConfiguration> E into(E into);
}
