package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.MemberEntity;

public class Member extends MemberEntity {

	/*
	 * Created by 田聪 on 2017/02/08
	 */
	private static final long serialVersionUID = -3245144436359209723L;
	public static final Integer STATUS_ENABLED = 1;
	public static final Integer STATUS_DISABLED = 0;
	private String organizationName;
	private String companyName;
	private Integer organizationLevel;
	private String jobName;
	private String positionLevel;
	private Organization organization;
	private Organization compOrganization;
	private Position majoyPosition;
	private String levelName;

	private Integer type;

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public static final String STATUS_REFUSE = "0c141d25-1a50-4561-958e-b6df9abcb5b1"; // 在职
	private Organization rootOrganization; // 所属根组织

	private Trainee trainee;
	
	private String path;

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getPositionLevel() {
		return positionLevel;
	}

	public void setPositionLevel(String positionLevel) {
		this.positionLevel = positionLevel;
	}

	public Trainee getTrainee() {
		return trainee;
	}

	public void setTrainee(Trainee trainee) {
		this.trainee = trainee;
	}

	public Integer getOrganizationLevel() {
		return organizationLevel;
	}

	public void setOrganizationLevel(Integer organizationLevel) {
		this.organizationLevel = organizationLevel;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public Organization getCompOrganization() {
		return compOrganization;
	}

	public void setCompOrganization(Organization compOrganization) {
		this.compOrganization = compOrganization;
	}

	public Position getMajoyPosition() {
		return majoyPosition;
	}

	public void setMajoyPosition(Position majoyPosition) {
		this.majoyPosition = majoyPosition;
	}

	public String getLevelName() {
		return levelName;
	}

	public void setLevelName(String levelName) {
		this.levelName = levelName;
	}

	public Organization getRootOrganization() {
		return rootOrganization;
	}

	public void setRootOrganization(Organization rootOrganization) {
		this.rootOrganization = rootOrganization;
	}
}
