/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IProjectOccupy extends Serializable {

    /**
     * Setter for <code>train.t_project_occupy.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_project_occupy.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_project_occupy.f_year</code>. 年份
     */
    public void setYear(Integer value);

    /**
     * Getter for <code>train.t_project_occupy.f_year</code>. 年份
     */
    public Integer getYear();

    /**
     * Setter for <code>train.t_project_occupy.f_month</code>. 月份
     */
    public void setMonth(Integer value);

    /**
     * Getter for <code>train.t_project_occupy.f_month</code>. 月份
     */
    public Integer getMonth();

    /**
     * Setter for <code>train.t_project_occupy.f_day</code>. 日期
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>train.t_project_occupy.f_day</code>. 日期
     */
    public Integer getDay();

    /**
     * Setter for <code>train.t_project_occupy.f_date</code>. 日期时间戳
     */
    public void setDate(Long value);

    /**
     * Getter for <code>train.t_project_occupy.f_date</code>. 日期时间戳
     */
    public Long getDate();

    /**
     * Setter for <code>train.t_project_occupy.f_default</code>. 默认资源数
     */
    public void setDefault(Integer value);

    /**
     * Getter for <code>train.t_project_occupy.f_default</code>. 默认资源数
     */
    public Integer getDefault();

    /**
     * Setter for <code>train.t_project_occupy.f_available</code>. 可用资源数
     */
    public void setAvailable(Integer value);

    /**
     * Getter for <code>train.t_project_occupy.f_available</code>. 可用资源数
     */
    public Integer getAvailable();

    /**
     * Setter for <code>train.t_project_occupy.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_project_occupy.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_project_occupy.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_project_occupy.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_project_occupy.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_project_occupy.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IProjectOccupy
     */
    public void from(IProjectOccupy from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IProjectOccupy
     */
    public <E extends IProjectOccupy> E into(E into);
}
