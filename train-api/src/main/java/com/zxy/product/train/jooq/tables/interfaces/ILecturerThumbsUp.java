/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 讲师点赞记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILecturerThumbsUp extends Serializable {

    /**
     * Setter for <code>train.t_lecturer_thumbs_up.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_lecturer_thumbs_up.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_lecturer_thumbs_up.f_lecturer_id</code>. 讲师ID
     */
    public void setLecturerId(String value);

    /**
     * Getter for <code>train.t_lecturer_thumbs_up.f_lecturer_id</code>. 讲师ID
     */
    public String getLecturerId();

    /**
     * Setter for <code>train.t_lecturer_thumbs_up.f_member_id</code>. 用户ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer_thumbs_up.f_member_id</code>. 用户ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_lecturer_thumbs_up.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_lecturer_thumbs_up.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILecturerThumbsUp
     */
    public void from(ILecturerThumbsUp from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILecturerThumbsUp
     */
    public <E extends ILecturerThumbsUp> E into(E into);
}
