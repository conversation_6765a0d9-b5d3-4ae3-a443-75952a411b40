/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamAchievementPraise;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamAchievementPraise;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 团队学习班-点赞明细表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamAchievementPraiseRecord extends UpdatableRecordImpl<StudyTeamAchievementPraiseRecord> implements Record7<String, String, Integer, String, Integer, Long, String>, IStudyTeamAchievementPraise {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_object_id</code>. 被点赞id
     */
    @Override
    public void setObjectId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_object_id</code>. 被点赞id
     */
    @Override
    public String getObjectId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_object_type</code>. 点赞对象类型 1:评论，2：回复
     */
    @Override
    public void setObjectType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_object_type</code>. 点赞对象类型 1:评论，2：回复
     */
    @Override
    public Integer getObjectType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_member_id</code>. 点赞用户id
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_member_id</code>. 点赞用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_organization_id</code>. 所属组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_organization_id</code>. 所属组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, String, Integer, Long, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, String, Integer, Long, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.OBJECT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.OBJECT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getObjectId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getObjectType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord value2(String value) {
        setObjectId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord value3(Integer value) {
        setObjectType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord value5(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord value7(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraiseRecord values(String value1, String value2, Integer value3, String value4, Integer value5, Long value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamAchievementPraise from) {
        setId(from.getId());
        setObjectId(from.getObjectId());
        setObjectType(from.getObjectType());
        setMemberId(from.getMemberId());
        setDeleteFlag(from.getDeleteFlag());
        setCreateTime(from.getCreateTime());
        setOrganizationId(from.getOrganizationId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamAchievementPraise> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamAchievementPraiseRecord
     */
    public StudyTeamAchievementPraiseRecord() {
        super(StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE);
    }

    /**
     * Create a detached, initialised StudyTeamAchievementPraiseRecord
     */
    public StudyTeamAchievementPraiseRecord(String id, String objectId, Integer objectType, String memberId, Integer deleteFlag, Long createTime, String organizationId) {
        super(StudyTeamAchievementPraise.STUDY_TEAM_ACHIEVEMENT_PRAISE);

        set(0, id);
        set(1, objectId);
        set(2, objectType);
        set(3, memberId);
        set(4, deleteFlag);
        set(5, createTime);
        set(6, organizationId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementPraiseEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementPraiseEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementPraiseEntity)source;
        pojo.into(this);
        return true;
    }
}
