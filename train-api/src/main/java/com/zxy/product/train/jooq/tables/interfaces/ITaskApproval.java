/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 作业审核表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITaskApproval extends Serializable {

    /**
     * Setter for <code>train.t_task_approval.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_task_approval.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_task_approval.t_task_member_id</code>. 用户提交作业ID
     */
    public void setTaskMemberId(String value);

    /**
     * Getter for <code>train.t_task_approval.t_task_member_id</code>. 用户提交作业ID
     */
    public String getTaskMemberId();

    /**
     * Setter for <code>train.t_task_approval.f_approval_member_id</code>. 审批人ID
     */
    public void setApprovalMemberId(String value);

    /**
     * Getter for <code>train.t_task_approval.f_approval_member_id</code>. 审批人ID
     */
    public String getApprovalMemberId();

    /**
     * Setter for <code>train.t_task_approval.f_score</code>. 评分
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>train.t_task_approval.f_score</code>. 评分
     */
    public Integer getScore();

    /**
     * Setter for <code>train.t_task_approval.f_comment</code>. 评语
     */
    public void setComment(String value);

    /**
     * Getter for <code>train.t_task_approval.f_comment</code>. 评语
     */
    public String getComment();

    /**
     * Setter for <code>train.t_task_approval.f_state</code>. 状态 0通过 1打回重新提交
     */
    public void setState(Integer value);

    /**
     * Getter for <code>train.t_task_approval.f_state</code>. 状态 0通过 1打回重新提交
     */
    public Integer getState();

    /**
     * Setter for <code>train.t_task_approval.f_create_time</code>. 审批时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_task_approval.f_create_time</code>. 审批时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITaskApproval
     */
    public void from(ITaskApproval from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITaskApproval
     */
    public <E extends ITaskApproval> E into(E into);
}
