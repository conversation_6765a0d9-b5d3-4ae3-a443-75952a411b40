/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 历史班级表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassHistory extends Serializable {

    /**
     * Setter for <code>train.t_class_history.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_history.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_history.f_project_id</code>. 计划ID
     */
    public void setProjectId(String value);

    /**
     * Getter for <code>train.t_class_history.f_project_id</code>. 计划ID
     */
    public String getProjectId();

    /**
     * Setter for <code>train.t_class_history.f_class_teacher</code>. 班主任
     */
    public void setClassTeacher(String value);

    /**
     * Getter for <code>train.t_class_history.f_class_teacher</code>. 班主任
     */
    public String getClassTeacher();

    /**
     * Setter for <code>train.t_class_history.f_class_teacher_phone</code>. 班主任电话
     */
    public void setClassTeacherPhone(String value);

    /**
     * Getter for <code>train.t_class_history.f_class_teacher_phone</code>. 班主任电话
     */
    public String getClassTeacherPhone();

    /**
     * Setter for <code>train.t_class_history.f_arrive_date</code>. 报道日
     */
    public void setArriveDate(Long value);

    /**
     * Getter for <code>train.t_class_history.f_arrive_date</code>. 报道日
     */
    public Long getArriveDate();

    /**
     * Setter for <code>train.t_class_history.f_return_date</code>. 返程日
     */
    public void setReturnDate(Long value);

    /**
     * Getter for <code>train.t_class_history.f_return_date</code>. 返程日
     */
    public Long getReturnDate();

    /**
     * Setter for <code>train.t_class_history.f_is_outside</code>. 是否外部举办:0否, 1是
     */
    public void setIsOutside(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_is_outside</code>. 是否外部举办:0否, 1是
     */
    public Integer getIsOutside();

    /**
     * Setter for <code>train.t_class_history.f_survey_type</code>. 需求调研方式
     */
    public void setSurveyType(String value);

    /**
     * Getter for <code>train.t_class_history.f_survey_type</code>. 需求调研方式
     */
    public String getSurveyType();

    /**
     * Setter for <code>train.t_class_history.f_target</code>. 培训目标
     */
    public void setTarget(String value);

    /**
     * Getter for <code>train.t_class_history.f_target</code>. 培训目标
     */
    public String getTarget();

    /**
     * Setter for <code>train.t_class_history.f_class_info_type</code>. 班级类别
     */
    public void setClassInfoType(String value);

    /**
     * Getter for <code>train.t_class_history.f_class_info_type</code>. 班级类别
     */
    public String getClassInfoType();

    /**
     * Setter for <code>train.t_class_history.f_student_type</code>. 人员类别
     */
    public void setStudentType(String value);

    /**
     * Getter for <code>train.t_class_history.f_student_type</code>. 人员类别
     */
    public String getStudentType();

    /**
     * Setter for <code>train.t_class_history.f_is_plan</code>. 是否计划内:0否, 1是
     */
    public void setIsPlan(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_is_plan</code>. 是否计划内:0否, 1是
     */
    public Integer getIsPlan();

    /**
     * Setter for <code>train.t_class_history.f_status</code>. 班级状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_status</code>. 班级状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_class_history.f_have_province_leader</code>. 是否有省公司二级经理参加:0否, 1是
     */
    public void setHaveProvinceLeader(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_have_province_leader</code>. 是否有省公司二级经理参加:0否, 1是
     */
    public Integer getHaveProvinceLeader();

    /**
     * Setter for <code>train.t_class_history.f_have_minister</code>. 是否有部长及以上领导参加:0否, 1是
     */
    public void setHaveMinister(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_have_minister</code>. 是否有部长及以上领导参加:0否, 1是
     */
    public Integer getHaveMinister();

    /**
     * Setter for <code>train.t_class_history.f_need_group_photo</code>. 是否合影:0否, 1是
     */
    public void setNeedGroupPhoto(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_need_group_photo</code>. 是否合影:0否, 1是
     */
    public Integer getNeedGroupPhoto();

    /**
     * Setter for <code>train.t_class_history.f_photo_time</code>. 合影时间
     */
    public void setPhotoTime(Long value);

    /**
     * Getter for <code>train.t_class_history.f_photo_time</code>. 合影时间
     */
    public Long getPhotoTime();

    /**
     * Setter for <code>train.t_class_history.f_need_video</code>. 课程录像:0否, 1是
     */
    public void setNeedVideo(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_need_video</code>. 课程录像:0否, 1是
     */
    public Integer getNeedVideo();

    /**
     * Setter for <code>train.t_class_history.f_video_requirement</code>. 录像需求
     */
    public void setVideoRequirement(String value);

    /**
     * Getter for <code>train.t_class_history.f_video_requirement</code>. 录像需求
     */
    public String getVideoRequirement();

    /**
     * Setter for <code>train.t_class_history.f_need_make_course</code>. 课程制作:0否, 1是
     */
    public void setNeedMakeCourse(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_need_make_course</code>. 课程制作:0否, 1是
     */
    public Integer getNeedMakeCourse();

    /**
     * Setter for <code>train.t_class_history.f_course_video_requirement</code>. 课程制作录像需求
     */
    public void setCourseVideoRequirement(String value);

    /**
     * Getter for <code>train.t_class_history.f_course_video_requirement</code>. 课程制作录像需求
     */
    public String getCourseVideoRequirement();

    /**
     * Setter for <code>train.t_class_history.f_table_type</code>. 教室桌形:1.上课,2.岛型,3.回字形,4.其他
     */
    public void setTableType(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_table_type</code>. 教室桌形:1.上课,2.岛型,3.回字形,4.其他
     */
    public Integer getTableType();

    /**
     * Setter for <code>train.t_class_history.f_other_requirement</code>. 其它需求
     */
    public void setOtherRequirement(String value);

    /**
     * Getter for <code>train.t_class_history.f_other_requirement</code>. 其它需求
     */
    public String getOtherRequirement();

    /**
     * Setter for <code>train.t_class_history.f_rest_room</code>. 客房
     */
    public void setRestRoom(String value);

    /**
     * Getter for <code>train.t_class_history.f_rest_room</code>. 客房
     */
    public String getRestRoom();

    /**
     * Setter for <code>train.t_class_history.f_dining_room</code>. 餐厅
     */
    public void setDiningRoom(String value);

    /**
     * Getter for <code>train.t_class_history.f_dining_room</code>. 餐厅
     */
    public String getDiningRoom();

    /**
     * Setter for <code>train.t_class_history.f_classroom</code>. 教室
     */
    public void setClassroom(String value);

    /**
     * Getter for <code>train.t_class_history.f_classroom</code>. 教室
     */
    public String getClassroom();

    /**
     * Setter for <code>train.t_class_history.f_is_open</code>. 是否开放报名:0否, 1是
     */
    public void setIsOpen(Integer value);

    /**
     * Getter for <code>train.t_class_history.f_is_open</code>. 是否开放报名:0否, 1是
     */
    public Integer getIsOpen();

    /**
     * Setter for <code>train.t_class_history.f_start_time</code>. 报名开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_class_history.f_start_time</code>. 报名开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_class_history.f_end_time</code>. 报名结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_class_history.f_end_time</code>. 报名结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_class_history.f_signup_code</code>. 报名码
     */
    public void setSignupCode(String value);

    /**
     * Getter for <code>train.t_class_history.f_signup_code</code>. 报名码
     */
    public String getSignupCode();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassHistory
     */
    public void from(IClassHistory from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassHistory
     */
    public <E extends IClassHistory> E into(E into);
}
