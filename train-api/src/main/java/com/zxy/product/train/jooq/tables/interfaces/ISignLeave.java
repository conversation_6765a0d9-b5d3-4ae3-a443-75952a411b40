/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 请假表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISignLeave extends Serializable {

    /**
     * Setter for <code>train.t_sign_leave.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_sign_leave.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_sign_leave.f_member_id</code>. 学员ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_sign_leave.f_member_id</code>. 学员ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_sign_leave.f_sign_id</code>. 签到表ID
     */
    public void setSignId(String value);

    /**
     * Getter for <code>train.t_sign_leave.f_sign_id</code>. 签到表ID
     */
    public String getSignId();

    /**
     * Setter for <code>train.t_sign_leave.f_state</code>. 状态 0待审批 1同意 2不同意
     */
    public void setState(Integer value);

    /**
     * Getter for <code>train.t_sign_leave.f_state</code>. 状态 0待审批 1同意 2不同意
     */
    public Integer getState();

    /**
     * Setter for <code>train.t_sign_leave.f_reason</code>. 请假原因
     */
    public void setReason(String value);

    /**
     * Getter for <code>train.t_sign_leave.f_reason</code>. 请假原因
     */
    public String getReason();

    /**
     * Setter for <code>train.t_sign_leave.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_sign_leave.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_sign_leave.f_approval_member</code>. 审批人
     */
    public void setApprovalMember(String value);

    /**
     * Getter for <code>train.t_sign_leave.f_approval_member</code>. 审批人
     */
    public String getApprovalMember();

    /**
     * Setter for <code>train.t_sign_leave.f_approval_time</code>. 审核时间
     */
    public void setApprovalTime(Long value);

    /**
     * Getter for <code>train.t_sign_leave.f_approval_time</code>. 审核时间
     */
    public Long getApprovalTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISignLeave
     */
    public void from(ISignLeave from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISignLeave
     */
    public <E extends ISignLeave> E into(E into);
}
