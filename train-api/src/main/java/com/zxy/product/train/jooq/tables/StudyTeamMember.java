/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamMemberRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习团队成员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamMember extends TableImpl<StudyTeamMemberRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_member</code>
     */
    public static final StudyTeamMember STUDY_TEAM_MEMBER = new StudyTeamMember();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamMemberRecord> getRecordType() {
        return StudyTeamMemberRecord.class;
    }

    /**
     * The column <code>train.t_study_team_member.f_id</code>. ID
     */
    public final TableField<StudyTeamMemberRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_study_team_member.f_team_id</code>. 团队id
     */
    public final TableField<StudyTeamMemberRecord, String> TEAM_ID = createField("f_team_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队id");

    /**
     * The column <code>train.t_study_team_member.f_member_id</code>. 人员id
     */
    public final TableField<StudyTeamMemberRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "人员id");

    /**
     * The column <code>train.t_study_team_member.f_role</code>. 人员角色 1-团队长 2-助理 3-正式人员 4-其他人员 5-历史人员
     */
    public final TableField<StudyTeamMemberRecord, Integer> ROLE = createField("f_role", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "人员角色 1-团队长 2-助理 3-正式人员 4-其他人员 5-历史人员");

    /**
     * The column <code>train.t_study_team_member.f_audit_status</code>. 审核状态: 0 待审核 1 通过 2 拒绝
     */
    public final TableField<StudyTeamMemberRecord, Integer> AUDIT_STATUS = createField("f_audit_status", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "审核状态: 0 待审核 1 通过 2 拒绝");

    /**
     * The column <code>train.t_study_team_member.f_sign_up_time</code>. 报名时间
     */
    public final TableField<StudyTeamMemberRecord, Long> SIGN_UP_TIME = createField("f_sign_up_time", org.jooq.impl.SQLDataType.BIGINT, this, "报名时间");

    /**
     * The column <code>train.t_study_team_member.f_remark</code>. 备注
     */
    public final TableField<StudyTeamMemberRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>train.t_study_team_member.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamMemberRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team_member</code> table reference
     */
    public StudyTeamMember() {
        this("t_study_team_member", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_member</code> table reference
     */
    public StudyTeamMember(String alias) {
        this(alias, STUDY_TEAM_MEMBER);
    }

    private StudyTeamMember(String alias, Table<StudyTeamMemberRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamMember(String alias, Table<StudyTeamMemberRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习团队成员表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamMemberRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_MEMBER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamMemberRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamMemberRecord>>asList(Keys.KEY_T_STUDY_TEAM_MEMBER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMember as(String alias) {
        return new StudyTeamMember(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamMember rename(String name) {
        return new StudyTeamMember(name, null);
    }
}
