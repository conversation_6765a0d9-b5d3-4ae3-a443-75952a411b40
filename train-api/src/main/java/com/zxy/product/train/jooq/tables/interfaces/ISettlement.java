/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISettlement extends Serializable {

    /**
     * Setter for <code>train.t_settlement.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_settlement.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_settlement.f_people_number</code>. 结算人数
     */
    public void setPeopleNumber(Integer value);

    /**
     * Getter for <code>train.t_settlement.f_people_number</code>. 结算人数
     */
    public Integer getPeopleNumber();

    /**
     * Setter for <code>train.t_settlement.f_day_number</code>. 结算天数
     */
    public void setDayNumber(Double value);

    /**
     * Getter for <code>train.t_settlement.f_day_number</code>. 结算天数
     */
    public Double getDayNumber();

    /**
     * Setter for <code>train.t_settlement.f_people_day</code>. 结算人日
     */
    public void setPeopleDay(Double value);

    /**
     * Getter for <code>train.t_settlement.f_people_day</code>. 结算人日
     */
    public Double getPeopleDay();

    /**
     * Setter for <code>train.t_settlement.f_explain</code>. 结算说明
     */
    public void setExplain(String value);

    /**
     * Getter for <code>train.t_settlement.f_explain</code>. 结算说明
     */
    public String getExplain();

    /**
     * Setter for <code>train.t_settlement.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_settlement.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_settlement.f_attend_days</code>. 上课天数
     */
    public void setAttendDays(Double value);

    /**
     * Getter for <code>train.t_settlement.f_attend_days</code>. 上课天数
     */
    public Double getAttendDays();

    /**
     * Setter for <code>train.t_settlement.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_settlement.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_settlement.f_create_mouth</code>. 结算月份
     */
    public void setCreateMouth(String value);

    /**
     * Getter for <code>train.t_settlement.f_create_mouth</code>. 结算月份
     */
    public String getCreateMouth();

    /**
     * Setter for <code>train.t_settlement.f_is_receipt</code>. 是否开票
     */
    public void setIsReceipt(Integer value);

    /**
     * Getter for <code>train.t_settlement.f_is_receipt</code>. 是否开票
     */
    public Integer getIsReceipt();

    /**
     * Setter for <code>train.t_settlement.f_train_day_num</code>. 培训天数
     */
    public void setTrainDayNum(Double value);

    /**
     * Getter for <code>train.t_settlement.f_train_day_num</code>. 培训天数
     */
    public Double getTrainDayNum();

    /**
     * Setter for <code>train.t_settlement.f_finance_month</code>. 财务结算月份
     */
    public void setFinanceMonth(String value);

    /**
     * Getter for <code>train.t_settlement.f_finance_month</code>. 财务结算月份
     */
    public String getFinanceMonth();

    /**
     * Setter for <code>train.t_settlement.f_settlement_quantity</code>. 结算人数(根据培训人数培训日期计算而得，新)
     */
    public void setSettlementQuantity(Integer value);

    /**
     * Getter for <code>train.t_settlement.f_settlement_quantity</code>. 结算人数(根据培训人数培训日期计算而得，新)
     */
    public Integer getSettlementQuantity();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISettlement
     */
    public void from(ISettlement from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISettlement
     */
    public <E extends ISettlement> E into(E into);
}
