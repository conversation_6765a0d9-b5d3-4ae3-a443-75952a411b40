/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.AudienceItemRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 受众项
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AudienceItem extends TableImpl<AudienceItemRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_audience_item</code>
     */
    public static final AudienceItem AUDIENCE_ITEM = new AudienceItem();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AudienceItemRecord> getRecordType() {
        return AudienceItemRecord.class;
    }

    /**
     * The column <code>train.t_audience_item.f_id</code>. ID
     */
    public final TableField<AudienceItemRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_audience_item.f_join_id</code>. 关联ID
     */
    public final TableField<AudienceItemRecord, String> JOIN_ID = createField("f_join_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联ID");

    /**
     * The column <code>train.t_audience_item.f_join_type</code>. 关联类型（1：部门(不包含子部门），2：部门（包含子部门），3：职位，4：职位，5：人员, 6:人员标签）
     */
    public final TableField<AudienceItemRecord, Integer> JOIN_TYPE = createField("f_join_type", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型（1：部门(不包含子部门），2：部门（包含子部门），3：职位，4：职位，5：人员, 6:人员标签）");

    /**
     * The column <code>train.t_audience_item.f_join_name</code>. 关联名称
     */
    public final TableField<AudienceItemRecord, String> JOIN_NAME = createField("f_join_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联名称");

    /**
     * The column <code>train.t_audience_item.f_reference_count</code>. 引用次数
     */
    public final TableField<AudienceItemRecord, Integer> REFERENCE_COUNT = createField("f_reference_count", org.jooq.impl.SQLDataType.INTEGER, this, "引用次数");

    /**
     * The column <code>train.t_audience_item.f_create_time</code>.
     */
    public final TableField<AudienceItemRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * Create a <code>train.t_audience_item</code> table reference
     */
    public AudienceItem() {
        this("t_audience_item", null);
    }

    /**
     * Create an aliased <code>train.t_audience_item</code> table reference
     */
    public AudienceItem(String alias) {
        this(alias, AUDIENCE_ITEM);
    }

    private AudienceItem(String alias, Table<AudienceItemRecord> aliased) {
        this(alias, aliased, null);
    }

    private AudienceItem(String alias, Table<AudienceItemRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "受众项");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AudienceItemRecord> getPrimaryKey() {
        return Keys.KEY_T_AUDIENCE_ITEM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AudienceItemRecord>> getKeys() {
        return Arrays.<UniqueKey<AudienceItemRecord>>asList(Keys.KEY_T_AUDIENCE_ITEM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AudienceItem as(String alias) {
        return new AudienceItem(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AudienceItem rename(String name) {
        return new AudienceItem(name, null);
    }
}
