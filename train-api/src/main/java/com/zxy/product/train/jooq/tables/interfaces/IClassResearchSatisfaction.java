/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 培训班课程问卷满意度结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassResearchSatisfaction extends Serializable {

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_id</code>. 主键id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_id</code>. 主键id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_overall_satisfaction</code>. 总体满意度
     */
    public void setOverallSatisfaction(Integer value);

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_overall_satisfaction</code>. 总体满意度
     */
    public Integer getOverallSatisfaction();

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_course_average_satisfaction</code>. 课程总体满意度均值
     */
    public void setCourseAverageSatisfaction(Integer value);

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_course_average_satisfaction</code>. 课程总体满意度均值
     */
    public Integer getCourseAverageSatisfaction();

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_update_time</code>. 修改时间
     */
    public void setUpdateTime(Long value);

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_update_time</code>. 修改时间
     */
    public Long getUpdateTime();

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassResearchSatisfaction
     */
    public void from(IClassResearchSatisfaction from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassResearchSatisfaction
     */
    public <E extends IClassResearchSatisfaction> E into(E into);
}
