/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CourseSalaryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseSalary extends TableImpl<CourseSalaryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_course_salary</code>
     */
    public static final CourseSalary COURSE_SALARY = new CourseSalary();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseSalaryRecord> getRecordType() {
        return CourseSalaryRecord.class;
    }

    /**
     * The column <code>train.t_course_salary.f_id</code>. 表id
     */
    public final TableField<CourseSalaryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_course_salary.f_lecturer_card</code>. 讲师身份证
     */
    public final TableField<CourseSalaryRecord, String> LECTURER_CARD = createField("f_lecturer_card", org.jooq.impl.SQLDataType.VARCHAR.length(120), this, "讲师身份证");

    /**
     * The column <code>train.t_course_salary.f_lecturer_bank_name</code>. 讲师银行名称
     */
    public final TableField<CourseSalaryRecord, String> LECTURER_BANK_NAME = createField("f_lecturer_bank_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "讲师银行名称");

    /**
     * The column <code>train.t_course_salary.f_lecturer_bank_card</code>. 讲师银行卡号
     */
    public final TableField<CourseSalaryRecord, String> LECTURER_BANK_CARD = createField("f_lecturer_bank_card", org.jooq.impl.SQLDataType.VARCHAR.length(120), this, "讲师银行卡号");

    /**
     * The column <code>train.t_course_salary.f_lecture_id</code>. 讲师id
     */
    public final TableField<CourseSalaryRecord, String> LECTURE_ID = createField("f_lecture_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "讲师id");

    /**
     * The column <code>train.t_course_salary.f_paid_pay</code>. 实付薪酬
     */
    public final TableField<CourseSalaryRecord, Double> PAID_PAY = createField("f_paid_pay", org.jooq.impl.SQLDataType.DOUBLE, this, "实付薪酬");

    /**
     * The column <code>train.t_course_salary.f_pay</code>. 酬金
     */
    public final TableField<CourseSalaryRecord, Double> PAY = createField("f_pay", org.jooq.impl.SQLDataType.DOUBLE, this, "酬金");

    /**
     * The column <code>train.t_course_salary.f_tax</code>. 税金
     */
    public final TableField<CourseSalaryRecord, Double> TAX = createField("f_tax", org.jooq.impl.SQLDataType.DOUBLE, this, "税金");

    /**
     * The column <code>train.t_course_salary.f_course_id</code>. 课程id
     */
    public final TableField<CourseSalaryRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程id");

    /**
     * The column <code>train.t_course_salary.f_class_id</code>. 班级id
     */
    public final TableField<CourseSalaryRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级id");

    /**
     * The column <code>train.t_course_salary.f_create_time</code>. 创建时间
     */
    public final TableField<CourseSalaryRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_course_salary.f_service_charge</code>. 服务费比例
     */
    public final TableField<CourseSalaryRecord, Double> SERVICE_CHARGE = createField("f_service_charge", org.jooq.impl.SQLDataType.DOUBLE, this, "服务费比例");

    /**
     * Create a <code>train.t_course_salary</code> table reference
     */
    public CourseSalary() {
        this("t_course_salary", null);
    }

    /**
     * Create an aliased <code>train.t_course_salary</code> table reference
     */
    public CourseSalary(String alias) {
        this(alias, COURSE_SALARY);
    }

    private CourseSalary(String alias, Table<CourseSalaryRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseSalary(String alias, Table<CourseSalaryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseSalaryRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_SALARY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseSalaryRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseSalaryRecord>>asList(Keys.KEY_T_COURSE_SALARY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseSalary as(String alias) {
        return new CourseSalary(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseSalary rename(String name) {
        return new CourseSalary(name, null);
    }
}
