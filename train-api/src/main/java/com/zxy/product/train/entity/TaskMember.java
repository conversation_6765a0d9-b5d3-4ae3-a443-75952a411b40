package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.TaskMemberEntity;

import java.util.HashMap;
import java.util.Map;

public class TaskMember extends TaskMemberEntity {

	private static final long serialVersionUID = 8796822121027276288L;
	public static final String CONSTANT_KEY = "TASK_MEMBER_CONSTANT_KEY";

	private Member member;// 人员对象

	private Task task;//Task对象

	private TaskApproval taskApproval;// 作业审核对象

	private String organizationName;//部门对象

	private String org2Name;//组织

	private String memberName;//提交人员工编号

	private String memberFullName;//提交人姓名

	private Integer score;//分数对象

	private String attachIds;//附件ID

	private String taskName;
	private String orgName;
	private String submitName;
	private String className;
	private Long submitTime;
	private Long approvalTime;
	private Integer submitStutas;
	private Integer approvalStutas;

	public static final int STATE_UNCOMMITTED=1;	//状态：1未提交
	public static final int STATE_AUDIT=2;			//状态：2审核中
	public static final int STATE_QUALIFIED=3;		//状态：3合格
	public static final int STATE_UNQUALIFIED=4;	//状态：4不合格

	public String getTaskName() {
		return taskName;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getSubmitName() {
		return submitName;
	}

	public void setSubmitName(String submitName) {
		this.submitName = submitName;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public Long getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Long submitTime) {
		this.submitTime = submitTime;
	}

	public Long getApprovalTime() {
		return approvalTime;
	}

	public void setApprovalTime(Long approvalTime) {
		this.approvalTime = approvalTime;
	}

	public Integer getSubmitStutas() {
		return submitStutas;
	}

	public void setSubmitStutas(Integer submitStutas) {
		this.submitStutas = submitStutas;
	}

	public Integer getApprovalStutas() {
		return approvalStutas;
	}

	public void setApprovalStutas(Integer approvalStutas) {
		this.approvalStutas = approvalStutas;
	}

	public String getAttachIds() {
		return attachIds;
	}

	public void setAttachIds(String attachIds) {
		this.attachIds = attachIds;
	}

	public Integer getScore() {
		return score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}

	public Member getMember() {
		return member;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Task getTask() {
		return task;
	}

	public void setTask(Task task) {
		this.task = task;
	}

	public TaskApproval getTaskApproval() {
		return taskApproval;
	}

	public void setTaskApproval(TaskApproval taskApproval) {
		this.taskApproval = taskApproval;
	}

	public String getMemberFullName() {
		return memberFullName;
	}

	public void setMemberFullName(String memberFullName) {
		this.memberFullName = memberFullName;
	}

	public String getOrg2Name() {
		return org2Name;
	}

	public void setOrg2Name(String org2Name) {
		this.org2Name = org2Name;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
}
