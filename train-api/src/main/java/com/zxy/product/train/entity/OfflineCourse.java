package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.F2fCourseEntity;

/**
 * l<PERSON><PERSON><PERSON>
 * 面授课程库
 *
 */
public class OfflineCourse extends F2fCourseEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = 4065781698056623103L;

	private String categoryId;

	private CourseCategory category; // 所属目录

	private String categoryName;

	private Lecturer lecturerT;

	private String lecturerId;

	private String lecturerName;

	private Integer lecturerType;

	private String lecturerUnit;

	private CourseSalary courseSalary;

	private String organizationName;

	private String organizationId;

	private String projectName;

	private Integer index; // 序号,用于生成excel时产生序号

	private Integer historyType;// 是否为历史数据1:是, 0:否
	
	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}


	public CourseSalary getCourseSalary() {
		return courseSalary;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public String getOrganizationId() {
		return organizationId;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setCourseSalary(CourseSalary courseSalary) {
		this.courseSalary = courseSalary;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getLecturerUnit() {
		return lecturerUnit;
	}

	public void setLecturerUnit(String lecturerUnit) {
		this.lecturerUnit = lecturerUnit;
	}


	private Integer course;

	public Integer getCourse() {
		return course;
	}

	public void setCourse(Integer course) {
		this.course = course;
	}

	private String courseCategoryName;

	public String getLecturerId() {
		return lecturerId;
	}

	public void setLecturerId(String lecturerId) {
		this.lecturerId = lecturerId;
	}

	public String getCourseCategoryName() {
		return courseCategoryName;
	}

	public void setCourseCategoryName(String courseCategoryName) {
		this.courseCategoryName = courseCategoryName;
	}

	public String getLecturerName() {
		return lecturerName;
	}

	public void setLecturerName(String lecturerName) {
		this.lecturerName = lecturerName;
	}

	public Integer getLecturerType() {
		return lecturerType;
	}

	public void setLecturerType(Integer lecturerType) {
		this.lecturerType = lecturerType;
	}

	private ClassOfflineCourse classOfflineCourse;

	public ClassOfflineCourse getClassOfflineCourse() {
		return classOfflineCourse;
	}

	public void setClassOfflineCourse(ClassOfflineCourse classOfflineCourse) {
		this.classOfflineCourse = classOfflineCourse;
	}

	public Lecturer getLecturerT() {
		return lecturerT;
	}

	public void setLecturerT(Lecturer lecturerT) {
		this.lecturerT = lecturerT;
	}

	public CourseCategory getCategory() {
		return category;
	}

	public void setCategory(CourseCategory category) {
		this.category = category;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getHistoryType() {
		return historyType;
	}

	public void setHistoryType(Integer historyType) {
		this.historyType = historyType;
	}

}
