/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.LecturerCourseConfig;
import com.zxy.product.train.jooq.tables.interfaces.ILecturerCourseConfig;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 讲师专业序列/讲师课程库课程分类配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerCourseConfigRecord extends UpdatableRecordImpl<LecturerCourseConfigRecord> implements Record10<String, String, String, Integer, String, String, Long, String, String, Integer>, ILecturerCourseConfig {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_lecturer_course_config.f_id</code>. 系统ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_id</code>. 系统ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_name</code>. 专业序列/课程分类名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_name</code>. 专业序列/课程分类名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_parent_id</code>. 上级序列ID【顶级为null】
     */
    @Override
    public void setParentId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_parent_id</code>. 上级序列ID【顶级为null】
     */
    @Override
    public String getParentId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_type</code>. 类型【0：讲师专业序列；1：课程分类】
     */
    @Override
    public void setType(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_type</code>. 类型【0：讲师专业序列；1：课程分类】
     */
    @Override
    public Integer getType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_code</code>. 课程分类/专业序列的目录编码
     */
    @Override
    public void setCode(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_code</code>. 课程分类/专业序列的目录编码
     */
    @Override
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_create_member_id</code>. 创建人ID
     */
    @Override
    public void setCreateMemberId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_create_member_id</code>. 创建人ID
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_path</code>. 保存向上的所有id
     */
    @Override
    public void setPath(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_path</code>. 保存向上的所有id
     */
    @Override
    public String getPath() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_lecturer_course_config.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_lecturer_course_config.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, Integer, String, String, Long, String, String, Integer> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, Integer, String, String, Long, String, String, Integer> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.PARENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return LecturerCourseConfig.LECTURER_COURSE_CONFIG.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getParentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value3(String value) {
        setParentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value4(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value5(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value6(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value7(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value8(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value9(String value) {
        setPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord value10(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfigRecord values(String value1, String value2, String value3, Integer value4, String value5, String value6, Long value7, String value8, String value9, Integer value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILecturerCourseConfig from) {
        setId(from.getId());
        setName(from.getName());
        setParentId(from.getParentId());
        setType(from.getType());
        setCode(from.getCode());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setOrganizationId(from.getOrganizationId());
        setPath(from.getPath());
        setSort(from.getSort());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILecturerCourseConfig> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LecturerCourseConfigRecord
     */
    public LecturerCourseConfigRecord() {
        super(LecturerCourseConfig.LECTURER_COURSE_CONFIG);
    }

    /**
     * Create a detached, initialised LecturerCourseConfigRecord
     */
    public LecturerCourseConfigRecord(String id, String name, String parentId, Integer type, String code, String createMemberId, Long createTime, String organizationId, String path, Integer sort) {
        super(LecturerCourseConfig.LECTURER_COURSE_CONFIG);

        set(0, id);
        set(1, name);
        set(2, parentId);
        set(3, type);
        set(4, code);
        set(5, createMemberId);
        set(6, createTime);
        set(7, organizationId);
        set(8, path);
        set(9, sort);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.LecturerCourseConfigEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.LecturerCourseConfigEntity pojo = (com.zxy.product.train.jooq.tables.pojos.LecturerCourseConfigEntity)source;
        pojo.into(this);
        return true;
    }
}
