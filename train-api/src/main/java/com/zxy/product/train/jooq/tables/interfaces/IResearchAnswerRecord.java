/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IResearchAnswerRecord extends Serializable {

    /**
     * Setter for <code>train.t_research_answer_record.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_research_answer_record.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_research_answer_record.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_research_answer_record.f_research_record_id</code>.
     */
    public void setResearchRecordId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_research_record_id</code>.
     */
    public String getResearchRecordId();

    /**
     * Setter for <code>train.t_research_answer_record.f_question_id</code>.
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_question_id</code>.
     */
    public String getQuestionId();

    /**
     * Setter for <code>train.t_research_answer_record.f_answer</code>.
     */
    public void setAnswer(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_answer</code>.
     */
    public String getAnswer();

    /**
     * Setter for <code>train.t_research_answer_record.f_is_right</code>.
     */
    public void setIsRight(Integer value);

    /**
     * Getter for <code>train.t_research_answer_record.f_is_right</code>.
     */
    public Integer getIsRight();

    /**
     * Setter for <code>train.t_research_answer_record.f_score</code>.
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>train.t_research_answer_record.f_score</code>.
     */
    public Integer getScore();

    /**
     * Setter for <code>train.t_research_answer_record.f_idea</code>. 意见
     */
    public void setIdea(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_idea</code>. 意见
     */
    public String getIdea();

    /**
     * Setter for <code>train.t_research_answer_record.f_member_id</code>. 用户ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_member_id</code>. 用户ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_research_answer_record.f_class_id</code>. 培训班ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_class_id</code>. 培训班ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_research_answer_record.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_research_answer_record.f_course_name</code>. 课程名称
     */
    public void setCourseName(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_course_name</code>. 课程名称
     */
    public String getCourseName();

    /**
     * Setter for <code>train.t_research_answer_record.f_teacher_id</code>. 讲师ID
     */
    public void setTeacherId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_teacher_id</code>. 讲师ID
     */
    public String getTeacherId();

    /**
     * Setter for <code>train.t_research_answer_record.f_teacher_name</code>. 讲师名称
     */
    public void setTeacherName(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_teacher_name</code>. 讲师名称
     */
    public String getTeacherName();

    /**
     * Setter for <code>train.t_research_answer_record.f_questionnaire_question_id</code>. 问题ID
     */
    public void setQuestionnaireQuestionId(String value);

    /**
     * Getter for <code>train.t_research_answer_record.f_questionnaire_question_id</code>. 问题ID
     */
    public String getQuestionnaireQuestionId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IResearchAnswerRecord
     */
    public void from(IResearchAnswerRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IResearchAnswerRecord
     */
    public <E extends IResearchAnswerRecord> E into(E into);
}
