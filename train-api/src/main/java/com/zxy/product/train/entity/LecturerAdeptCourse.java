package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.LecturerAdeptCourseEntity;

public class LecturerAdeptCourse extends LecturerAdeptCourseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3957585910414333914L;
	
	private String courseName;
	private String courseType;
	private String courseAttribute;

	private String lecturerName;
	private String lecturerType;
	
	private String jobName;
	
	private String attributeName;
	
	private String adeptLecturerId;
	
	private Integer type;
	
	private Integer isShare;
	
	private Boolean isLook;

	private String courseAttributeId;
	private String institutionId;

	private String cId; // 值等同于 courseId
	private String lId;	// 值等同于 lecturerId
	
	private String lecturerOrganizationId;;
	private Boolean isGrant;
	

	public Boolean getIsGrant() {
		return isGrant;
	}

	public void setIsGrant(Boolean isGrant) {
		this.isGrant = isGrant;
	}

	public String getLecturerOrganizationId() {
		return lecturerOrganizationId;
	}

	public void setLecturerOrganizationId(String lecturerOrganizationId) {
		this.lecturerOrganizationId = lecturerOrganizationId;
	}
	

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getAdeptLecturerId() {
		return adeptLecturerId;
	}

	public void setAdeptLecturerId(String adeptLecturerId) {
		this.adeptLecturerId = adeptLecturerId;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getAttributeName() {
		return attributeName;
	}

	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}

	
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getCourseType() {
		return courseType;
	}
	public void setCourseType(String courseType) {
		this.courseType = courseType;
	}
	public String getCourseAttribute() {
		return courseAttribute;
	}
	public void setCourseAttribute(String courseAttribute) {
		this.courseAttribute = courseAttribute;
	}
	public String getLecturerName() {
		return lecturerName;
	}
	public void setLecturerName(String lecturerName) {
		this.lecturerName = lecturerName;
	}
	public String getLecturerType() {
		return lecturerType;
	}
	public void setLecturerType(String lecturerType) {
		this.lecturerType = lecturerType;
	}

	public String getCId() {
		return cId;
	}

	public void setCId(String cId) {
		this.cId = cId;
	}

	public String getLId() {
		return lId;
	}

	public void setLId(String lId) {
		this.lId = lId;
	}

	public Integer getIsShare() {
		return isShare;
	}

	public void setIsShare(Integer isShare) {
		this.isShare = isShare;
	}

	public Boolean getIsLook() {
		return isLook;
	}

	public void setIsLook(Boolean isLook) {
		this.isLook = isLook;
	}

	public String getCourseAttributeId() {
		return courseAttributeId;
	}

	public void setCourseAttributeId(String courseAttributeId) {
		this.courseAttributeId = courseAttributeId;
	}

	public String getInstitutionId() {
		return institutionId;
	}

	public void setInstitutionId(String institutionId) {
		this.institutionId = institutionId;
	}

}
