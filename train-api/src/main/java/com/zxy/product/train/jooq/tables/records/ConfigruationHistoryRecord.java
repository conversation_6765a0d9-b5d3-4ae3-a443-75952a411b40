/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ConfigruationHistory;
import com.zxy.product.train.jooq.tables.interfaces.IConfigruationHistory;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 历史配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConfigruationHistoryRecord extends UpdatableRecordImpl<ConfigruationHistoryRecord> implements Record4<String, Integer, String, Long>, IConfigruationHistory {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_configruation_history.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_configruation_history.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_configruation_history.f_type</code>. 类别:1A,2B,3C,4G,5M,6O,7R,8T,9Z
     */
    @Override
    public void setType(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_configruation_history.f_type</code>. 类别:1A,2B,3C,4G,5M,6O,7R,8T,9Z
     */
    @Override
    public Integer getType() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>train.t_configruation_history.f_name</code>. 名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_configruation_history.f_name</code>. 名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_configruation_history.f_organization_id</code>. 归属机构
     */
    @Override
    public void setOrganizationId(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_configruation_history.f_organization_id</code>. 归属机构
     */
    @Override
    public Long getOrganizationId() {
        return (Long) get(3);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, Integer, String, Long> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, Integer, String, Long> valuesRow() {
        return (Row4) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ConfigruationHistory.CONFIGRUATION_HISTORY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return ConfigruationHistory.CONFIGRUATION_HISTORY.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ConfigruationHistory.CONFIGRUATION_HISTORY.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return ConfigruationHistory.CONFIGRUATION_HISTORY.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigruationHistoryRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigruationHistoryRecord value2(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigruationHistoryRecord value3(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigruationHistoryRecord value4(Long value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigruationHistoryRecord values(String value1, Integer value2, String value3, Long value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IConfigruationHistory from) {
        setId(from.getId());
        setType(from.getType());
        setName(from.getName());
        setOrganizationId(from.getOrganizationId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IConfigruationHistory> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConfigruationHistoryRecord
     */
    public ConfigruationHistoryRecord() {
        super(ConfigruationHistory.CONFIGRUATION_HISTORY);
    }

    /**
     * Create a detached, initialised ConfigruationHistoryRecord
     */
    public ConfigruationHistoryRecord(String id, Integer type, String name, Long organizationId) {
        super(ConfigruationHistory.CONFIGRUATION_HISTORY);

        set(0, id);
        set(1, type);
        set(2, name);
        set(3, organizationId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ConfigruationHistoryEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ConfigruationHistoryEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ConfigruationHistoryEntity)source;
        pojo.into(this);
        return true;
    }
}
