/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.SolutionConfiguration;
import com.zxy.product.train.jooq.tables.interfaces.ISolutionConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 智慧教务-配置情况
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SolutionConfigurationRecord extends UpdatableRecordImpl<SolutionConfigurationRecord> implements Record7<String, String, String, Integer, Long, String, Integer>, ISolutionConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_solution_configuration.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_solution_configuration.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_solution_configuration.f_name</code>. 名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_solution_configuration.f_name</code>. 名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_solution_configuration.f_code</code>. 编码
     */
    @Override
    public void setCode(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_solution_configuration.f_code</code>. 编码
     */
    @Override
    public String getCode() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_solution_configuration.f_order</code>. 排序
     */
    @Override
    public void setOrder(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_solution_configuration.f_order</code>. 排序
     */
    @Override
    public Integer getOrder() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_solution_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_solution_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_solution_configuration.f_create_member_id</code>. 创建人
     */
    @Override
    public void setCreateMemberId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_solution_configuration.f_create_member_id</code>. 创建人
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_solution_configuration.f_type</code>. 0=方案策划情况配置,1=实施方式配置
     */
    @Override
    public void setType(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_solution_configuration.f_type</code>. 0=方案策划情况配置,1=实施方式配置
     */
    @Override
    public Integer getType() {
        return (Integer) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Long, String, Integer> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Long, String, Integer> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SolutionConfiguration.SOLUTION_CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return SolutionConfiguration.SOLUTION_CONFIGURATION.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return SolutionConfiguration.SOLUTION_CONFIGURATION.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return SolutionConfiguration.SOLUTION_CONFIGURATION.ORDER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return SolutionConfiguration.SOLUTION_CONFIGURATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return SolutionConfiguration.SOLUTION_CONFIGURATION.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return SolutionConfiguration.SOLUTION_CONFIGURATION.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getOrder();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord value3(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord value4(Integer value) {
        setOrder(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord value6(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord value7(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfigurationRecord values(String value1, String value2, String value3, Integer value4, Long value5, String value6, Integer value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISolutionConfiguration from) {
        setId(from.getId());
        setName(from.getName());
        setCode(from.getCode());
        setOrder(from.getOrder());
        setCreateTime(from.getCreateTime());
        setCreateMemberId(from.getCreateMemberId());
        setType(from.getType());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISolutionConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SolutionConfigurationRecord
     */
    public SolutionConfigurationRecord() {
        super(SolutionConfiguration.SOLUTION_CONFIGURATION);
    }

    /**
     * Create a detached, initialised SolutionConfigurationRecord
     */
    public SolutionConfigurationRecord(String id, String name, String code, Integer order, Long createTime, String createMemberId, Integer type) {
        super(SolutionConfiguration.SOLUTION_CONFIGURATION);

        set(0, id);
        set(1, name);
        set(2, code);
        set(3, order);
        set(4, createTime);
        set(5, createMemberId);
        set(6, type);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.SolutionConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.SolutionConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.SolutionConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
