/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILecturer extends Serializable {

    /**
     * Setter for <code>train.t_lecturer.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_lecturer.f_member_id</code>. 用户id 
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_member_id</code>. 用户id 
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_lecturer.f_name</code>. 讲师名称  f_type=1时有效
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_lecturer.f_name</code>. 讲师名称  f_type=1时有效
     */
    public String getName();

    /**
     * Setter for <code>train.t_lecturer.f_type</code>. 类型 0 内部讲师  1 外部讲师
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_type</code>. 类型 0 内部讲师  1 外部讲师
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_lecturer.f_attribute_id</code>. 讲师属性
     */
    public void setAttributeId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_attribute_id</code>. 讲师属性
     */
    public String getAttributeId();

    /**
     * Setter for <code>train.t_lecturer.f_sequence_id</code>. 讲师专业条线及序列
     */
    public void setSequenceId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_sequence_id</code>. 讲师专业条线及序列
     */
    public String getSequenceId();

    /**
     * Setter for <code>train.t_lecturer.f_source_type</code>. 来源 0 自建 1 班级
     */
    public void setSourceType(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_source_type</code>. 来源 0 自建 1 班级
     */
    public Integer getSourceType();

    /**
     * Setter for <code>train.t_lecturer.f_organization_id</code>. 所属部门 f_type＝0有效
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_organization_id</code>. 所属部门 f_type＝0有效
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_lecturer.f_ascription_organization_id</code>. 归属部门
     */
    public void setAscriptionOrganizationId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_ascription_organization_id</code>. 归属部门
     */
    public String getAscriptionOrganizationId();

    /**
     * Setter for <code>train.t_lecturer.f_work_start_year</code>. 移动工作开始年份
     */
    public void setWorkStartYear(String value);

    /**
     * Getter for <code>train.t_lecturer.f_work_start_year</code>. 移动工作开始年份
     */
    public String getWorkStartYear();

    /**
     * Setter for <code>train.t_lecturer.f_head_portrait</code>. 头像
     */
    public void setHeadPortrait(String value);

    /**
     * Getter for <code>train.t_lecturer.f_head_portrait</code>. 头像
     */
    public String getHeadPortrait();

    /**
     * Setter for <code>train.t_lecturer.f_mobile</code>. 电话号码
     */
    public void setMobile(String value);

    /**
     * Getter for <code>train.t_lecturer.f_mobile</code>. 电话号码
     */
    public String getMobile();

    /**
     * Setter for <code>train.t_lecturer.f_level_id</code>. 级别
     */
    public void setLevelId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_level_id</code>. 级别
     */
    public String getLevelId();

    /**
     * Setter for <code>train.t_lecturer.f_unit</code>. 单位
     */
    public void setUnit(String value);

    /**
     * Getter for <code>train.t_lecturer.f_unit</code>. 单位
     */
    public String getUnit();

    /**
     * Setter for <code>train.t_lecturer.f_email</code>. 邮箱
     */
    public void setEmail(String value);

    /**
     * Getter for <code>train.t_lecturer.f_email</code>. 邮箱
     */
    public String getEmail();

    /**
     * Setter for <code>train.t_lecturer.f_job_name</code>. 职务／职称
     */
    public void setJobName(String value);

    /**
     * Getter for <code>train.t_lecturer.f_job_name</code>. 职务／职称
     */
    public String getJobName();

    /**
     * Setter for <code>train.t_lecturer.f_sex</code>. 性别 0 男 1 女
     */
    public void setSex(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_sex</code>. 性别 0 男 1 女
     */
    public Integer getSex();

    /**
     * Setter for <code>train.t_lecturer.f_description</code>. 简介
     */
    public void setDescription(String value);

    /**
     * Getter for <code>train.t_lecturer.f_description</code>. 简介
     */
    public String getDescription();

    /**
     * Setter for <code>train.t_lecturer.f_lecture_experience</code>. 授课经历
     */
    public void setLectureExperience(String value);

    /**
     * Getter for <code>train.t_lecturer.f_lecture_experience</code>. 授课经历
     */
    public String getLectureExperience();

    /**
     * Setter for <code>train.t_lecturer.f_bank_user</code>. 开户名
     */
    public void setBankUser(String value);

    /**
     * Getter for <code>train.t_lecturer.f_bank_user</code>. 开户名
     */
    public String getBankUser();

    /**
     * Setter for <code>train.t_lecturer.f_bank_identity</code>.
     */
    public void setBankIdentity(String value);

    /**
     * Getter for <code>train.t_lecturer.f_bank_identity</code>.
     */
    public String getBankIdentity();

    /**
     * Setter for <code>train.t_lecturer.f_bank</code>. 开户银行
     */
    public void setBank(String value);

    /**
     * Getter for <code>train.t_lecturer.f_bank</code>. 开户银行
     */
    public String getBank();

    /**
     * Setter for <code>train.t_lecturer.f_bank_card</code>.
     */
    public void setBankCard(String value);

    /**
     * Getter for <code>train.t_lecturer.f_bank_card</code>.
     */
    public String getBankCard();

    /**
     * Setter for <code>train.t_lecturer.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_lecturer.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_lecturer.f_cooperation_type</code>. 合作类型 0 个人  1 机构
     */
    public void setCooperationType(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_cooperation_type</code>. 合作类型 0 个人  1 机构
     */
    public Integer getCooperationType();

    /**
     * Setter for <code>train.t_lecturer.f_institutions</code>. 机构 f_cooperation_type＝1有效
     */
    public void setInstitutions(String value);

    /**
     * Getter for <code>train.t_lecturer.f_institutions</code>. 机构 f_cooperation_type＝1有效
     */
    public String getInstitutions();

    /**
     * Setter for <code>train.t_lecturer.f_linkman</code>. 机构联系人
     */
    public void setLinkman(String value);

    /**
     * Getter for <code>train.t_lecturer.f_linkman</code>. 机构联系人
     */
    public String getLinkman();

    /**
     * Setter for <code>train.t_lecturer.f_linkman_no</code>. 联系人电话  f_cooperation_type＝1有效
     */
    public void setLinkmanNo(String value);

    /**
     * Getter for <code>train.t_lecturer.f_linkman_no</code>. 联系人电话  f_cooperation_type＝1有效
     */
    public String getLinkmanNo();

    /**
     * Setter for <code>train.t_lecturer.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_lecturer.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_lecturer.f_modify_time</code>. 修改时间
     */
    public void setModifyTime(Long value);

    /**
     * Getter for <code>train.t_lecturer.f_modify_time</code>. 修改时间
     */
    public Long getModifyTime();

    /**
     * Setter for <code>train.t_lecturer.f_modify_member_id</code>. 修改人
     */
    public void setModifyMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_modify_member_id</code>. 修改人
     */
    public String getModifyMemberId();

    /**
     * Setter for <code>train.t_lecturer.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_lecturer.f_label_names</code>. 讲师标签（多个用逗号隔开）
     */
    public void setLabelNames(String value);

    /**
     * Getter for <code>train.t_lecturer.f_label_names</code>. 讲师标签（多个用逗号隔开）
     */
    public String getLabelNames();

    /**
     * Setter for <code>train.t_lecturer.f_cover_path</code>. 讲师头像url
     */
    public void setCoverPath(String value);

    /**
     * Getter for <code>train.t_lecturer.f_cover_path</code>. 讲师头像url
     */
    public String getCoverPath();

    /**
     * Setter for <code>train.t_lecturer.f_adept_course</code>. 擅讲课程
     */
    public void setAdeptCourse(String value);

    /**
     * Getter for <code>train.t_lecturer.f_adept_course</code>. 擅讲课程
     */
    public String getAdeptCourse();

    /**
     * Setter for <code>train.t_lecturer.f_attachment_id</code>. 附件ID
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_lecturer.f_attachment_id</code>. 附件ID
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_lecturer.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_lecturer.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    /**
     * Setter for <code>train.t_lecturer.f_status</code>. 0在库 1退库
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_status</code>. 0在库 1退库
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_lecturer.f_certification_year</code>. 认证年份
     */
    public void setCertificationYear(String value);

    /**
     * Getter for <code>train.t_lecturer.f_certification_year</code>. 认证年份
     */
    public String getCertificationYear();

    /**
     * Setter for <code>train.t_lecturer.f_browse_number</code>. 浏览数
     */
    public void setBrowseNumber(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_browse_number</code>. 浏览数
     */
    public Integer getBrowseNumber();

    /**
     * Setter for <code>train.t_lecturer.f_lecture_number</code>. 授课数
     */
    public void setLectureNumber(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_lecture_number</code>. 授课数
     */
    public Integer getLectureNumber();

    /**
     * Setter for <code>train.t_lecturer.f_thumbs_up_number</code>. 点赞数
     */
    public void setThumbsUpNumber(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_thumbs_up_number</code>. 点赞数
     */
    public Integer getThumbsUpNumber();

    /**
     * Setter for <code>train.t_lecturer.f_release_status</code>. 讲师发布状态：【0：未发布；1：已发布】
     */
    public void setReleaseStatus(Integer value);

    /**
     * Getter for <code>train.t_lecturer.f_release_status</code>. 讲师发布状态：【0：未发布；1：已发布】
     */
    public Integer getReleaseStatus();

    /**
     * Setter for <code>train.t_lecturer.f_attachment_path</code>. 附件路径
     */
    public void setAttachmentPath(String value);

    /**
     * Getter for <code>train.t_lecturer.f_attachment_path</code>. 附件路径
     */
    public String getAttachmentPath();

    /**
     * Setter for <code>train.t_lecturer.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_lecturer.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILecturer
     */
    public void from(ILecturer from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILecturer
     */
    public <E extends ILecturer> E into(E into);
}
