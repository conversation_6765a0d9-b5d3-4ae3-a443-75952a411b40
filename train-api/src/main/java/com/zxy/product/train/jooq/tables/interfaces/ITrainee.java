/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 培训学员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITrainee extends Serializable {

    /**
     * Setter for <code>train.t_trainee.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_trainee.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_trainee.f_member_id</code>. 用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_trainee.f_member_id</code>. 用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_trainee.f_type</code>. 学员类型： 0正式学员(默认) 1非正式成员
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_type</code>. 学员类型： 0正式学员(默认) 1非正式成员
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_trainee.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_trainee.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_trainee.f_phone_number</code>. 手机号码
     */
    public void setPhoneNumber(String value);

    /**
     * Getter for <code>train.t_trainee.f_phone_number</code>. 手机号码
     */
    public String getPhoneNumber();

    /**
     * Setter for <code>train.t_trainee.f_email</code>. 邮箱
     */
    public void setEmail(String value);

    /**
     * Getter for <code>train.t_trainee.f_email</code>. 邮箱
     */
    public String getEmail();

    /**
     * Setter for <code>train.t_trainee.f_sex</code>. 性别： 0男(默认) 1女
     */
    public void setSex(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_sex</code>. 性别： 0男(默认) 1女
     */
    public Integer getSex();

    /**
     * Setter for <code>train.t_trainee.f_level_id</code>. 职级(t_member_config)
     */
    public void setLevelId(String value);

    /**
     * Getter for <code>train.t_trainee.f_level_id</code>. 职级(t_member_config)
     */
    public String getLevelId();

    /**
     * Setter for <code>train.t_trainee.f_nation</code>. 民族
     */
    public void setNation(String value);

    /**
     * Getter for <code>train.t_trainee.f_nation</code>. 民族
     */
    public String getNation();

    /**
     * Setter for <code>train.t_trainee.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_trainee.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_trainee.f_audit_status</code>. 审核状态： 0待审核(默认) 1通过 2拒绝
     */
    public void setAuditStatus(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_audit_status</code>. 审核状态： 0待审核(默认) 1通过 2拒绝
     */
    public Integer getAuditStatus();

    /**
     * Setter for <code>train.t_trainee.f_audit_opinion</code>. 审核意见
     */
    public void setAuditOpinion(String value);

    /**
     * Getter for <code>train.t_trainee.f_audit_opinion</code>. 审核意见
     */
    public String getAuditOpinion();

    /**
     * Setter for <code>train.t_trainee.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_trainee.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_trainee.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_trainee.f_sort_for_group</code>. 分组中的排序
     */
    public void setSortForGroup(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_sort_for_group</code>. 分组中的排序
     */
    public Integer getSortForGroup();

    /**
     * Setter for <code>train.t_trainee.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_trainee.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_trainee.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_trainee.f_trainee_group_id</code>. 分组id
     */
    public void setTraineeGroupId(String value);

    /**
     * Getter for <code>train.t_trainee.f_trainee_group_id</code>. 分组id
     */
    public String getTraineeGroupId();

    /**
     * Setter for <code>train.t_trainee.f_create_member_id</code>. 创建人id
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_trainee.f_create_member_id</code>. 创建人id
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_trainee.f_settle_organization_id</code>. 结算单位ID（默认为当前学员的机构ID）
     */
    public void setSettleOrganizationId(String value);

    /**
     * Getter for <code>train.t_trainee.f_settle_organization_id</code>. 结算单位ID（默认为当前学员的机构ID）
     */
    public String getSettleOrganizationId();

    /**
     * Setter for <code>train.t_trainee.f_commit_questionary</code>. 是否提交满意度问卷，0未提交  1已提交
     */
    public void setCommitQuestionary(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_commit_questionary</code>. 是否提交满意度问卷，0未提交  1已提交
     */
    public Integer getCommitQuestionary();

    /**
     * Setter for <code>train.t_trainee.f_source</code>. 来源  0报名  1手动添加
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_source</code>. 来源  0报名  1手动添加
     */
    public Integer getSource();

    /**
     * Setter for <code>train.t_trainee.f_manual_sorting</code>. 是否手动排序
     */
    public void setManualSorting(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_manual_sorting</code>. 是否手动排序
     */
    public Integer getManualSorting();

    /**
     * Setter for <code>train.t_trainee.f_job</code>. 职务
     */
    public void setJob(String value);

    /**
     * Getter for <code>train.t_trainee.f_job</code>. 职务
     */
    public String getJob();

    /**
     * Setter for <code>train.t_trainee.f_new_company</code>. 修改后的公司名称
     */
    public void setNewCompany(String value);

    /**
     * Getter for <code>train.t_trainee.f_new_company</code>. 修改后的公司名称
     */
    public String getNewCompany();

    /**
     * Setter for <code>train.t_trainee.f_new_organization</code>. 修改后的部门名称
     */
    public void setNewOrganization(String value);

    /**
     * Getter for <code>train.t_trainee.f_new_organization</code>. 修改后的部门名称
     */
    public String getNewOrganization();

    /**
     * Setter for <code>train.t_trainee.f_commit_four_degrees</code>. 是否提交四度问卷，0未提交 1已提交
     */
    public void setCommitFourDegrees(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_commit_four_degrees</code>. 是否提交四度问卷，0未提交 1已提交
     */
    public Integer getCommitFourDegrees();

    /**
     * Setter for <code>train.t_trainee.f_commit_ability</code>. 是否提交能力习问卷，0未提交 1已提交
     */
    public void setCommitAbility(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_commit_ability</code>. 是否提交能力习问卷，0未提交 1已提交
     */
    public Integer getCommitAbility();

    /**
     * Setter for <code>train.t_trainee.f_commit_superior_leadership</code>. 是否提交学员上领导问卷，0未提交 1已提交
     */
    public void setCommitSuperiorLeadership(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_commit_superior_leadership</code>. 是否提交学员上领导问卷，0未提交 1已提交
     */
    public Integer getCommitSuperiorLeadership();

    /**
     * Setter for <code>train.t_trainee.f_audit_leadership</code>. 领导问卷答题人
     */
    public void setAuditLeadership(String value);

    /**
     * Getter for <code>train.t_trainee.f_audit_leadership</code>. 领导问卷答题人
     */
    public String getAuditLeadership();

    /**
     * Setter for <code>train.t_trainee.f_finance</code>. 财务结算用的
     */
    public void setFinance(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_finance</code>. 财务结算用的
     */
    public Integer getFinance();

    /**
     * Setter for <code>train.t_trainee.f_update_time</code>. 管理员修改时间
     */
    public void setUpdateTime(String value);

    /**
     * Getter for <code>train.t_trainee.f_update_time</code>. 管理员修改时间
     */
    public String getUpdateTime();

    /**
     * Setter for <code>train.t_trainee.f_update_month</code>. 管理员修改月份
     */
    public void setUpdateMonth(String value);

    /**
     * Getter for <code>train.t_trainee.f_update_month</code>. 管理员修改月份
     */
    public String getUpdateMonth();

    /**
     * Setter for <code>train.t_trainee.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_trainee.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>train.t_trainee.f_register</code>. 是否报到（0 未报到 1 已报到）
     */
    public void setRegister(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_register</code>. 是否报到（0 未报到 1 已报到）
     */
    public Integer getRegister();

    /**
     * Setter for <code>train.t_trainee.f_register_time</code>. 报到时间
     */
    public void setRegisterTime(Long value);

    /**
     * Getter for <code>train.t_trainee.f_register_time</code>. 报到时间
     */
    public Long getRegisterTime();

    /**
     * Setter for <code>train.t_trainee.f_sort_new</code>. 新班级成员排序字段，f_sort字段弃用
     */
    public void setSortNew(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_sort_new</code>. 新班级成员排序字段，f_sort字段弃用
     */
    public Integer getSortNew();

    /**
     * Setter for <code>train.t_trainee.f_mainland_personnel</code>. 内地人员（0 否 1 是）
     */
    public void setMainlandPersonnel(Integer value);

    /**
     * Getter for <code>train.t_trainee.f_mainland_personnel</code>. 内地人员（0 否 1 是）
     */
    public Integer getMainlandPersonnel();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITrainee
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.ITrainee from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITrainee
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.ITrainee> E into(E into);
}
