/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILecturerLabel extends Serializable {

    /**
     * Setter for <code>train.t_lecturer_label.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_lecturer_label.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_lecturer_label.f_lecturer_id</code>. 讲师id 
     */
    public void setLecturerId(String value);

    /**
     * Getter for <code>train.t_lecturer_label.f_lecturer_id</code>. 讲师id 
     */
    public String getLecturerId();

    /**
     * Setter for <code>train.t_lecturer_label.f_label_id</code>. 标签id
     */
    public void setLabelId(String value);

    /**
     * Getter for <code>train.t_lecturer_label.f_label_id</code>. 标签id
     */
    public String getLabelId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILecturerLabel
     */
    public void from(ILecturerLabel from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILecturerLabel
     */
    public <E extends ILecturerLabel> E into(E into);
}
