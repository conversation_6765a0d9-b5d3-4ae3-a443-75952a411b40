/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.GrantDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GrantDetail extends TableImpl<GrantDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_grant_detail</code>
     */
    public static final GrantDetail GRANT_DETAIL = new GrantDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GrantDetailRecord> getRecordType() {
        return GrantDetailRecord.class;
    }

    /**
     * The column <code>train.t_grant_detail.f_id</code>. ID
     */
    public final TableField<GrantDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_grant_detail.f_grant_id</code>. 授权ID
     */
    public final TableField<GrantDetailRecord, String> GRANT_ID = createField("f_grant_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "授权ID");

    /**
     * The column <code>train.t_grant_detail.f_member_id</code>. 人员ID
     */
    public final TableField<GrantDetailRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "人员ID");

    /**
     * The column <code>train.t_grant_detail.f_organization_id</code>. 组织ID
     */
    public final TableField<GrantDetailRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织ID");

    /**
     * The column <code>train.t_grant_detail.f_create_time</code>. 创建时间
     */
    public final TableField<GrantDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_grant_detail.f_uri</code>. 菜单uri
     */
    public final TableField<GrantDetailRecord, String> URI = createField("f_uri", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "菜单uri");

    /**
     * The column <code>train.t_grant_detail.f_menu_id</code>. 菜单id
     */
    public final TableField<GrantDetailRecord, String> MENU_ID = createField("f_menu_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "菜单id");

    /**
     * The column <code>train.t_grant_detail.f_operator_types</code>. 操作类型
     */
    public final TableField<GrantDetailRecord, String> OPERATOR_TYPES = createField("f_operator_types", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "操作类型");

    /**
     * Create a <code>train.t_grant_detail</code> table reference
     */
    public GrantDetail() {
        this("t_grant_detail", null);
    }

    /**
     * Create an aliased <code>train.t_grant_detail</code> table reference
     */
    public GrantDetail(String alias) {
        this(alias, GRANT_DETAIL);
    }

    private GrantDetail(String alias, Table<GrantDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private GrantDetail(String alias, Table<GrantDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GrantDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_GRANT_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GrantDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<GrantDetailRecord>>asList(Keys.KEY_T_GRANT_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GrantDetail as(String alias) {
        return new GrantDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GrantDetail rename(String name) {
        return new GrantDetail(name, null);
    }
}
