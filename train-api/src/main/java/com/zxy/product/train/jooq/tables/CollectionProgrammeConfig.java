/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CollectionProgrammeConfigRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 集采方案配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CollectionProgrammeConfig extends TableImpl<CollectionProgrammeConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_collection_programme_config</code>
     */
    public static final CollectionProgrammeConfig COLLECTION_PROGRAMME_CONFIG = new CollectionProgrammeConfig();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CollectionProgrammeConfigRecord> getRecordType() {
        return CollectionProgrammeConfigRecord.class;
    }

    /**
     * The column <code>train.t_collection_programme_config.f_id</code>. 系统ID
     */
    public final TableField<CollectionProgrammeConfigRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_collection_programme_config.f_mechanism_name</code>. 集采机构名称
     */
    public final TableField<CollectionProgrammeConfigRecord, String> MECHANISM_NAME = createField("f_mechanism_name", org.jooq.impl.SQLDataType.VARCHAR.length(32).nullable(false), this, "集采机构名称");

    /**
     * The column <code>train.t_collection_programme_config.f_mechanism_contacts</code>. 集采机构联系人
     */
    public final TableField<CollectionProgrammeConfigRecord, String> MECHANISM_CONTACTS = createField("f_mechanism_contacts", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "集采机构联系人");

    /**
     * The column <code>train.t_collection_programme_config.f_mechanism_contacts_number</code>. 集采机构联系人电话 
     */
    public final TableField<CollectionProgrammeConfigRecord, String> MECHANISM_CONTACTS_NUMBER = createField("f_mechanism_contacts_number", org.jooq.impl.SQLDataType.VARCHAR.length(16), this, "集采机构联系人电话 ");

    /**
     * The column <code>train.t_collection_programme_config.f_mechanism_contacts_email</code>. 集采机构联系人邮箱 
     */
    public final TableField<CollectionProgrammeConfigRecord, String> MECHANISM_CONTACTS_EMAIL = createField("f_mechanism_contacts_email", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "集采机构联系人邮箱 ");

    /**
     * The column <code>train.t_collection_programme_config.f_programme_name</code>. 集采方案名称 
     */
    public final TableField<CollectionProgrammeConfigRecord, String> PROGRAMME_NAME = createField("f_programme_name", org.jooq.impl.SQLDataType.VARCHAR.length(64).nullable(false), this, "集采方案名称 ");

    /**
     * The column <code>train.t_collection_programme_config.f_attachment_id</code>. 集采方案附件ID
     */
    public final TableField<CollectionProgrammeConfigRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "集采方案附件ID");

    /**
     * The column <code>train.t_collection_programme_config.f_attachment_name</code>. 集采方案附件名称
     */
    public final TableField<CollectionProgrammeConfigRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "集采方案附件名称");

    /**
     * The column <code>train.t_collection_programme_config.f_organization_id</code>. 组织ID
     */
    public final TableField<CollectionProgrammeConfigRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "组织ID");

    /**
     * The column <code>train.t_collection_programme_config.f_create_time</code>. 创建时间
     */
    public final TableField<CollectionProgrammeConfigRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_collection_programme_config.f_create_member_id</code>. 创建人ID
     */
    public final TableField<CollectionProgrammeConfigRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_collection_programme_config.f_attachment_type</code>. 集采方案附件类型
     */
    public final TableField<CollectionProgrammeConfigRecord, String> ATTACHMENT_TYPE = createField("f_attachment_type", org.jooq.impl.SQLDataType.VARCHAR.length(255), this, "集采方案附件类型");

    /**
     * The column <code>train.t_collection_programme_config.f_course_unit_price</code>. 集采课程单价(元)
     */
    public final TableField<CollectionProgrammeConfigRecord, Double> COURSE_UNIT_PRICE = createField("f_course_unit_price", org.jooq.impl.SQLDataType.DOUBLE, this, "集采课程单价(元)");

    /**
     * Create a <code>train.t_collection_programme_config</code> table reference
     */
    public CollectionProgrammeConfig() {
        this("t_collection_programme_config", null);
    }

    /**
     * Create an aliased <code>train.t_collection_programme_config</code> table reference
     */
    public CollectionProgrammeConfig(String alias) {
        this(alias, COLLECTION_PROGRAMME_CONFIG);
    }

    private CollectionProgrammeConfig(String alias, Table<CollectionProgrammeConfigRecord> aliased) {
        this(alias, aliased, null);
    }

    private CollectionProgrammeConfig(String alias, Table<CollectionProgrammeConfigRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "集采方案配置");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CollectionProgrammeConfigRecord> getPrimaryKey() {
        return Keys.KEY_T_COLLECTION_PROGRAMME_CONFIG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CollectionProgrammeConfigRecord>> getKeys() {
        return Arrays.<UniqueKey<CollectionProgrammeConfigRecord>>asList(Keys.KEY_T_COLLECTION_PROGRAMME_CONFIG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfig as(String alias) {
        return new CollectionProgrammeConfig(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CollectionProgrammeConfig rename(String name) {
        return new CollectionProgrammeConfig(name, null);
    }
}
