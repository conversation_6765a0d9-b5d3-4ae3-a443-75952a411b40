package com.zxy.product.train.entity;

import java.math.BigDecimal;

import com.zxy.product.train.jooq.tables.*;
import com.zxy.product.train.jooq.tables.pojos.SettlementEntity;

/**
 * Created by chun on 2017/3/7.
 */
public class Settlement extends SettlementEntity {

    private static final long serialVersionUID = -6475696399469945140L;
    private Integer peopleDayEntity; //培训人数
    private Double dayNumberEntity; //培训天数
    private Double peopleNumberEntity;//培训人日
    public static final int STATUS_RESERVE = 1; // 待预定
    public static final int STATUS_APPROVAL = 2;// 待审核
    public static final int STATUS_AGREE = 3;   // 同意申请
    public static final int STATUS_RESOURCES_FULL = 4; // 资源已满
    public static final int STATUS_REFUSE = 5; // 拒绝
    //是否开票
    public static final int STATUS_RECEIPT = 1; // 已开票
    public static final int NO_RECEIPT = 2;// 未开票

    private String projectName;//计划名称
    private String projectCode;//计划编码
    private String projectType;//培训类型
    private String projectCost;//培训级别
    private String headquarters;//总部标识
    private String organizationName;//需求方名称
    private String classTeacher;//班主任
    private String organizationId;//组织ID
    private String relation;//公司往来段
    private String settleOrganizationName;//结算单位
    private String settlementCompany; //结算单位导出的公司
    private BigDecimal joinDays;
    private String Path; //组织层级
    private Integer flag; //学员端班务是否可以提交
    private String nation; //名族id
    private MemberConfig memberConfig;//民族
    private String month;//导出的月份
    private Integer index;//用于导出表中的序号
    private String settleOrganizationId;//结算单位id
    private String orgId;//结算数据导出 财务部用
    private Long arriveDate;//报道日期
    private Long returnDate;//返程日期
    /**
     * @user fc
     */
    private Trainee trainee; //成员表，为导出结算成员准备
    private Organization organization; //组织表，为导出结算成员准备

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getProjectCost() {
        return projectCost;
    }

    public void setProjectCost(String projectCost) {
        this.projectCost = projectCost;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public BigDecimal getJoinDays() {
        return joinDays;
    }

    public void setJoinDays(BigDecimal joinDays) {
        this.joinDays = joinDays;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getSettleOrganizationName() {
        return settleOrganizationName;
    }

    public void setSettleOrganizationName(String settleOrganizationName) {
        this.settleOrganizationName = settleOrganizationName;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectCode() {
        return projectCode;
    }


    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getClassTeacher() {
        return classTeacher;
    }

    public void setClassTeacher(String classTeacher) {
        this.classTeacher = classTeacher;
    }

    //为导出月结算数据准备的Bean
    private Project project;
    private String organiaztionName;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getPeopleDayEntity() {
        return peopleDayEntity;
    }

    public void setPeopleDayEntity(Integer peopleDayEntity) {
        this.peopleDayEntity = peopleDayEntity;
    }

    public Double getDayNumberEntity() {
        return dayNumberEntity;
    }

    public void setDayNumberEntity(Double dayNumberEntity) {
        this.dayNumberEntity = dayNumberEntity;
    }

    public Double getPeopleNumberEntity() {
        return peopleNumberEntity;
    }

    public void setPeopleNumberEntity(Double peopleNumberEntity) {
        this.peopleNumberEntity = peopleNumberEntity;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public String getOrganiaztionName() {
        return organiaztionName;
    }

    public void setOrganiaztionName(String organiaztionName) {
        this.organiaztionName = organiaztionName;
    }

    public String getSettleOrganizationId() {
        return settleOrganizationId;
    }

    public void setSettleOrganizationId(String settleOrganizationId) {
        this.settleOrganizationId = settleOrganizationId;
    }

    public Trainee getTrainee() {
        return trainee;
    }

    public void setTrainee(Trainee trainee) {
        this.trainee = trainee;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public MemberConfig getMemberConfig() {
        return memberConfig;
    }

    public void setMemberConfig(MemberConfig memberConfig) {
        this.memberConfig = memberConfig;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }
    public Integer getIndex() {
        return index;}

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getHeadquarters() {
        return headquarters;
    }

    public void setHeadquarters(String headquarters) {
        this.headquarters = headquarters;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
    public String getSettlementCompany() {
        return settlementCompany;
    }

    public void setSettlementCompany(String settlementCompany) {
        this.settlementCompany = settlementCompany;
    }

    public String getPath() {
        return Path;
    }

    public void setPath(String path) {
        Path = path;
    }

    public Long getArriveDate() {
        return arriveDate;
    }

    public void setArriveDate(Long arriveDate) {
        this.arriveDate = arriveDate;
    }

    public Long getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(Long returnDate) {
        this.returnDate = returnDate;
    }
}