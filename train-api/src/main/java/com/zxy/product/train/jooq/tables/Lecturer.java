/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LecturerRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Lecturer extends TableImpl<LecturerRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_lecturer</code>
     */
    public static final Lecturer LECTURER = new Lecturer();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LecturerRecord> getRecordType() {
        return LecturerRecord.class;
    }

    /**
     * The column <code>train.t_lecturer.f_id</code>. ID
     */
    public final TableField<LecturerRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_lecturer.f_member_id</code>. 用户id 
     */
    public final TableField<LecturerRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id ");

    /**
     * The column <code>train.t_lecturer.f_name</code>. 讲师名称  f_type=1时有效
     */
    public final TableField<LecturerRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "讲师名称  f_type=1时有效");

    /**
     * The column <code>train.t_lecturer.f_type</code>. 类型 0 内部讲师  1 外部讲师
     */
    public final TableField<LecturerRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "类型 0 内部讲师  1 外部讲师");

    /**
     * The column <code>train.t_lecturer.f_attribute_id</code>. 讲师属性
     */
    public final TableField<LecturerRecord, String> ATTRIBUTE_ID = createField("f_attribute_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "讲师属性");

    /**
     * The column <code>train.t_lecturer.f_sequence_id</code>. 讲师专业条线及序列
     */
    public final TableField<LecturerRecord, String> SEQUENCE_ID = createField("f_sequence_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "讲师专业条线及序列");

    /**
     * The column <code>train.t_lecturer.f_source_type</code>. 来源 0 自建 1 班级
     */
    public final TableField<LecturerRecord, Integer> SOURCE_TYPE = createField("f_source_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源 0 自建 1 班级");

    /**
     * The column <code>train.t_lecturer.f_organization_id</code>. 所属部门 f_type＝0有效
     */
    public final TableField<LecturerRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属部门 f_type＝0有效");

    /**
     * The column <code>train.t_lecturer.f_ascription_organization_id</code>. 归属部门
     */
    public final TableField<LecturerRecord, String> ASCRIPTION_ORGANIZATION_ID = createField("f_ascription_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "归属部门");

    /**
     * The column <code>train.t_lecturer.f_work_start_year</code>. 移动工作开始年份
     */
    public final TableField<LecturerRecord, String> WORK_START_YEAR = createField("f_work_start_year", org.jooq.impl.SQLDataType.VARCHAR.length(16).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "移动工作开始年份");

    /**
     * The column <code>train.t_lecturer.f_head_portrait</code>. 头像
     */
    public final TableField<LecturerRecord, String> HEAD_PORTRAIT = createField("f_head_portrait", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "头像");

    /**
     * The column <code>train.t_lecturer.f_mobile</code>. 电话号码
     */
    public final TableField<LecturerRecord, String> MOBILE = createField("f_mobile", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "电话号码");

    /**
     * The column <code>train.t_lecturer.f_level_id</code>. 级别
     */
    public final TableField<LecturerRecord, String> LEVEL_ID = createField("f_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "级别");

    /**
     * The column <code>train.t_lecturer.f_unit</code>. 单位
     */
    public final TableField<LecturerRecord, String> UNIT = createField("f_unit", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "单位");

    /**
     * The column <code>train.t_lecturer.f_email</code>. 邮箱
     */
    public final TableField<LecturerRecord, String> EMAIL = createField("f_email", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "邮箱");

    /**
     * The column <code>train.t_lecturer.f_job_name</code>. 职务／职称
     */
    public final TableField<LecturerRecord, String> JOB_NAME = createField("f_job_name", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "职务／职称");

    /**
     * The column <code>train.t_lecturer.f_sex</code>. 性别 0 男 1 女
     */
    public final TableField<LecturerRecord, Integer> SEX = createField("f_sex", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "性别 0 男 1 女");

    /**
     * The column <code>train.t_lecturer.f_description</code>. 简介
     */
    public final TableField<LecturerRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "简介");

    /**
     * The column <code>train.t_lecturer.f_lecture_experience</code>. 授课经历
     */
    public final TableField<LecturerRecord, String> LECTURE_EXPERIENCE = createField("f_lecture_experience", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "授课经历");

    /**
     * The column <code>train.t_lecturer.f_bank_user</code>. 开户名
     */
    public final TableField<LecturerRecord, String> BANK_USER = createField("f_bank_user", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "开户名");

    /**
     * The column <code>train.t_lecturer.f_bank_identity</code>.
     */
    public final TableField<LecturerRecord, String> BANK_IDENTITY = createField("f_bank_identity", org.jooq.impl.SQLDataType.VARCHAR.length(120).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>train.t_lecturer.f_bank</code>. 开户银行
     */
    public final TableField<LecturerRecord, String> BANK = createField("f_bank", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "开户银行");

    /**
     * The column <code>train.t_lecturer.f_bank_card</code>.
     */
    public final TableField<LecturerRecord, String> BANK_CARD = createField("f_bank_card", org.jooq.impl.SQLDataType.VARCHAR.length(120).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>train.t_lecturer.f_remark</code>. 备注
     */
    public final TableField<LecturerRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>train.t_lecturer.f_cooperation_type</code>. 合作类型 0 个人  1 机构
     */
    public final TableField<LecturerRecord, Integer> COOPERATION_TYPE = createField("f_cooperation_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "合作类型 0 个人  1 机构");

    /**
     * The column <code>train.t_lecturer.f_institutions</code>. 机构 f_cooperation_type＝1有效
     */
    public final TableField<LecturerRecord, String> INSTITUTIONS = createField("f_institutions", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "机构 f_cooperation_type＝1有效");

    /**
     * The column <code>train.t_lecturer.f_linkman</code>. 机构联系人
     */
    public final TableField<LecturerRecord, String> LINKMAN = createField("f_linkman", org.jooq.impl.SQLDataType.VARCHAR.length(30).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "机构联系人");

    /**
     * The column <code>train.t_lecturer.f_linkman_no</code>. 联系人电话  f_cooperation_type＝1有效
     */
    public final TableField<LecturerRecord, String> LINKMAN_NO = createField("f_linkman_no", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "联系人电话  f_cooperation_type＝1有效");

    /**
     * The column <code>train.t_lecturer.f_create_time</code>. 创建时间
     */
    public final TableField<LecturerRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_lecturer.f_modify_time</code>. 修改时间
     */
    public final TableField<LecturerRecord, Long> MODIFY_TIME = createField("f_modify_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "修改时间");

    /**
     * The column <code>train.t_lecturer.f_modify_member_id</code>. 修改人
     */
    public final TableField<LecturerRecord, String> MODIFY_MEMBER_ID = createField("f_modify_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "修改人");

    /**
     * The column <code>train.t_lecturer.f_create_member_id</code>. 创建人
     */
    public final TableField<LecturerRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>train.t_lecturer.f_label_names</code>. 讲师标签（多个用逗号隔开）
     */
    public final TableField<LecturerRecord, String> LABEL_NAMES = createField("f_label_names", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "讲师标签（多个用逗号隔开）");

    /**
     * The column <code>train.t_lecturer.f_cover_path</code>. 讲师头像url
     */
    public final TableField<LecturerRecord, String> COVER_PATH = createField("f_cover_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "讲师头像url");

    /**
     * The column <code>train.t_lecturer.f_adept_course</code>. 擅讲课程
     */
    public final TableField<LecturerRecord, String> ADEPT_COURSE = createField("f_adept_course", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "擅讲课程");

    /**
     * The column <code>train.t_lecturer.f_attachment_id</code>. 附件ID
     */
    public final TableField<LecturerRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "附件ID");

    /**
     * The column <code>train.t_lecturer.f_attachment_name</code>. 附件名称
     */
    public final TableField<LecturerRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(512).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "附件名称");

    /**
     * The column <code>train.t_lecturer.f_status</code>. 0在库 1退库
     */
    public final TableField<LecturerRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0在库 1退库");

    /**
     * The column <code>train.t_lecturer.f_certification_year</code>. 认证年份
     */
    public final TableField<LecturerRecord, String> CERTIFICATION_YEAR = createField("f_certification_year", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "认证年份");

    /**
     * The column <code>train.t_lecturer.f_browse_number</code>. 浏览数
     */
    public final TableField<LecturerRecord, Integer> BROWSE_NUMBER = createField("f_browse_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "浏览数");

    /**
     * The column <code>train.t_lecturer.f_lecture_number</code>. 授课数
     */
    public final TableField<LecturerRecord, Integer> LECTURE_NUMBER = createField("f_lecture_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "授课数");

    /**
     * The column <code>train.t_lecturer.f_thumbs_up_number</code>. 点赞数
     */
    public final TableField<LecturerRecord, Integer> THUMBS_UP_NUMBER = createField("f_thumbs_up_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "点赞数");

    /**
     * The column <code>train.t_lecturer.f_release_status</code>. 讲师发布状态：【0：未发布；1：已发布】
     */
    public final TableField<LecturerRecord, Integer> RELEASE_STATUS = createField("f_release_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "讲师发布状态：【0：未发布；1：已发布】");

    /**
     * The column <code>train.t_lecturer.f_attachment_path</code>. 附件路径
     */
    public final TableField<LecturerRecord, String> ATTACHMENT_PATH = createField("f_attachment_path", org.jooq.impl.SQLDataType.VARCHAR.length(256).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "附件路径");

    /**
     * The column <code>train.t_lecturer.f_modify_date</code>. 修改时间
     */
    public final TableField<LecturerRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>train.t_lecturer</code> table reference
     */
    public Lecturer() {
        this("t_lecturer", null);
    }

    /**
     * Create an aliased <code>train.t_lecturer</code> table reference
     */
    public Lecturer(String alias) {
        this(alias, LECTURER);
    }

    private Lecturer(String alias, Table<LecturerRecord> aliased) {
        this(alias, aliased, null);
    }

    private Lecturer(String alias, Table<LecturerRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LecturerRecord> getPrimaryKey() {
        return Keys.KEY_T_LECTURER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LecturerRecord>> getKeys() {
        return Arrays.<UniqueKey<LecturerRecord>>asList(Keys.KEY_T_LECTURER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Lecturer as(String alias) {
        return new Lecturer(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Lecturer rename(String name) {
        return new Lecturer(name, null);
    }
}
