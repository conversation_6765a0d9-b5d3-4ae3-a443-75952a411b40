/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAcademicStatisticsInfo extends Serializable {

    /**
     * Setter for <code>train.t_academic_statistics_info.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_academic_statistics_info.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_academic_statistics_info.f_type</code>. 业务类型（1实施培训班数量、2培训学员人次、3培训学员人数）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_academic_statistics_info.f_type</code>. 业务类型（1实施培训班数量、2培训学员人次、3培训学员人数）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_academic_statistics_info.f_implementation_year</code>. 年份
     */
    public void setImplementationYear(Integer value);

    /**
     * Getter for <code>train.t_academic_statistics_info.f_implementation_year</code>. 年份
     */
    public Integer getImplementationYear();

    /**
     * Setter for <code>train.t_academic_statistics_info.f_total_count</code>. 总数
     */
    public void setTotalCount(Long value);

    /**
     * Getter for <code>train.t_academic_statistics_info.f_total_count</code>. 总数
     */
    public Long getTotalCount();

    /**
     * Setter for <code>train.t_academic_statistics_info.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_academic_statistics_info.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAcademicStatisticsInfo
     */
    public void from(IAcademicStatisticsInfo from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAcademicStatisticsInfo
     */
    public <E extends IAcademicStatisticsInfo> E into(E into);
}
