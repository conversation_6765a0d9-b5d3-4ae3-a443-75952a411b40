/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassSignupInfo extends Serializable {

    /**
     * Setter for <code>train.t_class_signup_info.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_signup_info.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_signup_info.f_class_id</code>. 培训班ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_signup_info.f_class_id</code>. 培训班ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_signup_info.f_is_open</code>. 是否开放报名（0否 1是）
     */
    public void setIsOpen(Integer value);

    /**
     * Getter for <code>train.t_class_signup_info.f_is_open</code>. 是否开放报名（0否 1是）
     */
    public Integer getIsOpen();

    /**
     * Setter for <code>train.t_class_signup_info.f_start_time</code>. 报名开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_class_signup_info.f_start_time</code>. 报名开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_class_signup_info.f_end_time</code>. 报名结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_class_signup_info.f_end_time</code>. 报名结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_class_signup_info.f_signup_code</code>. 报名码
     */
    public void setSignupCode(String value);

    /**
     * Getter for <code>train.t_class_signup_info.f_signup_code</code>. 报名码
     */
    public String getSignupCode();

    /**
     * Setter for <code>train.t_class_signup_info.f_using_two_brings</code>. 是否启用2个带来（0否 1是）
     */
    public void setUsingTwoBrings(Integer value);

    /**
     * Getter for <code>train.t_class_signup_info.f_using_two_brings</code>. 是否启用2个带来（0否 1是）
     */
    public Integer getUsingTwoBrings();

    /**
     * Setter for <code>train.t_class_signup_info.f_question1</code>. 问题1
     */
    public void setQuestion1(String value);

    /**
     * Getter for <code>train.t_class_signup_info.f_question1</code>. 问题1
     */
    public String getQuestion1();

    /**
     * Setter for <code>train.t_class_signup_info.f_question2</code>. 问题2
     */
    public void setQuestion2(String value);

    /**
     * Getter for <code>train.t_class_signup_info.f_question2</code>. 问题2
     */
    public String getQuestion2();

    /**
     * Setter for <code>train.t_class_signup_info.f_signup_url</code>. 报名链接
     */
    public void setSignupUrl(String value);

    /**
     * Getter for <code>train.t_class_signup_info.f_signup_url</code>. 报名链接
     */
    public String getSignupUrl();

    /**
     * Setter for <code>train.t_class_signup_info.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_signup_info.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_signup_info.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_signup_info.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_signup_info.f_face_attendance</code>. 是否人脸考勤（0否 1是）
     */
    public void setFaceAttendance(Integer value);

    /**
     * Getter for <code>train.t_class_signup_info.f_face_attendance</code>. 是否人脸考勤（0否 1是）
     */
    public Integer getFaceAttendance();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassSignupInfo
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.IClassSignupInfo from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassSignupInfo
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.IClassSignupInfo> E into(E into);
}
