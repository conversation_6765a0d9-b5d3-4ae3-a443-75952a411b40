/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SignDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 签到详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SignDetail extends TableImpl<SignDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_sign_detail</code>
     */
    public static final SignDetail SIGN_DETAIL = new SignDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SignDetailRecord> getRecordType() {
        return SignDetailRecord.class;
    }

    /**
     * The column <code>train.t_sign_detail.f_id</code>. 表ID
     */
    public final TableField<SignDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表ID");

    /**
     * The column <code>train.t_sign_detail.f_member_id</code>. 员工ID
     */
    public final TableField<SignDetailRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "员工ID");

    /**
     * The column <code>train.t_sign_detail.f_sign_date</code>. 签到时间
     */
    public final TableField<SignDetailRecord, Long> SIGN_DATE = createField("f_sign_date", org.jooq.impl.SQLDataType.BIGINT, this, "签到时间");

    /**
     * The column <code>train.t_sign_detail.f_state</code>. 状态 0全部 1正常 2迟到 3未签到 4请假
     */
    public final TableField<SignDetailRecord, Integer> STATE = createField("f_state", org.jooq.impl.SQLDataType.INTEGER, this, "状态 0全部 1正常 2迟到 3未签到 4请假");

    /**
     * The column <code>train.t_sign_detail.f_create_time</code>. 创建时间
     */
    public final TableField<SignDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_sign_detail.f_sign_id</code>. 签到表ID
     */
    public final TableField<SignDetailRecord, String> SIGN_ID = createField("f_sign_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "签到表ID");

    /**
     * Create a <code>train.t_sign_detail</code> table reference
     */
    public SignDetail() {
        this("t_sign_detail", null);
    }

    /**
     * Create an aliased <code>train.t_sign_detail</code> table reference
     */
    public SignDetail(String alias) {
        this(alias, SIGN_DETAIL);
    }

    private SignDetail(String alias, Table<SignDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private SignDetail(String alias, Table<SignDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "签到详情表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SignDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_SIGN_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SignDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<SignDetailRecord>>asList(Keys.KEY_T_SIGN_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SignDetail as(String alias) {
        return new SignDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SignDetail rename(String name) {
        return new SignDetail(name, null);
    }
}
