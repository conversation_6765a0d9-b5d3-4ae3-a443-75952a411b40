/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 团队学习班-活动资料表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamActivityAttachment extends Serializable {

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_activity_id</code>. 活动id
     */
    public void setActivityId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_activity_id</code>. 活动id
     */
    public String getActivityId();

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_name</code>. 资料名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_name</code>. 资料名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_attachment_id</code>. 资料id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_attachment_id</code>. 资料id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_attachment_type</code>. 附件类型，1: 文档, 2: 图片, 4: 压缩文件, 5: 音频, 6: 视频, 7: EPUB电子书, 10: 其它
     */
    public void setAttachmentType(Integer value);

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_attachment_type</code>. 附件类型，1: 文档, 2: 图片, 4: 压缩文件, 5: 音频, 6: 视频, 7: EPUB电子书, 10: 其它
     */
    public Integer getAttachmentType();

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamActivityAttachment
     */
    public void from(IStudyTeamActivityAttachment from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamActivityAttachment
     */
    public <E extends IStudyTeamActivityAttachment> E into(E into);
}
