/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudentDetail extends Serializable {

    /**
     * Setter for <code>train.t_student_detail.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_student_detail.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_student_detail.f_code</code>. MIS编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_student_detail.f_code</code>. MIS编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_student_detail.f_name</code>. 班级名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_student_detail.f_name</code>. 班级名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_student_detail.f_organization_name</code>. 主办部门
     */
    public void setOrganizationName(String value);

    /**
     * Getter for <code>train.t_student_detail.f_organization_name</code>. 主办部门
     */
    public String getOrganizationName();

    /**
     * Setter for <code>train.t_student_detail.f_train_type</code>. 培训类型
     */
    public void setTrainType(String value);

    /**
     * Getter for <code>train.t_student_detail.f_train_type</code>. 培训类型
     */
    public String getTrainType();

    /**
     * Setter for <code>train.t_student_detail.f_student_name</code>. 姓名
     */
    public void setStudentName(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_name</code>. 姓名
     */
    public String getStudentName();

    /**
     * Setter for <code>train.t_student_detail.f_student_code</code>. 员工编号
     */
    public void setStudentCode(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_code</code>. 员工编号
     */
    public String getStudentCode();

    /**
     * Setter for <code>train.t_student_detail.f_student_nation</code>. 民族
     */
    public void setStudentNation(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_nation</code>. 民族
     */
    public String getStudentNation();

    /**
     * Setter for <code>train.t_student_detail.f_student_sex</code>. 性别
     */
    public void setStudentSex(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_sex</code>. 性别
     */
    public String getStudentSex();

    /**
     * Setter for <code>train.t_student_detail.f_student_company</code>. 公司
     */
    public void setStudentCompany(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_company</code>. 公司
     */
    public String getStudentCompany();

    /**
     * Setter for <code>train.t_student_detail.f_student_department</code>. 部门
     */
    public void setStudentDepartment(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_department</code>. 部门
     */
    public String getStudentDepartment();

    /**
     * Setter for <code>train.t_student_detail.f_student_job</code>. 职位
     */
    public void setStudentJob(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_job</code>. 职位
     */
    public String getStudentJob();

    /**
     * Setter for <code>train.t_student_detail.f_student_phone</code>. 手机
     */
    public void setStudentPhone(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_phone</code>. 手机
     */
    public String getStudentPhone();

    /**
     * Setter for <code>train.t_student_detail.f_student_email</code>. 邮箱
     */
    public void setStudentEmail(String value);

    /**
     * Getter for <code>train.t_student_detail.f_student_email</code>. 邮箱
     */
    public String getStudentEmail();

    /**
     * Setter for <code>train.t_student_detail.f_month</code>. 月份
     */
    public void setMonth(String value);

    /**
     * Getter for <code>train.t_student_detail.f_month</code>. 月份
     */
    public String getMonth();

    /**
     * Setter for <code>train.t_student_detail.f_day</code>. 培训天数
     */
    public void setDay(Double value);

    /**
     * Getter for <code>train.t_student_detail.f_day</code>. 培训天数
     */
    public Double getDay();

    /**
     * Setter for <code>train.t_student_detail.f_settlement_company</code>. 结算公司
     */
    public void setSettlementCompany(String value);

    /**
     * Getter for <code>train.t_student_detail.f_settlement_company</code>. 结算公司
     */
    public String getSettlementCompany();

    /**
     * Setter for <code>train.t_student_detail.f_settlement_company_code</code>. 结算公司组织编码
     */
    public void setSettlementCompanyCode(String value);

    /**
     * Getter for <code>train.t_student_detail.f_settlement_company_code</code>. 结算公司组织编码
     */
    public String getSettlementCompanyCode();

    /**
     * Setter for <code>train.t_student_detail.f_year</code>. 年份
     */
    public void setYear(String value);

    /**
     * Getter for <code>train.t_student_detail.f_year</code>. 年份
     */
    public String getYear();

    /**
     * Setter for <code>train.t_student_detail.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_student_detail.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_student_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_student_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudentDetail
     */
    public void from(IStudentDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudentDetail
     */
    public <E extends IStudentDetail> E into(E into);
}
