/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TrainChatGroupRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训班群聊成员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TrainChatGroup extends TableImpl<TrainChatGroupRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_train_chat_group</code>
     */
    public static final TrainChatGroup TRAIN_CHAT_GROUP = new TrainChatGroup();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TrainChatGroupRecord> getRecordType() {
        return TrainChatGroupRecord.class;
    }

    /**
     * The column <code>train.t_train_chat_group.f_id</code>.
     */
    public final TableField<TrainChatGroupRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_train_chat_group.f_class_id</code>. 班级ID
     */
    public final TableField<TrainChatGroupRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班级ID");

    /**
     * The column <code>train.t_train_chat_group.f_chat_id</code>. 群聊ID
     */
    public final TableField<TrainChatGroupRecord, String> CHAT_ID = createField("f_chat_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "群聊ID");

    /**
     * The column <code>train.t_train_chat_group.f_member_id</code>. 成员ID
     */
    public final TableField<TrainChatGroupRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "成员ID");

    /**
     * The column <code>train.t_train_chat_group.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public final TableField<TrainChatGroupRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态：0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_train_chat_group.f_create_time</code>. 创建时间
     */
    public final TableField<TrainChatGroupRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_train_chat_group.f_modify_date</code>. 修改时间
     */
    public final TableField<TrainChatGroupRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>train.t_train_chat_group</code> table reference
     */
    public TrainChatGroup() {
        this("t_train_chat_group", null);
    }

    /**
     * Create an aliased <code>train.t_train_chat_group</code> table reference
     */
    public TrainChatGroup(String alias) {
        this(alias, TRAIN_CHAT_GROUP);
    }

    private TrainChatGroup(String alias, Table<TrainChatGroupRecord> aliased) {
        this(alias, aliased, null);
    }

    private TrainChatGroup(String alias, Table<TrainChatGroupRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训班群聊成员表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TrainChatGroupRecord> getPrimaryKey() {
        return Keys.KEY_T_TRAIN_CHAT_GROUP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TrainChatGroupRecord>> getKeys() {
        return Arrays.<UniqueKey<TrainChatGroupRecord>>asList(Keys.KEY_T_TRAIN_CHAT_GROUP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroup as(String alias) {
        return new TrainChatGroup(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TrainChatGroup rename(String name) {
        return new TrainChatGroup(name, null);
    }
}
