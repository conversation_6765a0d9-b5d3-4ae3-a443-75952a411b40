/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 受众对象表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAudienceObject extends Serializable {

    /**
     * Setter for <code>train.t_audience_object.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_audience_object.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_audience_object.f_business_id</code>. 业务id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>train.t_audience_object.f_business_id</code>. 业务id
     */
    public String getBusinessId();

    /**
     * Setter for <code>train.t_audience_object.f_business_type</code>. 业务类型:1 消息
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>train.t_audience_object.f_business_type</code>. 业务类型:1 消息
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>train.t_audience_object.f_item_id</code>. 受众项ID
     */
    public void setItemId(String value);

    /**
     * Getter for <code>train.t_audience_object.f_item_id</code>. 受众项ID
     */
    public String getItemId();

    /**
     * Setter for <code>train.t_audience_object.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_audience_object.f_create_time</code>.
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAudienceObject
     */
    public void from(IAudienceObject from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAudienceObject
     */
    public <E extends IAudienceObject> E into(E into);
}
