/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 面授课程库表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IF2fCourse extends Serializable {

    /**
     * Setter for <code>train.t_f2f_course.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_f2f_course.f_name</code>. 课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_name</code>. 课程名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_f2f_course.f_sequence</code>. 课程分类/序列
     */
    public void setSequence(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_sequence</code>. 课程分类/序列
     */
    public String getSequence();

    /**
     * Setter for <code>train.t_f2f_course.f_obj</code>. 授课对象
     */
    public void setObj(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_obj</code>. 授课对象
     */
    public String getObj();

    /**
     * Setter for <code>train.t_f2f_course.f_keyword</code>. 关键词
     */
    public void setKeyword(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_keyword</code>. 关键词
     */
    public String getKeyword();

    /**
     * Setter for <code>train.t_f2f_course.f_lecturer</code>. 课程讲师
     */
    public void setLecturer(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_lecturer</code>. 课程讲师
     */
    public String getLecturer();

    /**
     * Setter for <code>train.t_f2f_course.f_course_duration</code>. 课程时长
     */
    public void setCourseDuration(Double value);

    /**
     * Getter for <code>train.t_f2f_course.f_course_duration</code>. 课程时长
     */
    public Double getCourseDuration();

    /**
     * Setter for <code>train.t_f2f_course.f_course_reward</code>. 课酬
     */
    public void setCourseReward(Double value);

    /**
     * Getter for <code>train.t_f2f_course.f_course_reward</code>. 课酬
     */
    public Double getCourseReward();

    /**
     * Setter for <code>train.t_f2f_course.f_satisfied_degree</code>. 平均满意度
     */
    public void setSatisfiedDegree(Double value);

    /**
     * Getter for <code>train.t_f2f_course.f_satisfied_degree</code>. 平均满意度
     */
    public Double getSatisfiedDegree();

    /**
     * Setter for <code>train.t_f2f_course.f_teach_method</code>. 教学方式
     */
    public void setTeachMethod(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_teach_method</code>. 教学方式
     */
    public String getTeachMethod();

    /**
     * Setter for <code>train.t_f2f_course.f_trainee_number1</code>. 学员人数起始
     */
    public void setTraineeNumber1(Integer value);

    /**
     * Getter for <code>train.t_f2f_course.f_trainee_number1</code>. 学员人数起始
     */
    public Integer getTraineeNumber1();

    /**
     * Setter for <code>train.t_f2f_course.f_trainee_number2</code>. 学员人数结束
     */
    public void setTraineeNumber2(Integer value);

    /**
     * Getter for <code>train.t_f2f_course.f_trainee_number2</code>. 学员人数结束
     */
    public Integer getTraineeNumber2();

    /**
     * Setter for <code>train.t_f2f_course.f_summary</code>. 课程简介
     */
    public void setSummary(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_summary</code>. 课程简介
     */
    public String getSummary();

    /**
     * Setter for <code>train.t_f2f_course.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_f2f_course.f_target</code>. 课程目标
     */
    public void setTarget(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_target</code>. 课程目标
     */
    public String getTarget();

    /**
     * Setter for <code>train.t_f2f_course.f_outline</code>. 课程大纲
     */
    public void setOutline(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_outline</code>. 课程大纲
     */
    public String getOutline();

    /**
     * Setter for <code>train.t_f2f_course.f_institution</code>. 课程所属机构
     */
    public void setInstitution(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_institution</code>. 课程所属机构
     */
    public String getInstitution();

    /**
     * Setter for <code>train.t_f2f_course.f_contacts</code>. 机构联系人
     */
    public void setContacts(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_contacts</code>. 机构联系人
     */
    public String getContacts();

    /**
     * Setter for <code>train.t_f2f_course.f_contacts_tel</code>. 机构联系人电话
     */
    public void setContactsTel(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_contacts_tel</code>. 机构联系人电话
     */
    public String getContactsTel();

    /**
     * Setter for <code>train.t_f2f_course.f_contacts_email</code>. 机构联系人邮箱
     */
    public void setContactsEmail(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_contacts_email</code>. 机构联系人邮箱
     */
    public String getContactsEmail();

    /**
     * Setter for <code>train.t_f2f_course.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_f2f_course.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_f2f_course.f_pay</code>. 实付
     */
    public void setPay(Double value);

    /**
     * Getter for <code>train.t_f2f_course.f_pay</code>. 实付
     */
    public Double getPay();

    /**
     * Setter for <code>train.t_f2f_course.f_tax</code>. 税金
     */
    public void setTax(Double value);

    /**
     * Getter for <code>train.t_f2f_course.f_tax</code>. 税金
     */
    public Double getTax();

    /**
     * Setter for <code>train.t_f2f_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_f2f_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_f2f_course.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_f2f_course.f_type</code>. 0自建 1班级
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_f2f_course.f_type</code>. 0自建 1班级
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_f2f_course.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_f2f_course.f_course_date</code>. 上课日期
     */
    public void setCourseDate(Long value);

    /**
     * Getter for <code>train.t_f2f_course.f_course_date</code>. 上课日期
     */
    public Long getCourseDate();

    /**
     * Setter for <code>train.t_f2f_course.f_bank_user</code>. 开户人
     */
    public void setBankUser(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_bank_user</code>. 开户人
     */
    public String getBankUser();

    /**
     * Setter for <code>train.t_f2f_course.f_bank_identity</code>. 开户人身份证
     */
    public void setBankIdentity(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_bank_identity</code>. 开户人身份证
     */
    public String getBankIdentity();

    /**
     * Setter for <code>train.t_f2f_course.f_bank</code>. 开户行
     */
    public void setBank(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_bank</code>. 开户行
     */
    public String getBank();

    /**
     * Setter for <code>train.t_f2f_course.f_bank_card</code>. 卡号
     */
    public void setBankCard(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_bank_card</code>. 卡号
     */
    public String getBankCard();

    /**
     * Setter for <code>train.t_f2f_course.f_start_time</code>. 开始时间
     */
    public void setStartTime(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_start_time</code>. 开始时间
     */
    public String getStartTime();

    /**
     * Setter for <code>train.t_f2f_course.f_end_time</code>. 结束时间
     */
    public void setEndTime(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_end_time</code>. 结束时间
     */
    public String getEndTime();

    /**
     * Setter for <code>train.t_f2f_course.f_status</code>. 0在库 1退库
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_f2f_course.f_status</code>. 0在库 1退库
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_f2f_course.f_attachment_id</code>. 附件id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_attachment_id</code>. 附件id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_f2f_course.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_f2f_course.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IF2fCourse
     */
    public void from(IF2fCourse from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IF2fCourse
     */
    public <E extends IF2fCourse> E into(E into);
}
