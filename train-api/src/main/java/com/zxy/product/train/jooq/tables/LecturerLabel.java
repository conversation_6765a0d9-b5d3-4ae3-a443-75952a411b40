/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LecturerLabelRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerLabel extends TableImpl<LecturerLabelRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_lecturer_label</code>
     */
    public static final LecturerLabel LECTURER_LABEL = new LecturerLabel();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LecturerLabelRecord> getRecordType() {
        return LecturerLabelRecord.class;
    }

    /**
     * The column <code>train.t_lecturer_label.f_id</code>. ID
     */
    public final TableField<LecturerLabelRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_lecturer_label.f_lecturer_id</code>. 讲师id 
     */
    public final TableField<LecturerLabelRecord, String> LECTURER_ID = createField("f_lecturer_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "讲师id ");

    /**
     * The column <code>train.t_lecturer_label.f_label_id</code>. 标签id
     */
    public final TableField<LecturerLabelRecord, String> LABEL_ID = createField("f_label_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "标签id");

    /**
     * Create a <code>train.t_lecturer_label</code> table reference
     */
    public LecturerLabel() {
        this("t_lecturer_label", null);
    }

    /**
     * Create an aliased <code>train.t_lecturer_label</code> table reference
     */
    public LecturerLabel(String alias) {
        this(alias, LECTURER_LABEL);
    }

    private LecturerLabel(String alias, Table<LecturerLabelRecord> aliased) {
        this(alias, aliased, null);
    }

    private LecturerLabel(String alias, Table<LecturerLabelRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LecturerLabelRecord> getPrimaryKey() {
        return Keys.KEY_T_LECTURER_LABEL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LecturerLabelRecord>> getKeys() {
        return Arrays.<UniqueKey<LecturerLabelRecord>>asList(Keys.KEY_T_LECTURER_LABEL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerLabel as(String alias) {
        return new LecturerLabel(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LecturerLabel rename(String name) {
        return new LecturerLabel(name, null);
    }
}
