/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ProjectApprovalRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProjectApproval extends TableImpl<ProjectApprovalRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_project_approval</code>
     */
    public static final ProjectApproval PROJECT_APPROVAL = new ProjectApproval();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProjectApprovalRecord> getRecordType() {
        return ProjectApprovalRecord.class;
    }

    /**
     * The column <code>train.t_project_approval.f_id</code>. 主键
     */
    public final TableField<ProjectApprovalRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_project_approval.f_project_id</code>. 计划ID
     */
    public final TableField<ProjectApprovalRecord, String> PROJECT_ID = createField("f_project_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "计划ID");

    /**
     * The column <code>train.t_project_approval.f_status</code>. 状态（1待预订 2待审核 3同意申请 4资源已满 5不同意）
     */
    public final TableField<ProjectApprovalRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "状态（1待预订 2待审核 3同意申请 4资源已满 5不同意）");

    /**
     * The column <code>train.t_project_approval.f_suggestion</code>. 审核意见
     */
    public final TableField<ProjectApprovalRecord, String> SUGGESTION = createField("f_suggestion", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "审核意见");

    /**
     * The column <code>train.t_project_approval.f_approval_member</code>. 审核人
     */
    public final TableField<ProjectApprovalRecord, String> APPROVAL_MEMBER = createField("f_approval_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "审核人");

    /**
     * The column <code>train.t_project_approval.f_create_time</code>. 创建时间
     */
    public final TableField<ProjectApprovalRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_project_approval.f_create_member</code>. 创建人ID
     */
    public final TableField<ProjectApprovalRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_project_approval.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ProjectApprovalRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_project_approval</code> table reference
     */
    public ProjectApproval() {
        this("t_project_approval", null);
    }

    /**
     * Create an aliased <code>train.t_project_approval</code> table reference
     */
    public ProjectApproval(String alias) {
        this(alias, PROJECT_APPROVAL);
    }

    private ProjectApproval(String alias, Table<ProjectApprovalRecord> aliased) {
        this(alias, aliased, null);
    }

    private ProjectApproval(String alias, Table<ProjectApprovalRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ProjectApprovalRecord> getPrimaryKey() {
        return Keys.KEY_T_PROJECT_APPROVAL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ProjectApprovalRecord>> getKeys() {
        return Arrays.<UniqueKey<ProjectApprovalRecord>>asList(Keys.KEY_T_PROJECT_APPROVAL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectApproval as(String alias) {
        return new ProjectApproval(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ProjectApproval rename(String name) {
        return new ProjectApproval(name, null);
    }
}
