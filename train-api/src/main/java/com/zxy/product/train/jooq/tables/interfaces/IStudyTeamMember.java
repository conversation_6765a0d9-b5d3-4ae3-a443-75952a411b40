/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习团队成员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamMember extends Serializable {

    /**
     * Setter for <code>train.t_study_team_member.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_member.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_member.f_team_id</code>. 团队id
     */
    public void setTeamId(String value);

    /**
     * Getter for <code>train.t_study_team_member.f_team_id</code>. 团队id
     */
    public String getTeamId();

    /**
     * Setter for <code>train.t_study_team_member.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_member.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_study_team_member.f_role</code>. 人员角色 1-团队长 2-助理 3-正式人员 4-其他人员 5-历史人员
     */
    public void setRole(Integer value);

    /**
     * Getter for <code>train.t_study_team_member.f_role</code>. 人员角色 1-团队长 2-助理 3-正式人员 4-其他人员 5-历史人员
     */
    public Integer getRole();

    /**
     * Setter for <code>train.t_study_team_member.f_audit_status</code>. 审核状态: 0 待审核 1 通过 2 拒绝
     */
    public void setAuditStatus(Integer value);

    /**
     * Getter for <code>train.t_study_team_member.f_audit_status</code>. 审核状态: 0 待审核 1 通过 2 拒绝
     */
    public Integer getAuditStatus();

    /**
     * Setter for <code>train.t_study_team_member.f_sign_up_time</code>. 报名时间
     */
    public void setSignUpTime(Long value);

    /**
     * Getter for <code>train.t_study_team_member.f_sign_up_time</code>. 报名时间
     */
    public Long getSignUpTime();

    /**
     * Setter for <code>train.t_study_team_member.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_study_team_member.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_study_team_member.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_member.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamMember
     */
    public void from(IStudyTeamMember from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamMember
     */
    public <E extends IStudyTeamMember> E into(E into);
}
