/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILabel extends Serializable {

    /**
     * Setter for <code>train.t_label.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_label.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_label.f_name</code>. 名称 
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_label.f_name</code>. 名称 
     */
    public String getName();

    /**
     * Setter for <code>train.t_label.f_organization_id</code>. 所属组织
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_label.f_organization_id</code>. 所属组织
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_label.f_description</code>. 描述
     */
    public void setDescription(String value);

    /**
     * Getter for <code>train.t_label.f_description</code>. 描述
     */
    public String getDescription();

    /**
     * Setter for <code>train.t_label.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_label.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_label.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_label.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILabel
     */
    public void from(ILabel from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILabel
     */
    public <E extends ILabel> E into(E into);
}
