/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISettlementConfiguration extends Serializable {

    /**
     * Setter for <code>train.t_settlement_configuration.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_settlement_configuration.f_name</code>. 结算单位名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_name</code>. 结算单位名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_settlement_configuration.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_settlement_configuration.f_type_id</code>. 关联配置类型
     */
    public void setTypeId(Integer value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_type_id</code>. 关联配置类型
     */
    public Integer getTypeId();

    /**
     * Setter for <code>train.t_settlement_configuration.f_type</code>. 类别（1总部和省公司 ，2专业公司，3直属单位和境外机构）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_type</code>. 类别（1总部和省公司 ，2专业公司，3直属单位和境外机构）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_settlement_configuration.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_settlement_configuration.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_settlement_configuration.f_contacts</code>. 往来字段
     */
    public void setContacts(String value);

    /**
     * Getter for <code>train.t_settlement_configuration.f_contacts</code>. 往来字段
     */
    public String getContacts();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISettlementConfiguration
     */
    public void from(ISettlementConfiguration from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISettlementConfiguration
     */
    public <E extends ISettlementConfiguration> E into(E into);
}
