/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ConfigurationValue;
import com.zxy.product.train.jooq.tables.interfaces.IConfigurationValue;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConfigurationValueRecord extends UpdatableRecordImpl<ConfigurationValueRecord> implements Record9<String, String, Integer, Long, String, Integer, String, String, Integer>, IConfigurationValue {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_configuration_value.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public void setConfigurationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public String getConfigurationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_type_id</code>. 关联类型ID
     */
    @Override
    public void setTypeId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_type_id</code>. 关联类型ID
     */
    @Override
    public Integer getTypeId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_name</code>. 名称
     */
    @Override
    public void setName(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_name</code>. 名称
     */
    @Override
    public String getName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_coding</code>. 编码
     */
    @Override
    public void setCoding(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_coding</code>. 编码
     */
    @Override
    public String getCoding() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_configuration_value.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_configuration_value.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, Long, String, Integer, String, String, Integer> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, Long, String, Integer, String, String, Integer> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ConfigurationValue.CONFIGURATION_VALUE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ConfigurationValue.CONFIGURATION_VALUE.CONFIGURATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return ConfigurationValue.CONFIGURATION_VALUE.TYPE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return ConfigurationValue.CONFIGURATION_VALUE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ConfigurationValue.CONFIGURATION_VALUE.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return ConfigurationValue.CONFIGURATION_VALUE.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return ConfigurationValue.CONFIGURATION_VALUE.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return ConfigurationValue.CONFIGURATION_VALUE.CODING;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return ConfigurationValue.CONFIGURATION_VALUE.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getConfigurationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getTypeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getCoding();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value2(String value) {
        setConfigurationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value3(Integer value) {
        setTypeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value5(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value6(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value7(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value8(String value) {
        setCoding(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord value9(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValueRecord values(String value1, String value2, Integer value3, Long value4, String value5, Integer value6, String value7, String value8, Integer value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IConfigurationValue from) {
        setId(from.getId());
        setConfigurationId(from.getConfigurationId());
        setTypeId(from.getTypeId());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setName(from.getName());
        setCoding(from.getCoding());
        setSort(from.getSort());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IConfigurationValue> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ConfigurationValueRecord
     */
    public ConfigurationValueRecord() {
        super(ConfigurationValue.CONFIGURATION_VALUE);
    }

    /**
     * Create a detached, initialised ConfigurationValueRecord
     */
    public ConfigurationValueRecord(String id, String configurationId, Integer typeId, Long createTime, String createMember, Integer deleteFlag, String name, String coding, Integer sort) {
        super(ConfigurationValue.CONFIGURATION_VALUE);

        set(0, id);
        set(1, configurationId);
        set(2, typeId);
        set(3, createTime);
        set(4, createMember);
        set(5, deleteFlag);
        set(6, name);
        set(7, coding);
        set(8, sort);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ConfigurationValueEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ConfigurationValueEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ConfigurationValueEntity)source;
        pojo.into(this);
        return true;
    }
}
