/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SettlementConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SettlementConfiguration extends TableImpl<SettlementConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_settlement_configuration</code>
     */
    public static final SettlementConfiguration SETTLEMENT_CONFIGURATION = new SettlementConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SettlementConfigurationRecord> getRecordType() {
        return SettlementConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_settlement_configuration.f_id</code>. 主键
     */
    public final TableField<SettlementConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_settlement_configuration.f_name</code>. 结算单位名称
     */
    public final TableField<SettlementConfigurationRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "结算单位名称");

    /**
     * The column <code>train.t_settlement_configuration.f_sort</code>. 排序
     */
    public final TableField<SettlementConfigurationRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_settlement_configuration.f_type_id</code>. 关联配置类型
     */
    public final TableField<SettlementConfigurationRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER, this, "关联配置类型");

    /**
     * The column <code>train.t_settlement_configuration.f_type</code>. 类别（1总部和省公司 ，2专业公司，3直属单位和境外机构）
     */
    public final TableField<SettlementConfigurationRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类别（1总部和省公司 ，2专业公司，3直属单位和境外机构）");

    /**
     * The column <code>train.t_settlement_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<SettlementConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_settlement_configuration.f_create_member</code>. 创建人ID
     */
    public final TableField<SettlementConfigurationRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_settlement_configuration.f_contacts</code>. 往来字段
     */
    public final TableField<SettlementConfigurationRecord, String> CONTACTS = createField("f_contacts", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "往来字段");

    /**
     * Create a <code>train.t_settlement_configuration</code> table reference
     */
    public SettlementConfiguration() {
        this("t_settlement_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_settlement_configuration</code> table reference
     */
    public SettlementConfiguration(String alias) {
        this(alias, SETTLEMENT_CONFIGURATION);
    }

    private SettlementConfiguration(String alias, Table<SettlementConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private SettlementConfiguration(String alias, Table<SettlementConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SettlementConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_SETTLEMENT_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SettlementConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<SettlementConfigurationRecord>>asList(Keys.KEY_T_SETTLEMENT_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfiguration as(String alias) {
        return new SettlementConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SettlementConfiguration rename(String name) {
        return new SettlementConfiguration(name, null);
    }
}
