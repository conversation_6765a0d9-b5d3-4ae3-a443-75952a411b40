/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassTheme extends Serializable {

    /**
     * Setter for <code>train.t_class_theme.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_theme.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_theme.f_class_id</code>. 培训班ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_theme.f_class_id</code>. 培训班ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_theme.f_type</code>. 类型（1线下  2线上）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_class_theme.f_type</code>. 类型（1线下  2线上）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_class_theme.f_name</code>. 主题名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_class_theme.f_name</code>. 主题名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_class_theme.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_class_theme.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_class_theme.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_theme.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_theme.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_theme.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_theme.f_week</code>. 周次
     */
    public void setWeek(String value);

    /**
     * Getter for <code>train.t_class_theme.f_week</code>. 周次
     */
    public String getWeek();

    /**
     * Setter for <code>train.t_class_theme.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_class_theme.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_class_theme.f_end_time</code>. 结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_class_theme.f_end_time</code>. 结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_class_theme.f_required_theme</code>. 配置必修和选修的主题
     */
    public void setRequiredTheme(String value);

    /**
     * Getter for <code>train.t_class_theme.f_required_theme</code>. 配置必修和选修的主题
     */
    public String getRequiredTheme();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassTheme
     */
    public void from(IClassTheme from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassTheme
     */
    public <E extends IClassTheme> E into(E into);
}
