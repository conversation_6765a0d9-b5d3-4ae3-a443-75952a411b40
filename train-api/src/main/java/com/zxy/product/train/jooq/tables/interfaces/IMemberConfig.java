/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMemberConfig extends Serializable {

    /**
     * Setter for <code>train.t_member_config.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_member_config.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_member_config.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_member_config.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_member_config.f_key</code>. 1=国籍;2=民族;3=政治面貌;4=学历;5=证件类型;6=员工类型;7=在职状态;8=职级;9=条线
     */
    public void setKey(Integer value);

    /**
     * Getter for <code>train.t_member_config.f_key</code>. 1=国籍;2=民族;3=政治面貌;4=学历;5=证件类型;6=员工类型;7=在职状态;8=职级;9=条线
     */
    public Integer getKey();

    /**
     * Setter for <code>train.t_member_config.f_value</code>. 对应的值
     */
    public void setValue(String value);

    /**
     * Getter for <code>train.t_member_config.f_value</code>. 对应的值
     */
    public String getValue();

    /**
     * Setter for <code>train.t_member_config.f_init</code>. 是否为初始化数据(1=不是,0=是)
     */
    public void setInit(Integer value);

    /**
     * Getter for <code>train.t_member_config.f_init</code>. 是否为初始化数据(1=不是,0=是)
     */
    public Integer getInit();

    /**
     * Setter for <code>train.t_member_config.f_ext</code>. 扩展字段(用于查询比如在职的id)
     */
    public void setExt(String value);

    /**
     * Getter for <code>train.t_member_config.f_ext</code>. 扩展字段(用于查询比如在职的id)
     */
    public String getExt();

    /**
     * Setter for <code>train.t_member_config.f_organization_id</code>. 所属组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_member_config.f_organization_id</code>. 所属组织id
     */
    public String getOrganizationId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMemberConfig
     */
    public void from(IMemberConfig from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMemberConfig
     */
    public <E extends IMemberConfig> E into(E into);
}
