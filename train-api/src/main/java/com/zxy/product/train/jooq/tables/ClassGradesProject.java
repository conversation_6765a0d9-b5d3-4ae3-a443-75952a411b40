/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassGradesProjectRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassGradesProject extends TableImpl<ClassGradesProjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_grades_project</code>
     */
    public static final ClassGradesProject CLASS_GRADES_PROJECT = new ClassGradesProject();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassGradesProjectRecord> getRecordType() {
        return ClassGradesProjectRecord.class;
    }

    /**
     * The column <code>train.t_class_grades_project.f_id</code>. 表id
     */
    public final TableField<ClassGradesProjectRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_grades_project.f_name</code>. 项目名称
     */
    public final TableField<ClassGradesProjectRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "项目名称");

    /**
     * The column <code>train.t_class_grades_project.f_status</code>. 学员端状态 0:隐藏 1:显示
     */
    public final TableField<ClassGradesProjectRecord, String> STATUS = createField("f_status", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "学员端状态 0:隐藏 1:显示");

    /**
     * The column <code>train.t_class_grades_project.f_create_time</code>. 创建时间
     */
    public final TableField<ClassGradesProjectRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_class_grades_project.f_create_member</code>. 创建人
     */
    public final TableField<ClassGradesProjectRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>train.t_class_grades_project.f_class_id</code>. 培训班id
     */
    public final TableField<ClassGradesProjectRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训班id");

    /**
     * Create a <code>train.t_class_grades_project</code> table reference
     */
    public ClassGradesProject() {
        this("t_class_grades_project", null);
    }

    /**
     * Create an aliased <code>train.t_class_grades_project</code> table reference
     */
    public ClassGradesProject(String alias) {
        this(alias, CLASS_GRADES_PROJECT);
    }

    private ClassGradesProject(String alias, Table<ClassGradesProjectRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassGradesProject(String alias, Table<ClassGradesProjectRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassGradesProjectRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_GRADES_PROJECT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassGradesProjectRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassGradesProjectRecord>>asList(Keys.KEY_T_CLASS_GRADES_PROJECT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProject as(String alias) {
        return new ClassGradesProject(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassGradesProject rename(String name) {
        return new ClassGradesProject(name, null);
    }
}
