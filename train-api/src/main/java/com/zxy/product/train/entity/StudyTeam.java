package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudyTeamEntity;

/**
 * 学习团队实体类
 *
 * <AUTHOR>
 * @date 2021/4/14/0014 15:38
 */
public class StudyTeam extends StudyTeamEntity {
    private static final long serialVersionUID = -8767002290383166200L;

    /**
     * 状态 0-禁用 1-启用
     */
    public static final Integer FLAG_NO = 0;
    public static final Integer FLAG_YES = 1;

    /**
     * 团队长姓名
     */
    private String captainMemberName;

    /**
     * 归属部门名称
     */
    private String organizationName;

    /**
     * 发起部门id
     */
    private String createOrganizationId;

    /**
     * 发起部门名称
     */
    private String createOrganizationName;

    /**
     * 当前用户是不是管理员 0-否 1-是
     */
    private Integer adminFlag;

    /**
     * 当前用户角色 人员角色 1-团队长 2-助理 3-正式人员  4-其他人员 5-历史人员
     */
    private Integer role;

    /**
     * 组织编码
     */
    private String organizationCode;

    /**
     * 队长用户编码
     */
    private String captainMemberCode;

    /**
     * 最近学习活动
     */
    private StudyTeamActivity recentlyStudyTeamActivity;
    /**
     * 团队学习班-学习成果
     */
    private StudyTeamAchievement studyTeamAchievement;

    public String getCaptainMemberName() {
        return captainMemberName;
    }

    public void setCaptainMemberName(String captainMemberName) {
        this.captainMemberName = captainMemberName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getCreateOrganizationId() {
        return createOrganizationId;
    }

    public void setCreateOrganizationId(String createOrganizationId) {
        this.createOrganizationId = createOrganizationId;
    }

    public String getCreateOrganizationName() {
        return createOrganizationName;
    }

    public void setCreateOrganizationName(String createOrganizationName) {
        this.createOrganizationName = createOrganizationName;
    }

    public Integer getAdminFlag() {
        return adminFlag;
    }

    public void setAdminFlag(Integer adminFlag) {
        this.adminFlag = adminFlag;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getCaptainMemberCode() {
        return captainMemberCode;
    }

    public void setCaptainMemberCode(String captainMemberCode) {
        this.captainMemberCode = captainMemberCode;
    }

    public StudyTeamActivity getRecentlyStudyTeamActivity() {
        return recentlyStudyTeamActivity;
    }

    public void setRecentlyStudyTeamActivity(StudyTeamActivity recentlyStudyTeamActivity) {
        this.recentlyStudyTeamActivity = recentlyStudyTeamActivity;
    }

    public StudyTeamAchievement getStudyTeamAchievement() {
        return studyTeamAchievement;
    }

    public void setStudyTeamAchievement(StudyTeamAchievement studyTeamAchievement) {
        this.studyTeamAchievement = studyTeamAchievement;
    }

    public enum OutTrainingBusinessType {
        THE_GRID("网格",0),
        BUSINESS_DISTRICT_BUILDING_GRID("商圈楼宇型网格",1),
        COMMUNITY_TYPE_GRID("社区型网格",2),
        CLUSTERING_MARKET_GRID("聚类市场型网格",3),
        CAMPUS_TYPE_GRID("校园型网格",4),
        GARDEN_TYPE_GRID("园区型网格",5),
        TOWNSHIP_TYPE_GRID("乡镇型网格",6),
        OTHER("其他-非网格",7);

        private  String name;
        private Integer code;

        OutTrainingBusinessType(String name, Integer code) {
            this.name = name;
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public Integer getCode() {
            return code;
        }

        public static Integer getCodeByName(String name) {
            if (THE_GRID.name.equals(name)){
                return THE_GRID.code;
            }else if (OTHER.name.equals(name)){
                return OTHER.code;
            }else if (BUSINESS_DISTRICT_BUILDING_GRID.name.equals(name)){
                return BUSINESS_DISTRICT_BUILDING_GRID.code;
            }else if (COMMUNITY_TYPE_GRID.name.equals(name)){
                return COMMUNITY_TYPE_GRID.code;
            }else if (TOWNSHIP_TYPE_GRID.name.equals(name)){
                return TOWNSHIP_TYPE_GRID.code;
            }else if (CLUSTERING_MARKET_GRID.name.equals(name)){
                return CLUSTERING_MARKET_GRID.code;
            } else if (CAMPUS_TYPE_GRID.name.equals(name)){
                return CAMPUS_TYPE_GRID.code;
            } else if (GARDEN_TYPE_GRID.name.equals(name)){
                return GARDEN_TYPE_GRID.code;
            }else {
                return null;
            }
        }
        public static String[] getBusinessTypeNames(){
            return new String[]{THE_GRID.name,BUSINESS_DISTRICT_BUILDING_GRID.name,COMMUNITY_TYPE_GRID.name,CLUSTERING_MARKET_GRID.name,CAMPUS_TYPE_GRID.name,GARDEN_TYPE_GRID.name,TOWNSHIP_TYPE_GRID.name,OTHER.name};
        }
    }
}
