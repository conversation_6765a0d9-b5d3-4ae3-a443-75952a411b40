/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ConfigruationHistoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 历史配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConfigruationHistory extends TableImpl<ConfigruationHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_configruation_history</code>
     */
    public static final ConfigruationHistory CONFIGRUATION_HISTORY = new ConfigruationHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConfigruationHistoryRecord> getRecordType() {
        return ConfigruationHistoryRecord.class;
    }

    /**
     * The column <code>train.t_configruation_history.f_id</code>.
     */
    public final TableField<ConfigruationHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_configruation_history.f_type</code>. 类别:1A,2B,3C,4G,5M,6O,7R,8T,9Z
     */
    public final TableField<ConfigruationHistoryRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类别:1A,2B,3C,4G,5M,6O,7R,8T,9Z");

    /**
     * The column <code>train.t_configruation_history.f_name</code>. 名称
     */
    public final TableField<ConfigruationHistoryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "名称");

    /**
     * The column <code>train.t_configruation_history.f_organization_id</code>. 归属机构
     */
    public final TableField<ConfigruationHistoryRecord, Long> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.BIGINT, this, "归属机构");

    /**
     * Create a <code>train.t_configruation_history</code> table reference
     */
    public ConfigruationHistory() {
        this("t_configruation_history", null);
    }

    /**
     * Create an aliased <code>train.t_configruation_history</code> table reference
     */
    public ConfigruationHistory(String alias) {
        this(alias, CONFIGRUATION_HISTORY);
    }

    private ConfigruationHistory(String alias, Table<ConfigruationHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ConfigruationHistory(String alias, Table<ConfigruationHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "历史配置表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ConfigruationHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_CONFIGRUATION_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ConfigruationHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<ConfigruationHistoryRecord>>asList(Keys.KEY_T_CONFIGRUATION_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigruationHistory as(String alias) {
        return new ConfigruationHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ConfigruationHistory rename(String name) {
        return new ConfigruationHistory(name, null);
    }
}
