package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassTwoBringsEntity;

/**
 *
 * <AUTHOR> 两个带来实体类
 *
 */
public class ClassTwoBrings extends ClassTwoBringsEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = 3381658826343444426L;

	private Member member;

	private Organization organization;

	private ClassInfo classInfo;

	private ClassSignupInfo classSignupInfo;

	public ClassInfo getClassInfo() {
		return classInfo;
	}

	public void setClassInfo(ClassInfo classInfo) {
		this.classInfo = classInfo;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public ClassSignupInfo getClassSignupInfo() {
		return classSignupInfo;
	}

	public void setClassSignupInfo(ClassSignupInfo classSignupInfo) {
		this.classSignupInfo = classSignupInfo;
	}


}
