/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassGradesProjectMember extends Serializable {

    /**
     * Setter for <code>train.t_class_grades_project_member.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_grades_project_member.f_project_id</code>. t_class_grades_project表id
     */
    public void setProjectId(String value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_project_id</code>. t_class_grades_project表id
     */
    public String getProjectId();

    /**
     * Setter for <code>train.t_class_grades_project_member.f_desc</code>. 备注
     */
    public void setDesc(String value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_desc</code>. 备注
     */
    public String getDesc();

    /**
     * Setter for <code>train.t_class_grades_project_member.f_grades</code>. 成绩
     */
    public void setGrades(String value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_grades</code>. 成绩
     */
    public String getGrades();

    /**
     * Setter for <code>train.t_class_grades_project_member.f_member_id</code>.
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_member_id</code>.
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_class_grades_project_member.f_member_number</code>. 员工编号
     */
    public void setMemberNumber(String value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_member_number</code>. 员工编号
     */
    public String getMemberNumber();

    /**
     * Setter for <code>train.t_class_grades_project_member.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_grades_project_member.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_grades_project_member.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassGradesProjectMember
     */
    public void from(IClassGradesProjectMember from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassGradesProjectMember
     */
    public <E extends IClassGradesProjectMember> E into(E into);
}
