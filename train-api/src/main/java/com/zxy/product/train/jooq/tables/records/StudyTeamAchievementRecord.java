/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamAchievement;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamAchievement;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record17;
import org.jooq.Row17;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 团队学习班-学习成果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamAchievementRecord extends UpdatableRecordImpl<StudyTeamAchievementRecord> implements Record17<String, String, String, String, String, String, Integer, Integer, Inte<PERSON>, <PERSON>te<PERSON>, Integer, Integer, Long, String, String, Long, Long>, IStudyTeamAchievement {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_achievement.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_team_id</code>. 团队id
     */
    @Override
    public void setTeamId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_team_id</code>. 团队id
     */
    @Override
    public String getTeamId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_activity_id</code>. 活动id
     */
    @Override
    public void setActivityId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_activity_id</code>. 活动id
     */
    @Override
    public String getActivityId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_business_id</code>. 关联课程id
     */
    @Override
    public void setBusinessId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_business_id</code>. 关联课程id
     */
    @Override
    public String getBusinessId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_content</code>. 讨论内容
     */
    @Override
    public void setContent(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_content</code>. 讨论内容
     */
    @Override
    public String getContent() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_top_status</code>. 置顶(1是，0否)
     */
    @Override
    public void setTopStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_top_status</code>. 置顶(1是，0否)
     */
    @Override
    public Integer getTopStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_essence_status</code>. 精华(1是，0否)
     */
    @Override
    public void setEssenceStatus(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_essence_status</code>. 精华(1是，0否)
     */
    @Override
    public Integer getEssenceStatus() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_hide</code>. 隐藏(1是，0否)
     */
    @Override
    public void setHide(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_hide</code>. 隐藏(1是，0否)
     */
    @Override
    public Integer getHide() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_comment_count</code>. 讨论数
     */
    @Override
    public void setCommentCount(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_comment_count</code>. 讨论数
     */
    @Override
    public Integer getCommentCount() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_praise_count</code>. 点赞数
     */
    @Override
    public void setPraiseCount(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_praise_count</code>. 点赞数
     */
    @Override
    public Integer getPraiseCount() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_organization_id</code>. 所属组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_organization_id</code>. 所属组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(13);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_content_text</code>. 讨论内容——纯文本
     */
    @Override
    public void setContentText(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_content_text</code>. 讨论内容——纯文本
     */
    @Override
    public String getContentText() {
        return (String) get(14);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_essence_time</code>. 加精时间
     */
    @Override
    public void setEssenceTime(Long value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_essence_time</code>. 加精时间
     */
    @Override
    public Long getEssenceTime() {
        return (Long) get(15);
    }

    /**
     * Setter for <code>train.t_study_team_achievement.f_top_time</code>. 置顶时间
     */
    @Override
    public void setTopTime(Long value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement.f_top_time</code>. 置顶时间
     */
    @Override
    public Long getTopTime() {
        return (Long) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record17 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, String, String, String, String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long, String, String, Long, Long> fieldsRow() {
        return (Row17) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, String, String, String, String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long, String, String, Long, Long> valuesRow() {
        return (Row17) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.TEAM_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.ACTIVITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.CONTENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.TOP_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.ESSENCE_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.HIDE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.COMMENT_COUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.PRAISE_COUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field13() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field14() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field15() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.CONTENT_TEXT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field16() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.ESSENCE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field17() {
        return StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT.TOP_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getTeamId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getActivityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getContent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getTopStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getEssenceStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getHide();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getCommentCount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getPraiseCount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value13() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value14() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value15() {
        return getContentText();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value16() {
        return getEssenceTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value17() {
        return getTopTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value2(String value) {
        setTeamId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value3(String value) {
        setActivityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value5(String value) {
        setBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value6(String value) {
        setContent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value7(Integer value) {
        setTopStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value8(Integer value) {
        setEssenceStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value9(Integer value) {
        setHide(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value10(Integer value) {
        setCommentCount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value11(Integer value) {
        setPraiseCount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value12(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value13(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value14(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value15(String value) {
        setContentText(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value16(Long value) {
        setEssenceTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord value17(Long value) {
        setTopTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementRecord values(String value1, String value2, String value3, String value4, String value5, String value6, Integer value7, Integer value8, Integer value9, Integer value10, Integer value11, Integer value12, Long value13, String value14, String value15, Long value16, Long value17) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamAchievement from) {
        setId(from.getId());
        setTeamId(from.getTeamId());
        setActivityId(from.getActivityId());
        setMemberId(from.getMemberId());
        setBusinessId(from.getBusinessId());
        setContent(from.getContent());
        setTopStatus(from.getTopStatus());
        setEssenceStatus(from.getEssenceStatus());
        setHide(from.getHide());
        setCommentCount(from.getCommentCount());
        setPraiseCount(from.getPraiseCount());
        setDeleteFlag(from.getDeleteFlag());
        setCreateTime(from.getCreateTime());
        setOrganizationId(from.getOrganizationId());
        setContentText(from.getContentText());
        setEssenceTime(from.getEssenceTime());
        setTopTime(from.getTopTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamAchievement> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamAchievementRecord
     */
    public StudyTeamAchievementRecord() {
        super(StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT);
    }

    /**
     * Create a detached, initialised StudyTeamAchievementRecord
     */
    public StudyTeamAchievementRecord(String id, String teamId, String activityId, String memberId, String businessId, String content, Integer topStatus, Integer essenceStatus, Integer hide, Integer commentCount, Integer praiseCount, Integer deleteFlag, Long createTime, String organizationId, String contentText, Long essenceTime, Long topTime) {
        super(StudyTeamAchievement.STUDY_TEAM_ACHIEVEMENT);

        set(0, id);
        set(1, teamId);
        set(2, activityId);
        set(3, memberId);
        set(4, businessId);
        set(5, content);
        set(6, topStatus);
        set(7, essenceStatus);
        set(8, hide);
        set(9, commentCount);
        set(10, praiseCount);
        set(11, deleteFlag);
        set(12, createTime);
        set(13, organizationId);
        set(14, contentText);
        set(15, essenceTime);
        set(16, topTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementEntity)source;
        pojo.into(this);
        return true;
    }
}
