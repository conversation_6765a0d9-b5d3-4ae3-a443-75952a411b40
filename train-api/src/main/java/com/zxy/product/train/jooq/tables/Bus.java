/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.BusRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 班车表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Bus extends TableImpl<BusRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_bus</code>
     */
    public static final Bus BUS = new Bus();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BusRecord> getRecordType() {
        return BusRecord.class;
    }

    /**
     * The column <code>train.t_bus.f_id</code>. 主键
     */
    public final TableField<BusRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_bus.f_name</code>. 统计主题
     */
    public final TableField<BusRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "统计主题");

    /**
     * The column <code>train.t_bus.f_start_time</code>. 开始时间
     */
    public final TableField<BusRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * The column <code>train.t_bus.f_end_time</code>. 结束时间
     */
    public final TableField<BusRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "结束时间");

    /**
     * The column <code>train.t_bus.f_delete_flag</code>. 删除状态 0未删除(默认) 1已删除
     */
    public final TableField<BusRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除状态 0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_bus.f_create_time</code>. 创建时间
     */
    public final TableField<BusRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_bus.f_class_id</code>. 班级ID
     */
    public final TableField<BusRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_bus.f_create_mem</code>. 创建人ID
     */
    public final TableField<BusRecord, String> CREATE_MEM = createField("f_create_mem", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_bus.f_state</code>. 发布状态 0未开始 1已开始 2已结束
     */
    public final TableField<BusRecord, Integer> STATE = createField("f_state", org.jooq.impl.SQLDataType.INTEGER, this, "发布状态 0未开始 1已开始 2已结束");

    /**
     * Create a <code>train.t_bus</code> table reference
     */
    public Bus() {
        this("t_bus", null);
    }

    /**
     * Create an aliased <code>train.t_bus</code> table reference
     */
    public Bus(String alias) {
        this(alias, BUS);
    }

    private Bus(String alias, Table<BusRecord> aliased) {
        this(alias, aliased, null);
    }

    private Bus(String alias, Table<BusRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "班车表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<BusRecord> getPrimaryKey() {
        return Keys.KEY_T_BUS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<BusRecord>> getKeys() {
        return Arrays.<UniqueKey<BusRecord>>asList(Keys.KEY_T_BUS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Bus as(String alias) {
        return new Bus(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Bus rename(String name) {
        return new Bus(name, null);
    }
}
