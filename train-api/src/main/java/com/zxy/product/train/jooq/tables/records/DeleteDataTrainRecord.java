/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.DeleteDataTrain;
import com.zxy.product.train.jooq.tables.interfaces.IDeleteDataTrain;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 删除记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DeleteDataTrainRecord extends UpdatableRecordImpl<DeleteDataTrainRecord> implements Record7<String, String, String, String, Long, Timestamp, String>, IDeleteDataTrain {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_delete_data_train.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_delete_data_train.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_delete_data_train.f_database_name</code>. 数据库名称-全程
     */
    @Override
    public void setDatabaseName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_delete_data_train.f_database_name</code>. 数据库名称-全程
     */
    @Override
    public String getDatabaseName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_delete_data_train.f_table_name</code>. 表名称
     */
    @Override
    public void setTableName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_delete_data_train.f_table_name</code>. 表名称
     */
    @Override
    public String getTableName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_delete_data_train.f_business_id</code>. 业务id
     */
    @Override
    public void setBusinessId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_delete_data_train.f_business_id</code>. 业务id
     */
    @Override
    public String getBusinessId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_delete_data_train.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_delete_data_train.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_delete_data_train.f_modify_date</code>. 更新时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_delete_data_train.f_modify_date</code>. 更新时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(5);
    }

    /**
     * Setter for <code>train.t_delete_data_train.f_company_id</code>. 企业id
     */
    @Override
    public void setCompanyId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_delete_data_train.f_company_id</code>. 企业id
     */
    @Override
    public String getCompanyId() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, Long, Timestamp, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, Long, Timestamp, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return DeleteDataTrain.DELETE_DATA_TRAIN.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return DeleteDataTrain.DELETE_DATA_TRAIN.DATABASE_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return DeleteDataTrain.DELETE_DATA_TRAIN.TABLE_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return DeleteDataTrain.DELETE_DATA_TRAIN.BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return DeleteDataTrain.DELETE_DATA_TRAIN.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field6() {
        return DeleteDataTrain.DELETE_DATA_TRAIN.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return DeleteDataTrain.DELETE_DATA_TRAIN.COMPANY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getDatabaseName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getTableName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value6() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord value2(String value) {
        setDatabaseName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord value3(String value) {
        setTableName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord value4(String value) {
        setBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord value6(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord value7(String value) {
        setCompanyId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrainRecord values(String value1, String value2, String value3, String value4, Long value5, Timestamp value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IDeleteDataTrain from) {
        setId(from.getId());
        setDatabaseName(from.getDatabaseName());
        setTableName(from.getTableName());
        setBusinessId(from.getBusinessId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setCompanyId(from.getCompanyId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IDeleteDataTrain> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DeleteDataTrainRecord
     */
    public DeleteDataTrainRecord() {
        super(DeleteDataTrain.DELETE_DATA_TRAIN);
    }

    /**
     * Create a detached, initialised DeleteDataTrainRecord
     */
    public DeleteDataTrainRecord(String id, String databaseName, String tableName, String businessId, Long createTime, Timestamp modifyDate, String companyId) {
        super(DeleteDataTrain.DELETE_DATA_TRAIN);

        set(0, id);
        set(1, databaseName);
        set(2, tableName);
        set(3, businessId);
        set(4, createTime);
        set(5, modifyDate);
        set(6, companyId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.DeleteDataTrainEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.DeleteDataTrainEntity pojo = (com.zxy.product.train.jooq.tables.pojos.DeleteDataTrainEntity)source;
        pojo.into(this);
        return true;
    }
}
