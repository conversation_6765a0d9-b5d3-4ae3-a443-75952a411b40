/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LecturerReleaseAuthorityRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 讲师发布权限人员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerReleaseAuthority extends TableImpl<LecturerReleaseAuthorityRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_lecturer_release_authority</code>
     */
    public static final LecturerReleaseAuthority LECTURER_RELEASE_AUTHORITY = new LecturerReleaseAuthority();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LecturerReleaseAuthorityRecord> getRecordType() {
        return LecturerReleaseAuthorityRecord.class;
    }

    /**
     * The column <code>train.t_lecturer_release_authority.f_id</code>. 主键
     */
    public final TableField<LecturerReleaseAuthorityRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_lecturer_release_authority.f_member_id</code>. 用户ID
     */
    public final TableField<LecturerReleaseAuthorityRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户ID");

    /**
     * The column <code>train.t_lecturer_release_authority.f_name</code>. 人员编号
     */
    public final TableField<LecturerReleaseAuthorityRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.VARCHAR)), this, "人员编号");

    /**
     * The column <code>train.t_lecturer_release_authority.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<LecturerReleaseAuthorityRecord, Short> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.SMALLINT.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.SMALLINT)), this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_lecturer_release_authority.f_create_time</code>. 创建时间
     */
    public final TableField<LecturerReleaseAuthorityRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_lecturer_release_authority.f_create_member_id</code>. 创建人
     */
    public final TableField<LecturerReleaseAuthorityRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * Create a <code>train.t_lecturer_release_authority</code> table reference
     */
    public LecturerReleaseAuthority() {
        this("t_lecturer_release_authority", null);
    }

    /**
     * Create an aliased <code>train.t_lecturer_release_authority</code> table reference
     */
    public LecturerReleaseAuthority(String alias) {
        this(alias, LECTURER_RELEASE_AUTHORITY);
    }

    private LecturerReleaseAuthority(String alias, Table<LecturerReleaseAuthorityRecord> aliased) {
        this(alias, aliased, null);
    }

    private LecturerReleaseAuthority(String alias, Table<LecturerReleaseAuthorityRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "讲师发布权限人员表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LecturerReleaseAuthorityRecord> getPrimaryKey() {
        return Keys.KEY_T_LECTURER_RELEASE_AUTHORITY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LecturerReleaseAuthorityRecord>> getKeys() {
        return Arrays.<UniqueKey<LecturerReleaseAuthorityRecord>>asList(Keys.KEY_T_LECTURER_RELEASE_AUTHORITY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthority as(String alias) {
        return new LecturerReleaseAuthority(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LecturerReleaseAuthority rename(String name) {
        return new LecturerReleaseAuthority(name, null);
    }
}
