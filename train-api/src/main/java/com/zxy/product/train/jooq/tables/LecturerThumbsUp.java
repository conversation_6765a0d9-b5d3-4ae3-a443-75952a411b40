/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LecturerThumbsUpRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 讲师点赞记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerThumbsUp extends TableImpl<LecturerThumbsUpRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_lecturer_thumbs_up</code>
     */
    public static final LecturerThumbsUp LECTURER_THUMBS_UP = new LecturerThumbsUp();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LecturerThumbsUpRecord> getRecordType() {
        return LecturerThumbsUpRecord.class;
    }

    /**
     * The column <code>train.t_lecturer_thumbs_up.f_id</code>. 主键
     */
    public final TableField<LecturerThumbsUpRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_lecturer_thumbs_up.f_lecturer_id</code>. 讲师ID
     */
    public final TableField<LecturerThumbsUpRecord, String> LECTURER_ID = createField("f_lecturer_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "讲师ID");

    /**
     * The column <code>train.t_lecturer_thumbs_up.f_member_id</code>. 用户ID
     */
    public final TableField<LecturerThumbsUpRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "用户ID");

    /**
     * The column <code>train.t_lecturer_thumbs_up.f_create_time</code>. 创建时间
     */
    public final TableField<LecturerThumbsUpRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_lecturer_thumbs_up</code> table reference
     */
    public LecturerThumbsUp() {
        this("t_lecturer_thumbs_up", null);
    }

    /**
     * Create an aliased <code>train.t_lecturer_thumbs_up</code> table reference
     */
    public LecturerThumbsUp(String alias) {
        this(alias, LECTURER_THUMBS_UP);
    }

    private LecturerThumbsUp(String alias, Table<LecturerThumbsUpRecord> aliased) {
        this(alias, aliased, null);
    }

    private LecturerThumbsUp(String alias, Table<LecturerThumbsUpRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "讲师点赞记录");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LecturerThumbsUpRecord> getPrimaryKey() {
        return Keys.KEY_T_LECTURER_THUMBS_UP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LecturerThumbsUpRecord>> getKeys() {
        return Arrays.<UniqueKey<LecturerThumbsUpRecord>>asList(Keys.KEY_T_LECTURER_THUMBS_UP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerThumbsUp as(String alias) {
        return new LecturerThumbsUp(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LecturerThumbsUp rename(String name) {
        return new LecturerThumbsUp(name, null);
    }
}
