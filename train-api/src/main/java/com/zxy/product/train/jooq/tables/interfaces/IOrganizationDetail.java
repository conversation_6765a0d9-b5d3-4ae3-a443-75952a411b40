/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IOrganizationDetail extends Serializable {

    /**
     * Setter for <code>train.t_organization_detail.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_organization_detail.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_organization_detail.f_root</code>.  父节点
     */
    public void setRoot(String value);

    /**
     * Getter for <code>train.t_organization_detail.f_root</code>.  父节点
     */
    public String getRoot();

    /**
     * Setter for <code>train.t_organization_detail.f_sub</code>. 子节点
     */
    public void setSub(String value);

    /**
     * Getter for <code>train.t_organization_detail.f_sub</code>. 子节点
     */
    public String getSub();

    /**
     * Setter for <code>train.t_organization_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_organization_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IOrganizationDetail
     */
    public void from(IOrganizationDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IOrganizationDetail
     */
    public <E extends IOrganizationDetail> E into(E into);
}
