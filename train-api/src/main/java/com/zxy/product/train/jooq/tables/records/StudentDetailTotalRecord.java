/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudentDetailTotal;
import com.zxy.product.train.jooq.tables.interfaces.IStudentDetailTotal;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record19;
import org.jooq.Row19;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudentDetailTotalRecord extends UpdatableRecordImpl<StudentDetailTotalRecord> implements Record19<String, String, String, String, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Integer, Double, Long>, IStudentDetailTotal {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_student_detail_total.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_name</code>. 结算公司
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_name</code>. 结算公司
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_settlement_company_code</code>. 结算公司组织编码
     */
    @Override
    public void setSettlementCompanyCode(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_settlement_company_code</code>. 结算公司组织编码
     */
    @Override
    public String getSettlementCompanyCode() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_year</code>. 年份
     */
    @Override
    public void setYear(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_year</code>. 年份
     */
    @Override
    public String getYear() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_january</code>. 一月
     */
    @Override
    public void setJanuary(Double value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_january</code>. 一月
     */
    @Override
    public Double getJanuary() {
        return (Double) get(4);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_march</code>. 三月
     */
    @Override
    public void setMarch(Double value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_march</code>. 三月
     */
    @Override
    public Double getMarch() {
        return (Double) get(5);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_february</code>. 二月
     */
    @Override
    public void setFebruary(Double value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_february</code>. 二月
     */
    @Override
    public Double getFebruary() {
        return (Double) get(6);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_december</code>. 十二月
     */
    @Override
    public void setDecember(Double value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_december</code>. 十二月
     */
    @Override
    public Double getDecember() {
        return (Double) get(7);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_november</code>. 十一月
     */
    @Override
    public void setNovember(Double value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_november</code>. 十一月
     */
    @Override
    public Double getNovember() {
        return (Double) get(8);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_october</code>. 十月
     */
    @Override
    public void setOctober(Double value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_october</code>. 十月
     */
    @Override
    public Double getOctober() {
        return (Double) get(9);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_september</code>. 九月
     */
    @Override
    public void setSeptember(Double value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_september</code>. 九月
     */
    @Override
    public Double getSeptember() {
        return (Double) get(10);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_august</code>. 八月
     */
    @Override
    public void setAugust(Double value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_august</code>. 八月
     */
    @Override
    public Double getAugust() {
        return (Double) get(11);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_july</code>. 七月
     */
    @Override
    public void setJuly(Double value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_july</code>. 七月
     */
    @Override
    public Double getJuly() {
        return (Double) get(12);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_june</code>. 六月
     */
    @Override
    public void setJune(Double value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_june</code>. 六月
     */
    @Override
    public Double getJune() {
        return (Double) get(13);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_may</code>. 五月
     */
    @Override
    public void setMay(Double value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_may</code>. 五月
     */
    @Override
    public Double getMay() {
        return (Double) get(14);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_april</code>. 四月
     */
    @Override
    public void setApril(Double value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_april</code>. 四月
     */
    @Override
    public Double getApril() {
        return (Double) get(15);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(16);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_total</code>. 总计
     */
    @Override
    public void setTotal(Double value) {
        set(17, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_total</code>. 总计
     */
    @Override
    public Double getTotal() {
        return (Double) get(17);
    }

    /**
     * Setter for <code>train.t_student_detail_total.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(18, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(18);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record19 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row19<String, String, String, String, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Integer, Double, Long> fieldsRow() {
        return (Row19) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row19<String, String, String, String, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Double, Integer, Double, Long> valuesRow() {
        return (Row19) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.SETTLEMENT_COMPANY_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.YEAR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field5() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.JANUARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field6() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.MARCH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field7() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.FEBRUARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field8() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.DECEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field9() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.NOVEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field10() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.OCTOBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field11() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.SEPTEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field12() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.AUGUST;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field13() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.JULY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field14() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.JUNE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field15() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.MAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field16() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.APRIL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field17() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field18() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.TOTAL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field19() {
        return StudentDetailTotal.STUDENT_DETAIL_TOTAL.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getSettlementCompanyCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getYear();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value5() {
        return getJanuary();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value6() {
        return getMarch();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value7() {
        return getFebruary();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value8() {
        return getDecember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value9() {
        return getNovember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value10() {
        return getOctober();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value11() {
        return getSeptember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value12() {
        return getAugust();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value13() {
        return getJuly();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value14() {
        return getJune();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value15() {
        return getMay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value16() {
        return getApril();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value17() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value18() {
        return getTotal();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value19() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value3(String value) {
        setSettlementCompanyCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value4(String value) {
        setYear(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value5(Double value) {
        setJanuary(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value6(Double value) {
        setMarch(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value7(Double value) {
        setFebruary(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value8(Double value) {
        setDecember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value9(Double value) {
        setNovember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value10(Double value) {
        setOctober(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value11(Double value) {
        setSeptember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value12(Double value) {
        setAugust(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value13(Double value) {
        setJuly(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value14(Double value) {
        setJune(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value15(Double value) {
        setMay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value16(Double value) {
        setApril(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value17(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value18(Double value) {
        setTotal(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord value19(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalRecord values(String value1, String value2, String value3, String value4, Double value5, Double value6, Double value7, Double value8, Double value9, Double value10, Double value11, Double value12, Double value13, Double value14, Double value15, Double value16, Integer value17, Double value18, Long value19) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudentDetailTotal from) {
        setId(from.getId());
        setName(from.getName());
        setSettlementCompanyCode(from.getSettlementCompanyCode());
        setYear(from.getYear());
        setJanuary(from.getJanuary());
        setMarch(from.getMarch());
        setFebruary(from.getFebruary());
        setDecember(from.getDecember());
        setNovember(from.getNovember());
        setOctober(from.getOctober());
        setSeptember(from.getSeptember());
        setAugust(from.getAugust());
        setJuly(from.getJuly());
        setJune(from.getJune());
        setMay(from.getMay());
        setApril(from.getApril());
        setSort(from.getSort());
        setTotal(from.getTotal());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudentDetailTotal> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudentDetailTotalRecord
     */
    public StudentDetailTotalRecord() {
        super(StudentDetailTotal.STUDENT_DETAIL_TOTAL);
    }

    /**
     * Create a detached, initialised StudentDetailTotalRecord
     */
    public StudentDetailTotalRecord(String id, String name, String settlementCompanyCode, String year, Double january, Double march, Double february, Double december, Double november, Double october, Double september, Double august, Double july, Double june, Double may, Double april, Integer sort, Double total, Long createTime) {
        super(StudentDetailTotal.STUDENT_DETAIL_TOTAL);

        set(0, id);
        set(1, name);
        set(2, settlementCompanyCode);
        set(3, year);
        set(4, january);
        set(5, march);
        set(6, february);
        set(7, december);
        set(8, november);
        set(9, october);
        set(10, september);
        set(11, august);
        set(12, july);
        set(13, june);
        set(14, may);
        set(15, april);
        set(16, sort);
        set(17, total);
        set(18, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudentDetailTotalEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudentDetailTotalEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudentDetailTotalEntity)source;
        pojo.into(this);
        return true;
    }
}
