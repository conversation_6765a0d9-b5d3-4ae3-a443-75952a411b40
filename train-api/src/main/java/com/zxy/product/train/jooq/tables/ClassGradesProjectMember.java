/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassGradesProjectMemberRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassGradesProjectMember extends TableImpl<ClassGradesProjectMemberRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_grades_project_member</code>
     */
    public static final ClassGradesProjectMember CLASS_GRADES_PROJECT_MEMBER = new ClassGradesProjectMember();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassGradesProjectMemberRecord> getRecordType() {
        return ClassGradesProjectMemberRecord.class;
    }

    /**
     * The column <code>train.t_class_grades_project_member.f_id</code>. 表id
     */
    public final TableField<ClassGradesProjectMemberRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_grades_project_member.f_project_id</code>. t_class_grades_project表id
     */
    public final TableField<ClassGradesProjectMemberRecord, String> PROJECT_ID = createField("f_project_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "t_class_grades_project表id");

    /**
     * The column <code>train.t_class_grades_project_member.f_desc</code>. 备注
     */
    public final TableField<ClassGradesProjectMemberRecord, String> DESC = createField("f_desc", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>train.t_class_grades_project_member.f_grades</code>. 成绩
     */
    public final TableField<ClassGradesProjectMemberRecord, String> GRADES = createField("f_grades", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "成绩");

    /**
     * The column <code>train.t_class_grades_project_member.f_member_id</code>.
     */
    public final TableField<ClassGradesProjectMemberRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>train.t_class_grades_project_member.f_member_number</code>. 员工编号
     */
    public final TableField<ClassGradesProjectMemberRecord, String> MEMBER_NUMBER = createField("f_member_number", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工编号");

    /**
     * The column <code>train.t_class_grades_project_member.f_create_time</code>. 创建时间
     */
    public final TableField<ClassGradesProjectMemberRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_class_grades_project_member.f_create_member</code>. 创建人
     */
    public final TableField<ClassGradesProjectMemberRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * Create a <code>train.t_class_grades_project_member</code> table reference
     */
    public ClassGradesProjectMember() {
        this("t_class_grades_project_member", null);
    }

    /**
     * Create an aliased <code>train.t_class_grades_project_member</code> table reference
     */
    public ClassGradesProjectMember(String alias) {
        this(alias, CLASS_GRADES_PROJECT_MEMBER);
    }

    private ClassGradesProjectMember(String alias, Table<ClassGradesProjectMemberRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassGradesProjectMember(String alias, Table<ClassGradesProjectMemberRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassGradesProjectMemberRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_GRADES_PROJECT_MEMBER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassGradesProjectMemberRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassGradesProjectMemberRecord>>asList(Keys.KEY_T_CLASS_GRADES_PROJECT_MEMBER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMember as(String alias) {
        return new ClassGradesProjectMember(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassGradesProjectMember rename(String name) {
        return new ClassGradesProjectMember(name, null);
    }
}
