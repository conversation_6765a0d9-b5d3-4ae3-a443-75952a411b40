/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.GroupConfigurationValue;
import com.zxy.product.train.jooq.tables.interfaces.IGroupConfigurationValue;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GroupConfigurationValueRecord extends UpdatableRecordImpl<GroupConfigurationValueRecord> implements Record9<String, String, String, Integer, Long, String, Integer, String, String>, IGroupConfigurationValue {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_group_configuration_value.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_organization_id</code>. 单位名称
     */
    @Override
    public void setOrganizationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_organization_id</code>. 单位名称
     */
    @Override
    public String getOrganizationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_group_id</code>. 关联分组配置ID
     */
    @Override
    public void setGroupId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_group_id</code>. 关联分组配置ID
     */
    @Override
    public String getGroupId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    @Override
    public void setPath(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    @Override
    public String getPath() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_group_configuration_value.f_short_name</code>. 短名称
     */
    @Override
    public void setShortName(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_group_configuration_value.f_short_name</code>. 短名称
     */
    @Override
    public String getShortName() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, Integer, Long, String, Integer, String, String> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, Integer, Long, String, Integer, String, String> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.GROUP_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return GroupConfigurationValue.GROUP_CONFIGURATION_VALUE.SHORT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getGroupId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getShortName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value2(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value3(String value) {
        setGroupId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value4(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value6(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value7(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value8(String value) {
        setPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord value9(String value) {
        setShortName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValueRecord values(String value1, String value2, String value3, Integer value4, Long value5, String value6, Integer value7, String value8, String value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IGroupConfigurationValue from) {
        setId(from.getId());
        setOrganizationId(from.getOrganizationId());
        setGroupId(from.getGroupId());
        setSort(from.getSort());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setPath(from.getPath());
        setShortName(from.getShortName());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IGroupConfigurationValue> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached GroupConfigurationValueRecord
     */
    public GroupConfigurationValueRecord() {
        super(GroupConfigurationValue.GROUP_CONFIGURATION_VALUE);
    }

    /**
     * Create a detached, initialised GroupConfigurationValueRecord
     */
    public GroupConfigurationValueRecord(String id, String organizationId, String groupId, Integer sort, Long createTime, String createMember, Integer deleteFlag, String path, String shortName) {
        super(GroupConfigurationValue.GROUP_CONFIGURATION_VALUE);

        set(0, id);
        set(1, organizationId);
        set(2, groupId);
        set(3, sort);
        set(4, createTime);
        set(5, createMember);
        set(6, deleteFlag);
        set(7, path);
        set(8, shortName);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.GroupConfigurationValueEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.GroupConfigurationValueEntity pojo = (com.zxy.product.train.jooq.tables.pojos.GroupConfigurationValueEntity)source;
        pojo.into(this);
        return true;
    }
}
