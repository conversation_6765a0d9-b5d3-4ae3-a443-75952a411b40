/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TrainProjectRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训项目表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TrainProject extends TableImpl<TrainProjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_train_project</code>
     */
    public static final TrainProject TRAIN_PROJECT = new TrainProject();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TrainProjectRecord> getRecordType() {
        return TrainProjectRecord.class;
    }

    /**
     * The column <code>train.t_train_project.f_id</code>. 主键
     */
    public final TableField<TrainProjectRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_train_project.f_project_id</code>. 培训计划id
     */
    public final TableField<TrainProjectRecord, String> PROJECT_ID = createField("f_project_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训计划id");

    /**
     * The column <code>train.t_train_project.f_name</code>. 项目名称
     */
    public final TableField<TrainProjectRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "项目名称");

    /**
     * The column <code>train.t_train_project.f_code</code>. MIS编码
     */
    public final TableField<TrainProjectRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "MIS编码");

    /**
     * The column <code>train.t_train_project.f_organization_id</code>. 主办单位
     */
    public final TableField<TrainProjectRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "主办单位");

    /**
     * The column <code>train.t_train_project.f_train_day</code>. 培训天数
     */
    public final TableField<TrainProjectRecord, String> TRAIN_DAY = createField("f_train_day", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训天数");

    /**
     * The column <code>train.t_train_project.f_train_method</code>. 培训方式
     */
    public final TableField<TrainProjectRecord, String> TRAIN_METHOD = createField("f_train_method", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训方式");

    /**
     * The column <code>train.t_train_project.f_train_address</code>. 培训地点
     */
    public final TableField<TrainProjectRecord, String> TRAIN_ADDRESS = createField("f_train_address", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训地点");

    /**
     * The column <code>train.t_train_project.f_train_year</code>. 培训年份
     */
    public final TableField<TrainProjectRecord, Integer> TRAIN_YEAR = createField("f_train_year", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "培训年份");

    /**
     * The column <code>train.t_train_project.f_train_month</code>. 培训月份
     */
    public final TableField<TrainProjectRecord, String> TRAIN_MONTH = createField("f_train_month", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训月份");

    /**
     * The column <code>train.t_train_project.f_categories</code>. 培训大类
     */
    public final TableField<TrainProjectRecord, String> CATEGORIES = createField("f_categories", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训大类");

    /**
     * The column <code>train.t_train_project.f_subclass</code>. 培训小类
     */
    public final TableField<TrainProjectRecord, String> SUBCLASS = createField("f_subclass", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训小类");

    /**
     * The column <code>train.t_train_project.f_key_project</code>. 重点项目（0：非重点 1：重点）
     */
    public final TableField<TrainProjectRecord, Integer> KEY_PROJECT = createField("f_key_project", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "重点项目（0：非重点 1：重点）");

    /**
     * The column <code>train.t_train_project.f_project_nature</code>. 项目性质（0：垂直条线 1：非垂直条线）
     */
    public final TableField<TrainProjectRecord, Integer> PROJECT_NATURE = createField("f_project_nature", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "项目性质（0：垂直条线 1：非垂直条线）");

    /**
     * The column <code>train.t_train_project.f_plan_month</code>. 计划月份
     */
    public final TableField<TrainProjectRecord, String> PLAN_MONTH = createField("f_plan_month", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "计划月份");

    /**
     * The column <code>train.t_train_project.f_source</code>. 来源（0：同步培训计划 1：自建）
     */
    public final TableField<TrainProjectRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源（0：同步培训计划 1：自建）");

    /**
     * The column <code>train.t_train_project.f_create_time</code>. 创建时间
     */
    public final TableField<TrainProjectRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_train_project.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public final TableField<TrainProjectRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态：0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_train_project.f_modify_date</code>. 修改时间
     */
    public final TableField<TrainProjectRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>train.t_train_project.f_object</code>. 培训对象
     */
    public final TableField<TrainProjectRecord, String> OBJECT = createField("f_object", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训对象");

    /**
     * The column <code>train.t_train_project.f_plan_type</code>. 计划类型（0:培训班 1:专题）
     */
    public final TableField<TrainProjectRecord, Integer> PLAN_TYPE = createField("f_plan_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "计划类型（0:培训班 1:专题）");

    /**
     * The column <code>train.t_train_project.f_contact_member_id</code>. 需求单位联系人
     */
    public final TableField<TrainProjectRecord, String> CONTACT_MEMBER_ID = createField("f_contact_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "需求单位联系人");

    /**
     * Create a <code>train.t_train_project</code> table reference
     */
    public TrainProject() {
        this("t_train_project", null);
    }

    /**
     * Create an aliased <code>train.t_train_project</code> table reference
     */
    public TrainProject(String alias) {
        this(alias, TRAIN_PROJECT);
    }

    private TrainProject(String alias, Table<TrainProjectRecord> aliased) {
        this(alias, aliased, null);
    }

    private TrainProject(String alias, Table<TrainProjectRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训项目表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TrainProjectRecord> getPrimaryKey() {
        return Keys.KEY_T_TRAIN_PROJECT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TrainProjectRecord>> getKeys() {
        return Arrays.<UniqueKey<TrainProjectRecord>>asList(Keys.KEY_T_TRAIN_PROJECT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainProject as(String alias) {
        return new TrainProject(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TrainProject rename(String name) {
        return new TrainProject(name, null);
    }
}
