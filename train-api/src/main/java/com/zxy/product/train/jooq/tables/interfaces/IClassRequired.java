/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassRequired extends Serializable {

    /**
     * Setter for <code>train.t_class_required.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_required.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_required.f_theme</code>. 必修主题
     */
    public void setTheme(String value);

    /**
     * Getter for <code>train.t_class_required.f_theme</code>. 必修主题
     */
    public String getTheme();

    /**
     * Setter for <code>train.t_class_required.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_required.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_required.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_class_required.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_class_required.f_course_id</code>. 课程id
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_class_required.f_course_id</code>. 课程id
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_class_required.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_required.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_required.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_required.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_required.f_course_name</code>. 课程名称
     */
    public void setCourseName(String value);

    /**
     * Getter for <code>train.t_class_required.f_course_name</code>. 课程名称
     */
    public String getCourseName();

    /**
     * Setter for <code>train.t_class_required.f_publish_client</code>. 适用终端   0: 全部, 1: PC, 2: APP
     */
    public void setPublishClient(Integer value);

    /**
     * Getter for <code>train.t_class_required.f_publish_client</code>. 适用终端   0: 全部, 1: PC, 2: APP
     */
    public Integer getPublishClient();

    /**
     * Setter for <code>train.t_class_required.f_project_id</code>. 计划ID
     */
    public void setProjectId(String value);

    /**
     * Getter for <code>train.t_class_required.f_project_id</code>. 计划ID
     */
    public String getProjectId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassRequired
     */
    public void from(IClassRequired from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassRequired
     */
    public <E extends IClassRequired> E into(E into);
}
