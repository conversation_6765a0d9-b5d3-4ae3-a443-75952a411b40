/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TaskApprovalRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 作业审核表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TaskApproval extends TableImpl<TaskApprovalRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_task_approval</code>
     */
    public static final TaskApproval TASK_APPROVAL = new TaskApproval();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TaskApprovalRecord> getRecordType() {
        return TaskApprovalRecord.class;
    }

    /**
     * The column <code>train.t_task_approval.f_id</code>. 主键
     */
    public final TableField<TaskApprovalRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_task_approval.t_task_member_id</code>. 用户提交作业ID
     */
    public final TableField<TaskApprovalRecord, String> TASK_MEMBER_ID = createField("t_task_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户提交作业ID");

    /**
     * The column <code>train.t_task_approval.f_approval_member_id</code>. 审批人ID
     */
    public final TableField<TaskApprovalRecord, String> APPROVAL_MEMBER_ID = createField("f_approval_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "审批人ID");

    /**
     * The column <code>train.t_task_approval.f_score</code>. 评分
     */
    public final TableField<TaskApprovalRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER, this, "评分");

    /**
     * The column <code>train.t_task_approval.f_comment</code>. 评语
     */
    public final TableField<TaskApprovalRecord, String> COMMENT = createField("f_comment", org.jooq.impl.SQLDataType.VARCHAR.length(3500), this, "评语");

    /**
     * The column <code>train.t_task_approval.f_state</code>. 状态 0通过 1打回重新提交
     */
    public final TableField<TaskApprovalRecord, Integer> STATE = createField("f_state", org.jooq.impl.SQLDataType.INTEGER, this, "状态 0通过 1打回重新提交");

    /**
     * The column <code>train.t_task_approval.f_create_time</code>. 审批时间
     */
    public final TableField<TaskApprovalRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "审批时间");

    /**
     * Create a <code>train.t_task_approval</code> table reference
     */
    public TaskApproval() {
        this("t_task_approval", null);
    }

    /**
     * Create an aliased <code>train.t_task_approval</code> table reference
     */
    public TaskApproval(String alias) {
        this(alias, TASK_APPROVAL);
    }

    private TaskApproval(String alias, Table<TaskApprovalRecord> aliased) {
        this(alias, aliased, null);
    }

    private TaskApproval(String alias, Table<TaskApprovalRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "作业审核表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TaskApprovalRecord> getPrimaryKey() {
        return Keys.KEY_T_TASK_APPROVAL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TaskApprovalRecord>> getKeys() {
        return Arrays.<UniqueKey<TaskApprovalRecord>>asList(Keys.KEY_T_TASK_APPROVAL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskApproval as(String alias) {
        return new TaskApproval(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TaskApproval rename(String name) {
        return new TaskApproval(name, null);
    }
}
