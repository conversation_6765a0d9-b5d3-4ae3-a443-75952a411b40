/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.AlbumRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Album extends TableImpl<AlbumRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_album</code>
     */
    public static final Album ALBUM = new Album();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AlbumRecord> getRecordType() {
        return AlbumRecord.class;
    }

    /**
     * The column <code>train.t_album.f_id</code>. 表ID
     */
    public final TableField<AlbumRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表ID");

    /**
     * The column <code>train.t_album.f_name</code>. 照片名称
     */
    public final TableField<AlbumRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "照片名称");

    /**
     * The column <code>train.t_album.f_organization_id</code>. 组织id
     */
    public final TableField<AlbumRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织id");

    /**
     * The column <code>train.t_album.f_attachment_id</code>. attachment_id
     */
    public final TableField<AlbumRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "attachment_id");

    /**
     * The column <code>train.t_album.f_delete_flag</code>. 删除状态 0未删除（默认） 1已删除
     */
    public final TableField<AlbumRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除状态 0未删除（默认） 1已删除");

    /**
     * The column <code>train.t_album.f_author</code>. 作者
     */
    public final TableField<AlbumRecord, String> AUTHOR = createField("f_author", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "作者");

    /**
     * The column <code>train.t_album.f_class_id</code>. 班级ID
     */
    public final TableField<AlbumRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_album.f_create_time</code>. 创建时间
     */
    public final TableField<AlbumRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_album.f_thumbnail_id</code>. 缩略图id
     */
    public final TableField<AlbumRecord, String> THUMBNAIL_ID = createField("f_thumbnail_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "缩略图id");

    /**
     * The column <code>train.t_album.f_path</code>. 图片地址
     */
    public final TableField<AlbumRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "图片地址");

    /**
     * The column <code>train.t_album.f_thumbnail_path</code>. 缩略图地址
     */
    public final TableField<AlbumRecord, String> THUMBNAIL_PATH = createField("f_thumbnail_path", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "缩略图地址");

    /**
     * Create a <code>train.t_album</code> table reference
     */
    public Album() {
        this("t_album", null);
    }

    /**
     * Create an aliased <code>train.t_album</code> table reference
     */
    public Album(String alias) {
        this(alias, ALBUM);
    }

    private Album(String alias, Table<AlbumRecord> aliased) {
        this(alias, aliased, null);
    }

    private Album(String alias, Table<AlbumRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AlbumRecord> getPrimaryKey() {
        return Keys.KEY_T_ALBUM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AlbumRecord>> getKeys() {
        return Arrays.<UniqueKey<AlbumRecord>>asList(Keys.KEY_T_ALBUM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Album as(String alias) {
        return new Album(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Album rename(String name) {
        return new Album(name, null);
    }
}
