/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.QuestionnaireQuestionTypeRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训班满意度问卷类型问题表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionnaireQuestionType extends TableImpl<QuestionnaireQuestionTypeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_questionnaire_question_type</code>
     */
    public static final QuestionnaireQuestionType QUESTIONNAIRE_QUESTION_TYPE = new QuestionnaireQuestionType();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionnaireQuestionTypeRecord> getRecordType() {
        return QuestionnaireQuestionTypeRecord.class;
    }

    /**
     * The column <code>train.t_questionnaire_question_type.f_id</code>. 系统ID
     */
    public final TableField<QuestionnaireQuestionTypeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_questionnaire_question_type.f_questionnaire_code</code>. 问卷code码区分问卷
     */
    public final TableField<QuestionnaireQuestionTypeRecord, String> QUESTIONNAIRE_CODE = createField("f_questionnaire_code", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "问卷code码区分问卷");

    /**
     * The column <code>train.t_questionnaire_question_type.f_questionnaire_name</code>. 问卷名称
     */
    public final TableField<QuestionnaireQuestionTypeRecord, String> QUESTIONNAIRE_NAME = createField("f_questionnaire_name", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "问卷名称");

    /**
     * The column <code>train.t_questionnaire_question_type.f_group_name</code>. 分组名称
     */
    public final TableField<QuestionnaireQuestionTypeRecord, String> GROUP_NAME = createField("f_group_name", org.jooq.impl.SQLDataType.VARCHAR.length(64), this, "分组名称");

    /**
     * The column <code>train.t_questionnaire_question_type.f_question_name</code>. 问题名称
     */
    public final TableField<QuestionnaireQuestionTypeRecord, String> QUESTION_NAME = createField("f_question_name", org.jooq.impl.SQLDataType.VARCHAR.length(256), this, "问题名称");

    /**
     * The column <code>train.t_questionnaire_question_type.f_level</code>. 分组等级【1：总体评估；2：课程师资评价；3：主观题】
     */
    public final TableField<QuestionnaireQuestionTypeRecord, Short> LEVEL = createField("f_level", org.jooq.impl.SQLDataType.SMALLINT, this, "分组等级【1：总体评估；2：课程师资评价；3：主观题】");

    /**
     * The column <code>train.t_questionnaire_question_type.f_type</code>. leve为2时有效；类型【
1 针对性：目标明确，满足需求；
2 实用性：紧贴实际，指导实践；
3 启发性：拓展视野，启发思考；
4 逻辑性：科学严谨，结构合理；
5 知识水平：底蕴深厚，见解独到；
6 实战经验：联系实际，解决问题；
7 授课技巧：形式多样，互动得当；
8 控场能力：掌控力强，张弛有度；
9 授课态度：认真敬业，关注学员；
10 过程控制：进度合理，重点突出；
11 对本课程有何优化意见建议
】
     */
    public final TableField<QuestionnaireQuestionTypeRecord, Short> TYPE = createField("f_type", org.jooq.impl.SQLDataType.SMALLINT, this, "leve为2时有效；类型【\r\n1 针对性：目标明确，满足需求；\r\n2 实用性：紧贴实际，指导实践；\r\n3 启发性：拓展视野，启发思考；\r\n4 逻辑性：科学严谨，结构合理；\r\n5 知识水平：底蕴深厚，见解独到；\r\n6 实战经验：联系实际，解决问题；\r\n7 授课技巧：形式多样，互动得当；\r\n8 控场能力：掌控力强，张弛有度；\r\n9 授课态度：认真敬业，关注学员；\r\n10 过程控制：进度合理，重点突出；\r\n11 对本课程有何优化意见建议\r\n】");

    /**
     * The column <code>train.t_questionnaire_question_type.f_type_branch</code>. leve为2时有效；【1：课程内容；2：师资表现；3：课程建议】
     */
    public final TableField<QuestionnaireQuestionTypeRecord, Short> TYPE_BRANCH = createField("f_type_branch", org.jooq.impl.SQLDataType.SMALLINT, this, "leve为2时有效；【1：课程内容；2：师资表现；3：课程建议】");

    /**
     * The column <code>train.t_questionnaire_question_type.f_order</code>. 排序
     */
    public final TableField<QuestionnaireQuestionTypeRecord, Short> ORDER = createField("f_order", org.jooq.impl.SQLDataType.SMALLINT, this, "排序");

    /**
     * The column <code>train.t_questionnaire_question_type.f_create_time</code>. 创建时间
     */
    public final TableField<QuestionnaireQuestionTypeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_questionnaire_question_type</code> table reference
     */
    public QuestionnaireQuestionType() {
        this("t_questionnaire_question_type", null);
    }

    /**
     * Create an aliased <code>train.t_questionnaire_question_type</code> table reference
     */
    public QuestionnaireQuestionType(String alias) {
        this(alias, QUESTIONNAIRE_QUESTION_TYPE);
    }

    private QuestionnaireQuestionType(String alias, Table<QuestionnaireQuestionTypeRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionnaireQuestionType(String alias, Table<QuestionnaireQuestionTypeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训班满意度问卷类型问题表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionnaireQuestionTypeRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTIONNAIRE_QUESTION_TYPE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionnaireQuestionTypeRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionnaireQuestionTypeRecord>>asList(Keys.KEY_T_QUESTIONNAIRE_QUESTION_TYPE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionType as(String alias) {
        return new QuestionnaireQuestionType(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionnaireQuestionType rename(String name) {
        return new QuestionnaireQuestionType(name, null);
    }
}
