/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IOtherTeaching;

import javax.annotation.Generated;


/**
 * 其他教学教研记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OtherTeachingEntity extends BaseEntity implements IOtherTeaching {

    private static final long serialVersionUID = 1L;

    private String  lecturerId;
    private String  courseName;
    private Integer teachingType;
    private String  activityLevel;
    private Long    startTime;
    private Long    endTime;
    private Double  satisfiedDegree;
    private Integer status;
    private String  satisfiedEvaluate;
    private String  otherInstructions;
    private Long    approvalTime;
    private Integer approvalStatus;
    private String  organizationId;
    private String  createMember;
    private String  approvalMember;
    private String  remark;
    private String  lecturerName;
    private Integer source;
    private String  courseType;
    private Double  referenceTime;
    private String  objectOriented;
    private String  description;

    public OtherTeachingEntity() {}

    public OtherTeachingEntity(OtherTeachingEntity value) {
        this.lecturerId = value.lecturerId;
        this.courseName = value.courseName;
        this.teachingType = value.teachingType;
        this.activityLevel = value.activityLevel;
        this.startTime = value.startTime;
        this.endTime = value.endTime;
        this.satisfiedDegree = value.satisfiedDegree;
        this.status = value.status;
        this.satisfiedEvaluate = value.satisfiedEvaluate;
        this.otherInstructions = value.otherInstructions;
        this.approvalTime = value.approvalTime;
        this.approvalStatus = value.approvalStatus;
        this.organizationId = value.organizationId;
        this.createMember = value.createMember;
        this.approvalMember = value.approvalMember;
        this.remark = value.remark;
        this.lecturerName = value.lecturerName;
        this.source = value.source;
        this.courseType = value.courseType;
        this.referenceTime = value.referenceTime;
        this.objectOriented = value.objectOriented;
        this.description = value.description;
    }

    public OtherTeachingEntity(
        String  id,
        String  lecturerId,
        String  courseName,
        Integer teachingType,
        String  activityLevel,
        Long    startTime,
        Long    endTime,
        Double  satisfiedDegree,
        Integer status,
        String  satisfiedEvaluate,
        String  otherInstructions,
        Long    createTime,
        Long    approvalTime,
        Integer approvalStatus,
        String  organizationId,
        String  createMember,
        String  approvalMember,
        String  remark,
        String  lecturerName,
        Integer source,
        String  courseType,
        Double  referenceTime,
        String  objectOriented,
        String  description
    ) {
        super.setId(id);
        this.lecturerId = lecturerId;
        this.courseName = courseName;
        this.teachingType = teachingType;
        this.activityLevel = activityLevel;
        this.startTime = startTime;
        this.endTime = endTime;
        this.satisfiedDegree = satisfiedDegree;
        this.status = status;
        this.satisfiedEvaluate = satisfiedEvaluate;
        this.otherInstructions = otherInstructions;
        super.setCreateTime(createTime);
        this.approvalTime = approvalTime;
        this.approvalStatus = approvalStatus;
        this.organizationId = organizationId;
        this.createMember = createMember;
        this.approvalMember = approvalMember;
        this.remark = remark;
        this.lecturerName = lecturerName;
        this.source = source;
        this.courseType = courseType;
        this.referenceTime = referenceTime;
        this.objectOriented = objectOriented;
        this.description = description;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getLecturerId() {
        return this.lecturerId;
    }

    @Override
    public void setLecturerId(String lecturerId) {
        this.lecturerId = lecturerId;
    }

    @Override
    public String getCourseName() {
        return this.courseName;
    }

    @Override
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    @Override
    public Integer getTeachingType() {
        return this.teachingType;
    }

    @Override
    public void setTeachingType(Integer teachingType) {
        this.teachingType = teachingType;
    }

    @Override
    public String getActivityLevel() {
        return this.activityLevel;
    }

    @Override
    public void setActivityLevel(String activityLevel) {
        this.activityLevel = activityLevel;
    }

    @Override
    public Long getStartTime() {
        return this.startTime;
    }

    @Override
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    @Override
    public Long getEndTime() {
        return this.endTime;
    }

    @Override
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    @Override
    public Double getSatisfiedDegree() {
        return this.satisfiedDegree;
    }

    @Override
    public void setSatisfiedDegree(Double satisfiedDegree) {
        this.satisfiedDegree = satisfiedDegree;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getSatisfiedEvaluate() {
        return this.satisfiedEvaluate;
    }

    @Override
    public void setSatisfiedEvaluate(String satisfiedEvaluate) {
        this.satisfiedEvaluate = satisfiedEvaluate;
    }

    @Override
    public String getOtherInstructions() {
        return this.otherInstructions;
    }

    @Override
    public void setOtherInstructions(String otherInstructions) {
        this.otherInstructions = otherInstructions;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getApprovalTime() {
        return this.approvalTime;
    }

    @Override
    public void setApprovalTime(Long approvalTime) {
        this.approvalTime = approvalTime;
    }

    @Override
    public Integer getApprovalStatus() {
        return this.approvalStatus;
    }

    @Override
    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getCreateMember() {
        return this.createMember;
    }

    @Override
    public void setCreateMember(String createMember) {
        this.createMember = createMember;
    }

    @Override
    public String getApprovalMember() {
        return this.approvalMember;
    }

    @Override
    public void setApprovalMember(String approvalMember) {
        this.approvalMember = approvalMember;
    }

    @Override
    public String getRemark() {
        return this.remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String getLecturerName() {
        return this.lecturerName;
    }

    @Override
    public void setLecturerName(String lecturerName) {
        this.lecturerName = lecturerName;
    }

    @Override
    public Integer getSource() {
        return this.source;
    }

    @Override
    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String getCourseType() {
        return this.courseType;
    }

    @Override
    public void setCourseType(String courseType) {
        this.courseType = courseType;
    }

    @Override
    public Double getReferenceTime() {
        return this.referenceTime;
    }

    @Override
    public void setReferenceTime(Double referenceTime) {
        this.referenceTime = referenceTime;
    }

    @Override
    public String getObjectOriented() {
        return this.objectOriented;
    }

    @Override
    public void setObjectOriented(String objectOriented) {
        this.objectOriented = objectOriented;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("OtherTeachingEntity (");

        sb.append(getId());
        sb.append(", ").append(lecturerId);
        sb.append(", ").append(courseName);
        sb.append(", ").append(teachingType);
        sb.append(", ").append(activityLevel);
        sb.append(", ").append(startTime);
        sb.append(", ").append(endTime);
        sb.append(", ").append(satisfiedDegree);
        sb.append(", ").append(status);
        sb.append(", ").append(satisfiedEvaluate);
        sb.append(", ").append(otherInstructions);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(approvalTime);
        sb.append(", ").append(approvalStatus);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(createMember);
        sb.append(", ").append(approvalMember);
        sb.append(", ").append(remark);
        sb.append(", ").append(lecturerName);
        sb.append(", ").append(source);
        sb.append(", ").append(courseType);
        sb.append(", ").append(referenceTime);
        sb.append(", ").append(objectOriented);
        sb.append(", ").append(description);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOtherTeaching from) {
        setId(from.getId());
        setLecturerId(from.getLecturerId());
        setCourseName(from.getCourseName());
        setTeachingType(from.getTeachingType());
        setActivityLevel(from.getActivityLevel());
        setStartTime(from.getStartTime());
        setEndTime(from.getEndTime());
        setSatisfiedDegree(from.getSatisfiedDegree());
        setStatus(from.getStatus());
        setSatisfiedEvaluate(from.getSatisfiedEvaluate());
        setOtherInstructions(from.getOtherInstructions());
        setCreateTime(from.getCreateTime());
        setApprovalTime(from.getApprovalTime());
        setApprovalStatus(from.getApprovalStatus());
        setOrganizationId(from.getOrganizationId());
        setCreateMember(from.getCreateMember());
        setApprovalMember(from.getApprovalMember());
        setRemark(from.getRemark());
        setLecturerName(from.getLecturerName());
        setSource(from.getSource());
        setCourseType(from.getCourseType());
        setReferenceTime(from.getReferenceTime());
        setObjectOriented(from.getObjectOriented());
        setDescription(from.getDescription());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOtherTeaching> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends OtherTeachingEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.OtherTeachingRecord r = new com.zxy.product.train.jooq.tables.records.OtherTeachingRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ID, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.LECTURER_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.LECTURER_ID, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.LECTURER_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.COURSE_NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.COURSE_NAME, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.COURSE_NAME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.TEACHING_TYPE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.TEACHING_TYPE, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.TEACHING_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ACTIVITY_LEVEL) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ACTIVITY_LEVEL, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ACTIVITY_LEVEL));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.START_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.START_TIME, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.START_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.END_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.END_TIME, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.END_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SATISFIED_DEGREE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SATISFIED_DEGREE, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SATISFIED_DEGREE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.STATUS) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.STATUS, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.STATUS));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SATISFIED_EVALUATE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SATISFIED_EVALUATE, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SATISFIED_EVALUATE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.OTHER_INSTRUCTIONS) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.OTHER_INSTRUCTIONS, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.OTHER_INSTRUCTIONS));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_TIME, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_STATUS) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_STATUS, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ORGANIZATION_ID, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.CREATE_MEMBER) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.CREATE_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.CREATE_MEMBER));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_MEMBER) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.APPROVAL_MEMBER));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.REMARK) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.REMARK, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.REMARK));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.LECTURER_NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.LECTURER_NAME, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.LECTURER_NAME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SOURCE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SOURCE, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.SOURCE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.COURSE_TYPE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.COURSE_TYPE, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.COURSE_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.REFERENCE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.REFERENCE_TIME, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.REFERENCE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.OBJECT_ORIENTED) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.OBJECT_ORIENTED, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.OBJECT_ORIENTED));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.DESCRIPTION) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.DESCRIPTION, record.getValue(com.zxy.product.train.jooq.tables.OtherTeaching.OTHER_TEACHING.DESCRIPTION));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
