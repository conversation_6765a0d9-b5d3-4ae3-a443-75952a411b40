/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IAlbum;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AlbumEntity extends BaseEntity implements IAlbum {

    private static final long serialVersionUID = 1L;

    private String  name;
    private String  organizationId;
    private String  attachmentId;
    private Integer deleteFlag;
    private String  author;
    private String  classId;
    private String  thumbnailId;
    private String  path;
    private String  thumbnailPath;

    public AlbumEntity() {}

    public AlbumEntity(AlbumEntity value) {
        this.name = value.name;
        this.organizationId = value.organizationId;
        this.attachmentId = value.attachmentId;
        this.deleteFlag = value.deleteFlag;
        this.author = value.author;
        this.classId = value.classId;
        this.thumbnailId = value.thumbnailId;
        this.path = value.path;
        this.thumbnailPath = value.thumbnailPath;
    }

    public AlbumEntity(
        String  id,
        String  name,
        String  organizationId,
        String  attachmentId,
        Integer deleteFlag,
        String  author,
        String  classId,
        Long    createTime,
        String  thumbnailId,
        String  path,
        String  thumbnailPath
    ) {
        super.setId(id);
        this.name = name;
        this.organizationId = organizationId;
        this.attachmentId = attachmentId;
        this.deleteFlag = deleteFlag;
        this.author = author;
        this.classId = classId;
        super.setCreateTime(createTime);
        this.thumbnailId = thumbnailId;
        this.path = path;
        this.thumbnailPath = thumbnailPath;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getAttachmentId() {
        return this.attachmentId;
    }

    @Override
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String getAuthor() {
        return this.author;
    }

    @Override
    public void setAuthor(String author) {
        this.author = author;
    }

    @Override
    public String getClassId() {
        return this.classId;
    }

    @Override
    public void setClassId(String classId) {
        this.classId = classId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getThumbnailId() {
        return this.thumbnailId;
    }

    @Override
    public void setThumbnailId(String thumbnailId) {
        this.thumbnailId = thumbnailId;
    }

    @Override
    public String getPath() {
        return this.path;
    }

    @Override
    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public String getThumbnailPath() {
        return this.thumbnailPath;
    }

    @Override
    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AlbumEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(attachmentId);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(author);
        sb.append(", ").append(classId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(thumbnailId);
        sb.append(", ").append(path);
        sb.append(", ").append(thumbnailPath);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAlbum from) {
        setId(from.getId());
        setName(from.getName());
        setOrganizationId(from.getOrganizationId());
        setAttachmentId(from.getAttachmentId());
        setDeleteFlag(from.getDeleteFlag());
        setAuthor(from.getAuthor());
        setClassId(from.getClassId());
        setCreateTime(from.getCreateTime());
        setThumbnailId(from.getThumbnailId());
        setPath(from.getPath());
        setThumbnailPath(from.getThumbnailPath());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAlbum> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends AlbumEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.AlbumRecord r = new com.zxy.product.train.jooq.tables.records.AlbumRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.ID, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.NAME, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.NAME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.ORGANIZATION_ID, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.ATTACHMENT_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.ATTACHMENT_ID, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.ATTACHMENT_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.DELETE_FLAG) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.DELETE_FLAG, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.DELETE_FLAG));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.AUTHOR) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.AUTHOR, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.AUTHOR));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.CLASS_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.CLASS_ID, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.CLASS_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.THUMBNAIL_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.THUMBNAIL_ID, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.THUMBNAIL_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.PATH) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.PATH, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.PATH));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.Album.ALBUM.THUMBNAIL_PATH) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.Album.ALBUM.THUMBNAIL_PATH, record.getValue(com.zxy.product.train.jooq.tables.Album.ALBUM.THUMBNAIL_PATH));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
