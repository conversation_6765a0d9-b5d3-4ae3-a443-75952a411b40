/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamActivityRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习活动表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamActivity extends TableImpl<StudyTeamActivityRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_activity</code>
     */
    public static final StudyTeamActivity STUDY_TEAM_ACTIVITY = new StudyTeamActivity();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamActivityRecord> getRecordType() {
        return StudyTeamActivityRecord.class;
    }

    /**
     * The column <code>train.t_study_team_activity.f_id</code>. ID
     */
    public final TableField<StudyTeamActivityRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_study_team_activity.f_team_id</code>. 团队id
     */
    public final TableField<StudyTeamActivityRecord, String> TEAM_ID = createField("f_team_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队id");

    /**
     * The column <code>train.t_study_team_activity.f_name</code>. 活动名称
     */
    public final TableField<StudyTeamActivityRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).nullable(false).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "活动名称");

    /**
     * The column <code>train.t_study_team_activity.f_place</code>. 活动地点
     */
    public final TableField<StudyTeamActivityRecord, String> PLACE = createField("f_place", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "活动地点");

    /**
     * The column <code>train.t_study_team_activity.f_begin_time</code>. 活动开始时间
     */
    public final TableField<StudyTeamActivityRecord, Long> BEGIN_TIME = createField("f_begin_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "活动开始时间");

    /**
     * The column <code>train.t_study_team_activity.f_end_time</code>. 活动结束时间
     */
    public final TableField<StudyTeamActivityRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "活动结束时间");

    /**
     * The column <code>train.t_study_team_activity.f_leader_member_id</code>. 领学人id
     */
    public final TableField<StudyTeamActivityRecord, String> LEADER_MEMBER_ID = createField("f_leader_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "领学人id");

    /**
     * The column <code>train.t_study_team_activity.f_cover</code>. 封面
     */
    public final TableField<StudyTeamActivityRecord, String> COVER = createField("f_cover", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面");

    /**
     * The column <code>train.t_study_team_activity.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamActivityRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_study_team_activity.f_confirmation_status</code>. 活动结束一小时后自动同步时长状态1:已经同步，0:未同步
     */
    public final TableField<StudyTeamActivityRecord, Boolean> CONFIRMATION_STATUS = createField("f_confirmation_status", org.jooq.impl.SQLDataType.BIT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("b'0'", org.jooq.impl.SQLDataType.BIT)), this, "活动结束一小时后自动同步时长状态1:已经同步，0:未同步");

    /**
     * The column <code>train.t_study_team_activity.f_old</code>. 是否是历史数据的活动，初始化的时候用的切记不要更新
     */
    public final TableField<StudyTeamActivityRecord, Boolean> OLD = createField("f_old", org.jooq.impl.SQLDataType.BIT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("b'0'", org.jooq.impl.SQLDataType.BIT)), this, "是否是历史数据的活动，初始化的时候用的切记不要更新");

    /**
     * Create a <code>train.t_study_team_activity</code> table reference
     */
    public StudyTeamActivity() {
        this("t_study_team_activity", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_activity</code> table reference
     */
    public StudyTeamActivity(String alias) {
        this(alias, STUDY_TEAM_ACTIVITY);
    }

    private StudyTeamActivity(String alias, Table<StudyTeamActivityRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamActivity(String alias, Table<StudyTeamActivityRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习活动表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamActivityRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_ACTIVITY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamActivityRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamActivityRecord>>asList(Keys.KEY_T_STUDY_TEAM_ACTIVITY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivity as(String alias) {
        return new StudyTeamActivity(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamActivity rename(String name) {
        return new StudyTeamActivity(name, null);
    }
}
