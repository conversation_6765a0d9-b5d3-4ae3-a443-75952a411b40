package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ProjectApprovalEntity;

public class ProjectApproval extends ProjectApprovalEntity {

	/*
	 * Created by 田聪 on 2017/02/08
	 */
	private static final long serialVersionUID = 512523741975822778L;
	
	public static final int STATUS_RESERVE = 1;	// 待预定
	public static final int STATUS_APPROVAL = 2;// 待审核
	public static final int STATUS_AGREE = 3;	// 同意申请
	public static final int STATUS_RESOURCES_FULL = 4; // 资源已满
	public static final int STATUS_REFUSE = 5; // 拒绝
	
	public static final int DELETE_FLASE=0;	//删除状态：未删除
	public static final int DELETE_TRUE=1;	//删除状态，已删除
	
	private Member createMan;
	private Member approvalMan;
	public Member getCreateMan() {
		return createMan;
	}
	public void setCreateMan(Member createMan) {
		this.createMan = createMan;
	}
	public Member getApprovalMan() {
		return approvalMan;
	}
	public void setApprovalMan(Member approvalMan) {
		this.approvalMan = approvalMan;
	}

}
