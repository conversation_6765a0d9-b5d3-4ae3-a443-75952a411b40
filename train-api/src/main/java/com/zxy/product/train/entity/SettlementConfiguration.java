package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.SettlementConfigurationEntity;

public class SettlementConfiguration extends SettlementConfigurationEntity {

    /**
     *
     */
    private static final long serialVersionUID = 7446564191223015840L;
    
    public static final int ADD = 1; // 新增
    public static final int UPDATE = 0; // 修改
    public static final int DELETE = -1; // 删除

    private List<Settlement> settlementList;

    public List<Settlement> getSettlementList() {
        return settlementList;
    }

    public void setSettlementList(List<Settlement> settlementList) {
        this.settlementList = settlementList;
    }
}
