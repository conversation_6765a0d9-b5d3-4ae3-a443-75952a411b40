/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassGradesProjectMember;
import com.zxy.product.train.jooq.tables.interfaces.IClassGradesProjectMember;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassGradesProjectMemberRecord extends UpdatableRecordImpl<ClassGradesProjectMemberRecord> implements Record8<String, String, String, String, String, String, Long, String>, IClassGradesProjectMember {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_grades_project_member.f_id</code>. 表id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_id</code>. 表id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_grades_project_member.f_project_id</code>. t_class_grades_project表id
     */
    @Override
    public void setProjectId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_project_id</code>. t_class_grades_project表id
     */
    @Override
    public String getProjectId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_grades_project_member.f_desc</code>. 备注
     */
    @Override
    public void setDesc(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_desc</code>. 备注
     */
    @Override
    public String getDesc() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_class_grades_project_member.f_grades</code>. 成绩
     */
    @Override
    public void setGrades(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_grades</code>. 成绩
     */
    @Override
    public String getGrades() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_class_grades_project_member.f_member_id</code>.
     */
    @Override
    public void setMemberId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_member_id</code>.
     */
    @Override
    public String getMemberId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_class_grades_project_member.f_member_number</code>. 员工编号
     */
    @Override
    public void setMemberNumber(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_member_number</code>. 员工编号
     */
    @Override
    public String getMemberNumber() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_class_grades_project_member.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>train.t_class_grades_project_member.f_create_member</code>. 创建人
     */
    @Override
    public void setCreateMember(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_class_grades_project_member.f_create_member</code>. 创建人
     */
    @Override
    public String getCreateMember() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, String, String, Long, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, String, String, Long, String> valuesRow() {
        return (Row8) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.PROJECT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.DESC;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.GRADES;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.MEMBER_NUMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getProjectId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getDesc();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getGrades();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getMemberNumber();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value2(String value) {
        setProjectId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value3(String value) {
        setDesc(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value4(String value) {
        setGrades(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value5(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value6(String value) {
        setMemberNumber(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value7(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord value8(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassGradesProjectMemberRecord values(String value1, String value2, String value3, String value4, String value5, String value6, Long value7, String value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassGradesProjectMember from) {
        setId(from.getId());
        setProjectId(from.getProjectId());
        setDesc(from.getDesc());
        setGrades(from.getGrades());
        setMemberId(from.getMemberId());
        setMemberNumber(from.getMemberNumber());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassGradesProjectMember> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassGradesProjectMemberRecord
     */
    public ClassGradesProjectMemberRecord() {
        super(ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER);
    }

    /**
     * Create a detached, initialised ClassGradesProjectMemberRecord
     */
    public ClassGradesProjectMemberRecord(String id, String projectId, String desc, String grades, String memberId, String memberNumber, Long createTime, String createMember) {
        super(ClassGradesProjectMember.CLASS_GRADES_PROJECT_MEMBER);

        set(0, id);
        set(1, projectId);
        set(2, desc);
        set(3, grades);
        set(4, memberId);
        set(5, memberNumber);
        set(6, createTime);
        set(7, createMember);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassGradesProjectMemberEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassGradesProjectMemberEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassGradesProjectMemberEntity)source;
        pojo.into(this);
        return true;
    }
}
