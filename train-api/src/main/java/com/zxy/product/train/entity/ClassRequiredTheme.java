package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassRequiredThemeEntity;

/**
 * Created by chun on 2017/2/24.
 */
public class ClassRequiredTheme extends ClassRequiredThemeEntity {

    private static final long serialVersionUID = -4587208504782218941L;

    public static final int DELETE_FLASE=0;	//删除状态：未删除
    public static final int DELETE_TRUE=1;	//删除状态，已删除
    public static  final int TYPE_TWO = 2; //类型

    private String courseName; //课程名称
    private String className; //班级名称
    private Integer publishClient; //适用终端
    private String classRequiredId ; // 必修表id
    private String organizationDemand;//班级需求
    private String organization; // 归属单位
    private String classId; //班级id
    private String courseId;//课程id
    private String courseList; //课程id集合
    private String projectId; //计划id
    private long arriveDate;//开班时间(报道日)
    private long returnDate;//结束时间

    public long getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(long returnDate) {
        this.returnDate = returnDate;
    }

    public long getArriveDate() {
        return arriveDate;
    }

    public void setArriveDate(long arriveDate) {
        this.arriveDate = arriveDate;
    }


    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Integer getPublishClient() {
        return publishClient;
    }

    public void setPublishClient(Integer publishClient) {
        this.publishClient = publishClient;
    }

    public String getClassRequiredId() {
        return classRequiredId;
    }

    public void setClassRequiredId(String classRequiredId) {
        this.classRequiredId = classRequiredId;
    }

    public String getOrganizationDemand() {
        return organizationDemand;
    }

    public void setOrganizationDemand(String organizationDemand) {
        this.organizationDemand = organizationDemand;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getClassId() {
        return classId;
    }

    public void setClassId(String classId) {
        this.classId = classId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getCourseList() {
        return courseList;
    }

    public void setCourseList(String courseList) {
        this.courseList = courseList;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}
