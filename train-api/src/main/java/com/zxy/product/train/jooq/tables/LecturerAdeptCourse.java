/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LecturerAdeptCourseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 讲师擅长课程列表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerAdeptCourse extends TableImpl<LecturerAdeptCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_lecturer_adept_course</code>
     */
    public static final LecturerAdeptCourse LECTURER_ADEPT_COURSE = new LecturerAdeptCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LecturerAdeptCourseRecord> getRecordType() {
        return LecturerAdeptCourseRecord.class;
    }

    /**
     * The column <code>train.t_lecturer_adept_course.f_id</code>. 系统ID
     */
    public final TableField<LecturerAdeptCourseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_lecturer_adept_course.f_lecturer_id</code>. 讲师ID
     */
    public final TableField<LecturerAdeptCourseRecord, String> LECTURER_ID = createField("f_lecturer_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "讲师ID");

    /**
     * The column <code>train.t_lecturer_adept_course.f_course_id</code>. 所擅长的课程ID
     */
    public final TableField<LecturerAdeptCourseRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所擅长的课程ID");

    /**
     * The column <code>train.t_lecturer_adept_course.f_reference_time</code>. 参考时长
     */
    public final TableField<LecturerAdeptCourseRecord, Double> REFERENCE_TIME = createField("f_reference_time", org.jooq.impl.SQLDataType.FLOAT, this, "参考时长");

    /**
     * The column <code>train.t_lecturer_adept_course.f_reference_remuneration</code>. 参考课酬(元)
     */
    public final TableField<LecturerAdeptCourseRecord, Double> REFERENCE_REMUNERATION = createField("f_reference_remuneration", org.jooq.impl.SQLDataType.DOUBLE, this, "参考课酬(元)");

    /**
     * The column <code>train.t_lecturer_adept_course.f_course_url</code>. 授课视频URL
     */
    public final TableField<LecturerAdeptCourseRecord, String> COURSE_URL = createField("f_course_url", org.jooq.impl.SQLDataType.VARCHAR.length(1500), this, "授课视频URL");

    /**
     * The column <code>train.t_lecturer_adept_course.f_organization_id</code>. 组织ID
     */
    public final TableField<LecturerAdeptCourseRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织ID");

    /**
     * The column <code>train.t_lecturer_adept_course.f_create_time</code>. 创建时间
     */
    public final TableField<LecturerAdeptCourseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_lecturer_adept_course.f_create_member_id</code>. 创建人
     */
    public final TableField<LecturerAdeptCourseRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_lecturer_adept_course.f_attachment_id</code>. 附件ID
     */
    public final TableField<LecturerAdeptCourseRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(512), this, "附件ID");

    /**
     * The column <code>train.t_lecturer_adept_course.f_attachment_name</code>. 附件名称
     */
    public final TableField<LecturerAdeptCourseRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(512), this, "附件名称");

    /**
     * Create a <code>train.t_lecturer_adept_course</code> table reference
     */
    public LecturerAdeptCourse() {
        this("t_lecturer_adept_course", null);
    }

    /**
     * Create an aliased <code>train.t_lecturer_adept_course</code> table reference
     */
    public LecturerAdeptCourse(String alias) {
        this(alias, LECTURER_ADEPT_COURSE);
    }

    private LecturerAdeptCourse(String alias, Table<LecturerAdeptCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private LecturerAdeptCourse(String alias, Table<LecturerAdeptCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "讲师擅长课程列表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LecturerAdeptCourseRecord> getPrimaryKey() {
        return Keys.KEY_T_LECTURER_ADEPT_COURSE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LecturerAdeptCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<LecturerAdeptCourseRecord>>asList(Keys.KEY_T_LECTURER_ADEPT_COURSE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourse as(String alias) {
        return new LecturerAdeptCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LecturerAdeptCourse rename(String name) {
        return new LecturerAdeptCourse(name, null);
    }
}
