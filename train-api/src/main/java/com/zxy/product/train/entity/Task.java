package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.TaskEntity;

/**
 * <PERSON><PERSON><PERSON><PERSON>
 *
 */
public class Task extends TaskEntity{

	private static final long serialVersionUID = 2624286252679131790L;

	public static final int DELETE_FLASE=0;	//删除状态：未删除
	public static final int DELETE_TRUE=1;	//删除状态：已删除

	private int number;

	private List<TaskAttach> attachList;

	private List<TaskMember> taskMemberList;// XX当前作业的所有提交记录

	private List<TaskReviewer> taskReviewer;// 作业审批人集合

	private Integer recorde; //参与人数
	
	private Integer attachNumber;//提交的附件数量

	public Integer getAttachNumber() {
		return attachNumber;
	}

	public void setAttachNumber(Integer attachNumber) {
		this.attachNumber = attachNumber;
	}

	public List<TaskReviewer> getTaskReviewer() {
		return taskReviewer;
	}

	public void setTaskReviewer(List<TaskReviewer> taskReviewer) {
		this.taskReviewer = taskReviewer;
	}

	private TaskMember taskMember;			// 最后一次作业提交

	private String memberIds;				// 作业审批人Ids

	public String getMemberIds() {
		return memberIds;
	}

	public void setMemberIds(String memberIds) {
		this.memberIds = memberIds;
	}

	public void setNumber(int number) {
		this.number = number;
	}

	public int getNumber() {
		return number;
	}

	public List<TaskAttach> getAttachList() {
		return attachList;
	}

	public void setAttachList(List<TaskAttach> attachList) {
		this.attachList = attachList;
	}

	public TaskMember getTaskMember() {
		return taskMember;
	}

	public void setTaskMember(TaskMember taskMember) {
		this.taskMember = taskMember;
	}

	public List<TaskMember> getTaskMemberList() {
		return taskMemberList;
	}

	public void setTaskMemberList(List<TaskMember> taskMemberList) {
		this.taskMemberList = taskMemberList;
	}

    public Integer getRecorde() {
        return recorde;
    }

    public void setRecorde(Integer recorde) {
        this.recorde = recorde;
    }


}
