/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CorporateSegmentRelationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 公司段对应关系
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CorporateSegmentRelation extends TableImpl<CorporateSegmentRelationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_corporate_segment_relation</code>
     */
    public static final CorporateSegmentRelation CORPORATE_SEGMENT_RELATION = new CorporateSegmentRelation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CorporateSegmentRelationRecord> getRecordType() {
        return CorporateSegmentRelationRecord.class;
    }

    /**
     * The column <code>train.t_corporate_segment_relation.f_id</code>.
     */
    public final TableField<CorporateSegmentRelationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_corporate_segment_relation.f_organization_name</code>. 公司名称
     */
    public final TableField<CorporateSegmentRelationRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "公司名称");

    /**
     * The column <code>train.t_corporate_segment_relation.f_intercourse_section</code>. 往来段
     */
    public final TableField<CorporateSegmentRelationRecord, String> INTERCOURSE_SECTION = createField("f_intercourse_section", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "往来段");

    /**
     * The column <code>train.t_corporate_segment_relation.f_organization_id</code>. 机构ID
     */
    public final TableField<CorporateSegmentRelationRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "机构ID");

    /**
     * The column <code>train.t_corporate_segment_relation.f_order</code>.
     */
    public final TableField<CorporateSegmentRelationRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER, this, "");

    /**
     * Create a <code>train.t_corporate_segment_relation</code> table reference
     */
    public CorporateSegmentRelation() {
        this("t_corporate_segment_relation", null);
    }

    /**
     * Create an aliased <code>train.t_corporate_segment_relation</code> table reference
     */
    public CorporateSegmentRelation(String alias) {
        this(alias, CORPORATE_SEGMENT_RELATION);
    }

    private CorporateSegmentRelation(String alias, Table<CorporateSegmentRelationRecord> aliased) {
        this(alias, aliased, null);
    }

    private CorporateSegmentRelation(String alias, Table<CorporateSegmentRelationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "公司段对应关系");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CorporateSegmentRelationRecord> getPrimaryKey() {
        return Keys.KEY_T_CORPORATE_SEGMENT_RELATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CorporateSegmentRelationRecord>> getKeys() {
        return Arrays.<UniqueKey<CorporateSegmentRelationRecord>>asList(Keys.KEY_T_CORPORATE_SEGMENT_RELATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorporateSegmentRelation as(String alias) {
        return new CorporateSegmentRelation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CorporateSegmentRelation rename(String name) {
        return new CorporateSegmentRelation(name, null);
    }
}
