/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 培训类别表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITrainingType extends Serializable {

    /**
     * Setter for <code>train.t_training_type.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_training_type.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_training_type.f_type</code>. 培训类型,0=大类,1=小类
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_training_type.f_type</code>. 培训类型,0=大类,1=小类
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_training_type.f_name</code>. 培训类别名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_training_type.f_name</code>. 培训类别名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_training_type.f_code</code>. 培训编号
     */
    public void setCode(Integer value);

    /**
     * Getter for <code>train.t_training_type.f_code</code>. 培训编号
     */
    public Integer getCode();

    /**
     * Setter for <code>train.t_training_type.f_training_big_id</code>. 培训大类id
     */
    public void setTrainingBigId(String value);

    /**
     * Getter for <code>train.t_training_type.f_training_big_id</code>. 培训大类id
     */
    public String getTrainingBigId();

    /**
     * Setter for <code>train.t_training_type.f_quantity</code>. 关联培训数量
     */
    public void setQuantity(Integer value);

    /**
     * Getter for <code>train.t_training_type.f_quantity</code>. 关联培训数量
     */
    public Integer getQuantity();

    /**
     * Setter for <code>train.t_training_type.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_training_type.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_training_type.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_training_type.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>train.t_training_type.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_training_type.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_training_type.f_modify_member_id</code>. 修改人
     */
    public void setModifyMemberId(String value);

    /**
     * Getter for <code>train.t_training_type.f_modify_member_id</code>. 修改人
     */
    public String getModifyMemberId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITrainingType
     */
    public void from(ITrainingType from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITrainingType
     */
    public <E extends ITrainingType> E into(E into);
}
