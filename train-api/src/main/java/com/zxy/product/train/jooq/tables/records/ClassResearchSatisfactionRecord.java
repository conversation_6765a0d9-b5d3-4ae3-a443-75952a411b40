/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassResearchSatisfaction;
import com.zxy.product.train.jooq.tables.interfaces.IClassResearchSatisfaction;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;


/**
 * 培训班课程问卷满意度结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassResearchSatisfactionRecord extends UpdatableRecordImpl<ClassResearchSatisfactionRecord> implements Record7<String, String, Integer, Integer, <PERSON>, Long, Timestamp>, IClassResearchSatisfaction {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_id</code>. 主键id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_id</code>. 主键id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_class_id</code>. 班级id
     */
    @Override
    public void setClassId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_class_id</code>. 班级id
     */
    @Override
    public String getClassId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_overall_satisfaction</code>. 总体满意度
     */
    @Override
    public void setOverallSatisfaction(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_overall_satisfaction</code>. 总体满意度
     */
    @Override
    public Integer getOverallSatisfaction() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_course_average_satisfaction</code>. 课程总体满意度均值
     */
    @Override
    public void setCourseAverageSatisfaction(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_course_average_satisfaction</code>. 课程总体满意度均值
     */
    @Override
    public Integer getCourseAverageSatisfaction() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_update_time</code>. 修改时间
     */
    @Override
    public void setUpdateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_update_time</code>. 修改时间
     */
    @Override
    public Long getUpdateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_class_research_satisfaction.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_research_satisfaction.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, Integer, Long, Long, Timestamp> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, Integer, Long, Long, Timestamp> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.OVERALL_SATISFACTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.COURSE_AVERAGE_SATISFACTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.UPDATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field7() {
        return ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getOverallSatisfaction();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getCourseAverageSatisfaction();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getUpdateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value7() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord value2(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord value3(Integer value) {
        setOverallSatisfaction(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord value4(Integer value) {
        setCourseAverageSatisfaction(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord value6(Long value) {
        setUpdateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord value7(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfactionRecord values(String value1, String value2, Integer value3, Integer value4, Long value5, Long value6, Timestamp value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassResearchSatisfaction from) {
        setId(from.getId());
        setClassId(from.getClassId());
        setOverallSatisfaction(from.getOverallSatisfaction());
        setCourseAverageSatisfaction(from.getCourseAverageSatisfaction());
        setCreateTime(from.getCreateTime());
        setUpdateTime(from.getUpdateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassResearchSatisfaction> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassResearchSatisfactionRecord
     */
    public ClassResearchSatisfactionRecord() {
        super(ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION);
    }

    /**
     * Create a detached, initialised ClassResearchSatisfactionRecord
     */
    public ClassResearchSatisfactionRecord(String id, String classId, Integer overallSatisfaction, Integer courseAverageSatisfaction, Long createTime, Long updateTime, Timestamp modifyDate) {
        super(ClassResearchSatisfaction.CLASS_RESEARCH_SATISFACTION);

        set(0, id);
        set(1, classId);
        set(2, overallSatisfaction);
        set(3, courseAverageSatisfaction);
        set(4, createTime);
        set(5, updateTime);
        set(6, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassResearchSatisfactionEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassResearchSatisfactionEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassResearchSatisfactionEntity)source;
        pojo.into(this);
        return true;
    }
}
