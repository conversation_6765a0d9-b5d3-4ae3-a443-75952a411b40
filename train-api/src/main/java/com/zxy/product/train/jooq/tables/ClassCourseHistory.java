/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassCourseHistoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 历史班级课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassCourseHistory extends TableImpl<ClassCourseHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_course_history</code>
     */
    public static final ClassCourseHistory CLASS_COURSE_HISTORY = new ClassCourseHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassCourseHistoryRecord> getRecordType() {
        return ClassCourseHistoryRecord.class;
    }

    /**
     * The column <code>train.t_class_course_history.f_id</code>.
     */
    public final TableField<ClassCourseHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_course_history.f_type</code>. 课程类型:1F,2L,3O,4Q,5Z
     */
    public final TableField<ClassCourseHistoryRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "课程类型:1F,2L,3O,4Q,5Z");

    /**
     * The column <code>train.t_class_course_history.f_class_id</code>. 班级ID
     */
    public final TableField<ClassCourseHistoryRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_course_history.f_name</code>. 课程名称
     */
    public final TableField<ClassCourseHistoryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "课程名称");

    /**
     * The column <code>train.t_class_course_history.f_course_date</code>. 上课日期
     */
    public final TableField<ClassCourseHistoryRecord, Long> COURSE_DATE = createField("f_course_date", org.jooq.impl.SQLDataType.BIGINT, this, "上课日期");

    /**
     * The column <code>train.t_class_course_history.f_start_time</code>. 开始时间
     */
    public final TableField<ClassCourseHistoryRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * The column <code>train.t_class_course_history.f_end_time</code>. 结束时间
     */
    public final TableField<ClassCourseHistoryRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "结束时间");

    /**
     * The column <code>train.t_class_course_history.f_classroom_id</code>. 教室ID
     */
    public final TableField<ClassCourseHistoryRecord, String> CLASSROOM_ID = createField("f_classroom_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "教室ID");

    /**
     * The column <code>train.t_class_course_history.f_teacher_name</code>. 讲师姓名
     */
    public final TableField<ClassCourseHistoryRecord, String> TEACHER_NAME = createField("f_teacher_name", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "讲师姓名");

    /**
     * The column <code>train.t_class_course_history.f_teacher_organization</code>. 讲师单位
     */
    public final TableField<ClassCourseHistoryRecord, String> TEACHER_ORGANIZATION = createField("f_teacher_organization", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "讲师单位");

    /**
     * The column <code>train.t_class_course_history.f_teacher_title</code>. 讲师职称
     */
    public final TableField<ClassCourseHistoryRecord, String> TEACHER_TITLE = createField("f_teacher_title", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "讲师职称");

    /**
     * The column <code>train.t_class_course_history.f_teacher_phone</code>. 联系电话
     */
    public final TableField<ClassCourseHistoryRecord, String> TEACHER_PHONE = createField("f_teacher_phone", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "联系电话");

    /**
     * The column <code>train.t_class_course_history.f_teacher_type</code>. 讲师类型:0待议
     */
    public final TableField<ClassCourseHistoryRecord, Integer> TEACHER_TYPE = createField("f_teacher_type", org.jooq.impl.SQLDataType.INTEGER, this, "讲师类型:0待议");

    /**
     * Create a <code>train.t_class_course_history</code> table reference
     */
    public ClassCourseHistory() {
        this("t_class_course_history", null);
    }

    /**
     * Create an aliased <code>train.t_class_course_history</code> table reference
     */
    public ClassCourseHistory(String alias) {
        this(alias, CLASS_COURSE_HISTORY);
    }

    private ClassCourseHistory(String alias, Table<ClassCourseHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassCourseHistory(String alias, Table<ClassCourseHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "历史班级课程表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassCourseHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_COURSE_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassCourseHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassCourseHistoryRecord>>asList(Keys.KEY_T_CLASS_COURSE_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistory as(String alias) {
        return new ClassCourseHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassCourseHistory rename(String name) {
        return new ClassCourseHistory(name, null);
    }
}
