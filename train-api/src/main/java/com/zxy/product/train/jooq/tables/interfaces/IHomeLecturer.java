/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IHomeLecturer extends Serializable {

    /**
     * Setter for <code>train.t_home_lecturer.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_home_lecturer.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_home_lecturer.f_lecture_id</code>. 关联的讲师id 
     */
    public void setLectureId(String value);

    /**
     * Getter for <code>train.t_home_lecturer.f_lecture_id</code>. 关联的讲师id 
     */
    public String getLectureId();

    /**
     * Setter for <code>train.t_home_lecturer.f_module_config_id</code>. 关联的配置模块id
     */
    public void setModuleConfigId(String value);

    /**
     * Getter for <code>train.t_home_lecturer.f_module_config_id</code>. 关联的配置模块id
     */
    public String getModuleConfigId();

    /**
     * Setter for <code>train.t_home_lecturer.f_sort</code>. 排序好
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_home_lecturer.f_sort</code>. 排序好
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_home_lecturer.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_home_lecturer.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IHomeLecturer
     */
    public void from(IHomeLecturer from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IHomeLecturer
     */
    public <E extends IHomeLecturer> E into(E into);
}
