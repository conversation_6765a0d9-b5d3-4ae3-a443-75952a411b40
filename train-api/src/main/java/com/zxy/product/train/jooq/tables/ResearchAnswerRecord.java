/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ResearchAnswerRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchAnswerRecord extends TableImpl<ResearchAnswerRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_research_answer_record</code>
     */
    public static final ResearchAnswerRecord RESEARCH_ANSWER_RECORD = new ResearchAnswerRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchAnswerRecordRecord> getRecordType() {
        return ResearchAnswerRecordRecord.class;
    }

    /**
     * The column <code>train.t_research_answer_record.f_id</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_research_answer_record.f_create_time</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * The column <code>train.t_research_answer_record.f_research_record_id</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> RESEARCH_RECORD_ID = createField("f_research_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "");

    /**
     * The column <code>train.t_research_answer_record.f_question_id</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "");

    /**
     * The column <code>train.t_research_answer_record.f_answer</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB, this, "");

    /**
     * The column <code>train.t_research_answer_record.f_is_right</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, Integer> IS_RIGHT = createField("f_is_right", org.jooq.impl.SQLDataType.INTEGER, this, "");

    /**
     * The column <code>train.t_research_answer_record.f_score</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER, this, "");

    /**
     * The column <code>train.t_research_answer_record.f_idea</code>. 意见
     */
    public final TableField<ResearchAnswerRecordRecord, String> IDEA = createField("f_idea", org.jooq.impl.SQLDataType.CLOB, this, "意见");

    /**
     * The column <code>train.t_research_answer_record.f_member_id</code>. 用户ID
     */
    public final TableField<ResearchAnswerRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户ID");

    /**
     * The column <code>train.t_research_answer_record.f_class_id</code>. 培训班ID
     */
    public final TableField<ResearchAnswerRecordRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训班ID");

    /**
     * The column <code>train.t_research_answer_record.f_course_id</code>. 课程ID
     */
    public final TableField<ResearchAnswerRecordRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程ID");

    /**
     * The column <code>train.t_research_answer_record.f_course_name</code>. 课程名称
     */
    public final TableField<ResearchAnswerRecordRecord, String> COURSE_NAME = createField("f_course_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "课程名称");

    /**
     * The column <code>train.t_research_answer_record.f_teacher_id</code>. 讲师ID
     */
    public final TableField<ResearchAnswerRecordRecord, String> TEACHER_ID = createField("f_teacher_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "讲师ID");

    /**
     * The column <code>train.t_research_answer_record.f_teacher_name</code>. 讲师名称
     */
    public final TableField<ResearchAnswerRecordRecord, String> TEACHER_NAME = createField("f_teacher_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "讲师名称");

    /**
     * The column <code>train.t_research_answer_record.f_questionnaire_question_id</code>. 问题ID
     */
    public final TableField<ResearchAnswerRecordRecord, String> QUESTIONNAIRE_QUESTION_ID = createField("f_questionnaire_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "问题ID");

    /**
     * Create a <code>train.t_research_answer_record</code> table reference
     */
    public ResearchAnswerRecord() {
        this("t_research_answer_record", null);
    }

    /**
     * Create an aliased <code>train.t_research_answer_record</code> table reference
     */
    public ResearchAnswerRecord(String alias) {
        this(alias, RESEARCH_ANSWER_RECORD);
    }

    private ResearchAnswerRecord(String alias, Table<ResearchAnswerRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchAnswerRecord(String alias, Table<ResearchAnswerRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchAnswerRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchAnswerRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchAnswerRecordRecord>>asList(Keys.KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecord as(String alias) {
        return new ResearchAnswerRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchAnswerRecord rename(String name) {
        return new ResearchAnswerRecord(name, null);
    }
}
