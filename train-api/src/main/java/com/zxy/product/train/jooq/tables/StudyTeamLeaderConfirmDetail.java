/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;

import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamLeaderConfirmDetailRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 学习活动-课程领学人学习时间
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamLeaderConfirmDetail extends TableImpl<StudyTeamLeaderConfirmDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_leader_confirm_detail</code>
     */
    public static final StudyTeamLeaderConfirmDetail STUDY_TEAM_LEADER_CONFIRM_DETAIL = new StudyTeamLeaderConfirmDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamLeaderConfirmDetailRecord> getRecordType() {
        return StudyTeamLeaderConfirmDetailRecord.class;
    }

    /**
     * The column <code>train.t_study_team_leader_confirm_detail.f_id</code>. ID
     */
    public final TableField<StudyTeamLeaderConfirmDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_study_team_leader_confirm_detail.f_activity_task_id</code>. 任务id
     */
    public final TableField<StudyTeamLeaderConfirmDetailRecord, String> ACTIVITY_TASK_ID = createField("f_activity_task_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "任务id");

    /**
     * The column <code>train.t_study_team_leader_confirm_detail.f_study_time</code>. 学习时间
     */
    public final TableField<StudyTeamLeaderConfirmDetailRecord, Long> STUDY_TIME = createField("f_study_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "学习时间");

    /**
     * The column <code>train.t_study_team_leader_confirm_detail.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamLeaderConfirmDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team_leader_confirm_detail</code> table reference
     */
    public StudyTeamLeaderConfirmDetail() {
        this("t_study_team_leader_confirm_detail", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_leader_confirm_detail</code> table reference
     */
    public StudyTeamLeaderConfirmDetail(String alias) {
        this(alias, STUDY_TEAM_LEADER_CONFIRM_DETAIL);
    }

    private StudyTeamLeaderConfirmDetail(String alias, Table<StudyTeamLeaderConfirmDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamLeaderConfirmDetail(String alias, Table<StudyTeamLeaderConfirmDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习活动-课程领学人学习时间");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamLeaderConfirmDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_LEADER_CONFIRM_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamLeaderConfirmDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamLeaderConfirmDetailRecord>>asList(Keys.KEY_T_STUDY_TEAM_LEADER_CONFIRM_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamLeaderConfirmDetail as(String alias) {
        return new StudyTeamLeaderConfirmDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamLeaderConfirmDetail rename(String name) {
        return new StudyTeamLeaderConfirmDetail(name, null);
    }
}
