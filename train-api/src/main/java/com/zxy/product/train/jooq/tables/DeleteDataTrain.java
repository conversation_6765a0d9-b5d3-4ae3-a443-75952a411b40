/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.DeleteDataTrainRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 删除记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DeleteDataTrain extends TableImpl<DeleteDataTrainRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_delete_data_train</code>
     */
    public static final DeleteDataTrain DELETE_DATA_TRAIN = new DeleteDataTrain();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DeleteDataTrainRecord> getRecordType() {
        return DeleteDataTrainRecord.class;
    }

    /**
     * The column <code>train.t_delete_data_train.f_id</code>. 主键
     */
    public final TableField<DeleteDataTrainRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_delete_data_train.f_database_name</code>. 数据库名称-全程
     */
    public final TableField<DeleteDataTrainRecord, String> DATABASE_NAME = createField("f_database_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false).defaultValue(org.jooq.impl.DSL.inline("'human-resource'", org.jooq.impl.SQLDataType.VARCHAR)), this, "数据库名称-全程");

    /**
     * The column <code>train.t_delete_data_train.f_table_name</code>. 表名称
     */
    public final TableField<DeleteDataTrainRecord, String> TABLE_NAME = createField("f_table_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "表名称");

    /**
     * The column <code>train.t_delete_data_train.f_business_id</code>. 业务id
     */
    public final TableField<DeleteDataTrainRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务id");

    /**
     * The column <code>train.t_delete_data_train.f_create_time</code>. 创建时间
     */
    public final TableField<DeleteDataTrainRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_delete_data_train.f_modify_date</code>. 更新时间
     */
    public final TableField<DeleteDataTrainRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>train.t_delete_data_train.f_company_id</code>. 企业id
     */
    public final TableField<DeleteDataTrainRecord, String> COMPANY_ID = createField("f_company_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "企业id");

    /**
     * Create a <code>train.t_delete_data_train</code> table reference
     */
    public DeleteDataTrain() {
        this("t_delete_data_train", null);
    }

    /**
     * Create an aliased <code>train.t_delete_data_train</code> table reference
     */
    public DeleteDataTrain(String alias) {
        this(alias, DELETE_DATA_TRAIN);
    }

    private DeleteDataTrain(String alias, Table<DeleteDataTrainRecord> aliased) {
        this(alias, aliased, null);
    }

    private DeleteDataTrain(String alias, Table<DeleteDataTrainRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "删除记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<DeleteDataTrainRecord> getPrimaryKey() {
        return Keys.KEY_T_DELETE_DATA_TRAIN_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<DeleteDataTrainRecord>> getKeys() {
        return Arrays.<UniqueKey<DeleteDataTrainRecord>>asList(Keys.KEY_T_DELETE_DATA_TRAIN_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataTrain as(String alias) {
        return new DeleteDataTrain(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DeleteDataTrain rename(String name) {
        return new DeleteDataTrain(name, null);
    }
}
