/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassOfflineCourse extends Serializable {

    /**
     * Setter for <code>train.t_class_offline_course.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_offline_course.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_offline_course.f_type</code>. 课程类型（1面授  2观看录像 3直播 4其他）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_type</code>. 课程类型（1面授  2观看录像 3直播 4其他）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_class_offline_course.f_name</code>. 课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_name</code>. 课程名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_class_offline_course.f_course_date</code>. 上课日期
     */
    public void setCourseDate(Long value);

    /**
     * Getter for <code>train.t_class_offline_course.f_course_date</code>. 上课日期
     */
    public Long getCourseDate();

    /**
     * Setter for <code>train.t_class_offline_course.f_start_time</code>. 开始时间
     */
    public void setStartTime(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_start_time</code>. 开始时间
     */
    public String getStartTime();

    /**
     * Setter for <code>train.t_class_offline_course.f_end_time</code>. 结束时间
     */
    public void setEndTime(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_end_time</code>. 结束时间
     */
    public String getEndTime();

    /**
     * Setter for <code>train.t_class_offline_course.f_classroom_id</code>. 教室ID
     */
    public void setClassroomId(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_classroom_id</code>. 教室ID
     */
    public String getClassroomId();

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_id</code>. 讲师ID
     */
    public void setTeacherId(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_id</code>. 讲师ID
     */
    public String getTeacherId();

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_name</code>. 讲师姓名
     */
    public void setTeacherName(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_name</code>. 讲师姓名
     */
    public String getTeacherName();

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_organization</code>. 讲师单位
     */
    public void setTeacherOrganization(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_organization</code>. 讲师单位
     */
    public String getTeacherOrganization();

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_title</code>. 讲师职称
     */
    public void setTeacherTitle(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_title</code>. 讲师职称
     */
    public String getTeacherTitle();

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_phone</code>. 联系电话
     */
    public void setTeacherPhone(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_phone</code>. 联系电话
     */
    public String getTeacherPhone();

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_type</code>. 讲师类型(0内部  1外部)
     */
    public void setTeacherType(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_type</code>. 讲师类型(0内部  1外部)
     */
    public Integer getTeacherType();

    /**
     * Setter for <code>train.t_class_offline_course.f_is_relate_online</code>. 是否关联在线课程（0否 1是）
     */
    public void setIsRelateOnline(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_is_relate_online</code>. 是否关联在线课程（0否 1是）
     */
    public Integer getIsRelateOnline();

    /**
     * Setter for <code>train.t_class_offline_course.f_online_course_id</code>. 在线课程ID
     */
    public void setOnlineCourseId(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_online_course_id</code>. 在线课程ID
     */
    public String getOnlineCourseId();

    /**
     * Setter for <code>train.t_class_offline_course.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_offline_course.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_offline_course.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_offline_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_offline_course.f_online_course_status</code>. 关联在线课程的状态 (0未发布 1已发布)
     */
    public void setOnlineCourseStatus(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_online_course_status</code>. 关联在线课程的状态 (0未发布 1已发布)
     */
    public Integer getOnlineCourseStatus();

    /**
     * Setter for <code>train.t_class_offline_course.f_duration</code>. 课时
     */
    public void setDuration(Double value);

    /**
     * Getter for <code>train.t_class_offline_course.f_duration</code>. 课时
     */
    public Double getDuration();

    /**
     * Setter for <code>train.t_class_offline_course.f_course_satisfy</code>. 课程满意度（保留一位小数）
     */
    public void setCourseSatisfy(Double value);

    /**
     * Getter for <code>train.t_class_offline_course.f_course_satisfy</code>. 课程满意度（保留一位小数）
     */
    public Double getCourseSatisfy();

    /**
     * Setter for <code>train.t_class_offline_course.f_gensee_url</code>. 直播的播放路径
     */
    public void setGenseeUrl(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_gensee_url</code>. 直播的播放路径
     */
    public String getGenseeUrl();

    /**
     * Setter for <code>train.t_class_offline_course.f_recommend</code>. 推荐方
     */
    public void setRecommend(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_recommend</code>. 推荐方
     */
    public Integer getRecommend();

    /**
     * Setter for <code>train.t_class_offline_course.f_payment_method</code>. 支付方式
     */
    public void setPaymentMethod(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_payment_method</code>. 支付方式
     */
    public Integer getPaymentMethod();

    /**
     * Setter for <code>train.t_class_offline_course.f_remarks</code>. 备注
     */
    public void setRemarks(String value);

    /**
     * Getter for <code>train.t_class_offline_course.f_remarks</code>. 备注
     */
    public String getRemarks();

    /**
     * Setter for <code>train.t_class_offline_course.f_taxrate</code>. 税率
     */
    public void setTaxrate(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_taxrate</code>. 税率
     */
    public Integer getTaxrate();

    /**
     * Setter for <code>train.t_class_offline_course.f_lecturer_source</code>. 讲师类别
     */
    public void setLecturerSource(Integer value);

    /**
     * Getter for <code>train.t_class_offline_course.f_lecturer_source</code>. 讲师类别
     */
    public Integer getLecturerSource();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassOfflineCourse
     */
    public void from(IClassOfflineCourse from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassOfflineCourse
     */
    public <E extends IClassOfflineCourse> E into(E into);
}
