package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.SignEntity;

/**
 * <PERSON><PERSON><PERSON><PERSON>
 *
 */
public class Sign extends SignEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = -5443198966358667073L;

	//扫码签到
	public static final int SCAN_CODE = 0;
	//考勤机签到
	public static final int ATTENDANCE_MACHINE = 1;

	/**
	 * 半天签到
	 */
	public static final int TYPE_LONG_TIME = 1;
	/**
	 * 未删除
	 */
	public static final int IS_DEL_NO = 0;

	/**
	 * 全天签到
	 */
	public static final int TYPE_ALL_DAY = 2;

	public static final String NAME ="半天签到";

	//状态
	private int state;

	//班级总人数
	private int classNum;

	private Integer normal;

	private Integer late;

	private Integer notSign;

	private Integer leave;

	private Long signDate;//签到时间

	private Integer signState; //签到状态

	private Leave leaves;

	private String classroom;

	public String getClassroom() {
		return classroom;
	}

	public void setClassroom(String classroom) {
		this.classroom = classroom;
	}


	public Leave getLeaves() {
		return leaves;
	}

	public void setLeaves(Leave leaves) {
		this.leaves = leaves;
	}

	public int getClassNum() {
		return classNum;
	}

	public void setClassNum(int classNum) {
		this.classNum = classNum;
	}

	public Integer getNormal() {
		return normal;
	}

	public void setNormal(Integer normal) {
		this.normal = normal;
	}

	public Integer getLate() {
		return late;
	}

	public void setLate(Integer late) {
		this.late = late;
	}

	public Integer getNotSign() {
		return notSign;
	}

	public void setNotSign(Integer notSign) {
		this.notSign = notSign;
	}

	public Integer getLeave() {
		return leave;
	}

	public void setLeave(Integer leave) {
		this.leave = leave;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public int getclassNum() {
		return classNum;
	}

	public void setclassNum(int classNum) {
		this.classNum = classNum;
	}

	public Long getSignDate() {
		return signDate;
	}

	public void setSignDate(Long signDate) {
		this.signDate = signDate;
	}

	public Integer getSignState() {
		return signState;
	}

	public void setSignState(Integer signState) {
		this.signState = signState;
	}
}
