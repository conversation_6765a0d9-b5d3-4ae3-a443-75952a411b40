/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassDetail extends Serializable {

    /**
     * Setter for <code>train.t_class_detail.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_detail.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_detail.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_detail.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_detail.f_cover_id</code>. 封面
     */
    public void setCoverId(String value);

    /**
     * Getter for <code>train.t_class_detail.f_cover_id</code>. 封面
     */
    public String getCoverId();

    /**
     * Setter for <code>train.t_class_detail.f_banner_id</code>. banner图片
     */
    public void setBannerId(String value);

    /**
     * Getter for <code>train.t_class_detail.f_banner_id</code>. banner图片
     */
    public String getBannerId();

    /**
     * Setter for <code>train.t_class_detail.f_attendance_type</code>. 签到规则：1按半天签到 2按全天签到 3不签到
     */
    public void setAttendanceType(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_attendance_type</code>. 签到规则：1按半天签到 2按全天签到 3不签到
     */
    public Integer getAttendanceType();

    /**
     * Setter for <code>train.t_class_detail.f_have_province_leader</code>. 是否有省公司二级经理参加
     */
    public void setHaveProvinceLeader(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_have_province_leader</code>. 是否有省公司二级经理参加
     */
    public Integer getHaveProvinceLeader();

    /**
     * Setter for <code>train.t_class_detail.f_have_minister</code>. 是否有部长及以上领导参加
     */
    public void setHaveMinister(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_have_minister</code>. 是否有部长及以上领导参加
     */
    public Integer getHaveMinister();

    /**
     * Setter for <code>train.t_class_detail.f_need_group_photo</code>. 是否合影
     */
    public void setNeedGroupPhoto(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_need_group_photo</code>. 是否合影
     */
    public Integer getNeedGroupPhoto();

    /**
     * Setter for <code>train.t_class_detail.f_photo_time</code>. 合影时间
     */
    public void setPhotoTime(Long value);

    /**
     * Getter for <code>train.t_class_detail.f_photo_time</code>. 合影时间
     */
    public Long getPhotoTime();

    /**
     * Setter for <code>train.t_class_detail.f_need_video</code>. 课程录像
     */
    public void setNeedVideo(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_need_video</code>. 课程录像
     */
    public Integer getNeedVideo();

    /**
     * Setter for <code>train.t_class_detail.f_video_requirement</code>. 录像需求
     */
    public void setVideoRequirement(String value);

    /**
     * Getter for <code>train.t_class_detail.f_video_requirement</code>. 录像需求
     */
    public String getVideoRequirement();

    /**
     * Setter for <code>train.t_class_detail.f_need_make_course</code>. 课程制作
     */
    public void setNeedMakeCourse(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_need_make_course</code>. 课程制作
     */
    public Integer getNeedMakeCourse();

    /**
     * Setter for <code>train.t_class_detail.f_course_video_requirement</code>. 课程制作录像需求
     */
    public void setCourseVideoRequirement(String value);

    /**
     * Getter for <code>train.t_class_detail.f_course_video_requirement</code>. 课程制作录像需求
     */
    public String getCourseVideoRequirement();

    /**
     * Setter for <code>train.t_class_detail.f_need_net</code>. 是否需要网络
     */
    public void setNeedNet(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_need_net</code>. 是否需要网络
     */
    public Integer getNeedNet();

    /**
     * Setter for <code>train.t_class_detail.f_table_type</code>. 教室桌形
     */
    public void setTableType(String value);

    /**
     * Getter for <code>train.t_class_detail.f_table_type</code>. 教室桌形
     */
    public String getTableType();

    /**
     * Setter for <code>train.t_class_detail.f_other_requirement</code>. 其它需求
     */
    public void setOtherRequirement(String value);

    /**
     * Getter for <code>train.t_class_detail.f_other_requirement</code>. 其它需求
     */
    public String getOtherRequirement();

    /**
     * Setter for <code>train.t_class_detail.f_show_ranking</code>. 是否显示排行
     */
    public void setShowRanking(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_show_ranking</code>. 是否显示排行
     */
    public Integer getShowRanking();

    /**
     * Setter for <code>train.t_class_detail.f_ranking_rule</code>. 显示规则
     */
    public void setRankingRule(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_ranking_rule</code>. 显示规则
     */
    public Integer getRankingRule();

    /**
     * Setter for <code>train.t_class_detail.f_notice</code>. 公告
     */
    public void setNotice(String value);

    /**
     * Getter for <code>train.t_class_detail.f_notice</code>. 公告
     */
    public String getNotice();

    /**
     * Setter for <code>train.t_class_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_detail.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_detail.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_detail.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_detail.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_detail.f_path</code>. 班级图片路径
     */
    public void setPath(String value);

    /**
     * Getter for <code>train.t_class_detail.f_path</code>. 班级图片路径
     */
    public String getPath();

    /**
     * Setter for <code>train.t_class_detail.f_cover_path</code>. 班级封面路径
     */
    public void setCoverPath(String value);

    /**
     * Getter for <code>train.t_class_detail.f_cover_path</code>. 班级封面路径
     */
    public String getCoverPath();

    /**
     * Setter for <code>train.t_class_detail.f_notice_text</code>. 公告文本
     */
    public void setNoticeText(String value);

    /**
     * Getter for <code>train.t_class_detail.f_notice_text</code>. 公告文本
     */
    public String getNoticeText();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassDetail
     */
    public void from(IClassDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassDetail
     */
    public <E extends IClassDetail> E into(E into);
}
