/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 集采方案详解关联课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICollectionProgrammeCourse extends Serializable {

    /**
     * Setter for <code>train.t_collection_programme_course.f_id</code>. 系统ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_collection_programme_course.f_id</code>. 系统ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_collection_programme_course.f_collection_id</code>. 集采方案ID
     */
    public void setCollectionId(String value);

    /**
     * Getter for <code>train.t_collection_programme_course.f_collection_id</code>. 集采方案ID
     */
    public String getCollectionId();

    /**
     * Setter for <code>train.t_collection_programme_course.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_collection_programme_course.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_collection_programme_course.f_create_time</code>. 关联时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_collection_programme_course.f_create_time</code>. 关联时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_collection_programme_course.f_type</code>. 关联课程类型：0-在线课程，1-集采课程
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_collection_programme_course.f_type</code>. 关联课程类型：0-在线课程，1-集采课程
     */
    public Integer getType();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICollectionProgrammeCourse
     */
    public void from(ICollectionProgrammeCourse from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICollectionProgrammeCourse
     */
    public <E extends ICollectionProgrammeCourse> E into(E into);
}
