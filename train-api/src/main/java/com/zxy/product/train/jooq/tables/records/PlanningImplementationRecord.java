/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.PlanningImplementation;
import com.zxy.product.train.jooq.tables.interfaces.IPlanningImplementation;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record17;
import org.jooq.Row17;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 策划实施表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PlanningImplementationRecord extends UpdatableRecordImpl<PlanningImplementationRecord> implements Record17<String, String, String, String, Integer, Long, Long, String, String, String, Integer, Integer, String, Integer, <PERSON>, Integer, Timestamp>, IPlanningImplementation {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_planning_implementation.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_class_id</code>. 班级id
     */
    @Override
    public void setClassId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_class_id</code>. 班级id
     */
    @Override
    public String getClassId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_project_id</code>. 培训计划id
     */
    @Override
    public void setProjectId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_project_id</code>. 培训计划id
     */
    @Override
    public String getProjectId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_business_id</code>. 关联培训项目表id
     */
    @Override
    public void setBusinessId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_business_id</code>. 关联培训项目表id
     */
    @Override
    public String getBusinessId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_progress_details</code>. 进度详情（1未启动、2需求沟通、3方案设计、4资源建设、5实施中、6已完成、7取消、8待定）
     */
    @Override
    public void setProgressDetails(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_progress_details</code>. 进度详情（1未启动、2需求沟通、3方案设计、4资源建设、5实施中、6已完成、7取消、8待定）
     */
    @Override
    public Integer getProgressDetails() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_initiation_date</code>. 开始实施日期
     */
    @Override
    public void setInitiationDate(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_initiation_date</code>. 开始实施日期
     */
    @Override
    public Long getInitiationDate() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_finish_date</code>. 完成日期
     */
    @Override
    public void setFinishDate(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_finish_date</code>. 完成日期
     */
    @Override
    public Long getFinishDate() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_embodiment_id</code>. 实施方式Id
     */
    @Override
    public void setEmbodimentId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_embodiment_id</code>. 实施方式Id
     */
    @Override
    public String getEmbodimentId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_embodiment_address</code>. 实施地点
     */
    @Override
    public void setEmbodimentAddress(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_embodiment_address</code>. 实施地点
     */
    @Override
    public String getEmbodimentAddress() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_plan_planning_id</code>. 方案策划情况Id
     */
    @Override
    public void setPlanPlanningId(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_plan_planning_id</code>. 方案策划情况Id
     */
    @Override
    public String getPlanPlanningId() {
        return (String) get(9);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_teacher_num</code>. 实施师资数量
     */
    @Override
    public void setTeacherNum(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_teacher_num</code>. 实施师资数量
     */
    @Override
    public Integer getTeacherNum() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_course_num</code>. 电子课程采纳量
     */
    @Override
    public void setCourseNum(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_course_num</code>. 电子课程采纳量
     */
    @Override
    public Integer getCourseNum() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_content</code>. 备注
     */
    @Override
    public void setContent(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_content</code>. 备注
     */
    @Override
    public String getContent() {
        return (String) get(12);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_source</code>. 来源（0：同步班级管理 1：自建）
     */
    @Override
    public void setSource(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_source</code>. 来源（0：同步班级管理 1：自建）
     */
    @Override
    public Integer getSource() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>train.t_planning_implementation.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record17 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, String, String, String, Integer, Long, Long, String, String, String, Integer, Integer, String, Integer, Long, Integer, Timestamp> fieldsRow() {
        return (Row17) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, String, String, String, Integer, Long, Long, String, String, String, Integer, Integer, String, Integer, Long, Integer, Timestamp> valuesRow() {
        return (Row17) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.PROJECT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.PROGRESS_DETAILS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.INITIATION_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.FINISH_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.EMBODIMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.EMBODIMENT_ADDRESS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.PLAN_PLANNING_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.TEACHER_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.COURSE_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field13() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.CONTENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field14() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.SOURCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field15() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field16() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field17() {
        return PlanningImplementation.PLANNING_IMPLEMENTATION.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getProjectId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getProgressDetails();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getInitiationDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getFinishDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getEmbodimentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getEmbodimentAddress();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getPlanPlanningId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getTeacherNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getCourseNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value13() {
        return getContent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value14() {
        return getSource();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value15() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value16() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value17() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value2(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value3(String value) {
        setProjectId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value4(String value) {
        setBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value5(Integer value) {
        setProgressDetails(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value6(Long value) {
        setInitiationDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value7(Long value) {
        setFinishDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value8(String value) {
        setEmbodimentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value9(String value) {
        setEmbodimentAddress(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value10(String value) {
        setPlanPlanningId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value11(Integer value) {
        setTeacherNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value12(Integer value) {
        setCourseNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value13(String value) {
        setContent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value14(Integer value) {
        setSource(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value15(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value16(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord value17(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRecord values(String value1, String value2, String value3, String value4, Integer value5, Long value6, Long value7, String value8, String value9, String value10, Integer value11, Integer value12, String value13, Integer value14, Long value15, Integer value16, Timestamp value17) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPlanningImplementation from) {
        setId(from.getId());
        setClassId(from.getClassId());
        setProjectId(from.getProjectId());
        setBusinessId(from.getBusinessId());
        setProgressDetails(from.getProgressDetails());
        setInitiationDate(from.getInitiationDate());
        setFinishDate(from.getFinishDate());
        setEmbodimentId(from.getEmbodimentId());
        setEmbodimentAddress(from.getEmbodimentAddress());
        setPlanPlanningId(from.getPlanPlanningId());
        setTeacherNum(from.getTeacherNum());
        setCourseNum(from.getCourseNum());
        setContent(from.getContent());
        setSource(from.getSource());
        setCreateTime(from.getCreateTime());
        setDeleteFlag(from.getDeleteFlag());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPlanningImplementation> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PlanningImplementationRecord
     */
    public PlanningImplementationRecord() {
        super(PlanningImplementation.PLANNING_IMPLEMENTATION);
    }

    /**
     * Create a detached, initialised PlanningImplementationRecord
     */
    public PlanningImplementationRecord(String id, String classId, String projectId, String businessId, Integer progressDetails, Long initiationDate, Long finishDate, String embodimentId, String embodimentAddress, String planPlanningId, Integer teacherNum, Integer courseNum, String content, Integer source, Long createTime, Integer deleteFlag, Timestamp modifyDate) {
        super(PlanningImplementation.PLANNING_IMPLEMENTATION);

        set(0, id);
        set(1, classId);
        set(2, projectId);
        set(3, businessId);
        set(4, progressDetails);
        set(5, initiationDate);
        set(6, finishDate);
        set(7, embodimentId);
        set(8, embodimentAddress);
        set(9, planPlanningId);
        set(10, teacherNum);
        set(11, courseNum);
        set(12, content);
        set(13, source);
        set(14, createTime);
        set(15, deleteFlag);
        set(16, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.PlanningImplementationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.PlanningImplementationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.PlanningImplementationEntity)source;
        pojo.into(this);
        return true;
    }
}
