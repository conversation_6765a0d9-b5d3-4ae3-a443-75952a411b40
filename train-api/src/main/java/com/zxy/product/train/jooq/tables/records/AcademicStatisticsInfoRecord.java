/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.AcademicStatisticsInfo;
import com.zxy.product.train.jooq.tables.interfaces.IAcademicStatisticsInfo;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AcademicStatisticsInfoRecord extends UpdatableRecordImpl<AcademicStatisticsInfoRecord> implements Record5<String, Integer, Integer, Long, Long>, IAcademicStatisticsInfo {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_academic_statistics_info.f_id</code>. 表id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_academic_statistics_info.f_id</code>. 表id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_academic_statistics_info.f_type</code>. 业务类型（1实施培训班数量、2培训学员人次、3培训学员人数）
     */
    @Override
    public void setType(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_academic_statistics_info.f_type</code>. 业务类型（1实施培训班数量、2培训学员人次、3培训学员人数）
     */
    @Override
    public Integer getType() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>train.t_academic_statistics_info.f_implementation_year</code>. 年份
     */
    @Override
    public void setImplementationYear(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_academic_statistics_info.f_implementation_year</code>. 年份
     */
    @Override
    public Integer getImplementationYear() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_academic_statistics_info.f_total_count</code>. 总数
     */
    @Override
    public void setTotalCount(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_academic_statistics_info.f_total_count</code>. 总数
     */
    @Override
    public Long getTotalCount() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>train.t_academic_statistics_info.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_academic_statistics_info.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, Integer, Integer, Long, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, Integer, Integer, Long, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO.IMPLEMENTATION_YEAR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO.TOTAL_COUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getImplementationYear();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getTotalCount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AcademicStatisticsInfoRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AcademicStatisticsInfoRecord value2(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AcademicStatisticsInfoRecord value3(Integer value) {
        setImplementationYear(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AcademicStatisticsInfoRecord value4(Long value) {
        setTotalCount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AcademicStatisticsInfoRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AcademicStatisticsInfoRecord values(String value1, Integer value2, Integer value3, Long value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAcademicStatisticsInfo from) {
        setId(from.getId());
        setType(from.getType());
        setImplementationYear(from.getImplementationYear());
        setTotalCount(from.getTotalCount());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAcademicStatisticsInfo> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AcademicStatisticsInfoRecord
     */
    public AcademicStatisticsInfoRecord() {
        super(AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO);
    }

    /**
     * Create a detached, initialised AcademicStatisticsInfoRecord
     */
    public AcademicStatisticsInfoRecord(String id, Integer type, Integer implementationYear, Long totalCount, Long createTime) {
        super(AcademicStatisticsInfo.ACADEMIC_STATISTICS_INFO);

        set(0, id);
        set(1, type);
        set(2, implementationYear);
        set(3, totalCount);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.AcademicStatisticsInfoEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.AcademicStatisticsInfoEntity pojo = (com.zxy.product.train.jooq.tables.pojos.AcademicStatisticsInfoEntity)source;
        pojo.into(this);
        return true;
    }
}
