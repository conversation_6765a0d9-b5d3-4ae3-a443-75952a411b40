/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudentDetailTotalSort;
import com.zxy.product.train.jooq.tables.interfaces.IStudentDetailTotalSort;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record3;
import org.jooq.Row3;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学员明细规则排序表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudentDetailTotalSortRecord extends UpdatableRecordImpl<StudentDetailTotalSortRecord> implements Record3<String, String, Integer>, IStudentDetailTotalSort {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_student_detail_total_sort.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total_sort.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_student_detail_total_sort.f_short_name</code>. 短名称
     */
    @Override
    public void setShortName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total_sort.f_short_name</code>. 短名称
     */
    @Override
    public String getShortName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_student_detail_total_sort.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_student_detail_total_sort.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(2);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record3 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row3<String, String, Integer> fieldsRow() {
        return (Row3) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row3<String, String, Integer> valuesRow() {
        return (Row3) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT.SHORT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getShortName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalSortRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalSortRecord value2(String value) {
        setShortName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalSortRecord value3(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalSortRecord values(String value1, String value2, Integer value3) {
        value1(value1);
        value2(value2);
        value3(value3);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudentDetailTotalSort from) {
        setId(from.getId());
        setShortName(from.getShortName());
        setSort(from.getSort());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudentDetailTotalSort> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudentDetailTotalSortRecord
     */
    public StudentDetailTotalSortRecord() {
        super(StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT);
    }

    /**
     * Create a detached, initialised StudentDetailTotalSortRecord
     */
    public StudentDetailTotalSortRecord(String id, String shortName, Integer sort) {
        super(StudentDetailTotalSort.STUDENT_DETAIL_TOTAL_SORT);

        set(0, id);
        set(1, shortName);
        set(2, sort);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudentDetailTotalSortEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudentDetailTotalSortEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudentDetailTotalSortEntity)source;
        pojo.into(this);
        return true;
    }
}
