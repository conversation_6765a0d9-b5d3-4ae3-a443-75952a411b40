/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 班车表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IBus extends Serializable {

    /**
     * Setter for <code>train.t_bus.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_bus.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_bus.f_name</code>. 统计主题
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_bus.f_name</code>. 统计主题
     */
    public String getName();

    /**
     * Setter for <code>train.t_bus.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_bus.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_bus.f_end_time</code>. 结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_bus.f_end_time</code>. 结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_bus.f_delete_flag</code>. 删除状态 0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_bus.f_delete_flag</code>. 删除状态 0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_bus.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_bus.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_bus.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_bus.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_bus.f_create_mem</code>. 创建人ID
     */
    public void setCreateMem(String value);

    /**
     * Getter for <code>train.t_bus.f_create_mem</code>. 创建人ID
     */
    public String getCreateMem();

    /**
     * Setter for <code>train.t_bus.f_state</code>. 发布状态 0未开始 1已开始 2已结束
     */
    public void setState(Integer value);

    /**
     * Getter for <code>train.t_bus.f_state</code>. 发布状态 0未开始 1已开始 2已结束
     */
    public Integer getState();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IBus
     */
    public void from(IBus from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IBus
     */
    public <E extends IBus> E into(E into);
}
