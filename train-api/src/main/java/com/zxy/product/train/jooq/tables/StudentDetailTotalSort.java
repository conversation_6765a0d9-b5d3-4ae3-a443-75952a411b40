/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudentDetailTotalSortRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学员明细规则排序表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudentDetailTotalSort extends TableImpl<StudentDetailTotalSortRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_student_detail_total_sort</code>
     */
    public static final StudentDetailTotalSort STUDENT_DETAIL_TOTAL_SORT = new StudentDetailTotalSort();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudentDetailTotalSortRecord> getRecordType() {
        return StudentDetailTotalSortRecord.class;
    }

    /**
     * The column <code>train.t_student_detail_total_sort.f_id</code>. 主键
     */
    public final TableField<StudentDetailTotalSortRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_student_detail_total_sort.f_short_name</code>. 短名称
     */
    public final TableField<StudentDetailTotalSortRecord, String> SHORT_NAME = createField("f_short_name", org.jooq.impl.SQLDataType.VARCHAR.length(120), this, "短名称");

    /**
     * The column <code>train.t_student_detail_total_sort.f_sort</code>. 排序
     */
    public final TableField<StudentDetailTotalSortRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "排序");

    /**
     * Create a <code>train.t_student_detail_total_sort</code> table reference
     */
    public StudentDetailTotalSort() {
        this("t_student_detail_total_sort", null);
    }

    /**
     * Create an aliased <code>train.t_student_detail_total_sort</code> table reference
     */
    public StudentDetailTotalSort(String alias) {
        this(alias, STUDENT_DETAIL_TOTAL_SORT);
    }

    private StudentDetailTotalSort(String alias, Table<StudentDetailTotalSortRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudentDetailTotalSort(String alias, Table<StudentDetailTotalSortRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学员明细规则排序表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudentDetailTotalSortRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDENT_DETAIL_TOTAL_SORT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudentDetailTotalSortRecord>> getKeys() {
        return Arrays.<UniqueKey<StudentDetailTotalSortRecord>>asList(Keys.KEY_T_STUDENT_DETAIL_TOTAL_SORT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotalSort as(String alias) {
        return new StudentDetailTotalSort(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudentDetailTotalSort rename(String name) {
        return new StudentDetailTotalSort(name, null);
    }
}
