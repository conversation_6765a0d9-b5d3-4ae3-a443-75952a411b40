/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CollectionProgrammeCourseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 集采方案详解关联课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CollectionProgrammeCourse extends TableImpl<CollectionProgrammeCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_collection_programme_course</code>
     */
    public static final CollectionProgrammeCourse COLLECTION_PROGRAMME_COURSE = new CollectionProgrammeCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CollectionProgrammeCourseRecord> getRecordType() {
        return CollectionProgrammeCourseRecord.class;
    }

    /**
     * The column <code>train.t_collection_programme_course.f_id</code>. 系统ID
     */
    public final TableField<CollectionProgrammeCourseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_collection_programme_course.f_collection_id</code>. 集采方案ID
     */
    public final TableField<CollectionProgrammeCourseRecord, String> COLLECTION_ID = createField("f_collection_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "集采方案ID");

    /**
     * The column <code>train.t_collection_programme_course.f_course_id</code>. 课程ID
     */
    public final TableField<CollectionProgrammeCourseRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "课程ID");

    /**
     * The column <code>train.t_collection_programme_course.f_create_time</code>. 关联时间
     */
    public final TableField<CollectionProgrammeCourseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "关联时间");

    /**
     * The column <code>train.t_collection_programme_course.f_type</code>. 关联课程类型：0-在线课程，1-集采课程
     */
    public final TableField<CollectionProgrammeCourseRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "关联课程类型：0-在线课程，1-集采课程");

    /**
     * Create a <code>train.t_collection_programme_course</code> table reference
     */
    public CollectionProgrammeCourse() {
        this("t_collection_programme_course", null);
    }

    /**
     * Create an aliased <code>train.t_collection_programme_course</code> table reference
     */
    public CollectionProgrammeCourse(String alias) {
        this(alias, COLLECTION_PROGRAMME_COURSE);
    }

    private CollectionProgrammeCourse(String alias, Table<CollectionProgrammeCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private CollectionProgrammeCourse(String alias, Table<CollectionProgrammeCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "集采方案详解关联课程表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CollectionProgrammeCourseRecord> getPrimaryKey() {
        return Keys.KEY_T_COLLECTION_PROGRAMME_COURSE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CollectionProgrammeCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<CollectionProgrammeCourseRecord>>asList(Keys.KEY_T_COLLECTION_PROGRAMME_COURSE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeCourse as(String alias) {
        return new CollectionProgrammeCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CollectionProgrammeCourse rename(String name) {
        return new CollectionProgrammeCourse(name, null);
    }
}
