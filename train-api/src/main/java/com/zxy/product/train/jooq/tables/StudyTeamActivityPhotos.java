/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamActivityPhotosRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 团队学习班-活动相册资料表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamActivityPhotos extends TableImpl<StudyTeamActivityPhotosRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_activity_photos</code>
     */
    public static final StudyTeamActivityPhotos STUDY_TEAM_ACTIVITY_PHOTOS = new StudyTeamActivityPhotos();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamActivityPhotosRecord> getRecordType() {
        return StudyTeamActivityPhotosRecord.class;
    }

    /**
     * The column <code>train.t_study_team_activity_photos.f_id</code>. 主键
     */
    public final TableField<StudyTeamActivityPhotosRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_study_team_activity_photos.f_activity_id</code>. 活动id
     */
    public final TableField<StudyTeamActivityPhotosRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "活动id");

    /**
     * The column <code>train.t_study_team_activity_photos.f_img_path</code>. 相册路径
     */
    public final TableField<StudyTeamActivityPhotosRecord, String> IMG_PATH = createField("f_img_path", org.jooq.impl.SQLDataType.VARCHAR.length(500).nullable(false), this, "相册路径");

    /**
     * The column <code>train.t_study_team_activity_photos.f_upload_member_id</code>. 上传人
     */
    public final TableField<StudyTeamActivityPhotosRecord, String> UPLOAD_MEMBER_ID = createField("f_upload_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "上传人");

    /**
     * The column <code>train.t_study_team_activity_photos.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamActivityPhotosRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team_activity_photos</code> table reference
     */
    public StudyTeamActivityPhotos() {
        this("t_study_team_activity_photos", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_activity_photos</code> table reference
     */
    public StudyTeamActivityPhotos(String alias) {
        this(alias, STUDY_TEAM_ACTIVITY_PHOTOS);
    }

    private StudyTeamActivityPhotos(String alias, Table<StudyTeamActivityPhotosRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamActivityPhotos(String alias, Table<StudyTeamActivityPhotosRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "团队学习班-活动相册资料表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamActivityPhotosRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_ACTIVITY_PHOTOS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamActivityPhotosRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamActivityPhotosRecord>>asList(Keys.KEY_T_STUDY_TEAM_ACTIVITY_PHOTOS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityPhotos as(String alias) {
        return new StudyTeamActivityPhotos(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamActivityPhotos rename(String name) {
        return new StudyTeamActivityPhotos(name, null);
    }
}
