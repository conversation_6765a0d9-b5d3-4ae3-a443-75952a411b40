package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.CourseSalaryEntity;

/**
 * Created by chun on 2017/3/3.
 */
public class CourseSalary extends CourseSalaryEntity {

    private static final long serialVersionUID = -3017133209626933129L;
    public static final int TRANSLATE=1;	//面授课程
    public static final int INSIDE=0;	//内部讲师
    public static final int EXTERNAL=1;	//外部讲师
    public static final int DELETE_FLASE=0;	//删除状态：未删除
    public static final int DELETE_TRUE=1;	//删除状态，已删除

    private String lecturerName;
    private ClassOfflineCourse classOfflineCourse;
    private Lecturer lecturer;
    private Integer num;
    private String endTime;
    private String startTime;
    private Double time;
    private Double sumPaidPay;
    private Double sumPay;
    private Integer status;
    private Integer overproof;
    private boolean courseSalary;


    private Integer thecherType;//为列表提供，分辨内部讲师和外部讲师
    public String getLecturerName() {
        return lecturerName;
    }

    public void setLecturerName(String lecturerName) {
        this.lecturerName = lecturerName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Lecturer getLecturer() {
        return lecturer;
    }

    public void setLecturer(Lecturer lecturer) {
        this.lecturer = lecturer;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public Double getTime() {
        return time;
    }

    public void setTime(Double time) {
        this.time = time;
    }

    public ClassOfflineCourse getClassOfflineCourse() {
        return classOfflineCourse;
    }

    public void setClassOfflineCourse(ClassOfflineCourse classOfflineCourse) {
        this.classOfflineCourse = classOfflineCourse;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Double getSumPaidPay() {
        return sumPaidPay;
    }

    public void setSumPaidPay(Double sumPaidPay) {
        this.sumPaidPay = sumPaidPay;
    }

    public Double getSumPay() {
        return sumPay;
    }

    public void setSumPay(Double sumPay) {
        this.sumPay = sumPay;
    }

    public Integer getThecherType() {
        return thecherType;
    }

    public void setThecherType(Integer thecherType) {
        this.thecherType = thecherType;
    }

    public Integer getOverproof() {
        return overproof;
    }

    public void setOverproof(Integer overproof) {
        this.overproof = overproof;
    }

    public boolean isCourseSalary() {
        return courseSalary;
    }

    public void setCourseSalary(boolean courseSalary) {
        this.courseSalary = courseSalary;
    }
}
