/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 班车选项主题表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IBusOption extends Serializable {

    /**
     * Setter for <code>train.t_bus_option.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_bus_option.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_bus_option.f_name</code>. 选项主题
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_bus_option.f_name</code>. 选项主题
     */
    public String getName();

    /**
     * Setter for <code>train.t_bus_option.f_date</code>. 选项时间
     */
    public void setDate(Long value);

    /**
     * Getter for <code>train.t_bus_option.f_date</code>. 选项时间
     */
    public Long getDate();

    /**
     * Setter for <code>train.t_bus_option.f_address</code>. 地点
     */
    public void setAddress(String value);

    /**
     * Getter for <code>train.t_bus_option.f_address</code>. 地点
     */
    public String getAddress();

    /**
     * Setter for <code>train.t_bus_option.f_explain</code>. 其他说明
     */
    public void setExplain(String value);

    /**
     * Getter for <code>train.t_bus_option.f_explain</code>. 其他说明
     */
    public String getExplain();

    /**
     * Setter for <code>train.t_bus_option.f_create_mem</code>. 创建人ID
     */
    public void setCreateMem(String value);

    /**
     * Getter for <code>train.t_bus_option.f_create_mem</code>. 创建人ID
     */
    public String getCreateMem();

    /**
     * Setter for <code>train.t_bus_option.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_bus_option.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_bus_option.f_bus_id</code>. 班车ID
     */
    public void setBusId(String value);

    /**
     * Getter for <code>train.t_bus_option.f_bus_id</code>. 班车ID
     */
    public String getBusId();

    /**
     * Setter for <code>train.t_bus_option.f_flag</code>. option排序
     */
    public void setFlag(Integer value);

    /**
     * Getter for <code>train.t_bus_option.f_flag</code>. option排序
     */
    public Integer getFlag();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IBusOption
     */
    public void from(IBusOption from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IBusOption
     */
    public <E extends IBusOption> E into(E into);
}
