/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.PccwResultRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * PCCW HR接口调用结果
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PccwResult extends TableImpl<PccwResultRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_pccw_result</code>
     */
    public static final PccwResult PCCW_RESULT = new PccwResult();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PccwResultRecord> getRecordType() {
        return PccwResultRecord.class;
    }

    /**
     * The column <code>train.t_pccw_result.f_id</code>.
     */
    public final TableField<PccwResultRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(36).nullable(false), this, "");

    /**
     * The column <code>train.t_pccw_result.f_method</code>. 业务(接口)ID
     */
    public final TableField<PccwResultRecord, String> METHOD = createField("f_method", org.jooq.impl.SQLDataType.VARCHAR.length(32).nullable(false), this, "业务(接口)ID");

    /**
     * The column <code>train.t_pccw_result.f_instance_id</code>. 实例ID
     */
    public final TableField<PccwResultRecord, String> INSTANCE_ID = createField("f_instance_id", org.jooq.impl.SQLDataType.VARCHAR.length(64), this, "实例ID");

    /**
     * The column <code>train.t_pccw_result.f_link_id</code>. 上次处理ID
     */
    public final TableField<PccwResultRecord, String> LINK_ID = createField("f_link_id", org.jooq.impl.SQLDataType.VARCHAR.length(36), this, "上次处理ID");

    /**
     * The column <code>train.t_pccw_result.f_resp_code</code>. 响应编码
     */
    public final TableField<PccwResultRecord, String> RESP_CODE = createField("f_resp_code", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "响应编码");

    /**
     * The column <code>train.t_pccw_result.f_resp_desc</code>. 返回描述
     */
    public final TableField<PccwResultRecord, String> RESP_DESC = createField("f_resp_desc", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "返回描述");

    /**
     * The column <code>train.t_pccw_result.f_status_code</code>. 处理结果标识
     */
    public final TableField<PccwResultRecord, String> STATUS_CODE = createField("f_status_code", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "处理结果标识");

    /**
     * The column <code>train.t_pccw_result.f_err_reason</code>. 处理失败原因
     */
    public final TableField<PccwResultRecord, String> ERR_REASON = createField("f_err_reason", org.jooq.impl.SQLDataType.VARCHAR.length(1024), this, "处理失败原因");

    /**
     * The column <code>train.t_pccw_result.f_total_record</code>. 总条数
     */
    public final TableField<PccwResultRecord, Integer> TOTAL_RECORD = createField("f_total_record", org.jooq.impl.SQLDataType.INTEGER, this, "总条数");

    /**
     * The column <code>train.t_pccw_result.f_output_ext</code>. 查询结果扩展
     */
    public final TableField<PccwResultRecord, String> OUTPUT_EXT = createField("f_output_ext", org.jooq.impl.SQLDataType.VARCHAR.length(1024), this, "查询结果扩展");

    /**
     * The column <code>train.t_pccw_result.f_create_time</code>. 创建时间
     */
    public final TableField<PccwResultRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_pccw_result.f_status</code>. 1: 成功 0:失败 2:下一次处理
     */
    public final TableField<PccwResultRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "1: 成功 0:失败 2:下一次处理");

    /**
     * The column <code>train.t_pccw_result.f_update_time</code>. 更新时间
     */
    public final TableField<PccwResultRecord, Long> UPDATE_TIME = createField("f_update_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "更新时间");

    /**
     * Create a <code>train.t_pccw_result</code> table reference
     */
    public PccwResult() {
        this("t_pccw_result", null);
    }

    /**
     * Create an aliased <code>train.t_pccw_result</code> table reference
     */
    public PccwResult(String alias) {
        this(alias, PCCW_RESULT);
    }

    private PccwResult(String alias, Table<PccwResultRecord> aliased) {
        this(alias, aliased, null);
    }

    private PccwResult(String alias, Table<PccwResultRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "PCCW HR接口调用结果");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PccwResultRecord> getPrimaryKey() {
        return Keys.KEY_T_PCCW_RESULT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PccwResultRecord>> getKeys() {
        return Arrays.<UniqueKey<PccwResultRecord>>asList(Keys.KEY_T_PCCW_RESULT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PccwResult as(String alias) {
        return new PccwResult(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PccwResult rename(String name) {
        return new PccwResult(name, null);
    }
}
