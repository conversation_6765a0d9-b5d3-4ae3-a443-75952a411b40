/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamActivityTask;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamActivityTask;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学习活动-任务关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamActivityTaskRecord extends UpdatableRecordImpl<StudyTeamActivityTaskRecord> implements Record5<String, String, String, Integer, Long>, IStudyTeamActivityTask {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_activity_task.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_task.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_activity_task.f_activity_id</code>. 活动id
     */
    @Override
    public void setActivityId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_task.f_activity_id</code>. 活动id
     */
    @Override
    public String getActivityId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_activity_task.f_business_id</code>. 业务id
     */
    @Override
    public void setBusinessId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_task.f_business_id</code>. 业务id
     */
    @Override
    public String getBusinessId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_activity_task.f_business_type</code>. 业务类型：1.课程
     */
    @Override
    public void setBusinessType(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_task.f_business_type</code>. 业务类型：1.课程
     */
    @Override
    public Integer getBusinessType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_activity_task.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_task.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Integer, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Integer, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK.ACTIVITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK.BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK.BUSINESS_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getActivityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getBusinessType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityTaskRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityTaskRecord value2(String value) {
        setActivityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityTaskRecord value3(String value) {
        setBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityTaskRecord value4(Integer value) {
        setBusinessType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityTaskRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityTaskRecord values(String value1, String value2, String value3, Integer value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamActivityTask from) {
        setId(from.getId());
        setActivityId(from.getActivityId());
        setBusinessId(from.getBusinessId());
        setBusinessType(from.getBusinessType());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamActivityTask> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamActivityTaskRecord
     */
    public StudyTeamActivityTaskRecord() {
        super(StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK);
    }

    /**
     * Create a detached, initialised StudyTeamActivityTaskRecord
     */
    public StudyTeamActivityTaskRecord(String id, String activityId, String businessId, Integer businessType, Long createTime) {
        super(StudyTeamActivityTask.STUDY_TEAM_ACTIVITY_TASK);

        set(0, id);
        set(1, activityId);
        set(2, businessId);
        set(3, businessType);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityTaskEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityTaskEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityTaskEntity)source;
        pojo.into(this);
        return true;
    }
}
