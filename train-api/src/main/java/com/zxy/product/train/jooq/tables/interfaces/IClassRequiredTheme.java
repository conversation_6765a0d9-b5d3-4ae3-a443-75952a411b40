/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassRequiredTheme extends Serializable {

    /**
     * Setter for <code>train.t_class_required_theme.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_required_theme.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_required_theme.f_name</code>. 主题名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_class_required_theme.f_name</code>. 主题名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_class_required_theme.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_required_theme.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_required_theme.f_create_member</code>. 创建人id
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_required_theme.f_create_member</code>. 创建人id
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_required_theme.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_required_theme.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_required_theme.f_config</code>. 0 选修; 1 必修
     */
    public void setConfig(Integer value);

    /**
     * Getter for <code>train.t_class_required_theme.f_config</code>. 0 选修; 1 必修
     */
    public Integer getConfig();

    /**
     * Setter for <code>train.t_class_required_theme.f_day</code>. 必学天数
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>train.t_class_required_theme.f_day</code>. 必学天数
     */
    public Integer getDay();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassRequiredTheme
     */
    public void from(IClassRequiredTheme from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassRequiredTheme
     */
    public <E extends IClassRequiredTheme> E into(E into);
}
