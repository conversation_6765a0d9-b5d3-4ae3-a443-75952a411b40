/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ResearchRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchRecord extends TableImpl<ResearchRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_research_record</code>
     */
    public static final ResearchRecord RESEARCH_RECORD = new ResearchRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchRecordRecord> getRecordType() {
        return ResearchRecordRecord.class;
    }

    /**
     * The column <code>train.t_research_record.f_id</code>. 主键
     */
    public final TableField<ResearchRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_research_record.f_create_time</code>. 创建时间
     */
    public final TableField<ResearchRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_research_record.f_member_id</code>. 参与人
     */
    public final TableField<ResearchRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "参与人");

    /**
     * The column <code>train.t_research_record.f_status</code>. 状态 0：未参与 1：已参与
     */
    public final TableField<ResearchRecordRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "状态 0：未参与 1：已参与");

    /**
     * The column <code>train.t_research_record.f_research_questionary_id</code>. 调研ID
     */
    public final TableField<ResearchRecordRecord, String> RESEARCH_QUESTIONARY_ID = createField("f_research_questionary_id", org.jooq.impl.SQLDataType.VARCHAR.length(255), this, "调研ID");

    /**
     * The column <code>train.t_research_record.f_submit_time</code>. 提交时间
     */
    public final TableField<ResearchRecordRecord, Long> SUBMIT_TIME = createField("f_submit_time", org.jooq.impl.SQLDataType.BIGINT, this, "提交时间");

    /**
     * The column <code>train.t_research_record.f_score</code>. 分数
     */
    public final TableField<ResearchRecordRecord, Long> SCORE = createField("f_score", org.jooq.impl.SQLDataType.BIGINT, this, "分数");

    /**
     * The column <code>train.t_research_record.f_start_time</code>. 开始时间
     */
    public final TableField<ResearchRecordRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * Create a <code>train.t_research_record</code> table reference
     */
    public ResearchRecord() {
        this("t_research_record", null);
    }

    /**
     * Create an aliased <code>train.t_research_record</code> table reference
     */
    public ResearchRecord(String alias) {
        this(alias, RESEARCH_RECORD);
    }

    private ResearchRecord(String alias, Table<ResearchRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchRecord(String alias, Table<ResearchRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchRecordRecord>>asList(Keys.KEY_T_RESEARCH_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchRecord as(String alias) {
        return new ResearchRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchRecord rename(String name) {
        return new ResearchRecord(name, null);
    }
}
