/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassRequiredTheme;
import com.zxy.product.train.jooq.tables.interfaces.IClassRequiredTheme;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassRequiredThemeRecord extends UpdatableRecordImpl<ClassRequiredThemeRecord> implements Record7<String, String, Long, String, Integer, Integer, Integer>, IClassRequiredTheme {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_required_theme.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_required_theme.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_required_theme.f_name</code>. 主题名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_required_theme.f_name</code>. 主题名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_required_theme.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_required_theme.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>train.t_class_required_theme.f_create_member</code>. 创建人id
     */
    @Override
    public void setCreateMember(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_required_theme.f_create_member</code>. 创建人id
     */
    @Override
    public String getCreateMember() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_class_required_theme.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_required_theme.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_class_required_theme.f_config</code>. 0 选修; 1 必修
     */
    @Override
    public void setConfig(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_required_theme.f_config</code>. 0 选修; 1 必修
     */
    @Override
    public Integer getConfig() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>train.t_class_required_theme.f_day</code>. 必学天数
     */
    @Override
    public void setDay(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_required_theme.f_day</code>. 必学天数
     */
    @Override
    public Integer getDay() {
        return (Integer) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Long, String, Integer, Integer, Integer> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Long, String, Integer, Integer, Integer> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassRequiredTheme.CLASS_REQUIRED_THEME.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassRequiredTheme.CLASS_REQUIRED_THEME.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field3() {
        return ClassRequiredTheme.CLASS_REQUIRED_THEME.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ClassRequiredTheme.CLASS_REQUIRED_THEME.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return ClassRequiredTheme.CLASS_REQUIRED_THEME.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return ClassRequiredTheme.CLASS_REQUIRED_THEME.CONFIG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return ClassRequiredTheme.CLASS_REQUIRED_THEME.DAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value3() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getConfig();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getDay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord value3(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord value4(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord value5(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord value6(Integer value) {
        setConfig(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord value7(Integer value) {
        setDay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredThemeRecord values(String value1, String value2, Long value3, String value4, Integer value5, Integer value6, Integer value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassRequiredTheme from) {
        setId(from.getId());
        setName(from.getName());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setConfig(from.getConfig());
        setDay(from.getDay());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassRequiredTheme> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassRequiredThemeRecord
     */
    public ClassRequiredThemeRecord() {
        super(ClassRequiredTheme.CLASS_REQUIRED_THEME);
    }

    /**
     * Create a detached, initialised ClassRequiredThemeRecord
     */
    public ClassRequiredThemeRecord(String id, String name, Long createTime, String createMember, Integer deleteFlag, Integer config, Integer day) {
        super(ClassRequiredTheme.CLASS_REQUIRED_THEME);

        set(0, id);
        set(1, name);
        set(2, createTime);
        set(3, createMember);
        set(4, deleteFlag);
        set(5, config);
        set(6, day);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassRequiredThemeEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassRequiredThemeEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassRequiredThemeEntity)source;
        pojo.into(this);
        return true;
    }
}
