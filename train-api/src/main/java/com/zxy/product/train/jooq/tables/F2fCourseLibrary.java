/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.F2fCourseLibraryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 面授课程库表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class F2fCourseLibrary extends TableImpl<F2fCourseLibraryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_f2f_course_library</code>
     */
    public static final F2fCourseLibrary F2F_COURSE_LIBRARY = new F2fCourseLibrary();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<F2fCourseLibraryRecord> getRecordType() {
        return F2fCourseLibraryRecord.class;
    }

    /**
     * The column <code>train.t_f2f_course_library.f_id</code>. 主键
     */
    public final TableField<F2fCourseLibraryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_f2f_course_library.f_name</code>. 课程名称
     */
    public final TableField<F2fCourseLibraryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(120), this, "课程名称");

    /**
     * The column <code>train.t_f2f_course_library.f_sequence</code>. 课程分类/序列
     */
    public final TableField<F2fCourseLibraryRecord, String> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程分类/序列");

    /**
     * The column <code>train.t_f2f_course_library.f_obj</code>. 授课对象
     */
    public final TableField<F2fCourseLibraryRecord, String> OBJ = createField("f_obj", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "授课对象");

    /**
     * The column <code>train.t_f2f_course_library.f_keyword</code>. 关键词
     */
    public final TableField<F2fCourseLibraryRecord, String> KEYWORD = createField("f_keyword", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "关键词");

    /**
     * The column <code>train.t_f2f_course_library.f_summary</code>. 课程简介
     */
    public final TableField<F2fCourseLibraryRecord, String> SUMMARY = createField("f_summary", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "课程简介");

    /**
     * The column <code>train.t_f2f_course_library.f_remark</code>. 备注
     */
    public final TableField<F2fCourseLibraryRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "备注");

    /**
     * The column <code>train.t_f2f_course_library.f_target</code>. 课程目标
     */
    public final TableField<F2fCourseLibraryRecord, String> TARGET = createField("f_target", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "课程目标");

    /**
     * The column <code>train.t_f2f_course_library.f_outline</code>. 课程大纲
     */
    public final TableField<F2fCourseLibraryRecord, String> OUTLINE = createField("f_outline", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "课程大纲");

    /**
     * The column <code>train.t_f2f_course_library.f_course_attributes</code>. 课程属性
     */
    public final TableField<F2fCourseLibraryRecord, String> COURSE_ATTRIBUTES = createField("f_course_attributes", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程属性");

    /**
     * The column <code>train.t_f2f_course_library.f_institution_id</code>. 课程所属机构
     */
    public final TableField<F2fCourseLibraryRecord, String> INSTITUTION_ID = createField("f_institution_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程所属机构");

    /**
     * The column <code>train.t_f2f_course_library.f_organization_id</code>. 归属部门
     */
    public final TableField<F2fCourseLibraryRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "归属部门");

    /**
     * The column <code>train.t_f2f_course_library.f_reference_time</code>. 参考时长
     */
    public final TableField<F2fCourseLibraryRecord, Double> REFERENCE_TIME = createField("f_reference_time", org.jooq.impl.SQLDataType.DOUBLE, this, "参考时长");

    /**
     * The column <code>train.t_f2f_course_library.f_course_plan</code>. 课程方案
     */
    public final TableField<F2fCourseLibraryRecord, String> COURSE_PLAN = createField("f_course_plan", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "课程方案");

    /**
     * The column <code>train.t_f2f_course_library.f_is_share</code>. 是否共享0：是1：否
     */
    public final TableField<F2fCourseLibraryRecord, Integer> IS_SHARE = createField("f_is_share", org.jooq.impl.SQLDataType.INTEGER, this, "是否共享0：是1：否");

    /**
     * The column <code>train.t_f2f_course_library.f_is_use</code>. 0：启用1：禁用
     */
    public final TableField<F2fCourseLibraryRecord, Integer> IS_USE = createField("f_is_use", org.jooq.impl.SQLDataType.INTEGER, this, "0：启用1：禁用");

    /**
     * The column <code>train.t_f2f_course_library.f_create_time</code>. 创建时间
     */
    public final TableField<F2fCourseLibraryRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_f2f_course_library.f_update_time</code>. 修改时间
     */
    public final TableField<F2fCourseLibraryRecord, Long> UPDATE_TIME = createField("f_update_time", org.jooq.impl.SQLDataType.BIGINT, this, "修改时间");

    /**
     * The column <code>train.t_f2f_course_library.f_create_member</code>. 创建ID
     */
    public final TableField<F2fCourseLibraryRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建ID");

    /**
     * The column <code>train.t_f2f_course_library.f_update_member</code>. 修改人ID
     */
    public final TableField<F2fCourseLibraryRecord, String> UPDATE_MEMBER = createField("f_update_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "修改人ID");

    /**
     * The column <code>train.t_f2f_course_library.f_attachment_id</code>. 附件ID
     */
    public final TableField<F2fCourseLibraryRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(512), this, "附件ID");

    /**
     * The column <code>train.t_f2f_course_library.f_attachment_name</code>. 附件名称
     */
    public final TableField<F2fCourseLibraryRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(512), this, "附件名称");

    /**
     * Create a <code>train.t_f2f_course_library</code> table reference
     */
    public F2fCourseLibrary() {
        this("t_f2f_course_library", null);
    }

    /**
     * Create an aliased <code>train.t_f2f_course_library</code> table reference
     */
    public F2fCourseLibrary(String alias) {
        this(alias, F2F_COURSE_LIBRARY);
    }

    private F2fCourseLibrary(String alias, Table<F2fCourseLibraryRecord> aliased) {
        this(alias, aliased, null);
    }

    private F2fCourseLibrary(String alias, Table<F2fCourseLibraryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "面授课程库表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<F2fCourseLibraryRecord> getPrimaryKey() {
        return Keys.KEY_T_F2F_COURSE_LIBRARY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<F2fCourseLibraryRecord>> getKeys() {
        return Arrays.<UniqueKey<F2fCourseLibraryRecord>>asList(Keys.KEY_T_F2F_COURSE_LIBRARY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public F2fCourseLibrary as(String alias) {
        return new F2fCourseLibrary(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public F2fCourseLibrary rename(String name) {
        return new F2fCourseLibrary(name, null);
    }
}
