/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TrainingTypeRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训类别表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TrainingType extends TableImpl<TrainingTypeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_training_type</code>
     */
    public static final TrainingType TRAINING_TYPE = new TrainingType();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TrainingTypeRecord> getRecordType() {
        return TrainingTypeRecord.class;
    }

    /**
     * The column <code>train.t_training_type.f_id</code>.
     */
    public final TableField<TrainingTypeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_training_type.f_type</code>. 培训类型,0=大类,1=小类
     */
    public final TableField<TrainingTypeRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "培训类型,0=大类,1=小类");

    /**
     * The column <code>train.t_training_type.f_name</code>. 培训类别名称
     */
    public final TableField<TrainingTypeRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(30).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训类别名称");

    /**
     * The column <code>train.t_training_type.f_code</code>. 培训编号
     */
    public final TableField<TrainingTypeRecord, Integer> CODE = createField("f_code", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "培训编号");

    /**
     * The column <code>train.t_training_type.f_training_big_id</code>. 培训大类id
     */
    public final TableField<TrainingTypeRecord, String> TRAINING_BIG_ID = createField("f_training_big_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训大类id");

    /**
     * The column <code>train.t_training_type.f_quantity</code>. 关联培训数量
     */
    public final TableField<TrainingTypeRecord, Integer> QUANTITY = createField("f_quantity", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "关联培训数量");

    /**
     * The column <code>train.t_training_type.f_create_time</code>. 创建时间
     */
    public final TableField<TrainingTypeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_training_type.f_modify_date</code>. 修改时间
     */
    public final TableField<TrainingTypeRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>train.t_training_type.f_create_member_id</code>. 创建人
     */
    public final TableField<TrainingTypeRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>train.t_training_type.f_modify_member_id</code>. 修改人
     */
    public final TableField<TrainingTypeRecord, String> MODIFY_MEMBER_ID = createField("f_modify_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "修改人");

    /**
     * Create a <code>train.t_training_type</code> table reference
     */
    public TrainingType() {
        this("t_training_type", null);
    }

    /**
     * Create an aliased <code>train.t_training_type</code> table reference
     */
    public TrainingType(String alias) {
        this(alias, TRAINING_TYPE);
    }

    private TrainingType(String alias, Table<TrainingTypeRecord> aliased) {
        this(alias, aliased, null);
    }

    private TrainingType(String alias, Table<TrainingTypeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训类别表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TrainingTypeRecord> getPrimaryKey() {
        return Keys.KEY_T_TRAINING_TYPE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TrainingTypeRecord>> getKeys() {
        return Arrays.<UniqueKey<TrainingTypeRecord>>asList(Keys.KEY_T_TRAINING_TYPE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainingType as(String alias) {
        return new TrainingType(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TrainingType rename(String name) {
        return new TrainingType(name, null);
    }
}
