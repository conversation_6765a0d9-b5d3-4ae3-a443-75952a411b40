/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IQuestion extends Serializable {

    /**
     * Setter for <code>train.t_question.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_question.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_question.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_question.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_question.f_type</code>. 类型
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_question.f_type</code>. 类型
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_question.f_content</code>. 试题信息
     */
    public void setContent(String value);

    /**
     * Getter for <code>train.t_question.f_content</code>. 试题信息
     */
    public String getContent();

    /**
     * Setter for <code>train.t_question.f_is_subjective</code>. 是否主观题
     */
    public void setIsSubjective(Integer value);

    /**
     * Getter for <code>train.t_question.f_is_subjective</code>. 是否主观题
     */
    public Integer getIsSubjective();

    /**
     * Setter for <code>train.t_question.f_parent_id</code>. 父题
     */
    public void setParentId(String value);

    /**
     * Getter for <code>train.t_question.f_parent_id</code>. 父题
     */
    public String getParentId();

    /**
     * Setter for <code>train.t_question.f_question_depot_id</code>. 题库
     */
    public void setQuestionDepotId(String value);

    /**
     * Getter for <code>train.t_question.f_question_depot_id</code>. 题库
     */
    public String getQuestionDepotId();

    /**
     * Setter for <code>train.t_question.f_difficulty</code>. 难度
     */
    public void setDifficulty(Integer value);

    /**
     * Getter for <code>train.t_question.f_difficulty</code>. 难度
     */
    public Integer getDifficulty();

    /**
     * Setter for <code>train.t_question.f_organization_id</code>. 所属部门
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_question.f_organization_id</code>. 所属部门
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_question.f_score</code>. 分数
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>train.t_question.f_score</code>. 分数
     */
    public Integer getScore();

    /**
     * Setter for <code>train.t_question.f_error_rate</code>. 易错率
     */
    public void setErrorRate(Integer value);

    /**
     * Getter for <code>train.t_question.f_error_rate</code>. 易错率
     */
    public Integer getErrorRate();

    /**
     * Setter for <code>train.t_question.f_status</code>. 状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_question.f_status</code>. 状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_question.f_mark_amount</code>. 收藏
     */
    public void setMarkAmount(Integer value);

    /**
     * Getter for <code>train.t_question.f_mark_amount</code>. 收藏
     */
    public Integer getMarkAmount();

    /**
     * Setter for <code>train.t_question.f_recovery_count</code>. 纠错
     */
    public void setRecoveryCount(Integer value);

    /**
     * Getter for <code>train.t_question.f_recovery_count</code>. 纠错
     */
    public Integer getRecoveryCount();

    /**
     * Setter for <code>train.t_question.f_class_offline_course_id</code>. 线下课程id
     */
    public void setClassOfflineCourseId(String value);

    /**
     * Getter for <code>train.t_question.f_class_offline_course_id</code>. 线下课程id
     */
    public String getClassOfflineCourseId();

    /**
     * Setter for <code>train.t_question.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_question.f_order</code>. 排序
     */
    public Integer getOrder();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IQuestion
     */
    public void from(IQuestion from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IQuestion
     */
    public <E extends IQuestion> E into(E into);
}
