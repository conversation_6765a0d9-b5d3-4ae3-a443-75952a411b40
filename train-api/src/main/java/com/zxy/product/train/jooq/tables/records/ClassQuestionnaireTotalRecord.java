/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassQuestionnaireTotal;
import com.zxy.product.train.jooq.tables.interfaces.IClassQuestionnaireTotal;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 新版满意度问卷数据表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassQuestionnaireTotalRecord extends UpdatableRecordImpl<ClassQuestionnaireTotalRecord> implements Record7<String, String, String, String, String, String, Integer>, IClassQuestionnaireTotal {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_class_id</code>. 班级ID
     */
    @Override
    public void setClassId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_class_id</code>. 班级ID
     */
    @Override
    public String getClassId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_questionnaire_id</code>. 问卷ID
     */
    @Override
    public void setQuestionnaireId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_questionnaire_id</code>. 问卷ID
     */
    @Override
    public String getQuestionnaireId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_course_id</code>. 课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_course_id</code>. 课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_question_id</code>. 问题ID
     */
    @Override
    public void setQuestionId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_question_id</code>. 问题ID
     */
    @Override
    public String getQuestionId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_answer</code>. 答案
     */
    @Override
    public void setAnswer(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_answer</code>. 答案
     */
    @Override
    public String getAnswer() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_number</code>. 作答人数
     */
    @Override
    public void setNumber(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_number</code>. 作答人数
     */
    @Override
    public Integer getNumber() {
        return (Integer) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, String, String, Integer> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, String, String, Integer> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.QUESTIONNAIRE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.QUESTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.ANSWER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL.NUMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getQuestionnaireId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getQuestionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getAnswer();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getNumber();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord value2(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord value3(String value) {
        setQuestionnaireId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord value4(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord value5(String value) {
        setQuestionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord value6(String value) {
        setAnswer(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord value7(Integer value) {
        setNumber(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotalRecord values(String value1, String value2, String value3, String value4, String value5, String value6, Integer value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassQuestionnaireTotal from) {
        setId(from.getId());
        setClassId(from.getClassId());
        setQuestionnaireId(from.getQuestionnaireId());
        setCourseId(from.getCourseId());
        setQuestionId(from.getQuestionId());
        setAnswer(from.getAnswer());
        setNumber(from.getNumber());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassQuestionnaireTotal> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassQuestionnaireTotalRecord
     */
    public ClassQuestionnaireTotalRecord() {
        super(ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL);
    }

    /**
     * Create a detached, initialised ClassQuestionnaireTotalRecord
     */
    public ClassQuestionnaireTotalRecord(String id, String classId, String questionnaireId, String courseId, String questionId, String answer, Integer number) {
        super(ClassQuestionnaireTotal.CLASS_QUESTIONNAIRE_TOTAL);

        set(0, id);
        set(1, classId);
        set(2, questionnaireId);
        set(3, courseId);
        set(4, questionId);
        set(5, answer);
        set(6, number);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassQuestionnaireTotalEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassQuestionnaireTotalEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassQuestionnaireTotalEntity)source;
        pojo.into(this);
        return true;
    }
}
