/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassRequiredRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassRequired extends TableImpl<ClassRequiredRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_required</code>
     */
    public static final ClassRequired CLASS_REQUIRED = new ClassRequired();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassRequiredRecord> getRecordType() {
        return ClassRequiredRecord.class;
    }

    /**
     * The column <code>train.t_class_required.f_id</code>. 表id
     */
    public final TableField<ClassRequiredRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_required.f_theme</code>. 必修主题
     */
    public final TableField<ClassRequiredRecord, String> THEME = createField("f_theme", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "必修主题");

    /**
     * The column <code>train.t_class_required.f_class_id</code>. 班级id
     */
    public final TableField<ClassRequiredRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级id");

    /**
     * The column <code>train.t_class_required.f_organization_id</code>. 组织id
     */
    public final TableField<ClassRequiredRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织id");

    /**
     * The column <code>train.t_class_required.f_course_id</code>. 课程id
     */
    public final TableField<ClassRequiredRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程id");

    /**
     * The column <code>train.t_class_required.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassRequiredRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_required.f_create_member</code>. 创建人
     */
    public final TableField<ClassRequiredRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_required.f_course_name</code>. 课程名称
     */
    public final TableField<ClassRequiredRecord, String> COURSE_NAME = createField("f_course_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "课程名称");

    /**
     * The column <code>train.t_class_required.f_publish_client</code>. 适用终端   0: 全部, 1: PC, 2: APP
     */
    public final TableField<ClassRequiredRecord, Integer> PUBLISH_CLIENT = createField("f_publish_client", org.jooq.impl.SQLDataType.INTEGER, this, "适用终端   0: 全部, 1: PC, 2: APP");

    /**
     * The column <code>train.t_class_required.f_project_id</code>. 计划ID
     */
    public final TableField<ClassRequiredRecord, String> PROJECT_ID = createField("f_project_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "计划ID");

    /**
     * Create a <code>train.t_class_required</code> table reference
     */
    public ClassRequired() {
        this("t_class_required", null);
    }

    /**
     * Create an aliased <code>train.t_class_required</code> table reference
     */
    public ClassRequired(String alias) {
        this(alias, CLASS_REQUIRED);
    }

    private ClassRequired(String alias, Table<ClassRequiredRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassRequired(String alias, Table<ClassRequiredRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassRequiredRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_REQUIRED_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassRequiredRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassRequiredRecord>>asList(Keys.KEY_T_CLASS_REQUIRED_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequired as(String alias) {
        return new ClassRequired(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassRequired rename(String name) {
        return new ClassRequired(name, null);
    }
}
