/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ProjectRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Project extends TableImpl<ProjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_project</code>
     */
    public static final Project PROJECT = new Project();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProjectRecord> getRecordType() {
        return ProjectRecord.class;
    }

    /**
     * The column <code>train.t_project.f_id</code>. 主键
     */
    public final TableField<ProjectRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_project.f_name</code>. 计划名称
     */
    public final TableField<ProjectRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "计划名称");

    /**
     * The column <code>train.t_project.f_code</code>. MIS编码
     */
    public final TableField<ProjectRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "MIS编码");

    /**
     * The column <code>train.t_project.f_contact_member_id</code>. 需求单位联系人
     */
    public final TableField<ProjectRecord, String> CONTACT_MEMBER_ID = createField("f_contact_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "需求单位联系人");

    /**
     * The column <code>train.t_project.f_organization_id</code>. 需求单位
     */
    public final TableField<ProjectRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "需求单位");

    /**
     * The column <code>train.t_project.f_contact_phone</code>. 联系电话
     */
    public final TableField<ProjectRecord, String> CONTACT_PHONE = createField("f_contact_phone", org.jooq.impl.SQLDataType.VARCHAR.length(20), this, "联系电话");

    /**
     * The column <code>train.t_project.f_contact_email</code>. 联系邮箱
     */
    public final TableField<ProjectRecord, String> CONTACT_EMAIL = createField("f_contact_email", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "联系邮箱");

    /**
     * The column <code>train.t_project.f_year</code>. 计划年份
     */
    public final TableField<ProjectRecord, Integer> YEAR = createField("f_year", org.jooq.impl.SQLDataType.INTEGER, this, "计划年份");

    /**
     * The column <code>train.t_project.f_month</code>. 计划月份
     */
    public final TableField<ProjectRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER, this, "计划月份");

    /**
     * The column <code>train.t_project.f_amount</code>. 计划人数
     */
    public final TableField<ProjectRecord, Integer> AMOUNT = createField("f_amount", org.jooq.impl.SQLDataType.INTEGER, this, "计划人数");

    /**
     * The column <code>train.t_project.f_object</code>. 培训对象
     */
    public final TableField<ProjectRecord, String> OBJECT = createField("f_object", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "培训对象");

    /**
     * The column <code>train.t_project.f_days</code>. 计划培训天数
     */
    public final TableField<ProjectRecord, Integer> DAYS = createField("f_days", org.jooq.impl.SQLDataType.INTEGER, this, "计划培训天数");

    /**
     * The column <code>train.t_project.f_type_id</code>. 培训类型
     */
    public final TableField<ProjectRecord, String> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训类型");

    /**
     * The column <code>train.t_project.f_address</code>. 培训地点
     */
    public final TableField<ProjectRecord, String> ADDRESS = createField("f_address", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训地点");

    /**
     * The column <code>train.t_project.f_status</code>. 状态（1待预定2待审核3同意申请4资源已满5不同意）
     */
    public final TableField<ProjectRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "状态（1待预定2待审核3同意申请4资源已满5不同意）");

    /**
     * The column <code>train.t_project.f_create_time</code>. 创建时间
     */
    public final TableField<ProjectRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_project.f_create_member</code>. 创建人ID
     */
    public final TableField<ProjectRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_project.f_delete_flag</code>. 删除标记（0未删除1已删除）
     */
    public final TableField<ProjectRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除1已删除）");

    /**
     * The column <code>train.t_project.f_student_status</code>. 判断是否是学员端提交审核 0否 1是
     */
    public final TableField<ProjectRecord, Integer> STUDENT_STATUS = createField("f_student_status", org.jooq.impl.SQLDataType.INTEGER, this, "判断是否是学员端提交审核 0否 1是");

    /**
     * The column <code>train.t_project.f_cost</code>. 费用类型
     */
    public final TableField<ProjectRecord, String> COST = createField("f_cost", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "费用类型");

    /**
     * The column <code>train.t_project.f_survey_type</code>. 需求调研方式
     */
    public final TableField<ProjectRecord, String> SURVEY_TYPE = createField("f_survey_type", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "需求调研方式");

    /**
     * The column <code>train.t_project.f_target</code>. 培训目标
     */
    public final TableField<ProjectRecord, String> TARGET = createField("f_target", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "培训目标");

    /**
     * The column <code>train.t_project.f_temp</code>. 临时
     */
    public final TableField<ProjectRecord, String> TEMP = createField("f_temp", org.jooq.impl.SQLDataType.VARCHAR.length(2), this, "临时");

    /**
     * The column <code>train.t_project.f_is_party_cadre</code>. 是否是党干部培训班 0:否 1:是
     */
    public final TableField<ProjectRecord, Integer> IS_PARTY_CADRE = createField("f_is_party_cadre", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否是党干部培训班 0:否 1:是");

    /**
     * Create a <code>train.t_project</code> table reference
     */
    public Project() {
        this("t_project", null);
    }

    /**
     * Create an aliased <code>train.t_project</code> table reference
     */
    public Project(String alias) {
        this(alias, PROJECT);
    }

    private Project(String alias, Table<ProjectRecord> aliased) {
        this(alias, aliased, null);
    }

    private Project(String alias, Table<ProjectRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ProjectRecord> getPrimaryKey() {
        return Keys.KEY_T_PROJECT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ProjectRecord>> getKeys() {
        return Arrays.<UniqueKey<ProjectRecord>>asList(Keys.KEY_T_PROJECT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Project as(String alias) {
        return new Project(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Project rename(String name) {
        return new Project(name, null);
    }
}
