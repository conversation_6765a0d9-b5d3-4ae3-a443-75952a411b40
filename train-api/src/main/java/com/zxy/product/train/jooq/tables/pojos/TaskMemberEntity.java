/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.ITaskMember;

import javax.annotation.Generated;


/**
 * 用户提交作业详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TaskMemberEntity extends BaseEntity implements ITaskMember {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  taskId;
    private String  name;
    private String  description;
    private String  attachmentType;
    private String  attachmentId;
    private Integer state;

    public TaskMemberEntity() {}

    public TaskMemberEntity(TaskMemberEntity value) {
        this.memberId = value.memberId;
        this.taskId = value.taskId;
        this.name = value.name;
        this.description = value.description;
        this.attachmentType = value.attachmentType;
        this.attachmentId = value.attachmentId;
        this.state = value.state;
    }

    public TaskMemberEntity(
        String  id,
        String  memberId,
        Long    createTime,
        String  taskId,
        String  name,
        String  description,
        String  attachmentType,
        String  attachmentId,
        Integer state
    ) {
        super.setId(id);
        this.memberId = memberId;
        super.setCreateTime(createTime);
        this.taskId = taskId;
        this.name = name;
        this.description = description;
        this.attachmentType = attachmentType;
        this.attachmentId = attachmentId;
        this.state = state;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getTaskId() {
        return this.taskId;
    }

    @Override
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getAttachmentType() {
        return this.attachmentType;
    }

    @Override
    public void setAttachmentType(String attachmentType) {
        this.attachmentType = attachmentType;
    }

    @Override
    public String getAttachmentId() {
        return this.attachmentId;
    }

    @Override
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    @Override
    public Integer getState() {
        return this.state;
    }

    @Override
    public void setState(Integer state) {
        this.state = state;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("TaskMemberEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(taskId);
        sb.append(", ").append(name);
        sb.append(", ").append(description);
        sb.append(", ").append(attachmentType);
        sb.append(", ").append(attachmentId);
        sb.append(", ").append(state);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ITaskMember from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCreateTime(from.getCreateTime());
        setTaskId(from.getTaskId());
        setName(from.getName());
        setDescription(from.getDescription());
        setAttachmentType(from.getAttachmentType());
        setAttachmentId(from.getAttachmentId());
        setState(from.getState());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ITaskMember> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends TaskMemberEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.TaskMemberRecord r = new com.zxy.product.train.jooq.tables.records.TaskMemberRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ID, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.MEMBER_ID, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.TASK_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.TASK_ID, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.TASK_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.NAME, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.NAME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.DESCRIPTION) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.DESCRIPTION, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.DESCRIPTION));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ATTACHMENT_TYPE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ATTACHMENT_TYPE, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ATTACHMENT_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ATTACHMENT_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ATTACHMENT_ID, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.ATTACHMENT_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.STATE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.STATE, record.getValue(com.zxy.product.train.jooq.tables.TaskMember.TASK_MEMBER.STATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
