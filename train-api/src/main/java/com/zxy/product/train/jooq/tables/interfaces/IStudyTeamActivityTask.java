/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习活动-任务关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamActivityTask extends Serializable {

    /**
     * Setter for <code>train.t_study_team_activity_task.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_task.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_activity_task.f_activity_id</code>. 活动id
     */
    public void setActivityId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_task.f_activity_id</code>. 活动id
     */
    public String getActivityId();

    /**
     * Setter for <code>train.t_study_team_activity_task.f_business_id</code>. 业务id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_task.f_business_id</code>. 业务id
     */
    public String getBusinessId();

    /**
     * Setter for <code>train.t_study_team_activity_task.f_business_type</code>. 业务类型：1.课程
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>train.t_study_team_activity_task.f_business_type</code>. 业务类型：1.课程
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>train.t_study_team_activity_task.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_activity_task.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamActivityTask
     */
    public void from(IStudyTeamActivityTask from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamActivityTask
     */
    public <E extends IStudyTeamActivityTask> E into(E into);
}
