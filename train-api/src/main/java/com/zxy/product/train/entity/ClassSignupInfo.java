package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassSignupInfoEntity;

/**
 * <AUTHOR>
 * 班级报名信息
 *
 */
public class ClassSignupInfo extends ClassSignupInfoEntity {

    private static final long serialVersionUID = 5792790813825543678L;

    public static final int CODE_PREFIX = 1;//报名码前缀（1为培训班  2为考试  3为MOOC）
    public static final String SIGN_URL = "http://sign.chinamobile.com";//报名链接
    public static final int TOWBRINGS_TRUE = 1;//启用两个带来
    public static final int TOWBRINGS_FALSE = 0;//不启用两个带来
    public static final int IS_OPEN_TRUE = 1;//开放报名
    public static final int IS_OPEN_FALSE = 0;//不开放报名

    private ClassInfo classInfo;
    
    private Integer traineeType;

	public Integer getTraineeType() {
		return traineeType;
	}

	public void setTraineeType(Integer traineeType) {
		this.traineeType = traineeType;
	}

	public ClassInfo getClassInfo() {
		return classInfo;
	}

	public void setClassInfo(ClassInfo classInfo) {
		this.classInfo = classInfo;
	}


}
