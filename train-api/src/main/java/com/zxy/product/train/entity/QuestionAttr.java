package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.QuestionAttrEntity;

public class QuestionAttr extends QuestionAttrEntity{

	/**
	 * 题型分值实体  lulu
	 */
	private static final long serialVersionUID = 1874262475513812022L;

	private String questionContent; //题目
	private Integer manA;
	private Integer manB;
	private Integer manC;
	private Integer manD;
	private Integer manE;
	private Integer manF;
	private Integer manZ;
	private String manAF;
	private String manBF;
	private String manCF;
	private String manDF;
	private String manEF;
	private String manFF;
	private String manCMF;
	private Integer questionOrder;
	private String idea;//意见
	private Integer index; // 序号,用于生成excel时产生序号
	private String courseLecturer;//讲师（导出用）
	private String courseName;//课程（导出用）
	private String courseContent;//内容（导出用）
	private Integer sort;//排序（导出用）
	private String response;//反馈率（导出用）
	private String population;//总体满意率（导出用）
	private String curriculum;//课程满意率（导出用）
	private String path;//短名称用
	
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}
	public String getResponse() {
		return response;
	}
	public void setResponse(String response) {
		this.response = response;
	}
	public String getPopulation() {
		return population;
	}
	public void setPopulation(String population) {
		this.population = population;
	}
	public String getCurriculum() {
		return curriculum;
	}
	public void setCurriculum(String curriculum) {
		this.curriculum = curriculum;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getCourseLecturer() {
		return courseLecturer;
	}
	public void setCourseLecturer(String courseLecturer) {
		this.courseLecturer = courseLecturer;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getCourseContent() {
		return courseContent;
	}
	public void setCourseContent(String courseContent) {
		this.courseContent = courseContent;
	}
	public Integer getIndex() {
		return index;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}
	public String getIdea() {
		return idea;
	}
	public void setIdea(String idea) {
		this.idea = idea;
	}
	public Integer getQuestionOrder() {
		return questionOrder;
	}
	public void setQuestionOrder(Integer questionOrder) {
		this.questionOrder = questionOrder;
	}
	public String getManCMF() {
		return manCMF;
	}
	public void setManCMF(String manCMF) {
		this.manCMF = manCMF;
	}
	public Integer getManF() {
		return manF;
	}
	public void setManF(Integer manF) {
		this.manF = manF;
	}
	public String getManFF() {
		return manFF;
	}
	public void setManFF(String manFF) {
		this.manFF = manFF;
	}
	public Integer getManE() {
		return manE;
	}
	public void setManE(Integer manE) {
		this.manE = manE;
	}
	public String getManEF() {
		return manEF;
	}
	public void setManEF(String manEF) {
		this.manEF = manEF;
	}
	private String manZF;//满意率
	private Float manClassZF;//课程总体满意率
	private Float manZong;//总满意率
	private Float manKeZF;//总体满意率
	private String answer;
	private String mName;
	private String oName;
	private String phone;

	public String getManZF() {
		return manZF;
	}
	public void setManZF(String manZF) {
		this.manZF = manZF;
	}
	public String getManAF() {
		return manAF;
	}
	public void setManAF(String manAF) {
		this.manAF = manAF;
	}
	public String getManBF() {
		return manBF;
	}
	public void setManBF(String manBF) {
		this.manBF = manBF;
	}
	public String getManCF() {
		return manCF;
	}
	public void setManCF(String manCF) {
		this.manCF = manCF;
	}
	public String getManDF() {
		return manDF;
	}
	public void setManDF(String manDF) {
		this.manDF = manDF;
	}
	public String getAnswer() {
		return answer;
	}
	public void setAnswer(String answer) {
		this.answer = answer;
	}
	public String getmName() {
		return mName;
	}
	public void setmName(String mName) {
		this.mName = mName;
	}
	public String getoName() {
		return oName;
	}
	public void setoName(String oName) {
		this.oName = oName;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public Float getManClassZF() {
		return manClassZF;
	}
	public void setManClassZF(Float manClassZF) {
		this.manClassZF = manClassZF;
	}
	public Float getManZong() {
		return manZong;
	}
	public void setManZong(Float manZong) {
		this.manZong = manZong;
	}
	public Float getManKeZF() {
		return manKeZF;
	}
	public void setManKeZF(Float manKeZF) {
		this.manKeZF = manKeZF;
	}
	public Integer getManZ() {
		return manZ;
	}
	public void setManZ(Integer manZ) {
		this.manZ = manZ;
	}

	public String getQuestionContent() {
		return questionContent;
	}
	public void setQuestionContent(String questionContent) {
		this.questionContent = questionContent;
	}
	public Integer getManA() {
		return manA;
	}
	public void setManA(Integer manA) {
		this.manA = manA;
	}
	public Integer getManB() {
		return manB;
	}
	public void setManB(Integer manB) {
		this.manB = manB;
	}
	public Integer getManC() {
		return manC;
	}
	public void setManC(Integer manC) {
		this.manC = manC;
	}
	public Integer getManD() {
		return manD;
	}
	public void setManD(Integer manD) {
		this.manD = manD;
	}
}
