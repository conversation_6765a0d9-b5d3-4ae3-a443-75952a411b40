/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassBusinessProgress;
import com.zxy.product.train.jooq.tables.interfaces.IClassBusinessProgress;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassBusinessProgressRecord extends UpdatableRecordImpl<ClassBusinessProgressRecord> implements Record7<String, String, String, String, Integer, Integer, Long>, IClassBusinessProgress {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_business_progress.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_business_progress.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_business_progress.f_member_id</code>. 用户ID
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_business_progress.f_member_id</code>. 用户ID
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_business_progress.f_class_id</code>. 班级ID
     */
    @Override
    public void setClassId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_business_progress.f_class_id</code>. 班级ID
     */
    @Override
    public String getClassId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_class_business_progress.f_class_business_id</code>. 调研，评估或考试的ID，
     */
    @Override
    public void setClassBusinessId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_business_progress.f_class_business_id</code>. 调研，评估或考试的ID，
     */
    @Override
    public String getClassBusinessId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_class_business_progress.f_finish_status</code>. 参与状态：0--未参与，1--已完成，2--待评卷，3，未及格
     */
    @Override
    public void setFinishStatus(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_business_progress.f_finish_status</code>. 参与状态：0--未参与，1--已完成，2--待评卷，3，未及格
     */
    @Override
    public Integer getFinishStatus() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_class_business_progress.f_score</code>. 考试得分
     */
    @Override
    public void setScore(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_business_progress.f_score</code>. 考试得分
     */
    @Override
    public Integer getScore() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>train.t_class_business_progress.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_business_progress.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, Integer, Integer, Long> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, Integer, Integer, Long> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.CLASS_BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.FINISH_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.SCORE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return ClassBusinessProgress.CLASS_BUSINESS_PROGRESS.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getClassBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getFinishStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getScore();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord value3(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord value4(String value) {
        setClassBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord value5(Integer value) {
        setFinishStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord value6(Integer value) {
        setScore(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord value7(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgressRecord values(String value1, String value2, String value3, String value4, Integer value5, Integer value6, Long value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassBusinessProgress from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setClassId(from.getClassId());
        setClassBusinessId(from.getClassBusinessId());
        setFinishStatus(from.getFinishStatus());
        setScore(from.getScore());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassBusinessProgress> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassBusinessProgressRecord
     */
    public ClassBusinessProgressRecord() {
        super(ClassBusinessProgress.CLASS_BUSINESS_PROGRESS);
    }

    /**
     * Create a detached, initialised ClassBusinessProgressRecord
     */
    public ClassBusinessProgressRecord(String id, String memberId, String classId, String classBusinessId, Integer finishStatus, Integer score, Long createTime) {
        super(ClassBusinessProgress.CLASS_BUSINESS_PROGRESS);

        set(0, id);
        set(1, memberId);
        set(2, classId);
        set(3, classBusinessId);
        set(4, finishStatus);
        set(5, score);
        set(6, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassBusinessProgressEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassBusinessProgressEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassBusinessProgressEntity)source;
        pojo.into(this);
        return true;
    }
}
