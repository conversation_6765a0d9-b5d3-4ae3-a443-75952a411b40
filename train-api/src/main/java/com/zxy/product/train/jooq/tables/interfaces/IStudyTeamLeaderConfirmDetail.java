/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;

import javax.annotation.Generated;
import java.io.Serializable;


/**
 * 学习活动-课程领学人学习时间
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamLeaderConfirmDetail extends Serializable {

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_activity_task_id</code>. 任务id
     */
    public void setActivityTaskId(String value);

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_activity_task_id</code>. 任务id
     */
    public String getActivityTaskId();

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_study_time</code>. 学习时间
     */
    public void setStudyTime(Long value);

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_study_time</code>. 学习时间
     */
    public Long getStudyTime();

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamLeaderConfirmDetail
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.IStudyTeamLeaderConfirmDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamLeaderConfirmDetail
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.IStudyTeamLeaderConfirmDetail> E into(E into);
}
