package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.OtherTeachingEntity;

public class OtherTeaching extends OtherTeachingEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8497730473630485776L;
	public static final Integer SOURCE_SELF = 0;	//讲师自己申请
	public static final Integer SOURCE_MANAGE = 1;	//管理员添加
	public static final Integer SOURCE_IMPORT = 2;	//导入数据
	public static final Integer STATUS_TRUE = 0;	//是否通过省级及以上人力资源部门认定:是
	public static final Integer STATUS_FALSE = 1;	//是否通过省级及以上人力资源部门认定:否

	/**
	 * 待审核
	 */
	public static final Integer APPROVAL_STATUS_WAIT = 0;
	/**
	 * 通过
	 */
	public static final Integer APPROVAL_STATUS_PASS = 1;
	/**
	 * 拒绝
	 */
	public static final Integer APPROVAL_STATUS_REFUSE = 2;
	/**
	 * 无状态
	 */
	public static final Integer APPROVAL_STATUS_NULL = 3;

	private String approvalMemberName;		//审核人
	private String activityLevelName;		//活动级别名称
	
	private String memberCode; // 员工编码
	private String lecturerAttributeName; // 讲师属性
	private String startStopData; // 起止时间Data
	private String typeName; // 类型名称
	private String approvalStatusName; // 是否通过省级及以上人力资源部门审核认定/是否经业务部门及人力资源部门认定名称
	private String courseTypeName; //课程分类名称

	public String getApprovalMemberName() {
		return approvalMemberName;
	}

	public void setApprovalMemberName(String approvalMemberName) {
		this.approvalMemberName = approvalMemberName;
	}

	public String getActivityLevelName() {
		return activityLevelName;
	}

	public void setActivityLevelName(String activityLevelName) {
		this.activityLevelName = activityLevelName;
	}

	public String getMemberCode() {
		return memberCode;
	}

	public void setMemberCode(String memberCode) {
		this.memberCode = memberCode;
	}

	public String getLecturerAttributeName() {
		return lecturerAttributeName;
	}

	public void setLecturerAttributeName(String lecturerAttributeName) {
		this.lecturerAttributeName = lecturerAttributeName;
	}

	public String getStartStopData() {
		return startStopData;
	}

	public void setStartStopData(String startStopData) {
		this.startStopData = startStopData;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getApprovalStatusName() {
		return approvalStatusName;
	}

	public void setApprovalStatusName(String approvalStatusName) {
		this.approvalStatusName = approvalStatusName;
	}

	public String getCourseTypeName() {
		return courseTypeName;
	}

	public void setCourseTypeName(String courseTypeName) {
		this.courseTypeName = courseTypeName;
	}

}
