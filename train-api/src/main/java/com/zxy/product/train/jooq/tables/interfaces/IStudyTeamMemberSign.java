/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习团队成员签到表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamMemberSign extends Serializable {

    /**
     * Setter for <code>train.t_study_team_member_sign.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_activity_id</code>. 团队活动id
     */
    public void setActivityId(String value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_activity_id</code>. 团队活动id
     */
    public String getActivityId();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_team_member_id</code>. 团队成员id
     */
    public void setTeamMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_team_member_id</code>. 团队成员id
     */
    public String getTeamMemberId();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_sign_in_time</code>. 签到时间
     */
    public void setSignInTime(Long value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_sign_in_time</code>. 签到时间
     */
    public Long getSignInTime();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_sign_off_time</code>. 签退时间
     */
    public void setSignOffTime(Long value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_sign_off_time</code>. 签退时间
     */
    public Long getSignOffTime();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_status</code>. 状态 1-正常 2-迟到 3-早退 4-迟到&amp;早退
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_status</code>. 状态 1-正常 2-迟到 3-早退 4-迟到&amp;早退
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_confirm_status</code>. 确认时长状态 0-未确认 1-确认中 2-已确认
     */
    public void setConfirmStatus(Integer value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_confirm_status</code>. 确认时长状态 0-未确认 1-确认中 2-已确认
     */
    public Integer getConfirmStatus();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_study_team_member_sign.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_member_sign.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamMemberSign
     */
    public void from(IStudyTeamMemberSign from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamMemberSign
     */
    public <E extends IStudyTeamMemberSign> E into(E into);
}
