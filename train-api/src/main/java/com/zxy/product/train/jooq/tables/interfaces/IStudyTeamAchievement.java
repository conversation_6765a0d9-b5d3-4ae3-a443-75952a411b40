/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 团队学习班-学习成果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamAchievement extends Serializable {

    /**
     * Setter for <code>train.t_study_team_achievement.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_achievement.f_team_id</code>. 团队id
     */
    public void setTeamId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_team_id</code>. 团队id
     */
    public String getTeamId();

    /**
     * Setter for <code>train.t_study_team_achievement.f_activity_id</code>. 活动id
     */
    public void setActivityId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_activity_id</code>. 活动id
     */
    public String getActivityId();

    /**
     * Setter for <code>train.t_study_team_achievement.f_member_id</code>. 用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_member_id</code>. 用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_study_team_achievement.f_business_id</code>. 关联课程id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_business_id</code>. 关联课程id
     */
    public String getBusinessId();

    /**
     * Setter for <code>train.t_study_team_achievement.f_content</code>. 讨论内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_content</code>. 讨论内容
     */
    public String getContent();

    /**
     * Setter for <code>train.t_study_team_achievement.f_top_status</code>. 置顶(1是，0否)
     */
    public void setTopStatus(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_top_status</code>. 置顶(1是，0否)
     */
    public Integer getTopStatus();

    /**
     * Setter for <code>train.t_study_team_achievement.f_essence_status</code>. 精华(1是，0否)
     */
    public void setEssenceStatus(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_essence_status</code>. 精华(1是，0否)
     */
    public Integer getEssenceStatus();

    /**
     * Setter for <code>train.t_study_team_achievement.f_hide</code>. 隐藏(1是，0否)
     */
    public void setHide(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_hide</code>. 隐藏(1是，0否)
     */
    public Integer getHide();

    /**
     * Setter for <code>train.t_study_team_achievement.f_comment_count</code>. 讨论数
     */
    public void setCommentCount(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_comment_count</code>. 讨论数
     */
    public Integer getCommentCount();

    /**
     * Setter for <code>train.t_study_team_achievement.f_praise_count</code>. 点赞数
     */
    public void setPraiseCount(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_praise_count</code>. 点赞数
     */
    public Integer getPraiseCount();

    /**
     * Setter for <code>train.t_study_team_achievement.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_study_team_achievement.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_study_team_achievement.f_organization_id</code>. 所属组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_organization_id</code>. 所属组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_study_team_achievement.f_content_text</code>. 讨论内容——纯文本
     */
    public void setContentText(String value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_content_text</code>. 讨论内容——纯文本
     */
    public String getContentText();

    /**
     * Setter for <code>train.t_study_team_achievement.f_essence_time</code>. 加精时间
     */
    public void setEssenceTime(Long value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_essence_time</code>. 加精时间
     */
    public Long getEssenceTime();

    /**
     * Setter for <code>train.t_study_team_achievement.f_top_time</code>. 置顶时间
     */
    public void setTopTime(Long value);

    /**
     * Getter for <code>train.t_study_team_achievement.f_top_time</code>. 置顶时间
     */
    public Long getTopTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamAchievement
     */
    public void from(IStudyTeamAchievement from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamAchievement
     */
    public <E extends IStudyTeamAchievement> E into(E into);
}
