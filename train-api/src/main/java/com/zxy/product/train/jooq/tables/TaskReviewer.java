/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TaskReviewerRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 作业审核人关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TaskReviewer extends TableImpl<TaskReviewerRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_task_reviewer</code>
     */
    public static final TaskReviewer TASK_REVIEWER = new TaskReviewer();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TaskReviewerRecord> getRecordType() {
        return TaskReviewerRecord.class;
    }

    /**
     * The column <code>train.t_task_reviewer.f_id</code>. 主键
     */
    public final TableField<TaskReviewerRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_task_reviewer.f_approval_member_id</code>. 审核人id
     */
    public final TableField<TaskReviewerRecord, String> APPROVAL_MEMBER_ID = createField("f_approval_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "审核人id");

    /**
     * The column <code>train.t_task_reviewer.f_task_id</code>. 作业id
     */
    public final TableField<TaskReviewerRecord, String> TASK_ID = createField("f_task_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "作业id");

    /**
     * The column <code>train.t_task_reviewer.f_create_member_id</code>. 创建人id
     */
    public final TableField<TaskReviewerRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人id");

    /**
     * The column <code>train.t_task_reviewer.f_create_time</code>. 创建时间
     */
    public final TableField<TaskReviewerRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_task_reviewer</code> table reference
     */
    public TaskReviewer() {
        this("t_task_reviewer", null);
    }

    /**
     * Create an aliased <code>train.t_task_reviewer</code> table reference
     */
    public TaskReviewer(String alias) {
        this(alias, TASK_REVIEWER);
    }

    private TaskReviewer(String alias, Table<TaskReviewerRecord> aliased) {
        this(alias, aliased, null);
    }

    private TaskReviewer(String alias, Table<TaskReviewerRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "作业审核人关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TaskReviewerRecord> getPrimaryKey() {
        return Keys.KEY_T_TASK_REVIEWER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TaskReviewerRecord>> getKeys() {
        return Arrays.<UniqueKey<TaskReviewerRecord>>asList(Keys.KEY_T_TASK_REVIEWER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskReviewer as(String alias) {
        return new TaskReviewer(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TaskReviewer rename(String name) {
        return new TaskReviewer(name, null);
    }
}
