/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 团队学习班-点赞明细表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamAchievementPraise extends Serializable {

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_object_id</code>. 被点赞id
     */
    public void setObjectId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_object_id</code>. 被点赞id
     */
    public String getObjectId();

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_object_type</code>. 点赞对象类型 1:评论，2：回复
     */
    public void setObjectType(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_object_type</code>. 点赞对象类型 1:评论，2：回复
     */
    public Integer getObjectType();

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_member_id</code>. 点赞用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_member_id</code>. 点赞用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_study_team_achievement_praise.f_organization_id</code>. 所属组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_study_team_achievement_praise.f_organization_id</code>. 所属组织id
     */
    public String getOrganizationId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamAchievementPraise
     */
    public void from(IStudyTeamAchievementPraise from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamAchievementPraise
     */
    public <E extends IStudyTeamAchievementPraise> E into(E into);
}
