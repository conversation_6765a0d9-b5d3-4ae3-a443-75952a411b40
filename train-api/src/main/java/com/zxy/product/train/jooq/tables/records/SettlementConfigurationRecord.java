/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.SettlementConfiguration;
import com.zxy.product.train.jooq.tables.interfaces.ISettlementConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SettlementConfigurationRecord extends UpdatableRecordImpl<SettlementConfigurationRecord> implements Record8<String, String, Integer, Integer, Integer, Long, String, String>, ISettlementConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_settlement_configuration.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_settlement_configuration.f_name</code>. 结算单位名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_name</code>. 结算单位名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_settlement_configuration.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_settlement_configuration.f_type_id</code>. 关联配置类型
     */
    @Override
    public void setTypeId(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_type_id</code>. 关联配置类型
     */
    @Override
    public Integer getTypeId() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_settlement_configuration.f_type</code>. 类别（1总部和省公司 ，2专业公司，3直属单位和境外机构）
     */
    @Override
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_type</code>. 类别（1总部和省公司 ，2专业公司，3直属单位和境外机构）
     */
    @Override
    public Integer getType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_settlement_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_settlement_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_settlement_configuration.f_contacts</code>. 往来字段
     */
    @Override
    public void setContacts(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration.f_contacts</code>. 往来字段
     */
    @Override
    public String getContacts() {
        return (String) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, Integer, Integer, Integer, Long, String, String> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, Integer, Integer, Integer, Long, String, String> valuesRow() {
        return (Row8) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.TYPE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return SettlementConfiguration.SETTLEMENT_CONFIGURATION.CONTACTS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getTypeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getContacts();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value3(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value4(Integer value) {
        setTypeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value5(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value7(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord value8(String value) {
        setContacts(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationRecord values(String value1, String value2, Integer value3, Integer value4, Integer value5, Long value6, String value7, String value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISettlementConfiguration from) {
        setId(from.getId());
        setName(from.getName());
        setSort(from.getSort());
        setTypeId(from.getTypeId());
        setType(from.getType());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setContacts(from.getContacts());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISettlementConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SettlementConfigurationRecord
     */
    public SettlementConfigurationRecord() {
        super(SettlementConfiguration.SETTLEMENT_CONFIGURATION);
    }

    /**
     * Create a detached, initialised SettlementConfigurationRecord
     */
    public SettlementConfigurationRecord(String id, String name, Integer sort, Integer typeId, Integer type, Long createTime, String createMember, String contacts) {
        super(SettlementConfiguration.SETTLEMENT_CONFIGURATION);

        set(0, id);
        set(1, name);
        set(2, sort);
        set(3, typeId);
        set(4, type);
        set(5, createTime);
        set(6, createMember);
        set(7, contacts);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.SettlementConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.SettlementConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.SettlementConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
