/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.CourseTeachingActivities;
import com.zxy.product.train.jooq.tables.interfaces.ICourseTeachingActivities;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 教研教学活动配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseTeachingActivitiesRecord extends UpdatableRecordImpl<CourseTeachingActivitiesRecord> implements Record10<String, String, String, Double, String, Double, Double, String, String, Long>, ICourseTeachingActivities {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_course_teaching_activities.f_id</code>. 系统ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_id</code>. 系统ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_activity_id</code>. 活动ID
     */
    @Override
    public void setActivityId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_activity_id</code>. 活动ID
     */
    @Override
    public String getActivityId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_activity_name</code>. 活动名称
     */
    @Override
    public void setActivityName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_activity_name</code>. 活动名称
     */
    @Override
    public String getActivityName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_base</code>. 基数
     */
    @Override
    public void setBase(Double value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_base</code>. 基数
     */
    @Override
    public Double getBase() {
        return (Double) get(3);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_level_name</code>. 级别名称
     */
    @Override
    public void setLevelName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_level_name</code>. 级别名称
     */
    @Override
    public String getLevelName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_coefficient</code>. 系数
     */
    @Override
    public void setCoefficient(Double value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_coefficient</code>. 系数
     */
    @Override
    public Double getCoefficient() {
        return (Double) get(5);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_satisfied_degree</code>. 满意度要求
     */
    @Override
    public void setSatisfiedDegree(Double value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_satisfied_degree</code>. 满意度要求
     */
    @Override
    public Double getSatisfiedDegree() {
        return (Double) get(6);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_create_member_id</code>. 创建人ID
     */
    @Override
    public void setCreateMemberId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_create_member_id</code>. 创建人ID
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_course_teaching_activities.f_create_time</code>. 添加时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_course_teaching_activities.f_create_time</code>. 添加时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, Double, String, Double, Double, String, String, Long> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, Double, String, Double, Double, String, String, Long> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.ACTIVITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.ACTIVITY_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field4() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.BASE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.LEVEL_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field6() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.COEFFICIENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field7() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.SATISFIED_DEGREE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getActivityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getActivityName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value4() {
        return getBase();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getLevelName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value6() {
        return getCoefficient();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value7() {
        return getSatisfiedDegree();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value2(String value) {
        setActivityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value3(String value) {
        setActivityName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value4(Double value) {
        setBase(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value5(String value) {
        setLevelName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value6(Double value) {
        setCoefficient(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value7(Double value) {
        setSatisfiedDegree(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value8(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value9(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord value10(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivitiesRecord values(String value1, String value2, String value3, Double value4, String value5, Double value6, Double value7, String value8, String value9, Long value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseTeachingActivities from) {
        setId(from.getId());
        setActivityId(from.getActivityId());
        setActivityName(from.getActivityName());
        setBase(from.getBase());
        setLevelName(from.getLevelName());
        setCoefficient(from.getCoefficient());
        setSatisfiedDegree(from.getSatisfiedDegree());
        setOrganizationId(from.getOrganizationId());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseTeachingActivities> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseTeachingActivitiesRecord
     */
    public CourseTeachingActivitiesRecord() {
        super(CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES);
    }

    /**
     * Create a detached, initialised CourseTeachingActivitiesRecord
     */
    public CourseTeachingActivitiesRecord(String id, String activityId, String activityName, Double base, String levelName, Double coefficient, Double satisfiedDegree, String organizationId, String createMemberId, Long createTime) {
        super(CourseTeachingActivities.COURSE_TEACHING_ACTIVITIES);

        set(0, id);
        set(1, activityId);
        set(2, activityName);
        set(3, base);
        set(4, levelName);
        set(5, coefficient);
        set(6, satisfiedDegree);
        set(7, organizationId);
        set(8, createMemberId);
        set(9, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.CourseTeachingActivitiesEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.CourseTeachingActivitiesEntity pojo = (com.zxy.product.train.jooq.tables.pojos.CourseTeachingActivitiesEntity)source;
        pojo.into(this);
        return true;
    }
}
