/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassQuotaDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassQuotaDetail extends TableImpl<ClassQuotaDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_quota_detail</code>
     */
    public static final ClassQuotaDetail CLASS_QUOTA_DETAIL = new ClassQuotaDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassQuotaDetailRecord> getRecordType() {
        return ClassQuotaDetailRecord.class;
    }

    /**
     * The column <code>train.t_class_quota_detail.f_id</code>.
     */
    public final TableField<ClassQuotaDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_quota_detail.f_class_id</code>. 培训班ID
     */
    public final TableField<ClassQuotaDetailRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训班ID");

    /**
     * The column <code>train.t_class_quota_detail.f_organization_id</code>. 机构ID
     */
    public final TableField<ClassQuotaDetailRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "机构ID");

    /**
     * The column <code>train.t_class_quota_detail.f_quantity</code>. 配额数量
     */
    public final TableField<ClassQuotaDetailRecord, Integer> QUANTITY = createField("f_quantity", org.jooq.impl.SQLDataType.INTEGER, this, "配额数量");

    /**
     * The column <code>train.t_class_quota_detail.f_create_member</code>. 创建人
     */
    public final TableField<ClassQuotaDetailRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_quota_detail.f_create_time</code>. 创建时间
     */
    public final TableField<ClassQuotaDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_quota_detail.f_group_id</code>. 分组ID
     */
    public final TableField<ClassQuotaDetailRecord, String> GROUP_ID = createField("f_group_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "分组ID");

    /**
     * Create a <code>train.t_class_quota_detail</code> table reference
     */
    public ClassQuotaDetail() {
        this("t_class_quota_detail", null);
    }

    /**
     * Create an aliased <code>train.t_class_quota_detail</code> table reference
     */
    public ClassQuotaDetail(String alias) {
        this(alias, CLASS_QUOTA_DETAIL);
    }

    private ClassQuotaDetail(String alias, Table<ClassQuotaDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassQuotaDetail(String alias, Table<ClassQuotaDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassQuotaDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_QUOTA_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassQuotaDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassQuotaDetailRecord>>asList(Keys.KEY_T_CLASS_QUOTA_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuotaDetail as(String alias) {
        return new ClassQuotaDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassQuotaDetail rename(String name) {
        return new ClassQuotaDetail(name, null);
    }
}
