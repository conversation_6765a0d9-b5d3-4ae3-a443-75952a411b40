package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassOfflineCourseEntity;

import java.util.List;

/**
 * l<PERSON>hunan
 *
 */
public class ClassOfflineCourse extends ClassOfflineCourseEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = -193761342659998793L;

	public static final Integer TYPE_FACE = 1;//面授
	public static final Integer TYPE_VIDEO = 2;//录像
	public static final Integer TYPE_LIVE = 3;//直播
	public static final Integer TYPE_OTHER = 4;//其他
    public static final Integer TYPE_ONLINE_COURSE = 5;//在线课程
    public static final Integer TYPE_LIVE_INTERACT = 6;//互动直播

	public static final Integer DELETE_FALSE = 0;
	public static final Integer DELETE_TRUE = 1;
    public static final String[] COURSE_TIME = { "09:00-10:00", "10:30-12:00", "14:00-15:30", "16:00-17:30" };
    public static final Integer IS_RELATE_ONLINE_NO = 0;
    public static final Integer IS_RELATE_ONLINE_YES = 1;
    public static final String OFFLINE_COURSE_KEY = "offline-course-key";

    private boolean teacherFalg;
    private Lecturer lecturer;
	private String className;

	private String projectCode;

	private String organizationName;

	private ClassroomConfiguration classRoom;

	private CourseSalary courseSalary;

	private List<CourseAttach> attachList;

	private String typeName;//类型名称，用于导入

	private String classRoomCode;//教室编码

	private String teacherTypeName;//讲师类别名称

	private String courseStartTime;//上课开始时间

	private String courseEndTime;//上课结束时间

	private String classroomName;//教室名称

	private String courseDateStr;//上课日期

	private String courseTime;//上课时间

	private String lecturerCard;//身份证号

    private String lecturerBankName;//开户银行

    private String lecturerBankCard;//银行卡号

    private String paidPay;//实付

    private String pay;//酬金

    private String tax;//税金

    private Double time;//学习时长

    private Double paidPayD;//实付

    private Double payD;//酬金

    private Double taxD;//税金

    private String lecturerId;//面授课程库列表需要

    private String lecturerName; //面授课程库列表需要

	private Integer lecturerType; //面授课程库列表需要

	private String lectureUnit; //面授课程库列表需要

	private Integer status;//发布状态

	private Organization organization;//计划的归属单位

    private Integer flag; //内外部讲师 0为内部，1为外部

    private String organizationId;//需求单位ID

    private CourseInfo courseInfo;//在线课程信息

    private Project project; //班级信息

    private Integer AttachStatus;//是否存在附件 0存在 1不存在

    private String address;//地点

    private Boolean haveEvaluate; // 是否已课程师资评价

    private Integer implementationMonth;//实施月
    private String projectName;//培训班名称
    private String orgName;//主办方

    public Integer getImplementationMonth() {
        return implementationMonth;
    }

    public void setImplementationMonth(Integer implementationMonth) {
        this.implementationMonth = implementationMonth;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Integer getAttachStatus() {
		return AttachStatus;
	}

	public void setAttachStatus(Integer attachStatus) {
		AttachStatus = attachStatus;
	}

	public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getLectureUnit() {
		return lectureUnit;
	}

	public void setLectureUnit(String lectureUnit) {
		this.lectureUnit = lectureUnit;
	}

	public String getLecturerId() {
		return lecturerId;
	}

	public void setLecturerId(String lecturerId) {
		this.lecturerId = lecturerId;
	}

	public String getLecturerName() {
		return lecturerName;
	}

	public void setLecturerName(String lecturerName) {
		this.lecturerName = lecturerName;
	}

	public Integer getLecturerType() {
		return lecturerType;
	}

	public void setLecturerType(Integer lecturerType) {
		this.lecturerType = lecturerType;
	}

	public Double getPaidPayD() {
		return paidPayD;
	}

	public void setPaidPayD(Double paidPayD) {
		this.paidPayD = paidPayD;
	}

	public Double getPayD() {
		return payD;
	}

	public void setPayD(Double payD) {
		this.payD = payD;
	}

	public Double getTaxD() {
		return taxD;
	}

	public void setTaxD(Double taxD) {
		this.taxD = taxD;
	}

	public Double getTime() {
		return time;
	}

	public void setTime(Double time) {
		this.time = time;
	}

	public String getProjectCode() {
		return projectCode;
	}

	public void setProjectCode(String projectCode) {
		this.projectCode = projectCode;
	}

	public String getPaidPay() {
        return paidPay;
    }

    public void setPaidPay(String paidPay) {
        this.paidPay = paidPay;
    }

    public String getPay() {
        return pay;
    }

    public void setPay(String pay) {
        this.pay = pay;
    }

    public String getTax() {
        return tax;
    }

    public void setTax(String tax) {
        this.tax = tax;
    }

    public String getLecturerCard() {
        return lecturerCard;
    }

    public void setLecturerCard(String lecturerCard) {
        this.lecturerCard = lecturerCard;
    }

    public String getLecturerBankName() {
        return lecturerBankName;
    }

    public void setLecturerBankName(String lecturerBankName) {
        this.lecturerBankName = lecturerBankName;
    }

    public String getLecturerBankCard() {
        return lecturerBankCard;
    }

    public void setLecturerBankCard(String lecturerBankCard) {
        this.lecturerBankCard = lecturerBankCard;
    }

    public String getCourseTime() {
        return courseTime;
    }

    public Lecturer getLecturer() {
        return lecturer;
    }

    public void setLecturer(Lecturer lecturer) {
        this.lecturer = lecturer;
    }

    public void setCourseTime(String courseTime) {
        this.courseTime = courseTime;
    }

    public String getCourseDateStr() {
        return courseDateStr;
    }

    public void setCourseDateStr(String courseDateStr) {
        this.courseDateStr = courseDateStr;
    }

    public String getClassroomName() {
        return classroomName;
    }

    public void setClassroomName(String classroomName) {
        this.classroomName = classroomName;
    }

    public String getCourseStartTime() {
        return courseStartTime;
    }

    public void setCourseStartTime(String courseStartTime) {
        this.courseStartTime = courseStartTime;
    }

    public String getCourseEndTime() {
        return courseEndTime;
    }

    public void setCourseEndTime(String courseEndTime) {
        this.courseEndTime = courseEndTime;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getClassRoomCode() {
        return classRoomCode;
    }

    public void setClassRoomCode(String classRoomCode) {
        this.classRoomCode = classRoomCode;
    }

    public String getTeacherTypeName() {
        return teacherTypeName;
    }

    public void setTeacherTypeName(String teacherTypeName) {
        this.teacherTypeName = teacherTypeName;
    }

    public CourseSalary getCourseSalary() {
        return courseSalary;
    }

    public void setCourseSalary(CourseSalary courseSalary) {
        this.courseSalary = courseSalary;
    }

    public List<CourseAttach> getAttachList() {
        return attachList;
    }

    public void setAttachList(List<CourseAttach> attachList) {
        this.attachList = attachList;
    }

    public ClassroomConfiguration getClassRoom() {
        return classRoom;
    }

    public void setClassRoom(ClassroomConfiguration classRoom) {
        this.classRoom = classRoom;
    }

    public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public boolean isTeacherFalg() {
        return teacherFalg;
    }

    public void setTeacherFalg(boolean teacherFalg) {
        this.teacherFalg = teacherFalg;
    }

    public Boolean getHaveEvaluate() {
        return haveEvaluate;
    }

    public void setHaveEvaluate(Boolean haveEvaluate) {
        this.haveEvaluate = haveEvaluate;
    }
}
