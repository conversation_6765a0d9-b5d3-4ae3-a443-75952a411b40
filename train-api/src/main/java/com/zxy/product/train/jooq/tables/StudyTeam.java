/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习团队表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeam extends TableImpl<StudyTeamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team</code>
     */
    public static final StudyTeam STUDY_TEAM = new StudyTeam();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamRecord> getRecordType() {
        return StudyTeamRecord.class;
    }

    /**
     * The column <code>train.t_study_team.f_id</code>. ID
     */
    public final TableField<StudyTeamRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_study_team.f_name</code>. 团队名称
     */
    public final TableField<StudyTeamRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).nullable(false), this, "团队名称");

    /**
     * The column <code>train.t_study_team.f_captain_member_id</code>. 团队长id
     */
    public final TableField<StudyTeamRecord, String> CAPTAIN_MEMBER_ID = createField("f_captain_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队长id");

    /**
     * The column <code>train.t_study_team.f_organization_id</code>. 归属组织id
     */
    public final TableField<StudyTeamRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "归属组织id");

    /**
     * The column <code>train.t_study_team.f_cover</code>. 封面
     */
    public final TableField<StudyTeamRecord, String> COVER = createField("f_cover", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "封面");

    /**
     * The column <code>train.t_study_team.f_notice</code>. 学习团队公告
     */
    public final TableField<StudyTeamRecord, String> NOTICE = createField("f_notice", org.jooq.impl.SQLDataType.VARCHAR.length(500).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "学习团队公告");

    /**
     * The column <code>train.t_study_team.f_type</code>. 学习班类型 0-网格 1-其他
     */
    public final TableField<StudyTeamRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "学习班类型 0-网格 1-其他");

    /**
     * The column <code>train.t_study_team.f_status</code>. 状态 0-禁用 1-启用
     */
    public final TableField<StudyTeamRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "状态 0-禁用 1-启用");

    /**
     * The column <code>train.t_study_team.f_create_member_id</code>. 创建人id
     */
    public final TableField<StudyTeamRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "创建人id");

    /**
     * The column <code>train.t_study_team.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team</code> table reference
     */
    public StudyTeam() {
        this("t_study_team", null);
    }

    /**
     * Create an aliased <code>train.t_study_team</code> table reference
     */
    public StudyTeam(String alias) {
        this(alias, STUDY_TEAM);
    }

    private StudyTeam(String alias, Table<StudyTeamRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeam(String alias, Table<StudyTeamRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习团队表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamRecord>>asList(Keys.KEY_T_STUDY_TEAM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeam as(String alias) {
        return new StudyTeam(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeam rename(String name) {
        return new StudyTeam(name, null);
    }
}
