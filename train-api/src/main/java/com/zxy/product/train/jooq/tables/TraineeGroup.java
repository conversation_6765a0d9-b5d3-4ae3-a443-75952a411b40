/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TraineeGroupRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训学员分组表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TraineeGroup extends TableImpl<TraineeGroupRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_trainee_group</code>
     */
    public static final TraineeGroup TRAINEE_GROUP = new TraineeGroup();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TraineeGroupRecord> getRecordType() {
        return TraineeGroupRecord.class;
    }

    /**
     * The column <code>train.t_trainee_group.f_id</code>. 表id
     */
    public final TableField<TraineeGroupRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_trainee_group.f_name</code>. 分组名称
     */
    public final TableField<TraineeGroupRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "分组名称");

    /**
     * The column <code>train.t_trainee_group.f_trainee_number</code>. 学员人数
     */
    public final TableField<TraineeGroupRecord, Integer> TRAINEE_NUMBER = createField("f_trainee_number", org.jooq.impl.SQLDataType.INTEGER, this, "学员人数");

    /**
     * The column <code>train.t_trainee_group.f_class_id</code>. 班级id
     */
    public final TableField<TraineeGroupRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级id");

    /**
     * The column <code>train.t_trainee_group.f_sort</code>. 排序
     */
    public final TableField<TraineeGroupRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_trainee_group.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public final TableField<TraineeGroupRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态：0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_trainee_group.f_create_time</code>. 创建时间
     */
    public final TableField<TraineeGroupRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_trainee_group.f_create_member_id</code>. 创建人id
     */
    public final TableField<TraineeGroupRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人id");

    /**
     * Create a <code>train.t_trainee_group</code> table reference
     */
    public TraineeGroup() {
        this("t_trainee_group", null);
    }

    /**
     * Create an aliased <code>train.t_trainee_group</code> table reference
     */
    public TraineeGroup(String alias) {
        this(alias, TRAINEE_GROUP);
    }

    private TraineeGroup(String alias, Table<TraineeGroupRecord> aliased) {
        this(alias, aliased, null);
    }

    private TraineeGroup(String alias, Table<TraineeGroupRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训学员分组表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TraineeGroupRecord> getPrimaryKey() {
        return Keys.KEY_T_TRAINEE_GROUP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TraineeGroupRecord>> getKeys() {
        return Arrays.<UniqueKey<TraineeGroupRecord>>asList(Keys.KEY_T_TRAINEE_GROUP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TraineeGroup as(String alias) {
        return new TraineeGroup(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TraineeGroup rename(String name) {
        return new TraineeGroup(name, null);
    }
}
