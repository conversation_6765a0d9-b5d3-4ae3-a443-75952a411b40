package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.DeleteDataTrainEntity;
import org.jooq.impl.TableImpl;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class DeleteDataTrain extends DeleteDataTrainEntity {

    private static final String DATABASE_NAME = "train";

    public static final String TRAINEE = "t_trainee";

    public static DeleteDataTrain getDeleteData(String tableName, String businessId, String companyId){
        DeleteDataTrain deleteData = new DeleteDataTrain();
        deleteData.setDatabaseName(DATABASE_NAME);
        deleteData.setBusinessId(businessId);
        deleteData.setTableName(tableName);
        deleteData.setBusinessId(businessId);
        deleteData.setCompanyId(companyId);
        deleteData.forInsert();
        return deleteData;
    }
    /**
     * 分表的表名写起来麻烦 干脆这样
     * 包装一层的意义不大，就是想写点注释
     * @param table
     * @return
     */
    public static String getTableName(TableImpl<?> table){
        return Objects.nonNull(table) ? table.getName() : "other";
    }
    public static List<DeleteDataTrain> getDeleteDataList(String tableName, List<String> businessIds, String companyId) {
        return businessIds.stream().map(id -> getDeleteData(tableName, id, companyId)).collect(Collectors.toList());
    }
}
