package com.zxy.product.train.content;

import java.util.HashMap;
import java.util.Map;

public enum DepartmentEnum {
    onlineLearningDept("在线学习部", "100692824"),
    teachingSupportDept("教学支撑部", "145066603"),
    teachingDept("教学部", "96599"),
    partySchoolEducationDept("党校教育部", "96602");

    private String code;
    private String value;

    DepartmentEnum(String value, String code){
        this.code = code;
        this.value = value;
    }

    public static Map<String, String> departmentMap = new HashMap<>(18);

    static {
        for (DepartmentEnum type : values()) {
            departmentMap.put(type.getValue(), type.getCode());
        }
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
