package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.LecturerCourseConfigEntity;

public class LecturerCourseConfig extends LecturerCourseConfigEntity{
	
	private static final long serialVersionUID = 5130274139896180762L;

	//类型【0：讲师专业序列；1：课程分类】
	public final static Integer TYPE_LECTURER = 0;
	public final static Integer TYPE_COURSE = 1;

	/**
	 * 专业序列：综合
	 */
	public final static String SEQUENCE_COMPREHENSIVE = "1";
	/**
	 * 专业序列：其他
	 */
	public final static String SEQUENCE_OTHER = "2";
	/**
	 * 课程分类：综合
	 */
	public final static String TYPE_COMPREHENSIVE = "3";
	/**
	 * 课程分类：其他
	 */
	public final static String TYPE_OTHER = "4";
	
	
	private String parentName;
	

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}
	
}
