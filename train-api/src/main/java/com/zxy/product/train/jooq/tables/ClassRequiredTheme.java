/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassRequiredThemeRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassRequiredTheme extends TableImpl<ClassRequiredThemeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_required_theme</code>
     */
    public static final ClassRequiredTheme CLASS_REQUIRED_THEME = new ClassRequiredTheme();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassRequiredThemeRecord> getRecordType() {
        return ClassRequiredThemeRecord.class;
    }

    /**
     * The column <code>train.t_class_required_theme.f_id</code>.
     */
    public final TableField<ClassRequiredThemeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_required_theme.f_name</code>. 主题名称
     */
    public final TableField<ClassRequiredThemeRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(80), this, "主题名称");

    /**
     * The column <code>train.t_class_required_theme.f_create_time</code>. 创建时间
     */
    public final TableField<ClassRequiredThemeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_required_theme.f_create_member</code>. 创建人id
     */
    public final TableField<ClassRequiredThemeRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人id");

    /**
     * The column <code>train.t_class_required_theme.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassRequiredThemeRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_required_theme.f_config</code>. 0 选修; 1 必修
     */
    public final TableField<ClassRequiredThemeRecord, Integer> CONFIG = createField("f_config", org.jooq.impl.SQLDataType.INTEGER, this, "0 选修; 1 必修");

    /**
     * The column <code>train.t_class_required_theme.f_day</code>. 必学天数
     */
    public final TableField<ClassRequiredThemeRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER, this, "必学天数");

    /**
     * Create a <code>train.t_class_required_theme</code> table reference
     */
    public ClassRequiredTheme() {
        this("t_class_required_theme", null);
    }

    /**
     * Create an aliased <code>train.t_class_required_theme</code> table reference
     */
    public ClassRequiredTheme(String alias) {
        this(alias, CLASS_REQUIRED_THEME);
    }

    private ClassRequiredTheme(String alias, Table<ClassRequiredThemeRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassRequiredTheme(String alias, Table<ClassRequiredThemeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassRequiredThemeRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_REQUIRED_THEME_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassRequiredThemeRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassRequiredThemeRecord>>asList(Keys.KEY_T_CLASS_REQUIRED_THEME_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassRequiredTheme as(String alias) {
        return new ClassRequiredTheme(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassRequiredTheme rename(String name) {
        return new ClassRequiredTheme(name, null);
    }
}
