/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassEvaluate extends Serializable {

    /**
     * Setter for <code>train.t_class_evaluate.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_evaluate.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_evaluate.f_type</code>. 问卷类型（1考试 2调研  3评估  4学员满意度评估 5需求方满意度评估）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_class_evaluate.f_type</code>. 问卷类型（1考试 2调研  3评估  4学员满意度评估 5需求方满意度评估）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_class_evaluate.f_resource_id</code>. 问卷ID
     */
    public void setResourceId(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_resource_id</code>. 问卷ID
     */
    public String getResourceId();

    /**
     * Setter for <code>train.t_class_evaluate.f_resource_name</code>. 问卷名称
     */
    public void setResourceName(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_resource_name</code>. 问卷名称
     */
    public String getResourceName();

    /**
     * Setter for <code>train.t_class_evaluate.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_evaluate.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_evaluate.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_evaluate.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_evaluate.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_evaluate.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_class_evaluate.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_class_evaluate.f_end_time</code>. 结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_class_evaluate.f_end_time</code>. 结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_class_evaluate.f_release</code>. 是否发布（0未发布，1发布）
     */
    public void setRelease(Integer value);

    /**
     * Getter for <code>train.t_class_evaluate.f_release</code>. 是否发布（0未发布，1发布）
     */
    public Integer getRelease();

    /**
     * Setter for <code>train.t_class_evaluate.f_remarks</code>. 备注
     */
    public void setRemarks(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_remarks</code>. 备注
     */
    public String getRemarks();

    /**
     * Setter for <code>train.t_class_evaluate.f_is_add</code>. 是否为新增（0否  1是）
     */
    public void setIsAdd(Integer value);

    /**
     * Getter for <code>train.t_class_evaluate.f_is_add</code>. 是否为新增（0否  1是）
     */
    public Integer getIsAdd();

    /**
     * Setter for <code>train.t_class_evaluate.f_paper_class_id</code>. 试卷ID（type为1时有效）
     */
    public void setPaperClassId(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_paper_class_id</code>. 试卷ID（type为1时有效）
     */
    public String getPaperClassId();

    /**
     * Setter for <code>train.t_class_evaluate.f_source_id</code>. 选择的调研或评估原有的id
     */
    public void setSourceId(String value);

    /**
     * Getter for <code>train.t_class_evaluate.f_source_id</code>. 选择的调研或评估原有的id
     */
    public String getSourceId();

    /**
     * Setter for <code>train.t_class_evaluate.f_show_answer_rule</code>. 显示答案的规则
     */
    public void setShowAnswerRule(Integer value);

    /**
     * Getter for <code>train.t_class_evaluate.f_show_answer_rule</code>. 显示答案的规则
     */
    public Integer getShowAnswerRule();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassEvaluate
     */
    public void from(IClassEvaluate from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassEvaluate
     */
    public <E extends IClassEvaluate> E into(E into);
}
