/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassBusinessProgressRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassBusinessProgress extends TableImpl<ClassBusinessProgressRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_business_progress</code>
     */
    public static final ClassBusinessProgress CLASS_BUSINESS_PROGRESS = new ClassBusinessProgress();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassBusinessProgressRecord> getRecordType() {
        return ClassBusinessProgressRecord.class;
    }

    /**
     * The column <code>train.t_class_business_progress.f_id</code>. 主键
     */
    public final TableField<ClassBusinessProgressRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_class_business_progress.f_member_id</code>. 用户ID
     */
    public final TableField<ClassBusinessProgressRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户ID");

    /**
     * The column <code>train.t_class_business_progress.f_class_id</code>. 班级ID
     */
    public final TableField<ClassBusinessProgressRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_business_progress.f_class_business_id</code>. 调研，评估或考试的ID，
     */
    public final TableField<ClassBusinessProgressRecord, String> CLASS_BUSINESS_ID = createField("f_class_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "调研，评估或考试的ID，");

    /**
     * The column <code>train.t_class_business_progress.f_finish_status</code>. 参与状态：0--未参与，1--已完成，2--待评卷，3，未及格
     */
    public final TableField<ClassBusinessProgressRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER, this, "参与状态：0--未参与，1--已完成，2--待评卷，3，未及格");

    /**
     * The column <code>train.t_class_business_progress.f_score</code>. 考试得分
     */
    public final TableField<ClassBusinessProgressRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER, this, "考试得分");

    /**
     * The column <code>train.t_class_business_progress.f_create_time</code>. 创建时间
     */
    public final TableField<ClassBusinessProgressRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_class_business_progress</code> table reference
     */
    public ClassBusinessProgress() {
        this("t_class_business_progress", null);
    }

    /**
     * Create an aliased <code>train.t_class_business_progress</code> table reference
     */
    public ClassBusinessProgress(String alias) {
        this(alias, CLASS_BUSINESS_PROGRESS);
    }

    private ClassBusinessProgress(String alias, Table<ClassBusinessProgressRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassBusinessProgress(String alias, Table<ClassBusinessProgressRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassBusinessProgressRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_BUSINESS_PROGRESS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassBusinessProgressRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassBusinessProgressRecord>>asList(Keys.KEY_T_CLASS_BUSINESS_PROGRESS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassBusinessProgress as(String alias) {
        return new ClassBusinessProgress(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassBusinessProgress rename(String name) {
        return new ClassBusinessProgress(name, null);
    }
}
