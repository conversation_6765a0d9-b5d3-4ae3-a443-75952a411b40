/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SignLeaveRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 请假表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SignLeave extends TableImpl<SignLeaveRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_sign_leave</code>
     */
    public static final SignLeave SIGN_LEAVE = new SignLeave();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SignLeaveRecord> getRecordType() {
        return SignLeaveRecord.class;
    }

    /**
     * The column <code>train.t_sign_leave.f_id</code>. 主键
     */
    public final TableField<SignLeaveRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_sign_leave.f_member_id</code>. 学员ID
     */
    public final TableField<SignLeaveRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "学员ID");

    /**
     * The column <code>train.t_sign_leave.f_sign_id</code>. 签到表ID
     */
    public final TableField<SignLeaveRecord, String> SIGN_ID = createField("f_sign_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "签到表ID");

    /**
     * The column <code>train.t_sign_leave.f_state</code>. 状态 0待审批 1同意 2不同意
     */
    public final TableField<SignLeaveRecord, Integer> STATE = createField("f_state", org.jooq.impl.SQLDataType.INTEGER, this, "状态 0待审批 1同意 2不同意");

    /**
     * The column <code>train.t_sign_leave.f_reason</code>. 请假原因
     */
    public final TableField<SignLeaveRecord, String> REASON = createField("f_reason", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "请假原因");

    /**
     * The column <code>train.t_sign_leave.f_create_time</code>. 创建时间
     */
    public final TableField<SignLeaveRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_sign_leave.f_approval_member</code>. 审批人
     */
    public final TableField<SignLeaveRecord, String> APPROVAL_MEMBER = createField("f_approval_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "审批人");

    /**
     * The column <code>train.t_sign_leave.f_approval_time</code>. 审核时间
     */
    public final TableField<SignLeaveRecord, Long> APPROVAL_TIME = createField("f_approval_time", org.jooq.impl.SQLDataType.BIGINT, this, "审核时间");

    /**
     * Create a <code>train.t_sign_leave</code> table reference
     */
    public SignLeave() {
        this("t_sign_leave", null);
    }

    /**
     * Create an aliased <code>train.t_sign_leave</code> table reference
     */
    public SignLeave(String alias) {
        this(alias, SIGN_LEAVE);
    }

    private SignLeave(String alias, Table<SignLeaveRecord> aliased) {
        this(alias, aliased, null);
    }

    private SignLeave(String alias, Table<SignLeaveRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "请假表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SignLeaveRecord> getPrimaryKey() {
        return Keys.KEY_T_SIGN_LEAVE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SignLeaveRecord>> getKeys() {
        return Arrays.<UniqueKey<SignLeaveRecord>>asList(Keys.KEY_T_SIGN_LEAVE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SignLeave as(String alias) {
        return new SignLeave(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SignLeave rename(String name) {
        return new SignLeave(name, null);
    }
}
