/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.MessageDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MessageDetail extends TableImpl<MessageDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_message_detail</code>
     */
    public static final MessageDetail MESSAGE_DETAIL = new MessageDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MessageDetailRecord> getRecordType() {
        return MessageDetailRecord.class;
    }

    /**
     * The column <code>train.t_message_detail.f_id</code>. id
     */
    public final TableField<MessageDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "id");

    /**
     * The column <code>train.t_message_detail.f_message_record_id</code>. 短信表主键
     */
    public final TableField<MessageDetailRecord, String> MESSAGE_RECORD_ID = createField("f_message_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "短信表主键");

    /**
     * The column <code>train.t_message_detail.f_recipient</code>. 收件人
     */
    public final TableField<MessageDetailRecord, String> RECIPIENT = createField("f_recipient", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "收件人");

    /**
     * The column <code>train.t_message_detail.f_create_time</code>. 创建时间
     */
    public final TableField<MessageDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_message_detail</code> table reference
     */
    public MessageDetail() {
        this("t_message_detail", null);
    }

    /**
     * Create an aliased <code>train.t_message_detail</code> table reference
     */
    public MessageDetail(String alias) {
        this(alias, MESSAGE_DETAIL);
    }

    private MessageDetail(String alias, Table<MessageDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private MessageDetail(String alias, Table<MessageDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MessageDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_MESSAGE_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MessageDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<MessageDetailRecord>>asList(Keys.KEY_T_MESSAGE_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MessageDetail as(String alias) {
        return new MessageDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MessageDetail rename(String name) {
        return new MessageDetail(name, null);
    }
}
