package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.BusDetailEntity;

/**
 * liushunan
 *
 */
public class BusDetail extends BusDetailEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = 5139743321626343728L;

	private Member member;// 人员对象

	private Bus bus;//bus对象

	private String option;
	private String explain;//选项的解释
	private String name;//选项的名称

	public Member getMember() {
		return member;
	}


	public void setMember(Member member) {
		this.member = member;
	}

	public String getOption() {
		return option;
	}


	public void setOption(String option) {
		this.option = option;
	}


	public Bus getBus() {
		return bus;
	}

	public void setBus(Bus bus) {
		this.bus = bus;
	}

	public String getExplain() {
		return explain;
	}

	public void setExplain(String explain) {
		this.explain = explain;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
