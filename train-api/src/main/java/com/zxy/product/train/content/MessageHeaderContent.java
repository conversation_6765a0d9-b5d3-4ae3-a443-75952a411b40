package com.zxy.product.train.content;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public class MessageHeaderContent {
    public static final String ID  = "id";
    public static final String IDS  = "ids";
    public static final String CLASSID  = "classid";//班级ID
    public static final String RESEARCHQUESTIONARYID  = "researchquestionaryid";//问卷ID
    public static final String SENDER = "sender";//问卷ID
//    public static final String PROJECTMESSAGE = "projectmessage";
    public static final String QUESTIONNAIRECLASSID  = "questionnairidclassid";//保存课程满意度
    public static final String TASKID = "taskid";//作业id
    public static final String MEMBERID = "memberId";//学员id
    public static final String MEMBER_ID = "member_id";//学员id
    public static final String LEADER_MEMBER_ID = "leader_member_id";//领导id
    public static final String EMPLOYER_NAME = "employer_name";
    public static final String IDENTY_ID = "identy_id";//身份属性ID
    public static final String TYPE = "type";
    public static final String NAME = "name";//作业计划名称
    public static final String START_TIME = "startTime";//报道日
    public static final String END_TIME = "endTime";//返程日
    public static final String ANSWE_STRING = "answerString";//保存学员满意度答案
    public static final String PATH_PREFIX = "pathPrefix"; //文件组
    public static final String EXAM_ID = "examId";

    public static final String LECTURER_ID = "lecturerId"; //讲师ID
    public static final String COURSE_ID = "courseId"; //面授课程ID
    public static final String THEME_UID="themeId";
    public static final String BUSINESS_ID = "business_id"; //业务id
    public static final String TIME = "time";

    public static final String CHAT_GROUP_ID = "chat_group_id";// IM群聊ID

    /**首页配置消息队列传递参数*/
    public static final String HOME_CFG_ID="homeCfgId";
    public static final String CLIENT_TYPE="clientType";
}
