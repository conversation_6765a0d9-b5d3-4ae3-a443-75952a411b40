/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 团队学习班-活动相册资料表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamActivityPhotos extends Serializable {

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_activity_id</code>. 活动id
     */
    public void setActivityId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_activity_id</code>. 活动id
     */
    public String getActivityId();

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_img_path</code>. 相册路径
     */
    public void setImgPath(String value);

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_img_path</code>. 相册路径
     */
    public String getImgPath();

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_upload_member_id</code>. 上传人
     */
    public void setUploadMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_upload_member_id</code>. 上传人
     */
    public String getUploadMemberId();

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamActivityPhotos
     */
    public void from(IStudyTeamActivityPhotos from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamActivityPhotos
     */
    public <E extends IStudyTeamActivityPhotos> E into(E into);
}
