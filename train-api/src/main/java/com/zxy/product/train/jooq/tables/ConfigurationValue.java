/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ConfigurationValueRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConfigurationValue extends TableImpl<ConfigurationValueRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_configuration_value</code>
     */
    public static final ConfigurationValue CONFIGURATION_VALUE = new ConfigurationValue();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConfigurationValueRecord> getRecordType() {
        return ConfigurationValueRecord.class;
    }

    /**
     * The column <code>train.t_configuration_value.f_id</code>. 主键
     */
    public final TableField<ConfigurationValueRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_configuration_value.f_configuration_id</code>. 关联配置表ID
     */
    public final TableField<ConfigurationValueRecord, String> CONFIGURATION_ID = createField("f_configuration_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联配置表ID");

    /**
     * The column <code>train.t_configuration_value.f_type_id</code>. 关联类型ID
     */
    public final TableField<ConfigurationValueRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型ID");

    /**
     * The column <code>train.t_configuration_value.f_create_time</code>. 创建时间
     */
    public final TableField<ConfigurationValueRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_configuration_value.f_create_member</code>. 创建人ID
     */
    public final TableField<ConfigurationValueRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ConfigurationValueRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_configuration_value.f_name</code>. 名称
     */
    public final TableField<ConfigurationValueRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "名称");

    /**
     * The column <code>train.t_configuration_value.f_coding</code>. 编码
     */
    public final TableField<ConfigurationValueRecord, String> CODING = createField("f_coding", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "编码");

    /**
     * The column <code>train.t_configuration_value.f_sort</code>. 排序
     */
    public final TableField<ConfigurationValueRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * Create a <code>train.t_configuration_value</code> table reference
     */
    public ConfigurationValue() {
        this("t_configuration_value", null);
    }

    /**
     * Create an aliased <code>train.t_configuration_value</code> table reference
     */
    public ConfigurationValue(String alias) {
        this(alias, CONFIGURATION_VALUE);
    }

    private ConfigurationValue(String alias, Table<ConfigurationValueRecord> aliased) {
        this(alias, aliased, null);
    }

    private ConfigurationValue(String alias, Table<ConfigurationValueRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ConfigurationValueRecord> getPrimaryKey() {
        return Keys.KEY_T_CONFIGURATION_VALUE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ConfigurationValueRecord>> getKeys() {
        return Arrays.<UniqueKey<ConfigurationValueRecord>>asList(Keys.KEY_T_CONFIGURATION_VALUE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ConfigurationValue as(String alias) {
        return new ConfigurationValue(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ConfigurationValue rename(String name) {
        return new ConfigurationValue(name, null);
    }
}
