/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IProjectApproval extends Serializable {

    /**
     * Setter for <code>train.t_project_approval.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_project_approval.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_project_approval.f_project_id</code>. 计划ID
     */
    public void setProjectId(String value);

    /**
     * Getter for <code>train.t_project_approval.f_project_id</code>. 计划ID
     */
    public String getProjectId();

    /**
     * Setter for <code>train.t_project_approval.f_status</code>. 状态（1待预订 2待审核 3同意申请 4资源已满 5不同意）
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_project_approval.f_status</code>. 状态（1待预订 2待审核 3同意申请 4资源已满 5不同意）
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_project_approval.f_suggestion</code>. 审核意见
     */
    public void setSuggestion(String value);

    /**
     * Getter for <code>train.t_project_approval.f_suggestion</code>. 审核意见
     */
    public String getSuggestion();

    /**
     * Setter for <code>train.t_project_approval.f_approval_member</code>. 审核人
     */
    public void setApprovalMember(String value);

    /**
     * Getter for <code>train.t_project_approval.f_approval_member</code>. 审核人
     */
    public String getApprovalMember();

    /**
     * Setter for <code>train.t_project_approval.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_project_approval.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_project_approval.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_project_approval.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_project_approval.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_project_approval.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IProjectApproval
     */
    public void from(IProjectApproval from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IProjectApproval
     */
    public <E extends IProjectApproval> E into(E into);
}
