/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 培训学员分组表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITraineeGroup extends Serializable {

    /**
     * Setter for <code>train.t_trainee_group.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_trainee_group.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_trainee_group.f_name</code>. 分组名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_trainee_group.f_name</code>. 分组名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_trainee_group.f_trainee_number</code>. 学员人数
     */
    public void setTraineeNumber(Integer value);

    /**
     * Getter for <code>train.t_trainee_group.f_trainee_number</code>. 学员人数
     */
    public Integer getTraineeNumber();

    /**
     * Setter for <code>train.t_trainee_group.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_trainee_group.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_trainee_group.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_trainee_group.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_trainee_group.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_trainee_group.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_trainee_group.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_trainee_group.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_trainee_group.f_create_member_id</code>. 创建人id
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_trainee_group.f_create_member_id</code>. 创建人id
     */
    public String getCreateMemberId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITraineeGroup
     */
    public void from(ITraineeGroup from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITraineeGroup
     */
    public <E extends ITraineeGroup> E into(E into);
}
