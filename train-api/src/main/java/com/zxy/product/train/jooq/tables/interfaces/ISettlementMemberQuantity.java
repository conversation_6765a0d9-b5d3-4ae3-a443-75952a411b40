/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 培训班结算人数配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISettlementMemberQuantity extends Serializable {

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_date</code>. 培训日期
     */
    public void setDate(Long value);

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_date</code>. 培训日期
     */
    public Long getDate();

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_quantity</code>. 培训人数
     */
    public void setQuantity(Integer value);

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_quantity</code>. 培训人数
     */
    public Integer getQuantity();

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISettlementMemberQuantity
     */
    public void from(ISettlementMemberQuantity from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISettlementMemberQuantity
     */
    public <E extends ISettlementMemberQuantity> E into(E into);
}
