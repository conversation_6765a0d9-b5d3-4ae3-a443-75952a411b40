/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.MemberConfigRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberConfig extends TableImpl<MemberConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_member_config</code>
     */
    public static final MemberConfig MEMBER_CONFIG = new MemberConfig();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MemberConfigRecord> getRecordType() {
        return MemberConfigRecord.class;
    }

    /**
     * The column <code>train.t_member_config.f_id</code>.
     */
    public final TableField<MemberConfigRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_member_config.f_create_time</code>. 创建时间
     */
    public final TableField<MemberConfigRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_member_config.f_key</code>. 1=国籍;2=民族;3=政治面貌;4=学历;5=证件类型;6=员工类型;7=在职状态;8=职级;9=条线
     */
    public final TableField<MemberConfigRecord, Integer> KEY = createField("f_key", org.jooq.impl.SQLDataType.INTEGER, this, "1=国籍;2=民族;3=政治面貌;4=学历;5=证件类型;6=员工类型;7=在职状态;8=职级;9=条线");

    /**
     * The column <code>train.t_member_config.f_value</code>. 对应的值
     */
    public final TableField<MemberConfigRecord, String> VALUE = createField("f_value", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "对应的值");

    /**
     * The column <code>train.t_member_config.f_init</code>. 是否为初始化数据(1=不是,0=是)
     */
    public final TableField<MemberConfigRecord, Integer> INIT = createField("f_init", org.jooq.impl.SQLDataType.INTEGER, this, "是否为初始化数据(1=不是,0=是)");

    /**
     * The column <code>train.t_member_config.f_ext</code>. 扩展字段(用于查询比如在职的id)
     */
    public final TableField<MemberConfigRecord, String> EXT = createField("f_ext", org.jooq.impl.SQLDataType.VARCHAR.length(1), this, "扩展字段(用于查询比如在职的id)");

    /**
     * The column <code>train.t_member_config.f_organization_id</code>. 所属组织id
     */
    public final TableField<MemberConfigRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所属组织id");

    /**
     * Create a <code>train.t_member_config</code> table reference
     */
    public MemberConfig() {
        this("t_member_config", null);
    }

    /**
     * Create an aliased <code>train.t_member_config</code> table reference
     */
    public MemberConfig(String alias) {
        this(alias, MEMBER_CONFIG);
    }

    private MemberConfig(String alias, Table<MemberConfigRecord> aliased) {
        this(alias, aliased, null);
    }

    private MemberConfig(String alias, Table<MemberConfigRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MemberConfigRecord> getPrimaryKey() {
        return Keys.KEY_T_MEMBER_CONFIG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MemberConfigRecord>> getKeys() {
        return Arrays.<UniqueKey<MemberConfigRecord>>asList(Keys.KEY_T_MEMBER_CONFIG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberConfig as(String alias) {
        return new MemberConfig(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MemberConfig rename(String name) {
        return new MemberConfig(name, null);
    }
}
