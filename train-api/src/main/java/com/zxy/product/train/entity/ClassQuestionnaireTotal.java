package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassQuestionnaireTotalEntity;

/**
 * 满意度问卷统计
 * <AUTHOR>
 *
 */
public class ClassQuestionnaireTotal extends ClassQuestionnaireTotalEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = -6554863992116682732L;
	/** 课程师资评价统计(第二个sheet) */
	public static final String CLASS_QUESTIONNAIRE_COURSE_TOTAL = "class_questionnaire_course_total";
	/** 总体评价统计(第一个sheet) */
	public static final String CLASS_QUESTIONNAIRE_POPULATION_TOTAL = "class_questionnaire_population_total";

	/** 总体评价统计(第三个sheet) */
	public static final String CLASS_QUESTIONNAIRE_POPULATION_PROPOSAL = "class_questionnaire_population_proposal";
	/** 课程师资评价(第四个sheet) */
	public static final String CLASS_QUESTIONNAIRE_COURSE_PROPOSAL = "class_questionnaire_course_proposal";

	private String courseName;
	private Integer courseType;
	private String lecturerName;
	private Integer answerOne;
	private Integer answerTwo;
	private Integer answerThree;
	private Integer answerFour;
	private Integer answerFive;
	private Integer answerSix;
	private Integer answerSeven;
	private Integer answerEight;
	private Integer answerNine;
	private Integer answerTen;
	/** 百分比整数 */
	private String subitemSatisfaction;
	private String questionName;
	private Short questionType;
	private Short questionTypeBranch;
	/** 课程内容 */
	private String courseContentTotal;
	/** 师资评价 */
	private String teacherPerformanceTotal;
	private String averageTotal;
	private Integer classType;
	public static final String ONE = "1";
	public static final String TWO = "2";
	public static final String THREE = "3";
	public static final String FOUR = "4";
	public static final String FIVE = "5";
	public static final String SIX = "6";
	public static final String SEVEN = "7";
	public static final String EIGHT = "8";
	public static final String NINE = "9";
	public static final String TEN = "10";

	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	public String getLecturerName() {
		return lecturerName;
	}
	public void setLecturerName(String lecturerName) {
		this.lecturerName = lecturerName;
	}
	public Integer getAnswerOne() {
		return answerOne;
	}
	public void setAnswerOne(Integer answerOne) {
		this.answerOne = answerOne;
	}
	public Integer getAnswerTwo() {
		return answerTwo;
	}
	public void setAnswerTwo(Integer answerTwo) {
		this.answerTwo = answerTwo;
	}
	public Integer getAnswerThree() {
		return answerThree;
	}
	public void setAnswerThree(Integer answerThree) {
		this.answerThree = answerThree;
	}
	public Integer getAnswerFour() {
		return answerFour;
	}
	public void setAnswerFour(Integer answerFour) {
		this.answerFour = answerFour;
	}
	public Integer getAnswerFive() {
		return answerFive;
	}
	public void setAnswerFive(Integer answerFive) {
		this.answerFive = answerFive;
	}
	public Integer getAnswerSix() {
		return answerSix;
	}
	public void setAnswerSix(Integer answerSix) {
		this.answerSix = answerSix;
	}
	public Integer getAnswerSeven() {
		return answerSeven;
	}
	public void setAnswerSeven(Integer answerSeven) {
		this.answerSeven = answerSeven;
	}
	public Integer getAnswerEight() {
		return answerEight;
	}
	public void setAnswerEight(Integer answerEight) {
		this.answerEight = answerEight;
	}
	public Integer getAnswerNine() {
		return answerNine;
	}
	public void setAnswerNine(Integer answerNine) {
		this.answerNine = answerNine;
	}
	public Integer getAnswerTen() {
		return answerTen;
	}
	public void setAnswerTen(Integer answerTen) {
		this.answerTen = answerTen;
	}
	public String getSubitemSatisfaction() {
		return subitemSatisfaction;
	}
	public void setSubitemSatisfaction(String subitemSatisfaction) {
		this.subitemSatisfaction = subitemSatisfaction;
	}
	public String getQuestionName() {
		return questionName;
	}
	public void setQuestionName(String questionName) {
		this.questionName = questionName;
	}
	public Short getQuestionType() {
		return questionType;
	}
	public void setQuestionType(Short questionType) {
		this.questionType = questionType;
	}
	public Short getQuestionTypeBranch() {
		return questionTypeBranch;
	}
	public void setQuestionTypeBranch(Short questionTypeBranch) {
		this.questionTypeBranch = questionTypeBranch;
	}
	public String getCourseContentTotal() {
		return courseContentTotal;
	}
	public void setCourseContentTotal(String courseContentTotal) {
		this.courseContentTotal = courseContentTotal;
	}
	public String getTeacherPerformanceTotal() {
		return teacherPerformanceTotal;
	}
	public void setTeacherPerformanceTotal(String teacherPerformanceTotal) {
		this.teacherPerformanceTotal = teacherPerformanceTotal;
	}
	public String getAverageTotal() {
		return averageTotal;
	}
	public void setAverageTotal(String averageTotal) {
		this.averageTotal = averageTotal;
	}

	public Integer getCourseType() {
		return courseType;
	}

	public void setCourseType(Integer courseType) {
		this.courseType = courseType;
	}

	/** 培训班类型 */
	public Integer getClassType() {
		return classType;
	}

	public void setClassType(Integer classType) {
		this.classType = classType;
	}
}
