/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 历史学员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudentHistory extends Serializable {

    /**
     * Setter for <code>train.t_student_history.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_student_history.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_student_history.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_student_history.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_student_history.f_person_id</code>. 用户ID
     */
    public void setPersonId(String value);

    /**
     * Getter for <code>train.t_student_history.f_person_id</code>. 用户ID
     */
    public String getPersonId();

    /**
     * Setter for <code>train.t_student_history.f_type</code>. 学员类型:0N
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_student_history.f_type</code>. 学员类型:0N
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_student_history.f_phone</code>. 手机号码
     */
    public void setPhone(String value);

    /**
     * Getter for <code>train.t_student_history.f_phone</code>. 手机号码
     */
    public String getPhone();

    /**
     * Setter for <code>train.t_student_history.f_email</code>. 邮箱
     */
    public void setEmail(String value);

    /**
     * Getter for <code>train.t_student_history.f_email</code>. 邮箱
     */
    public String getEmail();

    /**
     * Setter for <code>train.t_student_history.f_sex</code>. 性别:0男,1女
     */
    public void setSex(Integer value);

    /**
     * Getter for <code>train.t_student_history.f_sex</code>. 性别:0男,1女
     */
    public Integer getSex();

    /**
     * Setter for <code>train.t_student_history.f_nation</code>. 民族
     */
    public void setNation(String value);

    /**
     * Getter for <code>train.t_student_history.f_nation</code>. 民族
     */
    public String getNation();

    /**
     * Setter for <code>train.t_student_history.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_student_history.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_student_history.f_status</code>. 审核状态:1N,2W,3Y
     */
    public void setStatus(String value);

    /**
     * Getter for <code>train.t_student_history.f_status</code>. 审核状态:1N,2W,3Y
     */
    public String getStatus();

    /**
     * Setter for <code>train.t_student_history.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_student_history.f_sort</code>. 排序
     */
    public Integer getSort();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudentHistory
     */
    public void from(IStudentHistory from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudentHistory
     */
    public <E extends IStudentHistory> E into(E into);
}
