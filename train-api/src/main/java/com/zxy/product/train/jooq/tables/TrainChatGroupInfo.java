/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TrainChatGroupInfoRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训班群聊表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TrainChatGroupInfo extends TableImpl<TrainChatGroupInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_train_chat_group_info</code>
     */
    public static final TrainChatGroupInfo TRAIN_CHAT_GROUP_INFO = new TrainChatGroupInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TrainChatGroupInfoRecord> getRecordType() {
        return TrainChatGroupInfoRecord.class;
    }

    /**
     * The column <code>train.t_train_chat_group_info.f_id</code>.
     */
    public final TableField<TrainChatGroupInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_train_chat_group_info.f_class_id</code>. 班级ID
     */
    public final TableField<TrainChatGroupInfoRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班级ID");

    /**
     * The column <code>train.t_train_chat_group_info.f_chat_id</code>. 群聊ID
     */
    public final TableField<TrainChatGroupInfoRecord, String> CHAT_ID = createField("f_chat_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "群聊ID");

    /**
     * The column <code>train.t_train_chat_group_info.f_conversion_id</code>. 会话ID
     */
    public final TableField<TrainChatGroupInfoRecord, String> CONVERSION_ID = createField("f_conversion_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "会话ID");

    /**
     * The column <code>train.t_train_chat_group_info.f_chat_name</code>. 群聊名称
     */
    public final TableField<TrainChatGroupInfoRecord, String> CHAT_NAME = createField("f_chat_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "群聊名称");

    /**
     * The column <code>train.t_train_chat_group_info.f_member_id</code>. 班主任ID
     */
    public final TableField<TrainChatGroupInfoRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班主任ID");

    /**
     * The column <code>train.t_train_chat_group_info.f_create_time</code>. 创建时间
     */
    public final TableField<TrainChatGroupInfoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_train_chat_group_info.f_modify_date</code>. 修改时间
     */
    public final TableField<TrainChatGroupInfoRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>train.t_train_chat_group_info</code> table reference
     */
    public TrainChatGroupInfo() {
        this("t_train_chat_group_info", null);
    }

    /**
     * Create an aliased <code>train.t_train_chat_group_info</code> table reference
     */
    public TrainChatGroupInfo(String alias) {
        this(alias, TRAIN_CHAT_GROUP_INFO);
    }

    private TrainChatGroupInfo(String alias, Table<TrainChatGroupInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private TrainChatGroupInfo(String alias, Table<TrainChatGroupInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训班群聊表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TrainChatGroupInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_TRAIN_CHAT_GROUP_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TrainChatGroupInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<TrainChatGroupInfoRecord>>asList(Keys.KEY_T_TRAIN_CHAT_GROUP_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfo as(String alias) {
        return new TrainChatGroupInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TrainChatGroupInfo rename(String name) {
        return new TrainChatGroupInfo(name, null);
    }
}
