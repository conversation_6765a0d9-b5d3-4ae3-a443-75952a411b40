/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.QuestionnaireQuestionType;
import com.zxy.product.train.jooq.tables.interfaces.IQuestionnaireQuestionType;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 培训班满意度问卷类型问题表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionnaireQuestionTypeRecord extends UpdatableRecordImpl<QuestionnaireQuestionTypeRecord> implements Record10<String, String, String, String, String, Short, Short, Short, Short, Long>, IQuestionnaireQuestionType {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_id</code>. 系统ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_id</code>. 系统ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_questionnaire_code</code>. 问卷code码区分问卷
     */
    @Override
    public void setQuestionnaireCode(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_questionnaire_code</code>. 问卷code码区分问卷
     */
    @Override
    public String getQuestionnaireCode() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_questionnaire_name</code>. 问卷名称
     */
    @Override
    public void setQuestionnaireName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_questionnaire_name</code>. 问卷名称
     */
    @Override
    public String getQuestionnaireName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_group_name</code>. 分组名称
     */
    @Override
    public void setGroupName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_group_name</code>. 分组名称
     */
    @Override
    public String getGroupName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_question_name</code>. 问题名称
     */
    @Override
    public void setQuestionName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_question_name</code>. 问题名称
     */
    @Override
    public String getQuestionName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_level</code>. 分组等级【1：总体评估；2：课程师资评价；3：主观题】
     */
    @Override
    public void setLevel(Short value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_level</code>. 分组等级【1：总体评估；2：课程师资评价；3：主观题】
     */
    @Override
    public Short getLevel() {
        return (Short) get(5);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_type</code>. leve为2时有效；类型【
1 针对性：目标明确，满足需求；
2 实用性：紧贴实际，指导实践；
3 启发性：拓展视野，启发思考；
4 逻辑性：科学严谨，结构合理；
5 知识水平：底蕴深厚，见解独到；
6 实战经验：联系实际，解决问题；
7 授课技巧：形式多样，互动得当；
8 控场能力：掌控力强，张弛有度；
9 授课态度：认真敬业，关注学员；
10 过程控制：进度合理，重点突出；
11 对本课程有何优化意见建议
】
     */
    @Override
    public void setType(Short value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_type</code>. leve为2时有效；类型【
1 针对性：目标明确，满足需求；
2 实用性：紧贴实际，指导实践；
3 启发性：拓展视野，启发思考；
4 逻辑性：科学严谨，结构合理；
5 知识水平：底蕴深厚，见解独到；
6 实战经验：联系实际，解决问题；
7 授课技巧：形式多样，互动得当；
8 控场能力：掌控力强，张弛有度；
9 授课态度：认真敬业，关注学员；
10 过程控制：进度合理，重点突出；
11 对本课程有何优化意见建议
】
     */
    @Override
    public Short getType() {
        return (Short) get(6);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_type_branch</code>. leve为2时有效；【1：课程内容；2：师资表现；3：课程建议】
     */
    @Override
    public void setTypeBranch(Short value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_type_branch</code>. leve为2时有效；【1：课程内容；2：师资表现；3：课程建议】
     */
    @Override
    public Short getTypeBranch() {
        return (Short) get(7);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_order</code>. 排序
     */
    @Override
    public void setOrder(Short value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_order</code>. 排序
     */
    @Override
    public Short getOrder() {
        return (Short) get(8);
    }

    /**
     * Setter for <code>train.t_questionnaire_question_type.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_questionnaire_question_type.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Short, Short, Short, Short, Long> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Short, Short, Short, Short, Long> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.QUESTIONNAIRE_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.QUESTIONNAIRE_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.GROUP_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.QUESTION_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Short> field6() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.LEVEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Short> field7() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Short> field8() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.TYPE_BRANCH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Short> field9() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.ORDER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getQuestionnaireCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getQuestionnaireName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getGroupName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getQuestionName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Short value6() {
        return getLevel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Short value7() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Short value8() {
        return getTypeBranch();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Short value9() {
        return getOrder();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value2(String value) {
        setQuestionnaireCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value3(String value) {
        setQuestionnaireName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value4(String value) {
        setGroupName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value5(String value) {
        setQuestionName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value6(Short value) {
        setLevel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value7(Short value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value8(Short value) {
        setTypeBranch(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value9(Short value) {
        setOrder(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord value10(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionnaireQuestionTypeRecord values(String value1, String value2, String value3, String value4, String value5, Short value6, Short value7, Short value8, Short value9, Long value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IQuestionnaireQuestionType from) {
        setId(from.getId());
        setQuestionnaireCode(from.getQuestionnaireCode());
        setQuestionnaireName(from.getQuestionnaireName());
        setGroupName(from.getGroupName());
        setQuestionName(from.getQuestionName());
        setLevel(from.getLevel());
        setType(from.getType());
        setTypeBranch(from.getTypeBranch());
        setOrder(from.getOrder());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IQuestionnaireQuestionType> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached QuestionnaireQuestionTypeRecord
     */
    public QuestionnaireQuestionTypeRecord() {
        super(QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE);
    }

    /**
     * Create a detached, initialised QuestionnaireQuestionTypeRecord
     */
    public QuestionnaireQuestionTypeRecord(String id, String questionnaireCode, String questionnaireName, String groupName, String questionName, Short level, Short type, Short typeBranch, Short order, Long createTime) {
        super(QuestionnaireQuestionType.QUESTIONNAIRE_QUESTION_TYPE);

        set(0, id);
        set(1, questionnaireCode);
        set(2, questionnaireName);
        set(3, groupName);
        set(4, questionName);
        set(5, level);
        set(6, type);
        set(7, typeBranch);
        set(8, order);
        set(9, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.QuestionnaireQuestionTypeEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.QuestionnaireQuestionTypeEntity pojo = (com.zxy.product.train.jooq.tables.pojos.QuestionnaireQuestionTypeEntity)source;
        pojo.into(this);
        return true;
    }
}
