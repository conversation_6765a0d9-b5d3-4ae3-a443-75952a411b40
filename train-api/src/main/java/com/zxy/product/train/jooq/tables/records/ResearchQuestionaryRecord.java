/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ResearchQuestionary;
import com.zxy.product.train.jooq.tables.interfaces.IResearchQuestionary;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record21;
import org.jooq.Row21;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchQuestionaryRecord extends UpdatableRecordImpl<ResearchQuestionaryRecord> implements Record21<String, String, String, String, String, Long, Long, Integer, Integer, String, Long, String, Integer, Inte<PERSON>, <PERSON>, <PERSON>te<PERSON>, <PERSON>, Integer, String, Integer, String>, IResearchQuestionary {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_research_questionary.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_cover_id</code>. 封面
     */
    @Override
    public void setCoverId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_cover_id</code>. 封面
     */
    @Override
    public String getCoverId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_organization_id</code>. 所属组织
     */
    @Override
    public void setOrganizationId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_organization_id</code>. 所属组织
     */
    @Override
    public String getOrganizationId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_publish_organization_id</code>. 发布部门
     */
    @Override
    public void setPublishOrganizationId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_publish_organization_id</code>. 发布部门
     */
    @Override
    public String getPublishOrganizationId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_publish_member_id</code>. 发布人
     */
    @Override
    public void setPublishMemberId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_publish_member_id</code>. 发布人
     */
    @Override
    public String getPublishMemberId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_start_time</code>. 开始时间
     */
    @Override
    public void setStartTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_start_time</code>. 开始时间
     */
    @Override
    public Long getStartTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_end_time</code>. 结束时间
     */
    @Override
    public void setEndTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_end_time</code>. 结束时间
     */
    @Override
    public Long getEndTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_is_anonymity</code>. 是否匿名 1：是 0： 否
     */
    @Override
    public void setIsAnonymity(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_is_anonymity</code>. 是否匿名 1：是 0： 否
     */
    @Override
    public Integer getIsAnonymity() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_permit_view_count</code>. 是否允许查看统计结果 1:是 0： 否
     */
    @Override
    public void setPermitViewCount(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_permit_view_count</code>. 是否允许查看统计结果 1:是 0： 否
     */
    @Override
    public Integer getPermitViewCount() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_questionary_detail</code>. 问卷须知
     */
    @Override
    public void setQuestionaryDetail(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_questionary_detail</code>. 问卷须知
     */
    @Override
    public String getQuestionaryDetail() {
        return (String) get(9);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_name</code>. 名字
     */
    @Override
    public void setName(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_name</code>. 名字
     */
    @Override
    public String getName() {
        return (String) get(11);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_type</code>. 类型 1：调研 2：评估问卷
     */
    @Override
    public void setType(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_type</code>. 类型 1：调研 2：评估问卷
     */
    @Override
    public Integer getType() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_status</code>. 1:已发布；0：未发布
     */
    @Override
    public void setStatus(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_status</code>. 1:已发布；0：未发布
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_publish_time</code>. 发布时间
     */
    @Override
    public void setPublishTime(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_publish_time</code>. 发布时间
     */
    @Override
    public Long getPublishTime() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_push_personal_center</code>.
     */
    @Override
    public void setPushPersonalCenter(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_push_personal_center</code>.
     */
    @Override
    public Integer getPushPersonalCenter() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_class_id</code>. 班级ID
     */
    @Override
    public void setClassId(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_class_id</code>. 班级ID
     */
    @Override
    public String getClassId() {
        return (String) get(16);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_answer_paper_rule</code>. 答卷方式 1:一页多题 2：一页一题
     */
    @Override
    public void setAnswerPaperRule(Integer value) {
        set(17, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_answer_paper_rule</code>. 答卷方式 1:一页多题 2：一页一题
     */
    @Override
    public Integer getAnswerPaperRule() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_member_id</code>. 领导id
     */
    @Override
    public void setMemberId(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_member_id</code>. 领导id
     */
    @Override
    public String getMemberId() {
        return (String) get(18);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_is_ensemble</code>. type=8时生效 0：周满意度 1：整体满意度
     */
    @Override
    public void setIsEnsemble(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_is_ensemble</code>. type=8时生效 0：周满意度 1：整体满意度
     */
    @Override
    public Integer getIsEnsemble() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>train.t_research_questionary.f_class_theme_id</code>. 周主题id
     */
    @Override
    public void setClassThemeId(String value) {
        set(20, value);
    }

    /**
     * Getter for <code>train.t_research_questionary.f_class_theme_id</code>. 周主题id
     */
    @Override
    public String getClassThemeId() {
        return (String) get(20);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record21 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row21<String, String, String, String, String, Long, Long, Integer, Integer, String, Long, String, Integer, Integer, Long, Integer, String, Integer, String, Integer, String> fieldsRow() {
        return (Row21) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row21<String, String, String, String, String, Long, Long, Integer, Integer, String, Long, String, Integer, Integer, Long, Integer, String, Integer, String, Integer, String> valuesRow() {
        return (Row21) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.COVER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.PUBLISH_ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.PUBLISH_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.START_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.END_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.IS_ANONYMITY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.PERMIT_VIEW_COUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.QUESTIONARY_DETAIL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field13() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field14() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field15() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.PUBLISH_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field16() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.PUSH_PERSONAL_CENTER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field17() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field18() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.ANSWER_PAPER_RULE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field19() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field20() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.IS_ENSEMBLE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field21() {
        return ResearchQuestionary.RESEARCH_QUESTIONARY.CLASS_THEME_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getCoverId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getPublishOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getPublishMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getStartTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getEndTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getIsAnonymity();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getPermitViewCount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getQuestionaryDetail();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value13() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value14() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value15() {
        return getPublishTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value16() {
        return getPushPersonalCenter();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value17() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value18() {
        return getAnswerPaperRule();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value19() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value20() {
        return getIsEnsemble();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value21() {
        return getClassThemeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value2(String value) {
        setCoverId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value3(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value4(String value) {
        setPublishOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value5(String value) {
        setPublishMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value6(Long value) {
        setStartTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value7(Long value) {
        setEndTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value8(Integer value) {
        setIsAnonymity(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value9(Integer value) {
        setPermitViewCount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value10(String value) {
        setQuestionaryDetail(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value11(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value12(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value13(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value14(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value15(Long value) {
        setPublishTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value16(Integer value) {
        setPushPersonalCenter(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value17(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value18(Integer value) {
        setAnswerPaperRule(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value19(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value20(Integer value) {
        setIsEnsemble(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord value21(String value) {
        setClassThemeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionaryRecord values(String value1, String value2, String value3, String value4, String value5, Long value6, Long value7, Integer value8, Integer value9, String value10, Long value11, String value12, Integer value13, Integer value14, Long value15, Integer value16, String value17, Integer value18, String value19, Integer value20, String value21) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        value21(value21);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IResearchQuestionary from) {
        setId(from.getId());
        setCoverId(from.getCoverId());
        setOrganizationId(from.getOrganizationId());
        setPublishOrganizationId(from.getPublishOrganizationId());
        setPublishMemberId(from.getPublishMemberId());
        setStartTime(from.getStartTime());
        setEndTime(from.getEndTime());
        setIsAnonymity(from.getIsAnonymity());
        setPermitViewCount(from.getPermitViewCount());
        setQuestionaryDetail(from.getQuestionaryDetail());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setType(from.getType());
        setStatus(from.getStatus());
        setPublishTime(from.getPublishTime());
        setPushPersonalCenter(from.getPushPersonalCenter());
        setClassId(from.getClassId());
        setAnswerPaperRule(from.getAnswerPaperRule());
        setMemberId(from.getMemberId());
        setIsEnsemble(from.getIsEnsemble());
        setClassThemeId(from.getClassThemeId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IResearchQuestionary> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResearchQuestionaryRecord
     */
    public ResearchQuestionaryRecord() {
        super(ResearchQuestionary.RESEARCH_QUESTIONARY);
    }

    /**
     * Create a detached, initialised ResearchQuestionaryRecord
     */
    public ResearchQuestionaryRecord(String id, String coverId, String organizationId, String publishOrganizationId, String publishMemberId, Long startTime, Long endTime, Integer isAnonymity, Integer permitViewCount, String questionaryDetail, Long createTime, String name, Integer type, Integer status, Long publishTime, Integer pushPersonalCenter, String classId, Integer answerPaperRule, String memberId, Integer isEnsemble, String classThemeId) {
        super(ResearchQuestionary.RESEARCH_QUESTIONARY);

        set(0, id);
        set(1, coverId);
        set(2, organizationId);
        set(3, publishOrganizationId);
        set(4, publishMemberId);
        set(5, startTime);
        set(6, endTime);
        set(7, isAnonymity);
        set(8, permitViewCount);
        set(9, questionaryDetail);
        set(10, createTime);
        set(11, name);
        set(12, type);
        set(13, status);
        set(14, publishTime);
        set(15, pushPersonalCenter);
        set(16, classId);
        set(17, answerPaperRule);
        set(18, memberId);
        set(19, isEnsemble);
        set(20, classThemeId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ResearchQuestionaryEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ResearchQuestionaryEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ResearchQuestionaryEntity)source;
        pojo.into(this);
        return true;
    }
}
