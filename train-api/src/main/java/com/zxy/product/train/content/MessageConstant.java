package com.zxy.product.train.content;

/**
 * 消息模板code
 * <AUTHOR>
 *
 */
public class MessageConstant {

    //消息类型 -- 站内信
    public static final int MESSAGE_TYPE_INNER = 1;
    //消息类型 -- 邮件
    public static final int MESSAGE_TYPE_EMAIL = 2;
    //消息类型 -- app
    public static final int MESSAGE_TYPE_APP = 3;
    //消息类型 -- 短信
    public static final int MESSAGE_TYPE_SHORT = 4;

    //消息业务类型 -- 知识
    public static final int TYPE_KNOWLEDGE = 1;
    //消息业务类型  -- 课程
    public static final int TYPE_COURSE = 2;
    //消息业务类型 -- 专题
    public static final int TYPE_SUBJECT = 3;
    //消息业务类型 -- 考试
    public static final int TYPE_EXAM = 4;
    //消息业务类型 -- 调研
    public static final int TYPE_RESEARCH = 5;
    //消息业务类型 -- 培训
    public static final int TYPE_TRAIN = 6;
    //消息业务类型 -- 问吧
    public static final int TYPE_BAR = 7;
    //消息业务类型 -- 登录
    public static final int TYPE_LOGIN = 8;
    //消息业务类型 -- 微课大赛
    public static final int TYPE_COMPETITION = 9;
    //消息业务类型 -- 积分商城
    public static final int TYPE_MALL = 10;
    //消息业务类型 -- 学习
    public static final int TYPE_STUDY = 11;
    //消息业务类型 -- 幕客
    public static final int TYPE_MOOC = 12;
    //消息业务类型 -- 直播
    public static final int TYPE_LIVE = 13;
    //消息业务类型 -- 班级
    public static final int TYPE_CLASS = 14;
    //消息业务类型 -- 账号
    public static final int TYPE_ACCOUNT = 15;
    //消息业务类型 -- 监控
    public static final int TYPE_MONITOR = 16;

    //是否发送开关 -- 关闭
    public static final int SEND_STATUS_CLOSE = 2;
    //是否发送开关 -- 打开
    public static final int SEND_STATUS_OPEN = 1;

    //报名验证码
    public static final String CLASS_IDENTIFYING_CODE = "class_identifying_code";
    //发送报名验证码
    public static final String CLASS_IDENTIFYING_SEND = "class_identifying_send";

    //学员作业提交
    public static final String CLASS_HOMEWORK_DEAL = "class_homework_deal";
    //作业添加审批人
    public static final String CLASS_HOMEWORK_PLAN = "class_homework_plan";
    //成员管理发送短信
    public static final String CLASS_SMS_TRAINEE = "class_sms_trainee";
    //计划审核发送培训意见
    public static final String CLASS_SMS_AUDIT = "class_sms_audit";
    //班级问卷问卷提醒
    public static final String CLASS_SMS_QUESTIONNAIR = "class_sms_questionnair";
    //提醒需求方
    public static final String CLASS_SMS_REMIND = "class_sms_remind";
    //成员管理发送报道短信
    public static final String CLASS_SMS_TRAINEE_REPORT = "class_sms_trainee_report";
    //学员满意度评估
    public static final String CLASS_SATISFACTION = "class_satisfaction";
    //学员四度评估
    public static final String CLASS_FOURQUESTIONNAIR = "class_fourquestionnair";
    //学员能力习得评估
    public static final String CLASS_COMPETENCYQUESTIONNAIR = "class_competencyquestionnair";
    //学员上级领导问卷
    public static final String CLASS_LEADERQUESTIONNAIR = "class_leaderquestionnair";
    public static final String TEAM_LEARNING_MEMBER_FAILED = "team_learning_member_failed";
    public static final String TEAM_LEARNING_MEMBER_SUCCESS = "team_learning_member_success";

    //短信小程序状态值：0.未发送（“/”）
    public static final int SEND_SMS_BY_MOBILE_STATUS_DEFAULT = 0;
    //短信小程序状态值：1.回执返回状态“发送成功”
    public static final int SEND_SMS_BY_MOBILE_STATUS_RETURN_SUCCESS = 1;
    //短信小程序状态值：3.回执返回状态“发送失败”
    public static final int SEND_SMS_BY_MOBILE_STATUS_RETURN_FAIL = 3;
    //短信小程序状态值：4接口调用成功
    public static final int SEND_SMS_BY_MOBILE_STATUS_INTERFACE_SUCCESS = 4;
    //短信小程序状态值：5接口调用失败
    public static final int SEND_SMS_BY_MOBILE_STATUS_INTERFACE_FAIL = 5;


}
