/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassQuota extends Serializable {

    /**
     * Setter for <code>train.t_class_quota.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_quota.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_quota.f_class_id</code>. 培训班ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_quota.f_class_id</code>. 培训班ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_quota.f_type</code>. 类型（1整体配额  2分省配额）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_class_quota.f_type</code>. 类型（1整体配额  2分省配额）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_class_quota.f_is_auto_approve</code>. 是否自动审批（0否  1是）
     */
    public void setIsAutoApprove(Integer value);

    /**
     * Getter for <code>train.t_class_quota.f_is_auto_approve</code>. 是否自动审批（0否  1是）
     */
    public Integer getIsAutoApprove();

    /**
     * Setter for <code>train.t_class_quota.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_quota.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_quota.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_quota.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassQuota
     */
    public void from(IClassQuota from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassQuota
     */
    public <E extends IClassQuota> E into(E into);
}
