/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IConfigruationHistory;

import javax.annotation.Generated;


/**
 * 历史配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ConfigruationHistoryEntity extends BaseEntity implements IConfigruationHistory {

    private static final long serialVersionUID = 1L;

    private Integer type;
    private String  name;
    private Long    organizationId;

    public ConfigruationHistoryEntity() {}

    public ConfigruationHistoryEntity(ConfigruationHistoryEntity value) {
        this.type = value.type;
        this.name = value.name;
        this.organizationId = value.organizationId;
    }

    public ConfigruationHistoryEntity(
        String  id,
        Integer type,
        String  name,
        Long    organizationId
    ) {
        super.setId(id);
        this.type = type;
        this.name = name;
        this.organizationId = organizationId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Long getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ConfigruationHistoryEntity (");

        sb.append(getId());
        sb.append(", ").append(type);
        sb.append(", ").append(name);
        sb.append(", ").append(organizationId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IConfigruationHistory from) {
        setId(from.getId());
        setType(from.getType());
        setName(from.getName());
        setOrganizationId(from.getOrganizationId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IConfigruationHistory> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ConfigruationHistoryEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.ConfigruationHistoryRecord r = new com.zxy.product.train.jooq.tables.records.ConfigruationHistoryRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.ID, record.getValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.TYPE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.TYPE, record.getValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.TYPE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.NAME, record.getValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.NAME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.ORGANIZATION_ID, record.getValue(com.zxy.product.train.jooq.tables.ConfigruationHistory.CONFIGRUATION_HISTORY.ORGANIZATION_ID));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
