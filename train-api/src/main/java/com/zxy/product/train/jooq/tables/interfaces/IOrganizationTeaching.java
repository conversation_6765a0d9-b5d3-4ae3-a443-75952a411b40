/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 各单位授课记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IOrganizationTeaching extends Serializable {

    /**
     * Setter for <code>train.t_organization_teaching.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_organization_teaching.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_organization_teaching.f_course_name</code>. 授课名称
     */
    public void setCourseName(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_course_name</code>. 授课名称
     */
    public String getCourseName();

    /**
     * Setter for <code>train.t_organization_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public void setLecturerId(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public String getLecturerId();

    /**
     * Setter for <code>train.t_organization_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    public void setLecturerName(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    public String getLecturerName();

    /**
     * Setter for <code>train.t_organization_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    public void setLecturerPost(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    public String getLecturerPost();

    /**
     * Setter for <code>train.t_organization_teaching.f_reference_dollars</code>. 参考课酬(元)
     */
    public void setReferenceDollars(Double value);

    /**
     * Getter for <code>train.t_organization_teaching.f_reference_dollars</code>. 参考课酬(元)
     */
    public Double getReferenceDollars();

    /**
     * Setter for <code>train.t_organization_teaching.f_course_duration</code>. 课程时长
     */
    public void setCourseDuration(Double value);

    /**
     * Getter for <code>train.t_organization_teaching.f_course_duration</code>. 课程时长
     */
    public Double getCourseDuration();

    /**
     * Setter for <code>train.t_organization_teaching.f_organization_name</code>. 主办部门
     */
    public void setOrganizationName(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_organization_name</code>. 主办部门
     */
    public String getOrganizationName();

    /**
     * Setter for <code>train.t_organization_teaching.f_end_date</code>. 授课结束日期
     */
    public void setEndDate(Long value);

    /**
     * Getter for <code>train.t_organization_teaching.f_end_date</code>. 授课结束日期
     */
    public Long getEndDate();

    /**
     * Setter for <code>train.t_organization_teaching.f_start_date</code>. 授课起止日期
     */
    public void setStartDate(Long value);

    /**
     * Getter for <code>train.t_organization_teaching.f_start_date</code>. 授课起止日期
     */
    public Long getStartDate();

    /**
     * Setter for <code>train.t_organization_teaching.f_class_name</code>. 培训班名称
     */
    public void setClassName(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_class_name</code>. 培训班名称
     */
    public String getClassName();

    /**
     * Setter for <code>train.t_organization_teaching.f_class_member</code>. 培训班联系人
     */
    public void setClassMember(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_class_member</code>. 培训班联系人
     */
    public String getClassMember();

    /**
     * Setter for <code>train.t_organization_teaching.f_object</code>. 授课对象
     */
    public void setObject(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_object</code>. 授课对象
     */
    public String getObject();

    /**
     * Setter for <code>train.t_organization_teaching.f_attachment_id</code>. 附件id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_attachment_id</code>. 附件id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_organization_teaching.f_member_count</code>. 授课人数
     */
    public void setMemberCount(Integer value);

    /**
     * Getter for <code>train.t_organization_teaching.f_member_count</code>. 授课人数
     */
    public Integer getMemberCount();

    /**
     * Setter for <code>train.t_organization_teaching.f_pay</code>. 实付(元)
     */
    public void setPay(Double value);

    /**
     * Getter for <code>train.t_organization_teaching.f_pay</code>. 实付(元)
     */
    public Double getPay();

    /**
     * Setter for <code>train.t_organization_teaching.f_tax</code>. 税金(元)
     */
    public void setTax(Double value);

    /**
     * Getter for <code>train.t_organization_teaching.f_tax</code>. 税金(元)
     */
    public Double getTax();

    /**
     * Setter for <code>train.t_organization_teaching.f_remuneration</code>. 酬金
     */
    public void setRemuneration(Double value);

    /**
     * Getter for <code>train.t_organization_teaching.f_remuneration</code>. 酬金
     */
    public Double getRemuneration();

    /**
     * Setter for <code>train.t_organization_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public void setSatisfiedDegree(Double value);

    /**
     * Getter for <code>train.t_organization_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public Double getSatisfiedDegree();

    /**
     * Setter for <code>train.t_organization_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    public void setSatisfiedEvaluate(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    public String getSatisfiedEvaluate();

    /**
     * Setter for <code>train.t_organization_teaching.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    /**
     * Setter for <code>train.t_organization_teaching.f_teaching_type</code>. 教学教研类型
     */
    public void setTeachingType(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_teaching_type</code>. 教学教研类型
     */
    public String getTeachingType();

    /**
     * Setter for <code>train.t_organization_teaching.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_organization_teaching.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_organization_teaching.f_approval_time</code>. 审核时间
     */
    public void setApprovalTime(Long value);

    /**
     * Getter for <code>train.t_organization_teaching.f_approval_time</code>. 审核时间
     */
    public Long getApprovalTime();

    /**
     * Setter for <code>train.t_organization_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    public void setApprovalStatus(Integer value);

    /**
     * Getter for <code>train.t_organization_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    public Integer getApprovalStatus();

    /**
     * Setter for <code>train.t_organization_teaching.f_organization_id</code>. 归属部门
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_organization_id</code>. 归属部门
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_organization_teaching.f_create_member</code>. 创建ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_create_member</code>. 创建ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_organization_teaching.f_approval_member</code>. 审核人ID
     */
    public void setApprovalMember(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_approval_member</code>. 审核人ID
     */
    public String getApprovalMember();

    /**
     * Setter for <code>train.t_organization_teaching.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_organization_teaching.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_organization_teaching.f_source</code>. 0：讲师自己申请 1：集采登记
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>train.t_organization_teaching.f_source</code>. 0：讲师自己申请 1：集采登记
     */
    public Integer getSource();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IOrganizationTeaching
     */
    public void from(IOrganizationTeaching from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IOrganizationTeaching
     */
    public <E extends IOrganizationTeaching> E into(E into);
}
