/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.AudienceMemberRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 受众人表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AudienceMember extends TableImpl<AudienceMemberRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_audience_member</code>
     */
    public static final AudienceMember AUDIENCE_MEMBER = new AudienceMember();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AudienceMemberRecord> getRecordType() {
        return AudienceMemberRecord.class;
    }

    /**
     * The column <code>train.t_audience_member.f_id</code>.
     */
    public final TableField<AudienceMemberRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_audience_member.f_item_id</code>. 受众项id
     */
    public final TableField<AudienceMemberRecord, String> ITEM_ID = createField("f_item_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "受众项id");

    /**
     * The column <code>train.t_audience_member.f_member_id</code>. 受众人id
     */
    public final TableField<AudienceMemberRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "受众人id");

    /**
     * The column <code>train.t_audience_member.f_create_time</code>.
     */
    public final TableField<AudienceMemberRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * Create a <code>train.t_audience_member</code> table reference
     */
    public AudienceMember() {
        this("t_audience_member", null);
    }

    /**
     * Create an aliased <code>train.t_audience_member</code> table reference
     */
    public AudienceMember(String alias) {
        this(alias, AUDIENCE_MEMBER);
    }

    private AudienceMember(String alias, Table<AudienceMemberRecord> aliased) {
        this(alias, aliased, null);
    }

    private AudienceMember(String alias, Table<AudienceMemberRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "受众人表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AudienceMemberRecord> getPrimaryKey() {
        return Keys.KEY_T_AUDIENCE_MEMBER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AudienceMemberRecord>> getKeys() {
        return Arrays.<UniqueKey<AudienceMemberRecord>>asList(Keys.KEY_T_AUDIENCE_MEMBER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AudienceMember as(String alias) {
        return new AudienceMember(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AudienceMember rename(String name) {
        return new AudienceMember(name, null);
    }
}
