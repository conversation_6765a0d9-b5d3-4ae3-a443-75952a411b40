/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassThemeRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassTheme extends TableImpl<ClassThemeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_theme</code>
     */
    public static final ClassTheme CLASS_THEME = new ClassTheme();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassThemeRecord> getRecordType() {
        return ClassThemeRecord.class;
    }

    /**
     * The column <code>train.t_class_theme.f_id</code>.
     */
    public final TableField<ClassThemeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_theme.f_class_id</code>. 培训班ID
     */
    public final TableField<ClassThemeRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训班ID");

    /**
     * The column <code>train.t_class_theme.f_type</code>. 类型（1线下  2线上）
     */
    public final TableField<ClassThemeRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类型（1线下  2线上）");

    /**
     * The column <code>train.t_class_theme.f_name</code>. 主题名称
     */
    public final TableField<ClassThemeRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "主题名称");

    /**
     * The column <code>train.t_class_theme.f_sort</code>. 排序
     */
    public final TableField<ClassThemeRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_class_theme.f_create_member</code>. 创建人
     */
    public final TableField<ClassThemeRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_theme.f_create_time</code>. 创建时间
     */
    public final TableField<ClassThemeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_theme.f_week</code>. 周次
     */
    public final TableField<ClassThemeRecord, String> WEEK = createField("f_week", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "周次");

    /**
     * The column <code>train.t_class_theme.f_start_time</code>. 开始时间
     */
    public final TableField<ClassThemeRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * The column <code>train.t_class_theme.f_end_time</code>. 结束时间
     */
    public final TableField<ClassThemeRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "结束时间");

    /**
     * The column <code>train.t_class_theme.f_required_theme</code>. 配置必修和选修的主题
     */
    public final TableField<ClassThemeRecord, String> REQUIRED_THEME = createField("f_required_theme", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "配置必修和选修的主题");

    /**
     * Create a <code>train.t_class_theme</code> table reference
     */
    public ClassTheme() {
        this("t_class_theme", null);
    }

    /**
     * Create an aliased <code>train.t_class_theme</code> table reference
     */
    public ClassTheme(String alias) {
        this(alias, CLASS_THEME);
    }

    private ClassTheme(String alias, Table<ClassThemeRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassTheme(String alias, Table<ClassThemeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassThemeRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_THEME_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassThemeRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassThemeRecord>>asList(Keys.KEY_T_CLASS_THEME_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassTheme as(String alias) {
        return new ClassTheme(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassTheme rename(String name) {
        return new ClassTheme(name, null);
    }
}
