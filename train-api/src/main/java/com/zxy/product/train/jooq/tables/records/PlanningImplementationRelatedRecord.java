/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.PlanningImplementationRelated;
import com.zxy.product.train.jooq.tables.interfaces.IPlanningImplementationRelated;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 策划实施关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PlanningImplementationRelatedRecord extends UpdatableRecordImpl<PlanningImplementationRelatedRecord> implements Record7<String, String, String, String, Integer, Long, Timestamp>, IPlanningImplementationRelated {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_planning_implementation_related.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation_related.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_planning_implementation_related.f_business_id</code>. 关联业务id
     */
    @Override
    public void setBusinessId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation_related.f_business_id</code>. 关联业务id
     */
    @Override
    public String getBusinessId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_planning_implementation_related.f_organization_id</code>. 部门id
     */
    @Override
    public void setOrganizationId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation_related.f_organization_id</code>. 部门id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_planning_implementation_related.f_member_id</code>. 人员id
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation_related.f_member_id</code>. 人员id
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_planning_implementation_related.f_type</code>. 来源（2：策划 1：实施）
     */
    @Override
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation_related.f_type</code>. 来源（2：策划 1：实施）
     */
    @Override
    public Integer getType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_planning_implementation_related.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation_related.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_planning_implementation_related.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_planning_implementation_related.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, Integer, Long, Timestamp> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, String, Integer, Long, Timestamp> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field7() {
        return PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value7() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord value2(String value) {
        setBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord value3(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord value5(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord value7(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementationRelatedRecord values(String value1, String value2, String value3, String value4, Integer value5, Long value6, Timestamp value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IPlanningImplementationRelated from) {
        setId(from.getId());
        setBusinessId(from.getBusinessId());
        setOrganizationId(from.getOrganizationId());
        setMemberId(from.getMemberId());
        setType(from.getType());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IPlanningImplementationRelated> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached PlanningImplementationRelatedRecord
     */
    public PlanningImplementationRelatedRecord() {
        super(PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED);
    }

    /**
     * Create a detached, initialised PlanningImplementationRelatedRecord
     */
    public PlanningImplementationRelatedRecord(String id, String businessId, String organizationId, String memberId, Integer type, Long createTime, Timestamp modifyDate) {
        super(PlanningImplementationRelated.PLANNING_IMPLEMENTATION_RELATED);

        set(0, id);
        set(1, businessId);
        set(2, organizationId);
        set(3, memberId);
        set(4, type);
        set(5, createTime);
        set(6, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.PlanningImplementationRelatedEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.PlanningImplementationRelatedEntity pojo = (com.zxy.product.train.jooq.tables.pojos.PlanningImplementationRelatedEntity)source;
        pojo.into(this);
        return true;
    }
}
