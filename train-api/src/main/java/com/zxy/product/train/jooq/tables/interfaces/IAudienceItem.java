/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 受众项
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAudienceItem extends Serializable {

    /**
     * Setter for <code>train.t_audience_item.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_audience_item.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_audience_item.f_join_id</code>. 关联ID
     */
    public void setJoinId(String value);

    /**
     * Getter for <code>train.t_audience_item.f_join_id</code>. 关联ID
     */
    public String getJoinId();

    /**
     * Setter for <code>train.t_audience_item.f_join_type</code>. 关联类型（1：部门(不包含子部门），2：部门（包含子部门），3：职位，4：职位，5：人员, 6:人员标签）
     */
    public void setJoinType(Integer value);

    /**
     * Getter for <code>train.t_audience_item.f_join_type</code>. 关联类型（1：部门(不包含子部门），2：部门（包含子部门），3：职位，4：职位，5：人员, 6:人员标签）
     */
    public Integer getJoinType();

    /**
     * Setter for <code>train.t_audience_item.f_join_name</code>. 关联名称
     */
    public void setJoinName(String value);

    /**
     * Getter for <code>train.t_audience_item.f_join_name</code>. 关联名称
     */
    public String getJoinName();

    /**
     * Setter for <code>train.t_audience_item.f_reference_count</code>. 引用次数
     */
    public void setReferenceCount(Integer value);

    /**
     * Getter for <code>train.t_audience_item.f_reference_count</code>. 引用次数
     */
    public Integer getReferenceCount();

    /**
     * Setter for <code>train.t_audience_item.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_audience_item.f_create_time</code>.
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAudienceItem
     */
    public void from(IAudienceItem from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAudienceItem
     */
    public <E extends IAudienceItem> E into(E into);
}
