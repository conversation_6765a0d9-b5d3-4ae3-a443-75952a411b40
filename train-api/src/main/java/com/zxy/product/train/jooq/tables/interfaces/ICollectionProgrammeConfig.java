/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 集采方案配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICollectionProgrammeConfig extends Serializable {

    /**
     * Setter for <code>train.t_collection_programme_config.f_id</code>. 系统ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_id</code>. 系统ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_name</code>. 集采机构名称
     */
    public void setMechanismName(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_name</code>. 集采机构名称
     */
    public String getMechanismName();

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_contacts</code>. 集采机构联系人
     */
    public void setMechanismContacts(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_contacts</code>. 集采机构联系人
     */
    public String getMechanismContacts();

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_contacts_number</code>. 集采机构联系人电话 
     */
    public void setMechanismContactsNumber(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_contacts_number</code>. 集采机构联系人电话 
     */
    public String getMechanismContactsNumber();

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_contacts_email</code>. 集采机构联系人邮箱 
     */
    public void setMechanismContactsEmail(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_contacts_email</code>. 集采机构联系人邮箱 
     */
    public String getMechanismContactsEmail();

    /**
     * Setter for <code>train.t_collection_programme_config.f_programme_name</code>. 集采方案名称 
     */
    public void setProgrammeName(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_programme_name</code>. 集采方案名称 
     */
    public String getProgrammeName();

    /**
     * Setter for <code>train.t_collection_programme_config.f_attachment_id</code>. 集采方案附件ID
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_attachment_id</code>. 集采方案附件ID
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_collection_programme_config.f_attachment_name</code>. 集采方案附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_attachment_name</code>. 集采方案附件名称
     */
    public String getAttachmentName();

    /**
     * Setter for <code>train.t_collection_programme_config.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_collection_programme_config.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_collection_programme_config.f_create_member_id</code>. 创建人ID
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_create_member_id</code>. 创建人ID
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_collection_programme_config.f_attachment_type</code>. 集采方案附件类型
     */
    public void setAttachmentType(String value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_attachment_type</code>. 集采方案附件类型
     */
    public String getAttachmentType();

    /**
     * Setter for <code>train.t_collection_programme_config.f_course_unit_price</code>. 集采课程单价(元)
     */
    public void setCourseUnitPrice(Double value);

    /**
     * Getter for <code>train.t_collection_programme_config.f_course_unit_price</code>. 集采课程单价(元)
     */
    public Double getCourseUnitPrice();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICollectionProgrammeConfig
     */
    public void from(ICollectionProgrammeConfig from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICollectionProgrammeConfig
     */
    public <E extends ICollectionProgrammeConfig> E into(E into);
}
