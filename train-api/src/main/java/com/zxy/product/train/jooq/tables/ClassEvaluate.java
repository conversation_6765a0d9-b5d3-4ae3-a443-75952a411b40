/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassEvaluateRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassEvaluate extends TableImpl<ClassEvaluateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_evaluate</code>
     */
    public static final ClassEvaluate CLASS_EVALUATE = new ClassEvaluate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassEvaluateRecord> getRecordType() {
        return ClassEvaluateRecord.class;
    }

    /**
     * The column <code>train.t_class_evaluate.f_id</code>.
     */
    public final TableField<ClassEvaluateRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_evaluate.f_class_id</code>. 班级ID
     */
    public final TableField<ClassEvaluateRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_evaluate.f_type</code>. 问卷类型（1考试 2调研  3评估  4学员满意度评估 5需求方满意度评估）
     */
    public final TableField<ClassEvaluateRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "问卷类型（1考试 2调研  3评估  4学员满意度评估 5需求方满意度评估）");

    /**
     * The column <code>train.t_class_evaluate.f_resource_id</code>. 问卷ID
     */
    public final TableField<ClassEvaluateRecord, String> RESOURCE_ID = createField("f_resource_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "问卷ID");

    /**
     * The column <code>train.t_class_evaluate.f_resource_name</code>. 问卷名称
     */
    public final TableField<ClassEvaluateRecord, String> RESOURCE_NAME = createField("f_resource_name", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "问卷名称");

    /**
     * The column <code>train.t_class_evaluate.f_create_time</code>. 创建时间
     */
    public final TableField<ClassEvaluateRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_evaluate.f_create_member</code>. 创建人
     */
    public final TableField<ClassEvaluateRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_evaluate.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassEvaluateRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_evaluate.f_start_time</code>. 开始时间
     */
    public final TableField<ClassEvaluateRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * The column <code>train.t_class_evaluate.f_end_time</code>. 结束时间
     */
    public final TableField<ClassEvaluateRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "结束时间");

    /**
     * The column <code>train.t_class_evaluate.f_release</code>. 是否发布（0未发布，1发布）
     */
    public final TableField<ClassEvaluateRecord, Integer> RELEASE = createField("f_release", org.jooq.impl.SQLDataType.INTEGER, this, "是否发布（0未发布，1发布）");

    /**
     * The column <code>train.t_class_evaluate.f_remarks</code>. 备注
     */
    public final TableField<ClassEvaluateRecord, String> REMARKS = createField("f_remarks", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "备注");

    /**
     * The column <code>train.t_class_evaluate.f_is_add</code>. 是否为新增（0否  1是）
     */
    public final TableField<ClassEvaluateRecord, Integer> IS_ADD = createField("f_is_add", org.jooq.impl.SQLDataType.INTEGER, this, "是否为新增（0否  1是）");

    /**
     * The column <code>train.t_class_evaluate.f_paper_class_id</code>. 试卷ID（type为1时有效）
     */
    public final TableField<ClassEvaluateRecord, String> PAPER_CLASS_ID = createField("f_paper_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "试卷ID（type为1时有效）");

    /**
     * The column <code>train.t_class_evaluate.f_source_id</code>. 选择的调研或评估原有的id
     */
    public final TableField<ClassEvaluateRecord, String> SOURCE_ID = createField("f_source_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "选择的调研或评估原有的id");

    /**
     * The column <code>train.t_class_evaluate.f_show_answer_rule</code>. 显示答案的规则
     */
    public final TableField<ClassEvaluateRecord, Integer> SHOW_ANSWER_RULE = createField("f_show_answer_rule", org.jooq.impl.SQLDataType.INTEGER, this, "显示答案的规则");

    /**
     * Create a <code>train.t_class_evaluate</code> table reference
     */
    public ClassEvaluate() {
        this("t_class_evaluate", null);
    }

    /**
     * Create an aliased <code>train.t_class_evaluate</code> table reference
     */
    public ClassEvaluate(String alias) {
        this(alias, CLASS_EVALUATE);
    }

    private ClassEvaluate(String alias, Table<ClassEvaluateRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassEvaluate(String alias, Table<ClassEvaluateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassEvaluateRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_EVALUATE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassEvaluateRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassEvaluateRecord>>asList(Keys.KEY_T_CLASS_EVALUATE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassEvaluate as(String alias) {
        return new ClassEvaluate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassEvaluate rename(String name) {
        return new ClassEvaluate(name, null);
    }
}
