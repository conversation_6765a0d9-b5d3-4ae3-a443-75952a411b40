/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 教研教学活动配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseTeachingActivities extends Serializable {

    /**
     * Setter for <code>train.t_course_teaching_activities.f_id</code>. 系统ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_id</code>. 系统ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_activity_id</code>. 活动ID
     */
    public void setActivityId(String value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_activity_id</code>. 活动ID
     */
    public String getActivityId();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_activity_name</code>. 活动名称
     */
    public void setActivityName(String value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_activity_name</code>. 活动名称
     */
    public String getActivityName();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_base</code>. 基数
     */
    public void setBase(Double value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_base</code>. 基数
     */
    public Double getBase();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_level_name</code>. 级别名称
     */
    public void setLevelName(String value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_level_name</code>. 级别名称
     */
    public String getLevelName();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_coefficient</code>. 系数
     */
    public void setCoefficient(Double value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_coefficient</code>. 系数
     */
    public Double getCoefficient();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_satisfied_degree</code>. 满意度要求
     */
    public void setSatisfiedDegree(Double value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_satisfied_degree</code>. 满意度要求
     */
    public Double getSatisfiedDegree();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_create_member_id</code>. 创建人ID
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_create_member_id</code>. 创建人ID
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_course_teaching_activities.f_create_time</code>. 添加时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_course_teaching_activities.f_create_time</code>. 添加时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseTeachingActivities
     */
    public void from(ICourseTeachingActivities from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseTeachingActivities
     */
    public <E extends ICourseTeachingActivities> E into(E into);
}
