package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.SignLeaveEntity;

/**
 * l<PERSON><PERSON><PERSON>
 *
 */
public class Leave extends SignLeaveEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = 7894325370842134738L;
	public static final int STATE_PENDING = 0;//待审批
	public static final int STATE_AGREE = 1;//同意
	public static final int STATE_DISAGREE = 2;//不同意
	private Long leaveTime;

	private Member member;// 人员对象

	private Sign sign;//SIGN对象

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Sign getSign() {
		return sign;
	}

	public void setSign(Sign sign) {
		this.sign = sign;
	}

	public Long getLeaveTime() {
		return leaveTime;
	}

	public void setLeaveTime(Long leaveTime) {
		this.leaveTime = leaveTime;
	}
}
