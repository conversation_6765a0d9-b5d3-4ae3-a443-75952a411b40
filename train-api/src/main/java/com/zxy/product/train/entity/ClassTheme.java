package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassThemeEntity;

/**
 * 班级主题
 * <AUTHOR>
 *
 */
public class ClassTheme extends ClassThemeEntity {

    private static final long serialVersionUID = -2637120635555099677L;

    public static final Integer TYPE_OFFLINE = 1;
    public static final Integer TYPE_ONLINE = 2;

    /*
     * 描述，针对于线下课程，例如：3月4日~3月6日
     */
    private String descr;
    /*
     * 开始日期，用于主题下的线下课程查询
     */
    private String startDate;
    /*
     * 结束日期，用于主题下的线下课程查询
     */
    private String endDate;

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }

}
