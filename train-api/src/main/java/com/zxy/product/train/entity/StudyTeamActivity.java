package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityEntity;

/**
 * 学习团队活动实体类
 *
 * <AUTHOR>
 * @date 2021/4/14/0014 15:49
 */
public class StudyTeamActivity extends StudyTeamActivityEntity {
    public static final Boolean CONFIRMATION_STATUS_YES = true;
    public static final Boolean CONFIRMATION_STATUS_NO = false;
    private static final long serialVersionUID = -976448845070123720L;
    private String teamName;
    private String leaderMemberName;
    private String organizationId;
    private Integer role;

    /**
     * 角色
     * @return
     */
    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    /**
     * 团队名称
     * @return
     */
    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    /**
     * 领学人姓名
     * @return
     */
    public String getLeaderMemberName() {
        return leaderMemberName;
    }

    public void setLeaderMemberName(String leaderMemberName) {
        this.leaderMemberName = leaderMemberName;
    }

    /**
     * 团队归属组织id
     * @return
     */
    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }
}
