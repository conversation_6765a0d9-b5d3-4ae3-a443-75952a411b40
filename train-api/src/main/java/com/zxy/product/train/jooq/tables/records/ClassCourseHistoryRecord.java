/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassCourseHistory;
import com.zxy.product.train.jooq.tables.interfaces.IClassCourseHistory;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 历史班级课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassCourseHistoryRecord extends UpdatableRecordImpl<ClassCourseHistoryRecord> implements Record13<String, Integer, String, String, Long, Long, Long, String, String, String, String, String, Integer>, IClassCourseHistory {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_course_history.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_type</code>. 课程类型:1F,2L,3O,4Q,5Z
     */
    @Override
    public void setType(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_type</code>. 课程类型:1F,2L,3O,4Q,5Z
     */
    @Override
    public Integer getType() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_class_id</code>. 班级ID
     */
    @Override
    public void setClassId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_class_id</code>. 班级ID
     */
    @Override
    public String getClassId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_name</code>. 课程名称
     */
    @Override
    public void setName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_name</code>. 课程名称
     */
    @Override
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_course_date</code>. 上课日期
     */
    @Override
    public void setCourseDate(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_course_date</code>. 上课日期
     */
    @Override
    public Long getCourseDate() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_start_time</code>. 开始时间
     */
    @Override
    public void setStartTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_start_time</code>. 开始时间
     */
    @Override
    public Long getStartTime() {
        return (Long) get(5);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_end_time</code>. 结束时间
     */
    @Override
    public void setEndTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_end_time</code>. 结束时间
     */
    @Override
    public Long getEndTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_classroom_id</code>. 教室ID
     */
    @Override
    public void setClassroomId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_classroom_id</code>. 教室ID
     */
    @Override
    public String getClassroomId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_name</code>. 讲师姓名
     */
    @Override
    public void setTeacherName(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_name</code>. 讲师姓名
     */
    @Override
    public String getTeacherName() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_organization</code>. 讲师单位
     */
    @Override
    public void setTeacherOrganization(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_organization</code>. 讲师单位
     */
    @Override
    public String getTeacherOrganization() {
        return (String) get(9);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_title</code>. 讲师职称
     */
    @Override
    public void setTeacherTitle(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_title</code>. 讲师职称
     */
    @Override
    public String getTeacherTitle() {
        return (String) get(10);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_phone</code>. 联系电话
     */
    @Override
    public void setTeacherPhone(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_phone</code>. 联系电话
     */
    @Override
    public String getTeacherPhone() {
        return (String) get(11);
    }

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_type</code>. 讲师类型:0待议
     */
    @Override
    public void setTeacherType(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_type</code>. 讲师类型:0待议
     */
    @Override
    public Integer getTeacherType() {
        return (Integer) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, Integer, String, String, Long, Long, Long, String, String, String, String, String, Integer> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, Integer, String, String, Long, Long, Long, String, String, String, String, String, Integer> valuesRow() {
        return (Row13) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.COURSE_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.START_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.END_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.CLASSROOM_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.TEACHER_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.TEACHER_ORGANIZATION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.TEACHER_TITLE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.TEACHER_PHONE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field13() {
        return ClassCourseHistory.CLASS_COURSE_HISTORY.TEACHER_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCourseDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getStartTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getEndTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getClassroomId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getTeacherName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getTeacherOrganization();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getTeacherTitle();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getTeacherPhone();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value13() {
        return getTeacherType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value2(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value3(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value4(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value5(Long value) {
        setCourseDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value6(Long value) {
        setStartTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value7(Long value) {
        setEndTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value8(String value) {
        setClassroomId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value9(String value) {
        setTeacherName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value10(String value) {
        setTeacherOrganization(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value11(String value) {
        setTeacherTitle(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value12(String value) {
        setTeacherPhone(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord value13(Integer value) {
        setTeacherType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassCourseHistoryRecord values(String value1, Integer value2, String value3, String value4, Long value5, Long value6, Long value7, String value8, String value9, String value10, String value11, String value12, Integer value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassCourseHistory from) {
        setId(from.getId());
        setType(from.getType());
        setClassId(from.getClassId());
        setName(from.getName());
        setCourseDate(from.getCourseDate());
        setStartTime(from.getStartTime());
        setEndTime(from.getEndTime());
        setClassroomId(from.getClassroomId());
        setTeacherName(from.getTeacherName());
        setTeacherOrganization(from.getTeacherOrganization());
        setTeacherTitle(from.getTeacherTitle());
        setTeacherPhone(from.getTeacherPhone());
        setTeacherType(from.getTeacherType());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassCourseHistory> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassCourseHistoryRecord
     */
    public ClassCourseHistoryRecord() {
        super(ClassCourseHistory.CLASS_COURSE_HISTORY);
    }

    /**
     * Create a detached, initialised ClassCourseHistoryRecord
     */
    public ClassCourseHistoryRecord(String id, Integer type, String classId, String name, Long courseDate, Long startTime, Long endTime, String classroomId, String teacherName, String teacherOrganization, String teacherTitle, String teacherPhone, Integer teacherType) {
        super(ClassCourseHistory.CLASS_COURSE_HISTORY);

        set(0, id);
        set(1, type);
        set(2, classId);
        set(3, name);
        set(4, courseDate);
        set(5, startTime);
        set(6, endTime);
        set(7, classroomId);
        set(8, teacherName);
        set(9, teacherOrganization);
        set(10, teacherTitle);
        set(11, teacherPhone);
        set(12, teacherType);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassCourseHistoryEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassCourseHistoryEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassCourseHistoryEntity)source;
        pojo.into(this);
        return true;
    }
}
