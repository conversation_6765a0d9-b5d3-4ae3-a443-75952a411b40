/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.JobRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Job extends TableImpl<JobRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_job</code>
     */
    public static final Job JOB = new Job();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<JobRecord> getRecordType() {
        return JobRecord.class;
    }

    /**
     * The column <code>train.t_job.f_id</code>. ID
     */
    public final TableField<JobRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).nullable(false), this, "ID");

    /**
     * The column <code>train.t_job.f_create_time</code>. 创建时间
     */
    public final TableField<JobRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_job.f_name</code>. 职务名称
     */
    public final TableField<JobRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "职务名称");

    /**
     * The column <code>train.t_job.f_code</code>. 职务编码
     */
    public final TableField<JobRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "职务编码");

    /**
     * The column <code>train.t_job.f_organization_id</code>. 所属机构
     */
    public final TableField<JobRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "所属机构");

    /**
     * The column <code>train.t_job.f_status</code>. 职务状态
     */
    public final TableField<JobRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "职务状态");

    /**
     * The column <code>train.t_job.f_job_type_id</code>. 职务类别
     */
    public final TableField<JobRecord, String> JOB_TYPE_ID = createField("f_job_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "职务类别");

    /**
     * The column <code>train.t_job.f_position_count</code>. 关联职位数
     */
    public final TableField<JobRecord, Integer> POSITION_COUNT = createField("f_position_count", org.jooq.impl.SQLDataType.INTEGER, this, "关联职位数");

    /**
     * The column <code>train.t_job.f_member_count</code>. 职务人数
     */
    public final TableField<JobRecord, Integer> MEMBER_COUNT = createField("f_member_count", org.jooq.impl.SQLDataType.INTEGER, this, "职务人数");

    /**
     * The column <code>train.t_job.f_mis_code</code>. MIS省份简称
     */
    public final TableField<JobRecord, String> MIS_CODE = createField("f_mis_code", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "MIS省份简称");

    /**
     * The column <code>train.t_job.f_mis_id</code>. MIS同步职务ID
     */
    public final TableField<JobRecord, String> MIS_ID = createField("f_mis_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "MIS同步职务ID");

    /**
     * Create a <code>train.t_job</code> table reference
     */
    public Job() {
        this("t_job", null);
    }

    /**
     * Create an aliased <code>train.t_job</code> table reference
     */
    public Job(String alias) {
        this(alias, JOB);
    }

    private Job(String alias, Table<JobRecord> aliased) {
        this(alias, aliased, null);
    }

    private Job(String alias, Table<JobRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<JobRecord> getPrimaryKey() {
        return Keys.KEY_T_JOB_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<JobRecord>> getKeys() {
        return Arrays.<UniqueKey<JobRecord>>asList(Keys.KEY_T_JOB_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Job as(String alias) {
        return new Job(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Job rename(String name) {
        return new Job(name, null);
    }
}
