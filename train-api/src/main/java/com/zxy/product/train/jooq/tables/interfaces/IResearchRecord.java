/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IResearchRecord extends Serializable {

    /**
     * Setter for <code>train.t_research_record.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_research_record.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_research_record.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_research_record.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_research_record.f_member_id</code>. 参与人
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_research_record.f_member_id</code>. 参与人
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_research_record.f_status</code>. 状态 0：未参与 1：已参与
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_research_record.f_status</code>. 状态 0：未参与 1：已参与
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_research_record.f_research_questionary_id</code>. 调研ID
     */
    public void setResearchQuestionaryId(String value);

    /**
     * Getter for <code>train.t_research_record.f_research_questionary_id</code>. 调研ID
     */
    public String getResearchQuestionaryId();

    /**
     * Setter for <code>train.t_research_record.f_submit_time</code>. 提交时间
     */
    public void setSubmitTime(Long value);

    /**
     * Getter for <code>train.t_research_record.f_submit_time</code>. 提交时间
     */
    public Long getSubmitTime();

    /**
     * Setter for <code>train.t_research_record.f_score</code>. 分数
     */
    public void setScore(Long value);

    /**
     * Getter for <code>train.t_research_record.f_score</code>. 分数
     */
    public Long getScore();

    /**
     * Setter for <code>train.t_research_record.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_research_record.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IResearchRecord
     */
    public void from(IResearchRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IResearchRecord
     */
    public <E extends IResearchRecord> E into(E into);
}
