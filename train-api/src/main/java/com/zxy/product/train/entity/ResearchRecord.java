package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ResearchRecordEntity;

public class ResearchRecord extends ResearchRecordEntity{

	/**
	 * lulu 统计与评估 调研记录实体
	 */
	private static final long serialVersionUID = -8815538821180651598L;

	private Member member;

	private Organization organization;

	private Long sTime;//开始时间

	private ClassInfo classInfo;

	private ResearchQuestionary researchQuestionary;

	/**
	 * 已完成
	 */
	public static final Integer STATUS_FINISHED = 1;
	public static final Integer STATUS_UNFINISH = 0;//未完成

	public ResearchQuestionary getResearchQuestionary() {
		return researchQuestionary;
	}

	public void setResearchQuestionary(ResearchQuestionary researchQuestionary) {
		this.researchQuestionary = researchQuestionary;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public Long getsTime() {
		return sTime;
	}

	public void setsTime(Long sTime) {
		this.sTime = sTime;
	}

	public ClassInfo getClassInfo() {
		return classInfo;
	}

	public void setClassInfo(ClassInfo classInfo) {
		this.classInfo = classInfo;
	}

}
