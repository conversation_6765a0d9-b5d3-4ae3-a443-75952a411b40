/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * PCCW HR接口调用结果
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPccwResult extends Serializable {

    /**
     * Setter for <code>train.t_pccw_result.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_pccw_result.f_method</code>. 业务(接口)ID
     */
    public void setMethod(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_method</code>. 业务(接口)ID
     */
    public String getMethod();

    /**
     * Setter for <code>train.t_pccw_result.f_instance_id</code>. 实例ID
     */
    public void setInstanceId(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_instance_id</code>. 实例ID
     */
    public String getInstanceId();

    /**
     * Setter for <code>train.t_pccw_result.f_link_id</code>. 上次处理ID
     */
    public void setLinkId(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_link_id</code>. 上次处理ID
     */
    public String getLinkId();

    /**
     * Setter for <code>train.t_pccw_result.f_resp_code</code>. 响应编码
     */
    public void setRespCode(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_resp_code</code>. 响应编码
     */
    public String getRespCode();

    /**
     * Setter for <code>train.t_pccw_result.f_resp_desc</code>. 返回描述
     */
    public void setRespDesc(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_resp_desc</code>. 返回描述
     */
    public String getRespDesc();

    /**
     * Setter for <code>train.t_pccw_result.f_status_code</code>. 处理结果标识
     */
    public void setStatusCode(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_status_code</code>. 处理结果标识
     */
    public String getStatusCode();

    /**
     * Setter for <code>train.t_pccw_result.f_err_reason</code>. 处理失败原因
     */
    public void setErrReason(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_err_reason</code>. 处理失败原因
     */
    public String getErrReason();

    /**
     * Setter for <code>train.t_pccw_result.f_total_record</code>. 总条数
     */
    public void setTotalRecord(Integer value);

    /**
     * Getter for <code>train.t_pccw_result.f_total_record</code>. 总条数
     */
    public Integer getTotalRecord();

    /**
     * Setter for <code>train.t_pccw_result.f_output_ext</code>. 查询结果扩展
     */
    public void setOutputExt(String value);

    /**
     * Getter for <code>train.t_pccw_result.f_output_ext</code>. 查询结果扩展
     */
    public String getOutputExt();

    /**
     * Setter for <code>train.t_pccw_result.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_pccw_result.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_pccw_result.f_status</code>. 1: 成功 0:失败 2:下一次处理
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_pccw_result.f_status</code>. 1: 成功 0:失败 2:下一次处理
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_pccw_result.f_update_time</code>. 更新时间
     */
    public void setUpdateTime(Long value);

    /**
     * Getter for <code>train.t_pccw_result.f_update_time</code>. 更新时间
     */
    public Long getUpdateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPccwResult
     */
    public void from(IPccwResult from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPccwResult
     */
    public <E extends IPccwResult> E into(E into);
}
