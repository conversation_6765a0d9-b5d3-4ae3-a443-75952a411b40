/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassQuotaDetail extends Serializable {

    /**
     * Setter for <code>train.t_class_quota_detail.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_quota_detail.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_quota_detail.f_class_id</code>. 培训班ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_quota_detail.f_class_id</code>. 培训班ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_quota_detail.f_organization_id</code>. 机构ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_class_quota_detail.f_organization_id</code>. 机构ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_class_quota_detail.f_quantity</code>. 配额数量
     */
    public void setQuantity(Integer value);

    /**
     * Getter for <code>train.t_class_quota_detail.f_quantity</code>. 配额数量
     */
    public Integer getQuantity();

    /**
     * Setter for <code>train.t_class_quota_detail.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_quota_detail.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_quota_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_quota_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_quota_detail.f_group_id</code>. 分组ID
     */
    public void setGroupId(String value);

    /**
     * Getter for <code>train.t_class_quota_detail.f_group_id</code>. 分组ID
     */
    public String getGroupId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassQuotaDetail
     */
    public void from(IClassQuotaDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassQuotaDetail
     */
    public <E extends IClassQuotaDetail> E into(E into);
}
