/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IGroupConfigurationValue extends Serializable {

    /**
     * Setter for <code>train.t_group_configuration_value.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_group_configuration_value.f_organization_id</code>. 单位名称
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_organization_id</code>. 单位名称
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_group_configuration_value.f_group_id</code>. 关联分组配置ID
     */
    public void setGroupId(String value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_group_id</code>. 关联分组配置ID
     */
    public String getGroupId();

    /**
     * Setter for <code>train.t_group_configuration_value.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_group_configuration_value.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_group_configuration_value.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_group_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_group_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    public void setPath(String value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    public String getPath();

    /**
     * Setter for <code>train.t_group_configuration_value.f_short_name</code>. 短名称
     */
    public void setShortName(String value);

    /**
     * Getter for <code>train.t_group_configuration_value.f_short_name</code>. 短名称
     */
    public String getShortName();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IGroupConfigurationValue
     */
    public void from(IGroupConfigurationValue from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IGroupConfigurationValue
     */
    public <E extends IGroupConfigurationValue> E into(E into);
}
