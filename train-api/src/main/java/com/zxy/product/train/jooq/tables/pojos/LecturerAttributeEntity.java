/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.ILecturerAttribute;

import javax.annotation.Generated;


/**
 * 讲师属性配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerAttributeEntity extends BaseEntity implements ILecturerAttribute {

    private static final long serialVersionUID = 1L;

    private Integer typeId;
    private String  attributeName;
    private String  createMemberId;
    private String  exitMemberId;
    private String  organizationId;
    private Long    updateTime;

    public LecturerAttributeEntity() {}

    public LecturerAttributeEntity(LecturerAttributeEntity value) {
        this.typeId = value.typeId;
        this.attributeName = value.attributeName;
        this.createMemberId = value.createMemberId;
        this.exitMemberId = value.exitMemberId;
        this.organizationId = value.organizationId;
        this.updateTime = value.updateTime;
    }

    public LecturerAttributeEntity(
        String  id,
        Integer typeId,
        String  attributeName,
        String  createMemberId,
        String  exitMemberId,
        String  organizationId,
        Long    createTime,
        Long    updateTime
    ) {
        super.setId(id);
        this.typeId = typeId;
        this.attributeName = attributeName;
        this.createMemberId = createMemberId;
        this.exitMemberId = exitMemberId;
        this.organizationId = organizationId;
        super.setCreateTime(createTime);
        this.updateTime = updateTime;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Integer getTypeId() {
        return this.typeId;
    }

    @Override
    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    @Override
    public String getAttributeName() {
        return this.attributeName;
    }

    @Override
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    @Override
    public String getCreateMemberId() {
        return this.createMemberId;
    }

    @Override
    public void setCreateMemberId(String createMemberId) {
        this.createMemberId = createMemberId;
    }

    @Override
    public String getExitMemberId() {
        return this.exitMemberId;
    }

    @Override
    public void setExitMemberId(String exitMemberId) {
        this.exitMemberId = exitMemberId;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getUpdateTime() {
        return this.updateTime;
    }

    @Override
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LecturerAttributeEntity (");

        sb.append(getId());
        sb.append(", ").append(typeId);
        sb.append(", ").append(attributeName);
        sb.append(", ").append(createMemberId);
        sb.append(", ").append(exitMemberId);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(updateTime);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILecturerAttribute from) {
        setId(from.getId());
        setTypeId(from.getTypeId());
        setAttributeName(from.getAttributeName());
        setCreateMemberId(from.getCreateMemberId());
        setExitMemberId(from.getExitMemberId());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setUpdateTime(from.getUpdateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILecturerAttribute> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends LecturerAttributeEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.LecturerAttributeRecord r = new com.zxy.product.train.jooq.tables.records.LecturerAttributeRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ID, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.TYPE_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.TYPE_ID, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.TYPE_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ATTRIBUTE_NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ATTRIBUTE_NAME, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ATTRIBUTE_NAME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.CREATE_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.CREATE_MEMBER_ID, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.CREATE_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.EXIT_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.EXIT_MEMBER_ID, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.EXIT_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ORGANIZATION_ID, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.UPDATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.UPDATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.LecturerAttribute.LECTURER_ATTRIBUTE.UPDATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
