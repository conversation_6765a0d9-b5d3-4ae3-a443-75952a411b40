/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamActivityAttachmentRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 团队学习班-活动资料表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamActivityAttachment extends TableImpl<StudyTeamActivityAttachmentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_activity_attachment</code>
     */
    public static final StudyTeamActivityAttachment STUDY_TEAM_ACTIVITY_ATTACHMENT = new StudyTeamActivityAttachment();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamActivityAttachmentRecord> getRecordType() {
        return StudyTeamActivityAttachmentRecord.class;
    }

    /**
     * The column <code>train.t_study_team_activity_attachment.f_id</code>. 主键
     */
    public final TableField<StudyTeamActivityAttachmentRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_study_team_activity_attachment.f_activity_id</code>. 活动id
     */
    public final TableField<StudyTeamActivityAttachmentRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "活动id");

    /**
     * The column <code>train.t_study_team_activity_attachment.f_name</code>. 资料名称
     */
    public final TableField<StudyTeamActivityAttachmentRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).nullable(false), this, "资料名称");

    /**
     * The column <code>train.t_study_team_activity_attachment.f_attachment_id</code>. 资料id
     */
    public final TableField<StudyTeamActivityAttachmentRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "资料id");

    /**
     * The column <code>train.t_study_team_activity_attachment.f_attachment_type</code>. 附件类型，1: 文档, 2: 图片, 4: 压缩文件, 5: 音频, 6: 视频, 7: EPUB电子书, 10: 其它
     */
    public final TableField<StudyTeamActivityAttachmentRecord, Integer> ATTACHMENT_TYPE = createField("f_attachment_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "附件类型，1: 文档, 2: 图片, 4: 压缩文件, 5: 音频, 6: 视频, 7: EPUB电子书, 10: 其它");

    /**
     * The column <code>train.t_study_team_activity_attachment.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamActivityAttachmentRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team_activity_attachment</code> table reference
     */
    public StudyTeamActivityAttachment() {
        this("t_study_team_activity_attachment", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_activity_attachment</code> table reference
     */
    public StudyTeamActivityAttachment(String alias) {
        this(alias, STUDY_TEAM_ACTIVITY_ATTACHMENT);
    }

    private StudyTeamActivityAttachment(String alias, Table<StudyTeamActivityAttachmentRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamActivityAttachment(String alias, Table<StudyTeamActivityAttachmentRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "团队学习班-活动资料表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamActivityAttachmentRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_ACTIVITY_ATTACHMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamActivityAttachmentRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamActivityAttachmentRecord>>asList(Keys.KEY_T_STUDY_TEAM_ACTIVITY_ATTACHMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachment as(String alias) {
        return new StudyTeamActivityAttachment(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamActivityAttachment rename(String name) {
        return new StudyTeamActivityAttachment(name, null);
    }
}
