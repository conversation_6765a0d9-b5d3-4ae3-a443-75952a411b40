/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 培训项目表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITrainProject extends Serializable {

    /**
     * Setter for <code>train.t_train_project.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_train_project.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_train_project.f_project_id</code>. 培训计划id
     */
    public void setProjectId(String value);

    /**
     * Getter for <code>train.t_train_project.f_project_id</code>. 培训计划id
     */
    public String getProjectId();

    /**
     * Setter for <code>train.t_train_project.f_name</code>. 项目名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_train_project.f_name</code>. 项目名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_train_project.f_code</code>. MIS编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_train_project.f_code</code>. MIS编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_train_project.f_organization_id</code>. 主办单位
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_train_project.f_organization_id</code>. 主办单位
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_train_project.f_train_day</code>. 培训天数
     */
    public void setTrainDay(String value);

    /**
     * Getter for <code>train.t_train_project.f_train_day</code>. 培训天数
     */
    public String getTrainDay();

    /**
     * Setter for <code>train.t_train_project.f_train_method</code>. 培训方式
     */
    public void setTrainMethod(String value);

    /**
     * Getter for <code>train.t_train_project.f_train_method</code>. 培训方式
     */
    public String getTrainMethod();

    /**
     * Setter for <code>train.t_train_project.f_train_address</code>. 培训地点
     */
    public void setTrainAddress(String value);

    /**
     * Getter for <code>train.t_train_project.f_train_address</code>. 培训地点
     */
    public String getTrainAddress();

    /**
     * Setter for <code>train.t_train_project.f_train_year</code>. 培训年份
     */
    public void setTrainYear(Integer value);

    /**
     * Getter for <code>train.t_train_project.f_train_year</code>. 培训年份
     */
    public Integer getTrainYear();

    /**
     * Setter for <code>train.t_train_project.f_train_month</code>. 培训月份
     */
    public void setTrainMonth(String value);

    /**
     * Getter for <code>train.t_train_project.f_train_month</code>. 培训月份
     */
    public String getTrainMonth();

    /**
     * Setter for <code>train.t_train_project.f_categories</code>. 培训大类
     */
    public void setCategories(String value);

    /**
     * Getter for <code>train.t_train_project.f_categories</code>. 培训大类
     */
    public String getCategories();

    /**
     * Setter for <code>train.t_train_project.f_subclass</code>. 培训小类
     */
    public void setSubclass(String value);

    /**
     * Getter for <code>train.t_train_project.f_subclass</code>. 培训小类
     */
    public String getSubclass();

    /**
     * Setter for <code>train.t_train_project.f_key_project</code>. 重点项目（0：非重点 1：重点）
     */
    public void setKeyProject(Integer value);

    /**
     * Getter for <code>train.t_train_project.f_key_project</code>. 重点项目（0：非重点 1：重点）
     */
    public Integer getKeyProject();

    /**
     * Setter for <code>train.t_train_project.f_project_nature</code>. 项目性质（0：垂直条线 1：非垂直条线）
     */
    public void setProjectNature(Integer value);

    /**
     * Getter for <code>train.t_train_project.f_project_nature</code>. 项目性质（0：垂直条线 1：非垂直条线）
     */
    public Integer getProjectNature();

    /**
     * Setter for <code>train.t_train_project.f_plan_month</code>. 计划月份
     */
    public void setPlanMonth(String value);

    /**
     * Getter for <code>train.t_train_project.f_plan_month</code>. 计划月份
     */
    public String getPlanMonth();

    /**
     * Setter for <code>train.t_train_project.f_source</code>. 来源（0：同步培训计划 1：自建）
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>train.t_train_project.f_source</code>. 来源（0：同步培训计划 1：自建）
     */
    public Integer getSource();

    /**
     * Setter for <code>train.t_train_project.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_train_project.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_train_project.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_train_project.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_train_project.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_train_project.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>train.t_train_project.f_object</code>. 培训对象
     */
    public void setObject(String value);

    /**
     * Getter for <code>train.t_train_project.f_object</code>. 培训对象
     */
    public String getObject();

    /**
     * Setter for <code>train.t_train_project.f_plan_type</code>. 计划类型（0:培训班 1:专题）
     */
    public void setPlanType(Integer value);

    /**
     * Getter for <code>train.t_train_project.f_plan_type</code>. 计划类型（0:培训班 1:专题）
     */
    public Integer getPlanType();

    /**
     * Setter for <code>train.t_train_project.f_contact_member_id</code>. 需求单位联系人
     */
    public void setContactMemberId(String value);

    /**
     * Getter for <code>train.t_train_project.f_contact_member_id</code>. 需求单位联系人
     */
    public String getContactMemberId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITrainProject
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.ITrainProject from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITrainProject
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.ITrainProject> E into(E into);
}
