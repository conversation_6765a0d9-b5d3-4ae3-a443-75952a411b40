/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习团队表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeam extends Serializable {

    /**
     * Setter for <code>train.t_study_team.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team.f_name</code>. 团队名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_study_team.f_name</code>. 团队名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_study_team.f_captain_member_id</code>. 团队长id
     */
    public void setCaptainMemberId(String value);

    /**
     * Getter for <code>train.t_study_team.f_captain_member_id</code>. 团队长id
     */
    public String getCaptainMemberId();

    /**
     * Setter for <code>train.t_study_team.f_organization_id</code>. 归属组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_study_team.f_organization_id</code>. 归属组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_study_team.f_cover</code>. 封面
     */
    public void setCover(String value);

    /**
     * Getter for <code>train.t_study_team.f_cover</code>. 封面
     */
    public String getCover();

    /**
     * Setter for <code>train.t_study_team.f_notice</code>. 学习团队公告
     */
    public void setNotice(String value);

    /**
     * Getter for <code>train.t_study_team.f_notice</code>. 学习团队公告
     */
    public String getNotice();

    /**
     * Setter for <code>train.t_study_team.f_type</code>. 学习班类型 0-网格 1-其他
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_study_team.f_type</code>. 学习班类型 0-网格 1-其他
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_study_team.f_status</code>. 状态 0-禁用 1-启用
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_study_team.f_status</code>. 状态 0-禁用 1-启用
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_study_team.f_create_member_id</code>. 创建人id
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_study_team.f_create_member_id</code>. 创建人id
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_study_team.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeam
     */
    public void from(IStudyTeam from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeam
     */
    public <E extends IStudyTeam> E into(E into);
}
