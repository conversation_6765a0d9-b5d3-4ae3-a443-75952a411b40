/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CourseAttachRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseAttach extends TableImpl<CourseAttachRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_course_attach</code>
     */
    public static final CourseAttach COURSE_ATTACH = new CourseAttach();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseAttachRecord> getRecordType() {
        return CourseAttachRecord.class;
    }

    /**
     * The column <code>train.t_course_attach.f_id</code>.
     */
    public final TableField<CourseAttachRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_course_attach.f_course_id</code>. 课程ID
     */
    public final TableField<CourseAttachRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程ID");

    /**
     * The column <code>train.t_course_attach.f_attach_id</code>. 附件ID
     */
    public final TableField<CourseAttachRecord, String> ATTACH_ID = createField("f_attach_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "附件ID");

    /**
     * The column <code>train.t_course_attach.f_attach_type</code>. 文件类型
     */
    public final TableField<CourseAttachRecord, String> ATTACH_TYPE = createField("f_attach_type", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "文件类型");

    /**
     * The column <code>train.t_course_attach.f_attach_name</code>. 文件名称
     */
    public final TableField<CourseAttachRecord, String> ATTACH_NAME = createField("f_attach_name", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "文件名称");

    /**
     * The column <code>train.t_course_attach.f_create_member</code>. 创建人
     */
    public final TableField<CourseAttachRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_course_attach.f_create_time</code>. 创建时间
     */
    public final TableField<CourseAttachRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_course_attach</code> table reference
     */
    public CourseAttach() {
        this("t_course_attach", null);
    }

    /**
     * Create an aliased <code>train.t_course_attach</code> table reference
     */
    public CourseAttach(String alias) {
        this(alias, COURSE_ATTACH);
    }

    private CourseAttach(String alias, Table<CourseAttachRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseAttach(String alias, Table<CourseAttachRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseAttachRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_ATTACH_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseAttachRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseAttachRecord>>asList(Keys.KEY_T_COURSE_ATTACH_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseAttach as(String alias) {
        return new CourseAttach(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseAttach rename(String name) {
        return new CourseAttach(name, null);
    }
}
