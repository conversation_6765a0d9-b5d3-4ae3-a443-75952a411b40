/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LimitConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LimitConfiguration extends TableImpl<LimitConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_limit_configuration</code>
     */
    public static final LimitConfiguration LIMIT_CONFIGURATION = new LimitConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LimitConfigurationRecord> getRecordType() {
        return LimitConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_limit_configuration.f_id</code>. 主键
     */
    public final TableField<LimitConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_limit_configuration.f_configuration_id</code>. 关联配置表ID
     */
    public final TableField<LimitConfigurationRecord, String> CONFIGURATION_ID = createField("f_configuration_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联配置表ID");

    /**
     * The column <code>train.t_limit_configuration.f_type_id</code>. 关联类型ID
     */
    public final TableField<LimitConfigurationRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型ID");

    /**
     * The column <code>train.t_limit_configuration.f_year</code>. 年份
     */
    public final TableField<LimitConfigurationRecord, Integer> YEAR = createField("f_year", org.jooq.impl.SQLDataType.INTEGER, this, "年份");

    /**
     * The column <code>train.t_limit_configuration.f_month</code>. 月份
     */
    public final TableField<LimitConfigurationRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER, this, "月份");

    /**
     * The column <code>train.t_limit_configuration.f_limit</code>. 额度
     */
    public final TableField<LimitConfigurationRecord, Integer> LIMIT = createField("f_limit", org.jooq.impl.SQLDataType.INTEGER, this, "额度");

    /**
     * The column <code>train.t_limit_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<LimitConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_limit_configuration.f_create_member</code>. 创建人ID
     */
    public final TableField<LimitConfigurationRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_limit_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<LimitConfigurationRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_limit_configuration</code> table reference
     */
    public LimitConfiguration() {
        this("t_limit_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_limit_configuration</code> table reference
     */
    public LimitConfiguration(String alias) {
        this(alias, LIMIT_CONFIGURATION);
    }

    private LimitConfiguration(String alias, Table<LimitConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private LimitConfiguration(String alias, Table<LimitConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LimitConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_LIMIT_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LimitConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<LimitConfigurationRecord>>asList(Keys.KEY_T_LIMIT_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfiguration as(String alias) {
        return new LimitConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LimitConfiguration rename(String name) {
        return new LimitConfiguration(name, null);
    }
}
