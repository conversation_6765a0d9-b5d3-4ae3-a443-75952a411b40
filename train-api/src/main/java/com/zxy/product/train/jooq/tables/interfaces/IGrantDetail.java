/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IGrantDetail extends Serializable {

    /**
     * Setter for <code>train.t_grant_detail.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_grant_detail.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_grant_detail.f_grant_id</code>. 授权ID
     */
    public void setGrantId(String value);

    /**
     * Getter for <code>train.t_grant_detail.f_grant_id</code>. 授权ID
     */
    public String getGrantId();

    /**
     * Setter for <code>train.t_grant_detail.f_member_id</code>. 人员ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_grant_detail.f_member_id</code>. 人员ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_grant_detail.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_grant_detail.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_grant_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_grant_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_grant_detail.f_uri</code>. 菜单uri
     */
    public void setUri(String value);

    /**
     * Getter for <code>train.t_grant_detail.f_uri</code>. 菜单uri
     */
    public String getUri();

    /**
     * Setter for <code>train.t_grant_detail.f_menu_id</code>. 菜单id
     */
    public void setMenuId(String value);

    /**
     * Getter for <code>train.t_grant_detail.f_menu_id</code>. 菜单id
     */
    public String getMenuId();

    /**
     * Setter for <code>train.t_grant_detail.f_operator_types</code>. 操作类型
     */
    public void setOperatorTypes(String value);

    /**
     * Getter for <code>train.t_grant_detail.f_operator_types</code>. 操作类型
     */
    public String getOperatorTypes();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IGrantDetail
     */
    public void from(IGrantDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IGrantDetail
     */
    public <E extends IGrantDetail> E into(E into);
}
