/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.GroupConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GroupConfiguration extends TableImpl<GroupConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_group_configuration</code>
     */
    public static final GroupConfiguration GROUP_CONFIGURATION = new GroupConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GroupConfigurationRecord> getRecordType() {
        return GroupConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_group_configuration.f_id</code>. 主键
     */
    public final TableField<GroupConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_group_configuration.f_configuration_id</code>. 关联配置表ID
     */
    public final TableField<GroupConfigurationRecord, String> CONFIGURATION_ID = createField("f_configuration_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联配置表ID");

    /**
     * The column <code>train.t_group_configuration.f_type_id</code>. 关联类型ID
     */
    public final TableField<GroupConfigurationRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型ID");

    /**
     * The column <code>train.t_group_configuration.f_group_name</code>. 分组名称
     */
    public final TableField<GroupConfigurationRecord, String> GROUP_NAME = createField("f_group_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "分组名称");

    /**
     * The column <code>train.t_group_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<GroupConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_group_configuration.f_create_member</code>. 创建人ID
     */
    public final TableField<GroupConfigurationRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_group_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<GroupConfigurationRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_group_configuration</code> table reference
     */
    public GroupConfiguration() {
        this("t_group_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_group_configuration</code> table reference
     */
    public GroupConfiguration(String alias) {
        this(alias, GROUP_CONFIGURATION);
    }

    private GroupConfiguration(String alias, Table<GroupConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private GroupConfiguration(String alias, Table<GroupConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GroupConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_GROUP_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GroupConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<GroupConfigurationRecord>>asList(Keys.KEY_T_GROUP_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfiguration as(String alias) {
        return new GroupConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GroupConfiguration rename(String name) {
        return new GroupConfiguration(name, null);
    }
}
