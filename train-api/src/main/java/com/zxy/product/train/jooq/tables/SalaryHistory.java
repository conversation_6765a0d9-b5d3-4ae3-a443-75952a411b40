/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SalaryHistoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 历史课酬表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SalaryHistory extends TableImpl<SalaryHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_salary_history</code>
     */
    public static final SalaryHistory SALARY_HISTORY = new SalaryHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SalaryHistoryRecord> getRecordType() {
        return SalaryHistoryRecord.class;
    }

    /**
     * The column <code>train.t_salary_history.f_id</code>.
     */
    public final TableField<SalaryHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_salary_history.f_course_id</code>. 课程ID
     */
    public final TableField<SalaryHistoryRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程ID");

    /**
     * The column <code>train.t_salary_history.f_lecturer_card</code>. 讲师身份证
     */
    public final TableField<SalaryHistoryRecord, String> LECTURER_CARD = createField("f_lecturer_card", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "讲师身份证");

    /**
     * The column <code>train.t_salary_history.f_lecturer_bank_name</code>. 讲师银行名称
     */
    public final TableField<SalaryHistoryRecord, String> LECTURER_BANK_NAME = createField("f_lecturer_bank_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "讲师银行名称");

    /**
     * The column <code>train.t_salary_history.f_lecturer_bank_card</code>. 讲师银行卡号
     */
    public final TableField<SalaryHistoryRecord, String> LECTURER_BANK_CARD = createField("f_lecturer_bank_card", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "讲师银行卡号");

    /**
     * The column <code>train.t_salary_history.f_paid_pay</code>. 实付薪酬
     */
    public final TableField<SalaryHistoryRecord, Double> PAID_PAY = createField("f_paid_pay", org.jooq.impl.SQLDataType.DOUBLE, this, "实付薪酬");

    /**
     * The column <code>train.t_salary_history.f_pay</code>. 酬金
     */
    public final TableField<SalaryHistoryRecord, Double> PAY = createField("f_pay", org.jooq.impl.SQLDataType.DOUBLE, this, "酬金");

    /**
     * The column <code>train.t_salary_history.f_tax</code>. 税金
     */
    public final TableField<SalaryHistoryRecord, Double> TAX = createField("f_tax", org.jooq.impl.SQLDataType.DOUBLE, this, "税金");

    /**
     * The column <code>train.t_salary_history.f_class_id</code>. 班级ID
     */
    public final TableField<SalaryHistoryRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * Create a <code>train.t_salary_history</code> table reference
     */
    public SalaryHistory() {
        this("t_salary_history", null);
    }

    /**
     * Create an aliased <code>train.t_salary_history</code> table reference
     */
    public SalaryHistory(String alias) {
        this(alias, SALARY_HISTORY);
    }

    private SalaryHistory(String alias, Table<SalaryHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private SalaryHistory(String alias, Table<SalaryHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "历史课酬表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SalaryHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_SALARY_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SalaryHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<SalaryHistoryRecord>>asList(Keys.KEY_T_SALARY_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SalaryHistory as(String alias) {
        return new SalaryHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SalaryHistory rename(String name) {
        return new SalaryHistory(name, null);
    }
}
