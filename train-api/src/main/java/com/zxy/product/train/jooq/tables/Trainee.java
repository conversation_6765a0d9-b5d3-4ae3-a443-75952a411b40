/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TraineeRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训学员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Trainee extends TableImpl<TraineeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_trainee</code>
     */
    public static final Trainee TRAINEE = new Trainee();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TraineeRecord> getRecordType() {
        return TraineeRecord.class;
    }

    /**
     * The column <code>train.t_trainee.f_id</code>. 表id
     */
    public final TableField<TraineeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_trainee.f_member_id</code>. 用户id
     */
    public final TableField<TraineeRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>train.t_trainee.f_type</code>. 学员类型： 0正式学员(默认) 1非正式成员
     */
    public final TableField<TraineeRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "学员类型： 0正式学员(默认) 1非正式成员");

    /**
     * The column <code>train.t_trainee.f_organization_id</code>. 组织id
     */
    public final TableField<TraineeRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织id");

    /**
     * The column <code>train.t_trainee.f_phone_number</code>. 手机号码
     */
    public final TableField<TraineeRecord, String> PHONE_NUMBER = createField("f_phone_number", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "手机号码");

    /**
     * The column <code>train.t_trainee.f_email</code>. 邮箱
     */
    public final TableField<TraineeRecord, String> EMAIL = createField("f_email", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "邮箱");

    /**
     * The column <code>train.t_trainee.f_sex</code>. 性别： 0男(默认) 1女
     */
    public final TableField<TraineeRecord, Integer> SEX = createField("f_sex", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "性别： 0男(默认) 1女");

    /**
     * The column <code>train.t_trainee.f_level_id</code>. 职级(t_member_config)
     */
    public final TableField<TraineeRecord, String> LEVEL_ID = createField("f_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "职级(t_member_config)");

    /**
     * The column <code>train.t_trainee.f_nation</code>. 民族
     */
    public final TableField<TraineeRecord, String> NATION = createField("f_nation", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "民族");

    /**
     * The column <code>train.t_trainee.f_remark</code>. 备注
     */
    public final TableField<TraineeRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(1500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>train.t_trainee.f_audit_status</code>. 审核状态： 0待审核(默认) 1通过 2拒绝
     */
    public final TableField<TraineeRecord, Integer> AUDIT_STATUS = createField("f_audit_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "审核状态： 0待审核(默认) 1通过 2拒绝");

    /**
     * The column <code>train.t_trainee.f_audit_opinion</code>. 审核意见
     */
    public final TableField<TraineeRecord, String> AUDIT_OPINION = createField("f_audit_opinion", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "审核意见");

    /**
     * The column <code>train.t_trainee.f_class_id</code>. 班级id
     */
    public final TableField<TraineeRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班级id");

    /**
     * The column <code>train.t_trainee.f_sort</code>. 排序
     */
    public final TableField<TraineeRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * The column <code>train.t_trainee.f_sort_for_group</code>. 分组中的排序
     */
    public final TableField<TraineeRecord, Integer> SORT_FOR_GROUP = createField("f_sort_for_group", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分组中的排序");

    /**
     * The column <code>train.t_trainee.f_create_time</code>. 创建时间
     */
    public final TableField<TraineeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_trainee.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public final TableField<TraineeRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态：0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_trainee.f_trainee_group_id</code>. 分组id
     */
    public final TableField<TraineeRecord, String> TRAINEE_GROUP_ID = createField("f_trainee_group_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("'0'", org.jooq.impl.SQLDataType.VARCHAR)), this, "分组id");

    /**
     * The column <code>train.t_trainee.f_create_member_id</code>. 创建人id
     */
    public final TableField<TraineeRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人id");

    /**
     * The column <code>train.t_trainee.f_settle_organization_id</code>. 结算单位ID（默认为当前学员的机构ID）
     */
    public final TableField<TraineeRecord, String> SETTLE_ORGANIZATION_ID = createField("f_settle_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "结算单位ID（默认为当前学员的机构ID）");

    /**
     * The column <code>train.t_trainee.f_commit_questionary</code>. 是否提交满意度问卷，0未提交  1已提交
     */
    public final TableField<TraineeRecord, Integer> COMMIT_QUESTIONARY = createField("f_commit_questionary", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否提交满意度问卷，0未提交  1已提交");

    /**
     * The column <code>train.t_trainee.f_source</code>. 来源  0报名  1手动添加
     */
    public final TableField<TraineeRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源  0报名  1手动添加");

    /**
     * The column <code>train.t_trainee.f_manual_sorting</code>. 是否手动排序
     */
    public final TableField<TraineeRecord, Integer> MANUAL_SORTING = createField("f_manual_sorting", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否手动排序");

    /**
     * The column <code>train.t_trainee.f_job</code>. 职务
     */
    public final TableField<TraineeRecord, String> JOB = createField("f_job", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "职务");

    /**
     * The column <code>train.t_trainee.f_new_company</code>. 修改后的公司名称
     */
    public final TableField<TraineeRecord, String> NEW_COMPANY = createField("f_new_company", org.jooq.impl.SQLDataType.VARCHAR.length(30).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "修改后的公司名称");

    /**
     * The column <code>train.t_trainee.f_new_organization</code>. 修改后的部门名称
     */
    public final TableField<TraineeRecord, String> NEW_ORGANIZATION = createField("f_new_organization", org.jooq.impl.SQLDataType.VARCHAR.length(30).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "修改后的部门名称");

    /**
     * The column <code>train.t_trainee.f_commit_four_degrees</code>. 是否提交四度问卷，0未提交 1已提交
     */
    public final TableField<TraineeRecord, Integer> COMMIT_FOUR_DEGREES = createField("f_commit_four_degrees", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否提交四度问卷，0未提交 1已提交");

    /**
     * The column <code>train.t_trainee.f_commit_ability</code>. 是否提交能力习问卷，0未提交 1已提交
     */
    public final TableField<TraineeRecord, Integer> COMMIT_ABILITY = createField("f_commit_ability", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否提交能力习问卷，0未提交 1已提交");

    /**
     * The column <code>train.t_trainee.f_commit_superior_leadership</code>. 是否提交学员上领导问卷，0未提交 1已提交
     */
    public final TableField<TraineeRecord, Integer> COMMIT_SUPERIOR_LEADERSHIP = createField("f_commit_superior_leadership", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否提交学员上领导问卷，0未提交 1已提交");

    /**
     * The column <code>train.t_trainee.f_audit_leadership</code>. 领导问卷答题人
     */
    public final TableField<TraineeRecord, String> AUDIT_LEADERSHIP = createField("f_audit_leadership", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "领导问卷答题人");

    /**
     * The column <code>train.t_trainee.f_finance</code>. 财务结算用的
     */
    public final TableField<TraineeRecord, Integer> FINANCE = createField("f_finance", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "财务结算用的");

    /**
     * The column <code>train.t_trainee.f_update_time</code>. 管理员修改时间
     */
    public final TableField<TraineeRecord, String> UPDATE_TIME = createField("f_update_time", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "管理员修改时间");

    /**
     * The column <code>train.t_trainee.f_update_month</code>. 管理员修改月份
     */
    public final TableField<TraineeRecord, String> UPDATE_MONTH = createField("f_update_month", org.jooq.impl.SQLDataType.VARCHAR.length(2).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "管理员修改月份");

    /**
     * The column <code>train.t_trainee.f_modify_date</code>. 修改时间
     */
    public final TableField<TraineeRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>train.t_trainee.f_register</code>. 是否报到（0 未报到 1 已报到）
     */
    public final TableField<TraineeRecord, Integer> REGISTER = createField("f_register", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否报到（0 未报到 1 已报到）");

    /**
     * The column <code>train.t_trainee.f_register_time</code>. 报到时间
     */
    public final TableField<TraineeRecord, Long> REGISTER_TIME = createField("f_register_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "报到时间");

    /**
     * The column <code>train.t_trainee.f_sort_new</code>. 新班级成员排序字段，f_sort字段弃用
     */
    public final TableField<TraineeRecord, Integer> SORT_NEW = createField("f_sort_new", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "新班级成员排序字段，f_sort字段弃用");

    /**
     * The column <code>train.t_trainee.f_mainland_personnel</code>. 内地人员（0 否 1 是）
     */
    public final TableField<TraineeRecord, Integer> MAINLAND_PERSONNEL = createField("f_mainland_personnel", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "内地人员（0 否 1 是）");

    /**
     * Create a <code>train.t_trainee</code> table reference
     */
    public Trainee() {
        this("t_trainee", null);
    }

    /**
     * Create an aliased <code>train.t_trainee</code> table reference
     */
    public Trainee(String alias) {
        this(alias, TRAINEE);
    }

    private Trainee(String alias, Table<TraineeRecord> aliased) {
        this(alias, aliased, null);
    }

    private Trainee(String alias, Table<TraineeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训学员表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TraineeRecord> getPrimaryKey() {
        return Keys.KEY_T_TRAINEE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TraineeRecord>> getKeys() {
        return Arrays.<UniqueKey<TraineeRecord>>asList(Keys.KEY_T_TRAINEE_PRIMARY, Keys.KEY_T_TRAINEE_UNIQUE_T_TRAIN_SECTION_P_MEMBER_TYPE_SECTION);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Trainee as(String alias) {
        return new Trainee(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Trainee rename(String name) {
        return new Trainee(name, null);
    }
}
