/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 需求方枚举表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IDemandSideOrganization extends Serializable {

    /**
     * Setter for <code>train.t_demand_side_organization.f_id</code>. 组织id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_id</code>. 组织id
     */
    public String getId();

    /**
     * Setter for <code>train.t_demand_side_organization.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_demand_side_organization.f_name</code>. 组织名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_name</code>. 组织名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_demand_side_organization.f_short_name</code>. 组织短名称
     */
    public void setShortName(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_short_name</code>. 组织短名称
     */
    public String getShortName();

    /**
     * Setter for <code>train.t_demand_side_organization.f_code</code>. 组织编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_code</code>. 组织编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_demand_side_organization.f_ihr_code</code>. ihr新组织编码
     */
    public void setIhrCode(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_ihr_code</code>. ihr新组织编码
     */
    public String getIhrCode();

    /**
     * Setter for <code>train.t_demand_side_organization.f_parent_id</code>. 上级组织
     */
    public void setParentId(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_parent_id</code>. 上级组织
     */
    public String getParentId();

    /**
     * Setter for <code>train.t_demand_side_organization.f_path</code>. 当前节点的所有父节点
     */
    public void setPath(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_path</code>. 当前节点的所有父节点
     */
    public String getPath();

    /**
     * Setter for <code>train.t_demand_side_organization.f_cmcc_level</code>. 公司分类(移动项目)
     */
    public void setCmccLevel(Integer value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_cmcc_level</code>. 公司分类(移动项目)
     */
    public Integer getCmccLevel();

    /**
     * Setter for <code>train.t_demand_side_organization.f_level</code>. 组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)
     */
    public void setLevel(Integer value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_level</code>. 组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)
     */
    public Integer getLevel();

    /**
     * Setter for <code>train.t_demand_side_organization.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_order</code>. 排序
     */
    public Integer getOrder();

    /**
     * Setter for <code>train.t_demand_side_organization.f_cmcc_attribute</code>. 组织机构属性(移动项目)
     */
    public void setCmccAttribute(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_cmcc_attribute</code>. 组织机构属性(移动项目)
     */
    public String getCmccAttribute();

    /**
     * Setter for <code>train.t_demand_side_organization.f_cmcc_category</code>. 组织机构类型(移动项目)
     */
    public void setCmccCategory(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_cmcc_category</code>. 组织机构类型(移动项目)
     */
    public String getCmccCategory();

    /**
     * Setter for <code>train.t_demand_side_organization.f_status</code>. 组织状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_status</code>. 组织状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_demand_side_organization.f_company_id</code>. 所属机构
     */
    public void setCompanyId(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_company_id</code>. 所属机构
     */
    public String getCompanyId();

    /**
     * Setter for <code>train.t_demand_side_organization.f_site_id</code>. 站点id
     */
    public void setSiteId(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_site_id</code>. 站点id
     */
    public String getSiteId();

    /**
     * Setter for <code>train.t_demand_side_organization.f_mis_code</code>. MIS省公司简称，用于同步数据
     */
    public void setMisCode(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_mis_code</code>. MIS省公司简称，用于同步数据
     */
    public String getMisCode();

    /**
     * Setter for <code>train.t_demand_side_organization.f_depth</code>. 表示当前组织深度
     */
    public void setDepth(Integer value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_depth</code>. 表示当前组织深度
     */
    public Integer getDepth();

    /**
     * Setter for <code>train.t_demand_side_organization.f_type</code>.
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_type</code>.
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_demand_side_organization.f_area_code</code>. 区号
     */
    public void setAreaCode(String value);

    /**
     * Getter for <code>train.t_demand_side_organization.f_area_code</code>. 区号
     */
    public String getAreaCode();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IDemandSideOrganization
     */
    public void from(IDemandSideOrganization from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IDemandSideOrganization
     */
    public <E extends IDemandSideOrganization> E into(E into);
}
