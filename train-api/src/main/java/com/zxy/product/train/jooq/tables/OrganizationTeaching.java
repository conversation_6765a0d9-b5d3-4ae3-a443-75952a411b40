/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.OrganizationTeachingRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 各单位授课记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationTeaching extends TableImpl<OrganizationTeachingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_organization_teaching</code>
     */
    public static final OrganizationTeaching ORGANIZATION_TEACHING = new OrganizationTeaching();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OrganizationTeachingRecord> getRecordType() {
        return OrganizationTeachingRecord.class;
    }

    /**
     * The column <code>train.t_organization_teaching.f_id</code>. 主键
     */
    public final TableField<OrganizationTeachingRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_organization_teaching.f_course_id</code>. 课程ID
     */
    public final TableField<OrganizationTeachingRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程ID");

    /**
     * The column <code>train.t_organization_teaching.f_course_name</code>. 授课名称
     */
    public final TableField<OrganizationTeachingRecord, String> COURSE_NAME = createField("f_course_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "授课名称");

    /**
     * The column <code>train.t_organization_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public final TableField<OrganizationTeachingRecord, String> LECTURER_ID = createField("f_lecturer_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程讲师ID");

    /**
     * The column <code>train.t_organization_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    public final TableField<OrganizationTeachingRecord, String> LECTURER_NAME = createField("f_lecturer_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "课程讲师姓名");

    /**
     * The column <code>train.t_organization_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    public final TableField<OrganizationTeachingRecord, String> LECTURER_POST = createField("f_lecturer_post", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程讲师职务");

    /**
     * The column <code>train.t_organization_teaching.f_reference_dollars</code>. 参考课酬(元)
     */
    public final TableField<OrganizationTeachingRecord, Double> REFERENCE_DOLLARS = createField("f_reference_dollars", org.jooq.impl.SQLDataType.DOUBLE, this, "参考课酬(元)");

    /**
     * The column <code>train.t_organization_teaching.f_course_duration</code>. 课程时长
     */
    public final TableField<OrganizationTeachingRecord, Double> COURSE_DURATION = createField("f_course_duration", org.jooq.impl.SQLDataType.FLOAT, this, "课程时长");

    /**
     * The column <code>train.t_organization_teaching.f_organization_name</code>. 主办部门
     */
    public final TableField<OrganizationTeachingRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "主办部门");

    /**
     * The column <code>train.t_organization_teaching.f_end_date</code>. 授课结束日期
     */
    public final TableField<OrganizationTeachingRecord, Long> END_DATE = createField("f_end_date", org.jooq.impl.SQLDataType.BIGINT, this, "授课结束日期");

    /**
     * The column <code>train.t_organization_teaching.f_start_date</code>. 授课起止日期
     */
    public final TableField<OrganizationTeachingRecord, Long> START_DATE = createField("f_start_date", org.jooq.impl.SQLDataType.BIGINT, this, "授课起止日期");

    /**
     * The column <code>train.t_organization_teaching.f_class_name</code>. 培训班名称
     */
    public final TableField<OrganizationTeachingRecord, String> CLASS_NAME = createField("f_class_name", org.jooq.impl.SQLDataType.VARCHAR.length(150), this, "培训班名称");

    /**
     * The column <code>train.t_organization_teaching.f_class_member</code>. 培训班联系人
     */
    public final TableField<OrganizationTeachingRecord, String> CLASS_MEMBER = createField("f_class_member", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "培训班联系人");

    /**
     * The column <code>train.t_organization_teaching.f_object</code>. 授课对象
     */
    public final TableField<OrganizationTeachingRecord, String> OBJECT = createField("f_object", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "授课对象");

    /**
     * The column <code>train.t_organization_teaching.f_attachment_id</code>. 附件id
     */
    public final TableField<OrganizationTeachingRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "附件id");

    /**
     * The column <code>train.t_organization_teaching.f_member_count</code>. 授课人数
     */
    public final TableField<OrganizationTeachingRecord, Integer> MEMBER_COUNT = createField("f_member_count", org.jooq.impl.SQLDataType.INTEGER, this, "授课人数");

    /**
     * The column <code>train.t_organization_teaching.f_pay</code>. 实付(元)
     */
    public final TableField<OrganizationTeachingRecord, Double> PAY = createField("f_pay", org.jooq.impl.SQLDataType.DOUBLE, this, "实付(元)");

    /**
     * The column <code>train.t_organization_teaching.f_tax</code>. 税金(元)
     */
    public final TableField<OrganizationTeachingRecord, Double> TAX = createField("f_tax", org.jooq.impl.SQLDataType.DOUBLE, this, "税金(元)");

    /**
     * The column <code>train.t_organization_teaching.f_remuneration</code>. 酬金
     */
    public final TableField<OrganizationTeachingRecord, Double> REMUNERATION = createField("f_remuneration", org.jooq.impl.SQLDataType.DOUBLE, this, "酬金");

    /**
     * The column <code>train.t_organization_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public final TableField<OrganizationTeachingRecord, Double> SATISFIED_DEGREE = createField("f_satisfied_degree", org.jooq.impl.SQLDataType.FLOAT, this, "平均满意度(十分制，小数点后保留一位)");

    /**
     * The column <code>train.t_organization_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    public final TableField<OrganizationTeachingRecord, String> SATISFIED_EVALUATE = createField("f_satisfied_evaluate", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "满意度评价");

    /**
     * The column <code>train.t_organization_teaching.f_attachment_name</code>. 附件名称
     */
    public final TableField<OrganizationTeachingRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "附件名称");

    /**
     * The column <code>train.t_organization_teaching.f_teaching_type</code>. 教学教研类型
     */
    public final TableField<OrganizationTeachingRecord, String> TEACHING_TYPE = createField("f_teaching_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "教学教研类型");

    /**
     * The column <code>train.t_organization_teaching.f_create_time</code>. 创建时间
     */
    public final TableField<OrganizationTeachingRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_organization_teaching.f_approval_time</code>. 审核时间
     */
    public final TableField<OrganizationTeachingRecord, Long> APPROVAL_TIME = createField("f_approval_time", org.jooq.impl.SQLDataType.BIGINT, this, "审核时间");

    /**
     * The column <code>train.t_organization_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    public final TableField<OrganizationTeachingRecord, Integer> APPROVAL_STATUS = createField("f_approval_status", org.jooq.impl.SQLDataType.INTEGER, this, "是否通过0：待审核1：通过2：拒绝");

    /**
     * The column <code>train.t_organization_teaching.f_organization_id</code>. 归属部门
     */
    public final TableField<OrganizationTeachingRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "归属部门");

    /**
     * The column <code>train.t_organization_teaching.f_create_member</code>. 创建ID
     */
    public final TableField<OrganizationTeachingRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建ID");

    /**
     * The column <code>train.t_organization_teaching.f_approval_member</code>. 审核人ID
     */
    public final TableField<OrganizationTeachingRecord, String> APPROVAL_MEMBER = createField("f_approval_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "审核人ID");

    /**
     * The column <code>train.t_organization_teaching.f_remark</code>. 备注
     */
    public final TableField<OrganizationTeachingRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "备注");

    /**
     * The column <code>train.t_organization_teaching.f_source</code>. 0：讲师自己申请 1：集采登记
     */
    public final TableField<OrganizationTeachingRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER, this, "0：讲师自己申请 1：集采登记");

    /**
     * Create a <code>train.t_organization_teaching</code> table reference
     */
    public OrganizationTeaching() {
        this("t_organization_teaching", null);
    }

    /**
     * Create an aliased <code>train.t_organization_teaching</code> table reference
     */
    public OrganizationTeaching(String alias) {
        this(alias, ORGANIZATION_TEACHING);
    }

    private OrganizationTeaching(String alias, Table<OrganizationTeachingRecord> aliased) {
        this(alias, aliased, null);
    }

    private OrganizationTeaching(String alias, Table<OrganizationTeachingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "各单位授课记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OrganizationTeachingRecord> getPrimaryKey() {
        return Keys.KEY_T_ORGANIZATION_TEACHING_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OrganizationTeachingRecord>> getKeys() {
        return Arrays.<UniqueKey<OrganizationTeachingRecord>>asList(Keys.KEY_T_ORGANIZATION_TEACHING_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationTeaching as(String alias) {
        return new OrganizationTeaching(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public OrganizationTeaching rename(String name) {
        return new OrganizationTeaching(name, null);
    }
}
