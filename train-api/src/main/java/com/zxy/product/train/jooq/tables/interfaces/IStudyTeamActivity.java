/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习活动表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamActivity extends Serializable {

    /**
     * Setter for <code>train.t_study_team_activity.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_activity.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_activity.f_team_id</code>. 团队id
     */
    public void setTeamId(String value);

    /**
     * Getter for <code>train.t_study_team_activity.f_team_id</code>. 团队id
     */
    public String getTeamId();

    /**
     * Setter for <code>train.t_study_team_activity.f_name</code>. 活动名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_study_team_activity.f_name</code>. 活动名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_study_team_activity.f_place</code>. 活动地点
     */
    public void setPlace(String value);

    /**
     * Getter for <code>train.t_study_team_activity.f_place</code>. 活动地点
     */
    public String getPlace();

    /**
     * Setter for <code>train.t_study_team_activity.f_begin_time</code>. 活动开始时间
     */
    public void setBeginTime(Long value);

    /**
     * Getter for <code>train.t_study_team_activity.f_begin_time</code>. 活动开始时间
     */
    public Long getBeginTime();

    /**
     * Setter for <code>train.t_study_team_activity.f_end_time</code>. 活动结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_study_team_activity.f_end_time</code>. 活动结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_study_team_activity.f_leader_member_id</code>. 领学人id
     */
    public void setLeaderMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_activity.f_leader_member_id</code>. 领学人id
     */
    public String getLeaderMemberId();

    /**
     * Setter for <code>train.t_study_team_activity.f_cover</code>. 封面
     */
    public void setCover(String value);

    /**
     * Getter for <code>train.t_study_team_activity.f_cover</code>. 封面
     */
    public String getCover();

    /**
     * Setter for <code>train.t_study_team_activity.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_activity.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_study_team_activity.f_confirmation_status</code>. 活动结束一小时后自动同步时长状态1:已经同步，0:未同步
     */
    public void setConfirmationStatus(Boolean value);

    /**
     * Getter for <code>train.t_study_team_activity.f_confirmation_status</code>. 活动结束一小时后自动同步时长状态1:已经同步，0:未同步
     */
    public Boolean getConfirmationStatus();

    /**
     * Setter for <code>train.t_study_team_activity.f_old</code>. 是否是历史数据的活动，初始化的时候用的切记不要更新
     */
    public void setOld(Boolean value);

    /**
     * Getter for <code>train.t_study_team_activity.f_old</code>. 是否是历史数据的活动，初始化的时候用的切记不要更新
     */
    public Boolean getOld();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamActivity
     */
    public void from(IStudyTeamActivity from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamActivity
     */
    public <E extends IStudyTeamActivity> E into(E into);
}
