/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassGradesProject extends Serializable {

    /**
     * Setter for <code>train.t_class_grades_project.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_grades_project.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_grades_project.f_name</code>. 项目名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_class_grades_project.f_name</code>. 项目名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_class_grades_project.f_status</code>. 学员端状态 0:隐藏 1:显示
     */
    public void setStatus(String value);

    /**
     * Getter for <code>train.t_class_grades_project.f_status</code>. 学员端状态 0:隐藏 1:显示
     */
    public String getStatus();

    /**
     * Setter for <code>train.t_class_grades_project.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_grades_project.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_grades_project.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_grades_project.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_grades_project.f_class_id</code>. 培训班id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_grades_project.f_class_id</code>. 培训班id
     */
    public String getClassId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassGradesProject
     */
    public void from(IClassGradesProject from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassGradesProject
     */
    public <E extends IClassGradesProject> E into(E into);
}
