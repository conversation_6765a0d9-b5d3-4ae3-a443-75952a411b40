/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamAchievementRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 团队学习班-学习成果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamAchievement extends TableImpl<StudyTeamAchievementRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_achievement</code>
     */
    public static final StudyTeamAchievement STUDY_TEAM_ACHIEVEMENT = new StudyTeamAchievement();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamAchievementRecord> getRecordType() {
        return StudyTeamAchievementRecord.class;
    }

    /**
     * The column <code>train.t_study_team_achievement.f_id</code>. 主键
     */
    public final TableField<StudyTeamAchievementRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_study_team_achievement.f_team_id</code>. 团队id
     */
    public final TableField<StudyTeamAchievementRecord, String> TEAM_ID = createField("f_team_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队id");

    /**
     * The column <code>train.t_study_team_achievement.f_activity_id</code>. 活动id
     */
    public final TableField<StudyTeamAchievementRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "活动id");

    /**
     * The column <code>train.t_study_team_achievement.f_member_id</code>. 用户id
     */
    public final TableField<StudyTeamAchievementRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "用户id");

    /**
     * The column <code>train.t_study_team_achievement.f_business_id</code>. 关联课程id
     */
    public final TableField<StudyTeamAchievementRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联课程id");

    /**
     * The column <code>train.t_study_team_achievement.f_content</code>. 讨论内容
     */
    public final TableField<StudyTeamAchievementRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB.nullable(false), this, "讨论内容");

    /**
     * The column <code>train.t_study_team_achievement.f_top_status</code>. 置顶(1是，0否)
     */
    public final TableField<StudyTeamAchievementRecord, Integer> TOP_STATUS = createField("f_top_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "置顶(1是，0否)");

    /**
     * The column <code>train.t_study_team_achievement.f_essence_status</code>. 精华(1是，0否)
     */
    public final TableField<StudyTeamAchievementRecord, Integer> ESSENCE_STATUS = createField("f_essence_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "精华(1是，0否)");

    /**
     * The column <code>train.t_study_team_achievement.f_hide</code>. 隐藏(1是，0否)
     */
    public final TableField<StudyTeamAchievementRecord, Integer> HIDE = createField("f_hide", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "隐藏(1是，0否)");

    /**
     * The column <code>train.t_study_team_achievement.f_comment_count</code>. 讨论数
     */
    public final TableField<StudyTeamAchievementRecord, Integer> COMMENT_COUNT = createField("f_comment_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "讨论数");

    /**
     * The column <code>train.t_study_team_achievement.f_praise_count</code>. 点赞数
     */
    public final TableField<StudyTeamAchievementRecord, Integer> PRAISE_COUNT = createField("f_praise_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "点赞数");

    /**
     * The column <code>train.t_study_team_achievement.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public final TableField<StudyTeamAchievementRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态(0未删除，1已删除)");

    /**
     * The column <code>train.t_study_team_achievement.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamAchievementRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_study_team_achievement.f_organization_id</code>. 所属组织id
     */
    public final TableField<StudyTeamAchievementRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "所属组织id");

    /**
     * The column <code>train.t_study_team_achievement.f_content_text</code>. 讨论内容——纯文本
     */
    public final TableField<StudyTeamAchievementRecord, String> CONTENT_TEXT = createField("f_content_text", org.jooq.impl.SQLDataType.CLOB.nullable(false), this, "讨论内容——纯文本");

    /**
     * The column <code>train.t_study_team_achievement.f_essence_time</code>. 加精时间
     */
    public final TableField<StudyTeamAchievementRecord, Long> ESSENCE_TIME = createField("f_essence_time", org.jooq.impl.SQLDataType.BIGINT, this, "加精时间");

    /**
     * The column <code>train.t_study_team_achievement.f_top_time</code>. 置顶时间
     */
    public final TableField<StudyTeamAchievementRecord, Long> TOP_TIME = createField("f_top_time", org.jooq.impl.SQLDataType.BIGINT, this, "置顶时间");

    /**
     * Create a <code>train.t_study_team_achievement</code> table reference
     */
    public StudyTeamAchievement() {
        this("t_study_team_achievement", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_achievement</code> table reference
     */
    public StudyTeamAchievement(String alias) {
        this(alias, STUDY_TEAM_ACHIEVEMENT);
    }

    private StudyTeamAchievement(String alias, Table<StudyTeamAchievementRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamAchievement(String alias, Table<StudyTeamAchievementRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "团队学习班-学习成果表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamAchievementRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_ACHIEVEMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamAchievementRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamAchievementRecord>>asList(Keys.KEY_T_STUDY_TEAM_ACHIEVEMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievement as(String alias) {
        return new StudyTeamAchievement(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamAchievement rename(String name) {
        return new StudyTeamAchievement(name, null);
    }
}
