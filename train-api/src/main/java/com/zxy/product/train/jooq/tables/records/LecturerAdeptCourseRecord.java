/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.LecturerAdeptCourse;
import com.zxy.product.train.jooq.tables.interfaces.ILecturerAdeptCourse;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 讲师擅长课程列表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerAdeptCourseRecord extends UpdatableRecordImpl<LecturerAdeptCourseRecord> implements Record11<String, String, String, Double, Double, String, String, Long, String, String, String>, ILecturerAdeptCourse {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_id</code>. 系统ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_id</code>. 系统ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_lecturer_id</code>. 讲师ID
     */
    @Override
    public void setLecturerId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_lecturer_id</code>. 讲师ID
     */
    @Override
    public String getLecturerId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_course_id</code>. 所擅长的课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_course_id</code>. 所擅长的课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_reference_time</code>. 参考时长
     */
    @Override
    public void setReferenceTime(Double value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_reference_time</code>. 参考时长
     */
    @Override
    public Double getReferenceTime() {
        return (Double) get(3);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_reference_remuneration</code>. 参考课酬(元)
     */
    @Override
    public void setReferenceRemuneration(Double value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_reference_remuneration</code>. 参考课酬(元)
     */
    @Override
    public Double getReferenceRemuneration() {
        return (Double) get(4);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_course_url</code>. 授课视频URL
     */
    @Override
    public void setCourseUrl(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_course_url</code>. 授课视频URL
     */
    @Override
    public String getCourseUrl() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_create_member_id</code>. 创建人
     */
    @Override
    public void setCreateMemberId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_create_member_id</code>. 创建人
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_attachment_id</code>. 附件ID
     */
    @Override
    public void setAttachmentId(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_attachment_id</code>. 附件ID
     */
    @Override
    public String getAttachmentId() {
        return (String) get(9);
    }

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_attachment_name</code>. 附件名称
     */
    @Override
    public void setAttachmentName(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_attachment_name</code>. 附件名称
     */
    @Override
    public String getAttachmentName() {
        return (String) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row11<String, String, String, Double, Double, String, String, Long, String, String, String> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row11<String, String, String, Double, Double, String, String, Long, String, String, String> valuesRow() {
        return (Row11) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.LECTURER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field4() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.REFERENCE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field5() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.REFERENCE_REMUNERATION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.COURSE_URL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field8() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.ATTACHMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return LecturerAdeptCourse.LECTURER_ADEPT_COURSE.ATTACHMENT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getLecturerId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value4() {
        return getReferenceTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value5() {
        return getReferenceRemuneration();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCourseUrl();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value8() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getAttachmentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getAttachmentName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value2(String value) {
        setLecturerId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value3(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value4(Double value) {
        setReferenceTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value5(Double value) {
        setReferenceRemuneration(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value6(String value) {
        setCourseUrl(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value7(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value8(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value9(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value10(String value) {
        setAttachmentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord value11(String value) {
        setAttachmentName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAdeptCourseRecord values(String value1, String value2, String value3, Double value4, Double value5, String value6, String value7, Long value8, String value9, String value10, String value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILecturerAdeptCourse from) {
        setId(from.getId());
        setLecturerId(from.getLecturerId());
        setCourseId(from.getCourseId());
        setReferenceTime(from.getReferenceTime());
        setReferenceRemuneration(from.getReferenceRemuneration());
        setCourseUrl(from.getCourseUrl());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setCreateMemberId(from.getCreateMemberId());
        setAttachmentId(from.getAttachmentId());
        setAttachmentName(from.getAttachmentName());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILecturerAdeptCourse> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LecturerAdeptCourseRecord
     */
    public LecturerAdeptCourseRecord() {
        super(LecturerAdeptCourse.LECTURER_ADEPT_COURSE);
    }

    /**
     * Create a detached, initialised LecturerAdeptCourseRecord
     */
    public LecturerAdeptCourseRecord(String id, String lecturerId, String courseId, Double referenceTime, Double referenceRemuneration, String courseUrl, String organizationId, Long createTime, String createMemberId, String attachmentId, String attachmentName) {
        super(LecturerAdeptCourse.LECTURER_ADEPT_COURSE);

        set(0, id);
        set(1, lecturerId);
        set(2, courseId);
        set(3, referenceTime);
        set(4, referenceRemuneration);
        set(5, courseUrl);
        set(6, organizationId);
        set(7, createTime);
        set(8, createMemberId);
        set(9, attachmentId);
        set(10, attachmentName);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.LecturerAdeptCourseEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.LecturerAdeptCourseEntity pojo = (com.zxy.product.train.jooq.tables.pojos.LecturerAdeptCourseEntity)source;
        pojo.into(this);
        return true;
    }
}
