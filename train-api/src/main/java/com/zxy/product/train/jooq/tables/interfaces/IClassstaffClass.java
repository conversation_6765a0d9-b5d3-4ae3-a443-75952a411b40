/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 培训班级和班务人员关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassstaffClass extends Serializable {

    /**
     * Setter for <code>train.t_classstaff_class.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_classstaff_class.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_classstaff_class.f_member_id</code>. 班务人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_classstaff_class.f_member_id</code>. 班务人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_classstaff_class.f_type</code>. 班务人员类型： 0.班务人员 1.班主任
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_classstaff_class.f_type</code>. 班务人员类型： 0.班务人员 1.班主任
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_classstaff_class.f_call_name</code>. 称呼
     */
    public void setCallName(String value);

    /**
     * Getter for <code>train.t_classstaff_class.f_call_name</code>. 称呼
     */
    public String getCallName();

    /**
     * Setter for <code>train.t_classstaff_class.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_classstaff_class.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_classstaff_class.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_classstaff_class.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_classstaff_class.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_classstaff_class.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_classstaff_class.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_classstaff_class.f_sort</code>. 排序
     */
    public Integer getSort();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassstaffClass
     */
    public void from(IClassstaffClass from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassstaffClass
     */
    public <E extends IClassstaffClass> E into(E into);
}
