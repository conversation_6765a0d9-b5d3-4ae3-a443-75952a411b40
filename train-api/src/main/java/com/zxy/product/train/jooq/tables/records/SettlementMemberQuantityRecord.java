/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.SettlementMemberQuantity;
import com.zxy.product.train.jooq.tables.interfaces.ISettlementMemberQuantity;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 培训班结算人数配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SettlementMemberQuantityRecord extends UpdatableRecordImpl<SettlementMemberQuantityRecord> implements Record5<String, String, Long, Integer, Long>, ISettlementMemberQuantity {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_class_id</code>. 班级id
     */
    @Override
    public void setClassId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_class_id</code>. 班级id
     */
    @Override
    public String getClassId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_date</code>. 培训日期
     */
    @Override
    public void setDate(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_date</code>. 培训日期
     */
    @Override
    public Long getDate() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_quantity</code>. 培训人数
     */
    @Override
    public void setQuantity(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_quantity</code>. 培训人数
     */
    @Override
    public Integer getQuantity() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_settlement_member_quantity.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_settlement_member_quantity.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, Long, Integer, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, Long, Integer, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field3() {
        return SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY.DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY.QUANTITY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value3() {
        return getDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getQuantity();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementMemberQuantityRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementMemberQuantityRecord value2(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementMemberQuantityRecord value3(Long value) {
        setDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementMemberQuantityRecord value4(Integer value) {
        setQuantity(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementMemberQuantityRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementMemberQuantityRecord values(String value1, String value2, Long value3, Integer value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISettlementMemberQuantity from) {
        setId(from.getId());
        setClassId(from.getClassId());
        setDate(from.getDate());
        setQuantity(from.getQuantity());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISettlementMemberQuantity> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SettlementMemberQuantityRecord
     */
    public SettlementMemberQuantityRecord() {
        super(SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY);
    }

    /**
     * Create a detached, initialised SettlementMemberQuantityRecord
     */
    public SettlementMemberQuantityRecord(String id, String classId, Long date, Integer quantity, Long createTime) {
        super(SettlementMemberQuantity.SETTLEMENT_MEMBER_QUANTITY);

        set(0, id);
        set(1, classId);
        set(2, date);
        set(3, quantity);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.SettlementMemberQuantityEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.SettlementMemberQuantityEntity pojo = (com.zxy.product.train.jooq.tables.pojos.SettlementMemberQuantityEntity)source;
        pojo.into(this);
        return true;
    }
}
