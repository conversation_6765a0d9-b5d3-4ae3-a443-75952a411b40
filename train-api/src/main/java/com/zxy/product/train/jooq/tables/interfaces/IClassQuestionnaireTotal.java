/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 新版满意度问卷数据表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassQuestionnaireTotal extends Serializable {

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_questionnaire_id</code>. 问卷ID
     */
    public void setQuestionnaireId(String value);

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_questionnaire_id</code>. 问卷ID
     */
    public String getQuestionnaireId();

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_question_id</code>. 问题ID
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_question_id</code>. 问题ID
     */
    public String getQuestionId();

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_answer</code>. 答案
     */
    public void setAnswer(String value);

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_answer</code>. 答案
     */
    public String getAnswer();

    /**
     * Setter for <code>train.t_class_questionnaire_total.f_number</code>. 作答人数
     */
    public void setNumber(Integer value);

    /**
     * Getter for <code>train.t_class_questionnaire_total.f_number</code>. 作答人数
     */
    public Integer getNumber();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassQuestionnaireTotal
     */
    public void from(IClassQuestionnaireTotal from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassQuestionnaireTotal
     */
    public <E extends IClassQuestionnaireTotal> E into(E into);
}
