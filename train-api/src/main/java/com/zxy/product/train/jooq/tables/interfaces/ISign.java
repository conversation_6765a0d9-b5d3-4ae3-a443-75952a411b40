/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 签到表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISign extends Serializable {

    /**
     * Setter for <code>train.t_sign.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_sign.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_sign.f_name</code>. 签到名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_sign.f_name</code>. 签到名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_sign.f_start_time</code>. 签到起始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_sign.f_start_time</code>. 签到起始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_sign.f_end_time</code>. 签到结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_sign.f_end_time</code>. 签到结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_sign.f_place</code>. 签到地点
     */
    public void setPlace(String value);

    /**
     * Getter for <code>train.t_sign.f_place</code>. 签到地点
     */
    public String getPlace();

    /**
     * Setter for <code>train.t_sign.f_late_time</code>. 迟到时间设置
     */
    public void setLateTime(Long value);

    /**
     * Getter for <code>train.t_sign.f_late_time</code>. 迟到时间设置
     */
    public Long getLateTime();

    /**
     * Setter for <code>train.t_sign.f_delete_flag</code>. 删除状态 0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_sign.f_delete_flag</code>. 删除状态 0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_sign.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_sign.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_sign.f_sign_num</code>. 签到人数
     */
    public void setSignNum(Integer value);

    /**
     * Getter for <code>train.t_sign.f_sign_num</code>. 签到人数
     */
    public Integer getSignNum();

    /**
     * Setter for <code>train.t_sign.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_sign.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_sign.f_create_mem</code>. 创建人ID
     */
    public void setCreateMem(String value);

    /**
     * Getter for <code>train.t_sign.f_create_mem</code>. 创建人ID
     */
    public String getCreateMem();

    /**
     * Setter for <code>train.t_sign.f_type</code>. 签到类型 1半天 2全天 3不签到
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_sign.f_type</code>. 签到类型 1半天 2全天 3不签到
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_sign.f_sign_in_type</code>. 签到方式（0扫码签到 1 考勤机签到）
     */
    public void setSignInType(Integer value);

    /**
     * Getter for <code>train.t_sign.f_sign_in_type</code>. 签到方式（0扫码签到 1 考勤机签到）
     */
    public Integer getSignInType();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISign
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.ISign from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISign
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.ISign> E into(E into);
}
