/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IDimension extends Serializable {

    /**
     * Setter for <code>train.t_dimension.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_dimension.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_dimension.f_name</code>. 维度名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_dimension.f_name</code>. 维度名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_dimension.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_dimension.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_dimension.f_description</code>. 描述
     */
    public void setDescription(String value);

    /**
     * Getter for <code>train.t_dimension.f_description</code>. 描述
     */
    public String getDescription();

    /**
     * Setter for <code>train.t_dimension.f_research_questionary_id</code>. 调研问卷
     */
    public void setResearchQuestionaryId(String value);

    /**
     * Getter for <code>train.t_dimension.f_research_questionary_id</code>. 调研问卷
     */
    public String getResearchQuestionaryId();

    /**
     * Setter for <code>train.t_dimension.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_dimension.f_order</code>. 排序
     */
    public Integer getOrder();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IDimension
     */
    public void from(IDimension from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IDimension
     */
    public <E extends IDimension> E into(E into);
}
