/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassHistoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 历史班级表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassHistory extends TableImpl<ClassHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_history</code>
     */
    public static final ClassHistory CLASS_HISTORY = new ClassHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassHistoryRecord> getRecordType() {
        return ClassHistoryRecord.class;
    }

    /**
     * The column <code>train.t_class_history.f_id</code>.
     */
    public final TableField<ClassHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_history.f_project_id</code>. 计划ID
     */
    public final TableField<ClassHistoryRecord, String> PROJECT_ID = createField("f_project_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "计划ID");

    /**
     * The column <code>train.t_class_history.f_class_teacher</code>. 班主任
     */
    public final TableField<ClassHistoryRecord, String> CLASS_TEACHER = createField("f_class_teacher", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班主任");

    /**
     * The column <code>train.t_class_history.f_class_teacher_phone</code>. 班主任电话
     */
    public final TableField<ClassHistoryRecord, String> CLASS_TEACHER_PHONE = createField("f_class_teacher_phone", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班主任电话");

    /**
     * The column <code>train.t_class_history.f_arrive_date</code>. 报道日
     */
    public final TableField<ClassHistoryRecord, Long> ARRIVE_DATE = createField("f_arrive_date", org.jooq.impl.SQLDataType.BIGINT, this, "报道日");

    /**
     * The column <code>train.t_class_history.f_return_date</code>. 返程日
     */
    public final TableField<ClassHistoryRecord, Long> RETURN_DATE = createField("f_return_date", org.jooq.impl.SQLDataType.BIGINT, this, "返程日");

    /**
     * The column <code>train.t_class_history.f_is_outside</code>. 是否外部举办:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> IS_OUTSIDE = createField("f_is_outside", org.jooq.impl.SQLDataType.INTEGER, this, "是否外部举办:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_survey_type</code>. 需求调研方式
     */
    public final TableField<ClassHistoryRecord, String> SURVEY_TYPE = createField("f_survey_type", org.jooq.impl.SQLDataType.VARCHAR.length(1000), this, "需求调研方式");

    /**
     * The column <code>train.t_class_history.f_target</code>. 培训目标
     */
    public final TableField<ClassHistoryRecord, String> TARGET = createField("f_target", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "培训目标");

    /**
     * The column <code>train.t_class_history.f_class_info_type</code>. 班级类别
     */
    public final TableField<ClassHistoryRecord, String> CLASS_INFO_TYPE = createField("f_class_info_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级类别");

    /**
     * The column <code>train.t_class_history.f_student_type</code>. 人员类别
     */
    public final TableField<ClassHistoryRecord, String> STUDENT_TYPE = createField("f_student_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "人员类别");

    /**
     * The column <code>train.t_class_history.f_is_plan</code>. 是否计划内:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> IS_PLAN = createField("f_is_plan", org.jooq.impl.SQLDataType.INTEGER, this, "是否计划内:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_status</code>. 班级状态
     */
    public final TableField<ClassHistoryRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "班级状态");

    /**
     * The column <code>train.t_class_history.f_have_province_leader</code>. 是否有省公司二级经理参加:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> HAVE_PROVINCE_LEADER = createField("f_have_province_leader", org.jooq.impl.SQLDataType.INTEGER, this, "是否有省公司二级经理参加:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_have_minister</code>. 是否有部长及以上领导参加:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> HAVE_MINISTER = createField("f_have_minister", org.jooq.impl.SQLDataType.INTEGER, this, "是否有部长及以上领导参加:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_need_group_photo</code>. 是否合影:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> NEED_GROUP_PHOTO = createField("f_need_group_photo", org.jooq.impl.SQLDataType.INTEGER, this, "是否合影:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_photo_time</code>. 合影时间
     */
    public final TableField<ClassHistoryRecord, Long> PHOTO_TIME = createField("f_photo_time", org.jooq.impl.SQLDataType.BIGINT, this, "合影时间");

    /**
     * The column <code>train.t_class_history.f_need_video</code>. 课程录像:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> NEED_VIDEO = createField("f_need_video", org.jooq.impl.SQLDataType.INTEGER, this, "课程录像:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_video_requirement</code>. 录像需求
     */
    public final TableField<ClassHistoryRecord, String> VIDEO_REQUIREMENT = createField("f_video_requirement", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "录像需求");

    /**
     * The column <code>train.t_class_history.f_need_make_course</code>. 课程制作:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> NEED_MAKE_COURSE = createField("f_need_make_course", org.jooq.impl.SQLDataType.INTEGER, this, "课程制作:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_course_video_requirement</code>. 课程制作录像需求
     */
    public final TableField<ClassHistoryRecord, String> COURSE_VIDEO_REQUIREMENT = createField("f_course_video_requirement", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "课程制作录像需求");

    /**
     * The column <code>train.t_class_history.f_table_type</code>. 教室桌形:1.上课,2.岛型,3.回字形,4.其他
     */
    public final TableField<ClassHistoryRecord, Integer> TABLE_TYPE = createField("f_table_type", org.jooq.impl.SQLDataType.INTEGER, this, "教室桌形:1.上课,2.岛型,3.回字形,4.其他");

    /**
     * The column <code>train.t_class_history.f_other_requirement</code>. 其它需求
     */
    public final TableField<ClassHistoryRecord, String> OTHER_REQUIREMENT = createField("f_other_requirement", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "其它需求");

    /**
     * The column <code>train.t_class_history.f_rest_room</code>. 客房
     */
    public final TableField<ClassHistoryRecord, String> REST_ROOM = createField("f_rest_room", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "客房");

    /**
     * The column <code>train.t_class_history.f_dining_room</code>. 餐厅
     */
    public final TableField<ClassHistoryRecord, String> DINING_ROOM = createField("f_dining_room", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "餐厅");

    /**
     * The column <code>train.t_class_history.f_classroom</code>. 教室
     */
    public final TableField<ClassHistoryRecord, String> CLASSROOM = createField("f_classroom", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "教室");

    /**
     * The column <code>train.t_class_history.f_is_open</code>. 是否开放报名:0否, 1是
     */
    public final TableField<ClassHistoryRecord, Integer> IS_OPEN = createField("f_is_open", org.jooq.impl.SQLDataType.INTEGER, this, "是否开放报名:0否, 1是");

    /**
     * The column <code>train.t_class_history.f_start_time</code>. 报名开始时间
     */
    public final TableField<ClassHistoryRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "报名开始时间");

    /**
     * The column <code>train.t_class_history.f_end_time</code>. 报名结束时间
     */
    public final TableField<ClassHistoryRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "报名结束时间");

    /**
     * The column <code>train.t_class_history.f_signup_code</code>. 报名码
     */
    public final TableField<ClassHistoryRecord, String> SIGNUP_CODE = createField("f_signup_code", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "报名码");

    /**
     * Create a <code>train.t_class_history</code> table reference
     */
    public ClassHistory() {
        this("t_class_history", null);
    }

    /**
     * Create an aliased <code>train.t_class_history</code> table reference
     */
    public ClassHistory(String alias) {
        this(alias, CLASS_HISTORY);
    }

    private ClassHistory(String alias, Table<ClassHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassHistory(String alias, Table<ClassHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "历史班级表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassHistoryRecord>>asList(Keys.KEY_T_CLASS_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassHistory as(String alias) {
        return new ClassHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassHistory rename(String name) {
        return new ClassHistory(name, null);
    }
}
