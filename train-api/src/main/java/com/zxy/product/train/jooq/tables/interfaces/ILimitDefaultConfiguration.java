/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILimitDefaultConfiguration extends Serializable {

    /**
     * Setter for <code>train.t_limit_default_configuration.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_limit_default_configuration.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_limit_default_configuration.f_default_value</code>. 默认值
     */
    public void setDefaultValue(Integer value);

    /**
     * Getter for <code>train.t_limit_default_configuration.f_default_value</code>. 默认值
     */
    public Integer getDefaultValue();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILimitDefaultConfiguration
     */
    public void from(ILimitDefaultConfiguration from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILimitDefaultConfiguration
     */
    public <E extends ILimitDefaultConfiguration> E into(E into);
}
