/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LimitDefaultConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LimitDefaultConfiguration extends TableImpl<LimitDefaultConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_limit_default_configuration</code>
     */
    public static final LimitDefaultConfiguration LIMIT_DEFAULT_CONFIGURATION = new LimitDefaultConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LimitDefaultConfigurationRecord> getRecordType() {
        return LimitDefaultConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_limit_default_configuration.f_id</code>. 主键
     */
    public final TableField<LimitDefaultConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_limit_default_configuration.f_default_value</code>. 默认值
     */
    public final TableField<LimitDefaultConfigurationRecord, Integer> DEFAULT_VALUE = createField("f_default_value", org.jooq.impl.SQLDataType.INTEGER, this, "默认值");

    /**
     * Create a <code>train.t_limit_default_configuration</code> table reference
     */
    public LimitDefaultConfiguration() {
        this("t_limit_default_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_limit_default_configuration</code> table reference
     */
    public LimitDefaultConfiguration(String alias) {
        this(alias, LIMIT_DEFAULT_CONFIGURATION);
    }

    private LimitDefaultConfiguration(String alias, Table<LimitDefaultConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private LimitDefaultConfiguration(String alias, Table<LimitDefaultConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LimitDefaultConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_LIMIT_DEFAULT_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LimitDefaultConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<LimitDefaultConfigurationRecord>>asList(Keys.KEY_T_LIMIT_DEFAULT_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitDefaultConfiguration as(String alias) {
        return new LimitDefaultConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LimitDefaultConfiguration rename(String name) {
        return new LimitDefaultConfiguration(name, null);
    }
}
