/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassroomConfiguration extends Serializable {

    /**
     * Setter for <code>train.t_classroom_configuration.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_classroom_configuration.f_configuration_id</code>. 关联配置表ID
     */
    public void setConfigurationId(String value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_configuration_id</code>. 关联配置表ID
     */
    public String getConfigurationId();

    /**
     * Setter for <code>train.t_classroom_configuration.f_type_id</code>. 关联类型ID
     */
    public void setTypeId(Integer value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_type_id</code>. 关联类型ID
     */
    public Integer getTypeId();

    /**
     * Setter for <code>train.t_classroom_configuration.f_classroom</code>.
     */
    public void setClassroom(String value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_classroom</code>.
     */
    public String getClassroom();

    /**
     * Setter for <code>train.t_classroom_configuration.f_classroom_coding</code>. 教室编号
     */
    public void setClassroomCoding(String value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_classroom_coding</code>. 教室编号
     */
    public String getClassroomCoding();

    /**
     * Setter for <code>train.t_classroom_configuration.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_classroom_configuration.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_classroom_configuration.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_classroom_configuration.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_classroom_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_classroom_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassroomConfiguration
     */
    public void from(IClassroomConfiguration from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassroomConfiguration
     */
    public <E extends IClassroomConfiguration> E into(E into);
}
