/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CourseInfoRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 在线课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfo extends TableImpl<CourseInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_course_info</code>
     */
    public static final CourseInfo COURSE_INFO = new CourseInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseInfoRecord> getRecordType() {
        return CourseInfoRecord.class;
    }

    /**
     * The column <code>train.t_course_info.f_id</code>.
     */
    public final TableField<CourseInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_course_info.f_name</code>. 课程名称
     */
    public final TableField<CourseInfoRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "课程名称");

    /**
     * The column <code>train.t_course_info.f_status</code>. 状态（0：未发布，1：已发布，2：取消发布，3：测试中）
     */
    public final TableField<CourseInfoRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "状态（0：未发布，1：已发布，2：取消发布，3：测试中）");

    /**
     * Create a <code>train.t_course_info</code> table reference
     */
    public CourseInfo() {
        this("t_course_info", null);
    }

    /**
     * Create an aliased <code>train.t_course_info</code> table reference
     */
    public CourseInfo(String alias) {
        this(alias, COURSE_INFO);
    }

    private CourseInfo(String alias, Table<CourseInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseInfo(String alias, Table<CourseInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "在线课程表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseInfoRecord>>asList(Keys.KEY_T_COURSE_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfo as(String alias) {
        return new CourseInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseInfo rename(String name) {
        return new CourseInfo(name, null);
    }
}
