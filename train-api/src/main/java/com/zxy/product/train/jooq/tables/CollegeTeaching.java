/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CollegeTeachingRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学院授课记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CollegeTeaching extends TableImpl<CollegeTeachingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_college_teaching</code>
     */
    public static final CollegeTeaching COLLEGE_TEACHING = new CollegeTeaching();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CollegeTeachingRecord> getRecordType() {
        return CollegeTeachingRecord.class;
    }

    /**
     * The column <code>train.t_college_teaching.f_id</code>. 主键
     */
    public final TableField<CollegeTeachingRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_college_teaching.f_course_id</code>. 课程ID
     */
    public final TableField<CollegeTeachingRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程ID");

    /**
     * The column <code>train.t_college_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public final TableField<CollegeTeachingRecord, String> LECTURER_ID = createField("f_lecturer_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程讲师ID");

    /**
     * The column <code>train.t_college_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    public final TableField<CollegeTeachingRecord, String> LECTURER_NAME = createField("f_lecturer_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程讲师姓名");

    /**
     * The column <code>train.t_college_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    public final TableField<CollegeTeachingRecord, String> LECTURER_POST = createField("f_lecturer_post", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程讲师职务");

    /**
     * The column <code>train.t_college_teaching.f_class_id</code>. 班级id
     */
    public final TableField<CollegeTeachingRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级id");

    /**
     * The column <code>train.t_college_teaching.f_course_date</code>. 上课日期
     */
    public final TableField<CollegeTeachingRecord, Long> COURSE_DATE = createField("f_course_date", org.jooq.impl.SQLDataType.BIGINT, this, "上课日期");

    /**
     * The column <code>train.t_college_teaching.f_organization_name</code>. 主办部门
     */
    public final TableField<CollegeTeachingRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "主办部门");

    /**
     * The column <code>train.t_college_teaching.f_class_name</code>. 培训班名称
     */
    public final TableField<CollegeTeachingRecord, String> CLASS_NAME = createField("f_class_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "培训班名称");

    /**
     * The column <code>train.t_college_teaching.f_class_member</code>. 培训班联系人
     */
    public final TableField<CollegeTeachingRecord, String> CLASS_MEMBER = createField("f_class_member", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "培训班联系人");

    /**
     * The column <code>train.t_college_teaching.f_course_duration</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public final TableField<CollegeTeachingRecord, Double> COURSE_DURATION = createField("f_course_duration", org.jooq.impl.SQLDataType.FLOAT, this, "平均满意度(十分制，小数点后保留一位)");

    /**
     * The column <code>train.t_college_teaching.f_obj</code>. 授课对象
     */
    public final TableField<CollegeTeachingRecord, String> OBJ = createField("f_obj", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "授课对象");

    /**
     * The column <code>train.t_college_teaching.f_attachment_id</code>. 附件id
     */
    public final TableField<CollegeTeachingRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "附件id");

    /**
     * The column <code>train.t_college_teaching.f_obj_num</code>. 授课人数
     */
    public final TableField<CollegeTeachingRecord, Integer> OBJ_NUM = createField("f_obj_num", org.jooq.impl.SQLDataType.INTEGER, this, "授课人数");

    /**
     * The column <code>train.t_college_teaching.f_tax</code>. 税金
     */
    public final TableField<CollegeTeachingRecord, Double> TAX = createField("f_tax", org.jooq.impl.SQLDataType.DOUBLE, this, "税金");

    /**
     * The column <code>train.t_college_teaching.f_pay</code>. 实付
     */
    public final TableField<CollegeTeachingRecord, Double> PAY = createField("f_pay", org.jooq.impl.SQLDataType.DOUBLE, this, "实付");

    /**
     * The column <code>train.t_college_teaching.f_course_reward</code>. 课酬
     */
    public final TableField<CollegeTeachingRecord, Double> COURSE_REWARD = createField("f_course_reward", org.jooq.impl.SQLDataType.DOUBLE, this, "课酬");

    /**
     * The column <code>train.t_college_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public final TableField<CollegeTeachingRecord, Double> SATISFIED_DEGREE = createField("f_satisfied_degree", org.jooq.impl.SQLDataType.FLOAT, this, "平均满意度(十分制，小数点后保留一位)");

    /**
     * Create a <code>train.t_college_teaching</code> table reference
     */
    public CollegeTeaching() {
        this("t_college_teaching", null);
    }

    /**
     * Create an aliased <code>train.t_college_teaching</code> table reference
     */
    public CollegeTeaching(String alias) {
        this(alias, COLLEGE_TEACHING);
    }

    private CollegeTeaching(String alias, Table<CollegeTeachingRecord> aliased) {
        this(alias, aliased, null);
    }

    private CollegeTeaching(String alias, Table<CollegeTeachingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学院授课记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CollegeTeachingRecord> getPrimaryKey() {
        return Keys.KEY_T_COLLEGE_TEACHING_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CollegeTeachingRecord>> getKeys() {
        return Arrays.<UniqueKey<CollegeTeachingRecord>>asList(Keys.KEY_T_COLLEGE_TEACHING_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollegeTeaching as(String alias) {
        return new CollegeTeaching(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CollegeTeaching rename(String name) {
        return new CollegeTeaching(name, null);
    }
}
