/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassOnlineCourse extends Serializable {

    /**
     * Setter for <code>train.t_class_online_course.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_online_course.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_online_course.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_online_course.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_online_course.f_type</code>. 课程类型（1在线课程 2知识）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_class_online_course.f_type</code>. 课程类型（1在线课程 2知识）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_class_online_course.f_resource_id</code>. 资源ID
     */
    public void setResourceId(String value);

    /**
     * Getter for <code>train.t_class_online_course.f_resource_id</code>. 资源ID
     */
    public String getResourceId();

    /**
     * Setter for <code>train.t_class_online_course.f_resource_name</code>. 资源名称
     */
    public void setResourceName(String value);

    /**
     * Getter for <code>train.t_class_online_course.f_resource_name</code>. 资源名称
     */
    public String getResourceName();

    /**
     * Setter for <code>train.t_class_online_course.f_theme_id</code>. 主题ID
     */
    public void setThemeId(String value);

    /**
     * Getter for <code>train.t_class_online_course.f_theme_id</code>. 主题ID
     */
    public String getThemeId();

    /**
     * Setter for <code>train.t_class_online_course.f_learn_time</code>. 学习时间
     */
    public void setLearnTime(Long value);

    /**
     * Getter for <code>train.t_class_online_course.f_learn_time</code>. 学习时间
     */
    public Long getLearnTime();

    /**
     * Setter for <code>train.t_class_online_course.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_class_online_course.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_class_online_course.f_is_required</code>. 是否必修（0否  1是）
     */
    public void setIsRequired(Integer value);

    /**
     * Getter for <code>train.t_class_online_course.f_is_required</code>. 是否必修（0否  1是）
     */
    public Integer getIsRequired();

    /**
     * Setter for <code>train.t_class_online_course.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_online_course.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_online_course.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_online_course.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_online_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_online_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_class_online_course.f_theme_sort</code>. 主题排序
     */
    public void setThemeSort(Integer value);

    /**
     * Getter for <code>train.t_class_online_course.f_theme_sort</code>. 主题排序
     */
    public Integer getThemeSort();

    /**
     * Setter for <code>train.t_class_online_course.f_required_theme</code>. 配置必修和选修的主题
     */
    public void setRequiredTheme(String value);

    /**
     * Getter for <code>train.t_class_online_course.f_required_theme</code>. 配置必修和选修的主题
     */
    public String getRequiredTheme();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassOnlineCourse
     */
    public void from(IClassOnlineCourse from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassOnlineCourse
     */
    public <E extends IClassOnlineCourse> E into(E into);
}
