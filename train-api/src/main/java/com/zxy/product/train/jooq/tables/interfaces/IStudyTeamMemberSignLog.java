/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习团队成员签到表流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudyTeamMemberSignLog extends Serializable {

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_activity_id</code>. 团队活动id
     */
    public void setActivityId(String value);

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_activity_id</code>. 团队活动id
     */
    public String getActivityId();

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_team_member_id</code>. 团队成员id
     */
    public void setTeamMemberId(String value);

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_team_member_id</code>. 团队成员id
     */
    public String getTeamMemberId();

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_sign_time</code>. 签到时间
     */
    public void setSignTime(Long value);

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_sign_time</code>. 签到时间
     */
    public Long getSignTime();

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_sign_type</code>. 签到类型 1-签到 2-签退
     */
    public void setSignType(Integer value);

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_sign_type</code>. 签到类型 1-签到 2-签退
     */
    public Integer getSignType();

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudyTeamMemberSignLog
     */
    public void from(IStudyTeamMemberSignLog from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudyTeamMemberSignLog
     */
    public <E extends IStudyTeamMemberSignLog> E into(E into);
}
