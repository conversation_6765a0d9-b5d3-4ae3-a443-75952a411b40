/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 历史课酬表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISalaryHistory extends Serializable {

    /**
     * Setter for <code>train.t_salary_history.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_salary_history.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_salary_history.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_salary_history.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_salary_history.f_lecturer_card</code>. 讲师身份证
     */
    public void setLecturerCard(String value);

    /**
     * Getter for <code>train.t_salary_history.f_lecturer_card</code>. 讲师身份证
     */
    public String getLecturerCard();

    /**
     * Setter for <code>train.t_salary_history.f_lecturer_bank_name</code>. 讲师银行名称
     */
    public void setLecturerBankName(String value);

    /**
     * Getter for <code>train.t_salary_history.f_lecturer_bank_name</code>. 讲师银行名称
     */
    public String getLecturerBankName();

    /**
     * Setter for <code>train.t_salary_history.f_lecturer_bank_card</code>. 讲师银行卡号
     */
    public void setLecturerBankCard(String value);

    /**
     * Getter for <code>train.t_salary_history.f_lecturer_bank_card</code>. 讲师银行卡号
     */
    public String getLecturerBankCard();

    /**
     * Setter for <code>train.t_salary_history.f_paid_pay</code>. 实付薪酬
     */
    public void setPaidPay(Double value);

    /**
     * Getter for <code>train.t_salary_history.f_paid_pay</code>. 实付薪酬
     */
    public Double getPaidPay();

    /**
     * Setter for <code>train.t_salary_history.f_pay</code>. 酬金
     */
    public void setPay(Double value);

    /**
     * Getter for <code>train.t_salary_history.f_pay</code>. 酬金
     */
    public Double getPay();

    /**
     * Setter for <code>train.t_salary_history.f_tax</code>. 税金
     */
    public void setTax(Double value);

    /**
     * Getter for <code>train.t_salary_history.f_tax</code>. 税金
     */
    public Double getTax();

    /**
     * Setter for <code>train.t_salary_history.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_salary_history.f_class_id</code>. 班级ID
     */
    public String getClassId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISalaryHistory
     */
    public void from(ISalaryHistory from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISalaryHistory
     */
    public <E extends ISalaryHistory> E into(E into);
}
