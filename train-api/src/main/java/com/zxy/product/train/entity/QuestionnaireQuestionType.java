package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.QuestionnaireQuestionTypeEntity;

public class QuestionnaireQuestionType extends QuestionnaireQuestionTypeEntity{

	private static final long serialVersionUID = -1216252864134295859L;

	/** 培训满意度评估表问题 */
	public static final String TRAINING_SATISFACTION_ASSESSMENT = "TRAINING_SATISFACTION_ASSESSMENT";
	/** 总体评估 */
	public static final Short LEVEL_OVERALL_ASSESSMENT = 1;
	/** 课程师资评价 */
	public static final Short LEVEL_EVALUATION_OF_CURRICULUM_TEACHERS = 2;
	/** 主观题 */
	public static final Short LEVEL_SUBJECTIVE_QUESTIONS = 3;
	/** 总体评估：课程内容 */
	public static final Short TYPE_COURSE_CONTENT = 98;
	/** 总体评估：师资表现 */
	public static final Short TYPE_TEACHER_PERFORMANCE = 99;
	/** 课程师资评价：课程建议 */
	public static final Short TYPE_CURRICULUM_RECOMMENDATIONS = 11;
	/** 课程师资评价：课程内容 */
	public static final Short TYPE_BRANCH_COURSE_CONTENT = 1;
	/** 课程师资评价：课程内容分项满意度 */
	public static final Short TYPE_BRANCH_COURSE_CONTENT_SATISFACTION = 101;
	/** 课程师资评价：师资表现 */
	public static final Short TYPE_BRANCH_TEACHER_PERFORMANCE = 2;
	/** 课程师资评价：师资表现分项满意度 */
	public static final Short TYPE_BRANCH_TEACHER_PERFORMANCE_SATISFACTION = 201;
	/** 课程师资评价：课程建议 */
	public static final Short TYPE_BRANCH_CURRICULUM_RECOMMENDATIONS = 3;

	/** 教学用品、教学设施满足要求情况 */
	public static final Short TYPE_BRANCH_CURRICULUM_APPLIANCE = 12;
	/** 房间教室、文体设施管理及卫生情况*/
	public static final Short TYPE_BRANCH_CURRICULUM_ROOM = 13;
	/** 饭菜卫生及质量 */
	public static final Short TYPE_BRANCH_CURRICULUM_FOOD_HYGIENE = 14;

	private String courseId;
	private String courseName;
	private Long courseTime;
	private String courseStartTime;
	private String courseEndTime;
	private String answer;
	private String idea;
	private String teacherName;
	private String teacherId;
	private Integer countMember;
	private Boolean isAnswer;
	private Integer courseType;
	private Integer classType;

	/** 班级类型*/
	public Integer getClassType() {
		return classType;
	}

	public void setClassType(Integer classType) {
		this.classType = classType;
	}
	/** 课程ID */
	public String getCourseId() {
		return courseId;
	}
	/** 课程ID */
	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}
	/** 课程名称 */
	public String getCourseName() {
		return courseName;
	}
	/** 课程名称 */
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}
	/** 上课日期（年月日） */
	public Long getCourseTime() {
		return courseTime;
	}
	/** 上课日期（年月日） */
	public void setCourseTime(Long courseTime) {
		this.courseTime = courseTime;
	}
	/** 上课开始时间（时分秒） */
	public String getCourseStartTime() {
		return courseStartTime;
	}
	/** 上课开始时间（时分秒） */
	public void setCourseStartTime(String courseStartTime) {
		this.courseStartTime = courseStartTime;
	}
	/** 上课结束时间（时分秒） */
	public String getCourseEndTime() {
		return courseEndTime;
	}
	/** 上课结束时间（时分秒） */
	public void setCourseEndTime(String courseEndTime) {
		this.courseEndTime = courseEndTime;
	}
	/** 答案 */
	public String getAnswer() {
		return answer;
	}
	/** 答案 */
	public void setAnswer(String answer) {
		this.answer = answer;
	}
	/** 意见 */
	public String getIdea() {
		return idea;
	}
	/** 意见 */
	public void setIdea(String idea) {
		this.idea = idea;
	}
	/** 统计人数 */
	public Integer getCountMember() {
		return countMember;
	}
	/** 统计人数 */
	public void setCountMember(Integer countMember) {
		this.countMember = countMember;
	}
	/** 讲师姓名 */
	public String getTeacherName() {
		return teacherName;
	}
	/** 讲师姓名 */
	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}
	/** 讲师ID */
	public String getTeacherId() {
		return teacherId;
	}
	/** 讲师ID */
	public void setTeacherId(String teacherId) {
		this.teacherId = teacherId;
	}
	/** 用户是否已作答 */
	public Boolean getIsAnswer() {
		return isAnswer;
	}
	/** 用户是否已作答 */
	public void setIsAnswer(Boolean isAnswer) {
		this.isAnswer = isAnswer;
	}

	/** 课程类型 1.面授 3.直播 5.在线**/
	public Integer getCourseType() {
		return courseType;
	}

	public void setCourseType(Integer courseType) {
		this.courseType = courseType;
	}
}
