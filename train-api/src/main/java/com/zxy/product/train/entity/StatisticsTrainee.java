package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.TraineeEntity;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class StatisticsTrainee extends TraineeEntity {
    private String fullName;
    private String code;
    private String organizationName;


    private Integer classCount;

    private BigDecimal traineeTime;

    private Integer courseCount;

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public Integer getClassCount() {
        return classCount;
    }

    public void setClassCount(Integer classCount) {
        this.classCount = classCount;
    }

    public BigDecimal getTraineeTime() {
        return traineeTime;
    }

    public void setTraineeTime(BigDecimal traineeTime) {
        this.traineeTime = traineeTime;
    }

    public Integer getCourseCount() {
        return courseCount;
    }

    public void setCourseCount(Integer courseCount) {
        this.courseCount = courseCount;
    }
}
