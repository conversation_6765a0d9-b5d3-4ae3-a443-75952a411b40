package com.zxy.product.train.util;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Optional;
import java.util.Random;

/**
 * Created by keel<PERSON> on 16/10/17.
 */
public interface StringUtils {

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String MM_DD_HH_MM = "MM-dd HH:mm";
    public static final String HH_MM = "HH:mm";
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    static long dateString2OptionalLong(String t) {
        return Date.from(LocalDate.parse(t).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }
    static Optional<Long> dateString2OptionalLong(Optional<String> value) {
        return value.map(StringUtils::dateString2OptionalLong);
    }
    static String long2Date(Long date){
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(date));
    }
    static String long2Date(Long date, String format){
        return new SimpleDateFormat(format).format(new Date(date));
    }
    static int getDaysByMonth(Integer year, Integer month){
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }
    static long datetimeString2OptionalLong(String t) {
        LocalDateTime now = LocalDateTime.of(LocalDate.parse(t.split(" ")[0]), LocalTime.parse(t.split(" ")[1]));
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }
    static Optional<Long> datetimeString2OptionalLong(Optional<String> value) {
        return value.map(StringUtils::datetimeString2OptionalLong);
    }
    /**
     * 返回X位随机数
     * @param length    位数
     * @return
     */
    static String random(int length){
        Random rm = new Random();
        // 获得随机数
        double pross = (1 + rm.nextDouble()) * Math.pow(10, length);
        // 将获得的获得随机数转化为字符串
        String fixLenthString = String.valueOf(pross);
        // 返回固定的长度的随机数
        return fixLenthString.substring(1, length + 1);
    }

    /**
     * 获取时间（当前时间前后的任意日期）
     * @param sourceDate
     * @param dateFormat
     * @param days
     * @return
     */
    static String getDayString(Optional<Long> sourceDate,Optional<String> dateFormat,Integer days){
        Long time=sourceDate.orElse(System.currentTimeMillis());
        Calendar a = Calendar.getInstance();
        a.setTimeInMillis(time);
        a.set(Calendar.DATE, days);
        long yesTime=a.getTimeInMillis();
        String format=dateFormat.orElse("yyyy-MM-dd HH:mm:ss");
        return long2Date(yesTime,format);
    }

    /**
     * 学习成果时间转换
     * @param createTime
     * @return
     */
    public static String discussTime(Long createTime){
        Calendar cCreate = Calendar.getInstance();
        cCreate.setTimeInMillis(createTime);
        Calendar cNow = Calendar.getInstance();
        cNow.setTime(new java.util.Date());
        if(cCreate.get(Calendar.YEAR) < cNow.get(Calendar.YEAR)){
            return dateLongToString(createTime,YYYY_MM_DD_HH_MM_SS);
        }
        if(cCreate.get(Calendar.MONTH) < cNow.get(Calendar.MONTH)){
            return dateLongToString(createTime,MM_DD_HH_MM);
        }
        if(cNow.get(Calendar.DAY_OF_MONTH) - cCreate.get(Calendar.DAY_OF_MONTH) >= 2 ){
            return dateLongToString(createTime,MM_DD_HH_MM);
        }
        if(cCreate.get(Calendar.DAY_OF_MONTH) < cNow.get(Calendar.DAY_OF_MONTH)){
            return "昨日 " + dateLongToString(createTime,HH_MM);
        }
        return "今日 " + dateLongToString(createTime,HH_MM);
    }

    /**
     * Long类型日期转String类型
     * @param dateLong
     * @param format
     * @return
     */
    public static String dateLongToString(Long dateLong,String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        java.util.Date date = new java.util.Date(dateLong);
        return sdf.format(date);
    }

}
