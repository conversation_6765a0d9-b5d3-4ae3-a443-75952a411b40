/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassOnlineCourseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassOnlineCourse extends TableImpl<ClassOnlineCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_online_course</code>
     */
    public static final ClassOnlineCourse CLASS_ONLINE_COURSE = new ClassOnlineCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassOnlineCourseRecord> getRecordType() {
        return ClassOnlineCourseRecord.class;
    }

    /**
     * The column <code>train.t_class_online_course.f_id</code>.
     */
    public final TableField<ClassOnlineCourseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_online_course.f_class_id</code>. 班级ID
     */
    public final TableField<ClassOnlineCourseRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_online_course.f_type</code>. 课程类型（1在线课程 2知识）
     */
    public final TableField<ClassOnlineCourseRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "课程类型（1在线课程 2知识）");

    /**
     * The column <code>train.t_class_online_course.f_resource_id</code>. 资源ID
     */
    public final TableField<ClassOnlineCourseRecord, String> RESOURCE_ID = createField("f_resource_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "资源ID");

    /**
     * The column <code>train.t_class_online_course.f_resource_name</code>. 资源名称
     */
    public final TableField<ClassOnlineCourseRecord, String> RESOURCE_NAME = createField("f_resource_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "资源名称");

    /**
     * The column <code>train.t_class_online_course.f_theme_id</code>. 主题ID
     */
    public final TableField<ClassOnlineCourseRecord, String> THEME_ID = createField("f_theme_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "主题ID");

    /**
     * The column <code>train.t_class_online_course.f_learn_time</code>. 学习时间
     */
    public final TableField<ClassOnlineCourseRecord, Long> LEARN_TIME = createField("f_learn_time", org.jooq.impl.SQLDataType.BIGINT, this, "学习时间");

    /**
     * The column <code>train.t_class_online_course.f_sort</code>. 排序
     */
    public final TableField<ClassOnlineCourseRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_class_online_course.f_is_required</code>. 是否必修（0否  1是）
     */
    public final TableField<ClassOnlineCourseRecord, Integer> IS_REQUIRED = createField("f_is_required", org.jooq.impl.SQLDataType.INTEGER, this, "是否必修（0否  1是）");

    /**
     * The column <code>train.t_class_online_course.f_create_time</code>. 创建时间
     */
    public final TableField<ClassOnlineCourseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_online_course.f_create_member</code>. 创建人
     */
    public final TableField<ClassOnlineCourseRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_online_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassOnlineCourseRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_online_course.f_theme_sort</code>. 主题排序
     */
    public final TableField<ClassOnlineCourseRecord, Integer> THEME_SORT = createField("f_theme_sort", org.jooq.impl.SQLDataType.INTEGER, this, "主题排序");

    /**
     * The column <code>train.t_class_online_course.f_required_theme</code>. 配置必修和选修的主题
     */
    public final TableField<ClassOnlineCourseRecord, String> REQUIRED_THEME = createField("f_required_theme", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "配置必修和选修的主题");

    /**
     * Create a <code>train.t_class_online_course</code> table reference
     */
    public ClassOnlineCourse() {
        this("t_class_online_course", null);
    }

    /**
     * Create an aliased <code>train.t_class_online_course</code> table reference
     */
    public ClassOnlineCourse(String alias) {
        this(alias, CLASS_ONLINE_COURSE);
    }

    private ClassOnlineCourse(String alias, Table<ClassOnlineCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassOnlineCourse(String alias, Table<ClassOnlineCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassOnlineCourseRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_ONLINE_COURSE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassOnlineCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassOnlineCourseRecord>>asList(Keys.KEY_T_CLASS_ONLINE_COURSE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassOnlineCourse as(String alias) {
        return new ClassOnlineCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassOnlineCourse rename(String name) {
        return new ClassOnlineCourse(name, null);
    }
}
