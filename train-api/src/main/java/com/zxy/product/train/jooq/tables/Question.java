/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.QuestionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Question extends TableImpl<QuestionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_question</code>
     */
    public static final Question QUESTION = new Question();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionRecord> getRecordType() {
        return QuestionRecord.class;
    }

    /**
     * The column <code>train.t_question.f_id</code>.
     */
    public final TableField<QuestionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_question.f_create_time</code>.
     */
    public final TableField<QuestionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * The column <code>train.t_question.f_type</code>. 类型
     */
    public final TableField<QuestionRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类型");

    /**
     * The column <code>train.t_question.f_content</code>. 试题信息
     */
    public final TableField<QuestionRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.VARCHAR.length(5000), this, "试题信息");

    /**
     * The column <code>train.t_question.f_is_subjective</code>. 是否主观题
     */
    public final TableField<QuestionRecord, Integer> IS_SUBJECTIVE = createField("f_is_subjective", org.jooq.impl.SQLDataType.INTEGER, this, "是否主观题");

    /**
     * The column <code>train.t_question.f_parent_id</code>. 父题
     */
    public final TableField<QuestionRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "父题");

    /**
     * The column <code>train.t_question.f_question_depot_id</code>. 题库
     */
    public final TableField<QuestionRecord, String> QUESTION_DEPOT_ID = createField("f_question_depot_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "题库");

    /**
     * The column <code>train.t_question.f_difficulty</code>. 难度
     */
    public final TableField<QuestionRecord, Integer> DIFFICULTY = createField("f_difficulty", org.jooq.impl.SQLDataType.INTEGER, this, "难度");

    /**
     * The column <code>train.t_question.f_organization_id</code>. 所属部门
     */
    public final TableField<QuestionRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所属部门");

    /**
     * The column <code>train.t_question.f_score</code>. 分数
     */
    public final TableField<QuestionRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER, this, "分数");

    /**
     * The column <code>train.t_question.f_error_rate</code>. 易错率
     */
    public final TableField<QuestionRecord, Integer> ERROR_RATE = createField("f_error_rate", org.jooq.impl.SQLDataType.INTEGER, this, "易错率");

    /**
     * The column <code>train.t_question.f_status</code>. 状态
     */
    public final TableField<QuestionRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "状态");

    /**
     * The column <code>train.t_question.f_mark_amount</code>. 收藏
     */
    public final TableField<QuestionRecord, Integer> MARK_AMOUNT = createField("f_mark_amount", org.jooq.impl.SQLDataType.INTEGER, this, "收藏");

    /**
     * The column <code>train.t_question.f_recovery_count</code>. 纠错
     */
    public final TableField<QuestionRecord, Integer> RECOVERY_COUNT = createField("f_recovery_count", org.jooq.impl.SQLDataType.INTEGER, this, "纠错");

    /**
     * The column <code>train.t_question.f_class_offline_course_id</code>. 线下课程id
     */
    public final TableField<QuestionRecord, String> CLASS_OFFLINE_COURSE_ID = createField("f_class_offline_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "线下课程id");

    /**
     * The column <code>train.t_question.f_order</code>. 排序
     */
    public final TableField<QuestionRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * Create a <code>train.t_question</code> table reference
     */
    public Question() {
        this("t_question", null);
    }

    /**
     * Create an aliased <code>train.t_question</code> table reference
     */
    public Question(String alias) {
        this(alias, QUESTION);
    }

    private Question(String alias, Table<QuestionRecord> aliased) {
        this(alias, aliased, null);
    }

    private Question(String alias, Table<QuestionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionRecord>>asList(Keys.KEY_T_QUESTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Question as(String alias) {
        return new Question(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Question rename(String name) {
        return new Question(name, null);
    }
}
