/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamAchievementPraiseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 团队学习班-点赞明细表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamAchievementPraise extends TableImpl<StudyTeamAchievementPraiseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_achievement_praise</code>
     */
    public static final StudyTeamAchievementPraise STUDY_TEAM_ACHIEVEMENT_PRAISE = new StudyTeamAchievementPraise();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamAchievementPraiseRecord> getRecordType() {
        return StudyTeamAchievementPraiseRecord.class;
    }

    /**
     * The column <code>train.t_study_team_achievement_praise.f_id</code>. 主键
     */
    public final TableField<StudyTeamAchievementPraiseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_study_team_achievement_praise.f_object_id</code>. 被点赞id
     */
    public final TableField<StudyTeamAchievementPraiseRecord, String> OBJECT_ID = createField("f_object_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "被点赞id");

    /**
     * The column <code>train.t_study_team_achievement_praise.f_object_type</code>. 点赞对象类型 1:评论，2：回复
     */
    public final TableField<StudyTeamAchievementPraiseRecord, Integer> OBJECT_TYPE = createField("f_object_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "点赞对象类型 1:评论，2：回复");

    /**
     * The column <code>train.t_study_team_achievement_praise.f_member_id</code>. 点赞用户id
     */
    public final TableField<StudyTeamAchievementPraiseRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "点赞用户id");

    /**
     * The column <code>train.t_study_team_achievement_praise.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public final TableField<StudyTeamAchievementPraiseRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态(0未删除，1已删除)");

    /**
     * The column <code>train.t_study_team_achievement_praise.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamAchievementPraiseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_study_team_achievement_praise.f_organization_id</code>. 所属组织id
     */
    public final TableField<StudyTeamAchievementPraiseRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "所属组织id");

    /**
     * Create a <code>train.t_study_team_achievement_praise</code> table reference
     */
    public StudyTeamAchievementPraise() {
        this("t_study_team_achievement_praise", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_achievement_praise</code> table reference
     */
    public StudyTeamAchievementPraise(String alias) {
        this(alias, STUDY_TEAM_ACHIEVEMENT_PRAISE);
    }

    private StudyTeamAchievementPraise(String alias, Table<StudyTeamAchievementPraiseRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamAchievementPraise(String alias, Table<StudyTeamAchievementPraiseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "团队学习班-点赞明细表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamAchievementPraiseRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_ACHIEVEMENT_PRAISE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamAchievementPraiseRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamAchievementPraiseRecord>>asList(Keys.KEY_T_STUDY_TEAM_ACHIEVEMENT_PRAISE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementPraise as(String alias) {
        return new StudyTeamAchievementPraise(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamAchievementPraise rename(String name) {
        return new StudyTeamAchievementPraise(name, null);
    }
}
