package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassDetailEntity;

/**
 * 培训班详细信息
 *
 * <AUTHOR>
 */
public class ClassDetail extends ClassDetailEntity {

    private static final long serialVersionUID = 932851863861818523L;

    public static final int DELETE_FLASE = 0; //删除状态：未删除
    public static final int DELETE_TRUE = 1;  //删除状态，已删除
    public boolean flag = true; //是否是特殊班级
    public static final String CACHE_DETAIL_KEY = "class-info-for-class-detail-page";

    private Integer isGrant;

    public Integer getIsGrant() {
        return isGrant;
    }

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public void setIsGrant(Integer isGrant) {
        this.isGrant = isGrant;
    }


}
