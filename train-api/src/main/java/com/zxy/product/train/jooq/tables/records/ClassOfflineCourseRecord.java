/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassOfflineCourse;
import com.zxy.product.train.jooq.tables.interfaces.IClassOfflineCourse;

import javax.annotation.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassOfflineCourseRecord extends UpdatableRecordImpl<ClassOfflineCourseRecord> implements IClassOfflineCourse {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_class_offline_course.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_class_id</code>. 班级ID
     */
    @Override
    public void setClassId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_class_id</code>. 班级ID
     */
    @Override
    public String getClassId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_type</code>. 课程类型（1面授  2观看录像 3直播 4其他）
     */
    @Override
    public void setType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_type</code>. 课程类型（1面授  2观看录像 3直播 4其他）
     */
    @Override
    public Integer getType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_name</code>. 课程名称
     */
    @Override
    public void setName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_name</code>. 课程名称
     */
    @Override
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_course_date</code>. 上课日期
     */
    @Override
    public void setCourseDate(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_course_date</code>. 上课日期
     */
    @Override
    public Long getCourseDate() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_start_time</code>. 开始时间
     */
    @Override
    public void setStartTime(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_start_time</code>. 开始时间
     */
    @Override
    public String getStartTime() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_end_time</code>. 结束时间
     */
    @Override
    public void setEndTime(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_end_time</code>. 结束时间
     */
    @Override
    public String getEndTime() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_classroom_id</code>. 教室ID
     */
    @Override
    public void setClassroomId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_classroom_id</code>. 教室ID
     */
    @Override
    public String getClassroomId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_id</code>. 讲师ID
     */
    @Override
    public void setTeacherId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_id</code>. 讲师ID
     */
    @Override
    public String getTeacherId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_name</code>. 讲师姓名
     */
    @Override
    public void setTeacherName(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_name</code>. 讲师姓名
     */
    @Override
    public String getTeacherName() {
        return (String) get(9);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_organization</code>. 讲师单位
     */
    @Override
    public void setTeacherOrganization(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_organization</code>. 讲师单位
     */
    @Override
    public String getTeacherOrganization() {
        return (String) get(10);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_title</code>. 讲师职称
     */
    @Override
    public void setTeacherTitle(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_title</code>. 讲师职称
     */
    @Override
    public String getTeacherTitle() {
        return (String) get(11);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_phone</code>. 联系电话
     */
    @Override
    public void setTeacherPhone(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_phone</code>. 联系电话
     */
    @Override
    public String getTeacherPhone() {
        return (String) get(12);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_teacher_type</code>. 讲师类型(0内部  1外部)
     */
    @Override
    public void setTeacherType(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_teacher_type</code>. 讲师类型(0内部  1外部)
     */
    @Override
    public Integer getTeacherType() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_is_relate_online</code>. 是否关联在线课程（0否 1是）
     */
    @Override
    public void setIsRelateOnline(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_is_relate_online</code>. 是否关联在线课程（0否 1是）
     */
    @Override
    public Integer getIsRelateOnline() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_online_course_id</code>. 在线课程ID
     */
    @Override
    public void setOnlineCourseId(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_online_course_id</code>. 在线课程ID
     */
    @Override
    public String getOnlineCourseId() {
        return (String) get(15);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(16);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_create_member</code>. 创建人
     */
    @Override
    public void setCreateMember(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_create_member</code>. 创建人
     */
    @Override
    public String getCreateMember() {
        return (String) get(17);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(18, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_online_course_status</code>. 关联在线课程的状态 (0未发布 1已发布)
     */
    @Override
    public void setOnlineCourseStatus(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_online_course_status</code>. 关联在线课程的状态 (0未发布 1已发布)
     */
    @Override
    public Integer getOnlineCourseStatus() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_duration</code>. 课时
     */
    @Override
    public void setDuration(Double value) {
        set(20, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_duration</code>. 课时
     */
    @Override
    public Double getDuration() {
        return (Double) get(20);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_course_satisfy</code>. 课程满意度（保留一位小数）
     */
    @Override
    public void setCourseSatisfy(Double value) {
        set(21, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_course_satisfy</code>. 课程满意度（保留一位小数）
     */
    @Override
    public Double getCourseSatisfy() {
        return (Double) get(21);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_gensee_url</code>. 直播的播放路径
     */
    @Override
    public void setGenseeUrl(String value) {
        set(22, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_gensee_url</code>. 直播的播放路径
     */
    @Override
    public String getGenseeUrl() {
        return (String) get(22);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_recommend</code>. 推荐方
     */
    @Override
    public void setRecommend(Integer value) {
        set(23, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_recommend</code>. 推荐方
     */
    @Override
    public Integer getRecommend() {
        return (Integer) get(23);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_payment_method</code>. 支付方式
     */
    @Override
    public void setPaymentMethod(Integer value) {
        set(24, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_payment_method</code>. 支付方式
     */
    @Override
    public Integer getPaymentMethod() {
        return (Integer) get(24);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_remarks</code>. 备注
     */
    @Override
    public void setRemarks(String value) {
        set(25, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_remarks</code>. 备注
     */
    @Override
    public String getRemarks() {
        return (String) get(25);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_taxrate</code>. 税率
     */
    @Override
    public void setTaxrate(Integer value) {
        set(26, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_taxrate</code>. 税率
     */
    @Override
    public Integer getTaxrate() {
        return (Integer) get(26);
    }

    /**
     * Setter for <code>train.t_class_offline_course.f_lecturer_source</code>. 讲师类别
     */
    @Override
    public void setLecturerSource(Integer value) {
        set(27, value);
    }

    /**
     * Getter for <code>train.t_class_offline_course.f_lecturer_source</code>. 讲师类别
     */
    @Override
    public Integer getLecturerSource() {
        return (Integer) get(27);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassOfflineCourse from) {
        setId(from.getId());
        setClassId(from.getClassId());
        setType(from.getType());
        setName(from.getName());
        setCourseDate(from.getCourseDate());
        setStartTime(from.getStartTime());
        setEndTime(from.getEndTime());
        setClassroomId(from.getClassroomId());
        setTeacherId(from.getTeacherId());
        setTeacherName(from.getTeacherName());
        setTeacherOrganization(from.getTeacherOrganization());
        setTeacherTitle(from.getTeacherTitle());
        setTeacherPhone(from.getTeacherPhone());
        setTeacherType(from.getTeacherType());
        setIsRelateOnline(from.getIsRelateOnline());
        setOnlineCourseId(from.getOnlineCourseId());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
        setOnlineCourseStatus(from.getOnlineCourseStatus());
        setDuration(from.getDuration());
        setCourseSatisfy(from.getCourseSatisfy());
        setGenseeUrl(from.getGenseeUrl());
        setRecommend(from.getRecommend());
        setPaymentMethod(from.getPaymentMethod());
        setRemarks(from.getRemarks());
        setTaxrate(from.getTaxrate());
        setLecturerSource(from.getLecturerSource());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassOfflineCourse> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassOfflineCourseRecord
     */
    public ClassOfflineCourseRecord() {
        super(ClassOfflineCourse.CLASS_OFFLINE_COURSE);
    }

    /**
     * Create a detached, initialised ClassOfflineCourseRecord
     */
    public ClassOfflineCourseRecord(String id, String classId, Integer type, String name, Long courseDate, String startTime, String endTime, String classroomId, String teacherId, String teacherName, String teacherOrganization, String teacherTitle, String teacherPhone, Integer teacherType, Integer isRelateOnline, String onlineCourseId, Long createTime, String createMember, Integer deleteFlag, Integer onlineCourseStatus, Double duration, Double courseSatisfy, String genseeUrl, Integer recommend, Integer paymentMethod, String remarks, Integer taxrate, Integer lecturerSource) {
        super(ClassOfflineCourse.CLASS_OFFLINE_COURSE);

        set(0, id);
        set(1, classId);
        set(2, type);
        set(3, name);
        set(4, courseDate);
        set(5, startTime);
        set(6, endTime);
        set(7, classroomId);
        set(8, teacherId);
        set(9, teacherName);
        set(10, teacherOrganization);
        set(11, teacherTitle);
        set(12, teacherPhone);
        set(13, teacherType);
        set(14, isRelateOnline);
        set(15, onlineCourseId);
        set(16, createTime);
        set(17, createMember);
        set(18, deleteFlag);
        set(19, onlineCourseStatus);
        set(20, duration);
        set(21, courseSatisfy);
        set(22, genseeUrl);
        set(23, recommend);
        set(24, paymentMethod);
        set(25, remarks);
        set(26, taxrate);
        set(27, lecturerSource);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassOfflineCourseEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassOfflineCourseEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassOfflineCourseEntity)source;
        pojo.into(this);
        return true;
    }
}
