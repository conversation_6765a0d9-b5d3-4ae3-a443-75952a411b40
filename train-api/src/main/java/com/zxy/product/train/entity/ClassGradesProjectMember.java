package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassGradesProjectMemberEntity;

import java.util.Objects;

public class ClassGradesProjectMember extends ClassGradesProjectMemberEntity {
    private String projectName;
    private String memberName;
    private String orgName;

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }


    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClassGradesProjectMember that = (ClassGradesProjectMember) o;
        return Objects.equals(projectName, that.projectName) && Objects.equals(memberName, that.memberName) && Objects.equals(orgName, that.orgName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(projectName, memberName, orgName);
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
}
