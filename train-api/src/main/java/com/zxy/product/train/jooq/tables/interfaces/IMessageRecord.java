/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMessageRecord extends Serializable {

    /**
     * Setter for <code>train.t_message_record.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_message_record.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_message_record.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_message_record.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_message_record.f_type</code>. 类型：1 学员管理短信； 2班级问卷短信； 3培训计划短信
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_message_record.f_type</code>. 类型：1 学员管理短信； 2班级问卷短信； 3培训计划短信
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_message_record.f_sender</code>. 发送人id
     */
    public void setSender(String value);

    /**
     * Getter for <code>train.t_message_record.f_sender</code>. 发送人id
     */
    public String getSender();

    /**
     * Setter for <code>train.t_message_record.f_receiver</code>. 接收人id，逗号隔开
     */
    public void setReceiver(String value);

    /**
     * Getter for <code>train.t_message_record.f_receiver</code>. 接收人id，逗号隔开
     */
    public String getReceiver();

    /**
     * Setter for <code>train.t_message_record.f_content</code>. 短信内容
     */
    public void setContent(String value);

    /**
     * Getter for <code>train.t_message_record.f_content</code>. 短信内容
     */
    public String getContent();

    /**
     * Setter for <code>train.t_message_record.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_message_record.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_message_record.f_organization</code>. 发件人组织
     */
    public void setOrganization(String value);

    /**
     * Getter for <code>train.t_message_record.f_organization</code>. 发件人组织
     */
    public String getOrganization();

    /**
     * Setter for <code>train.t_message_record.f_status</code>. 0.未发送（“/”）、1.回执返回状态“发送成功、3.回执返回状态“发送失败”、4接口调用成功、5接口调用失败
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_message_record.f_status</code>. 0.未发送（“/”）、1.回执返回状态“发送成功、3.回执返回状态“发送失败”、4接口调用成功、5接口调用失败
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_message_record.f_task_id</code>. 发送短信同步返回内容中包含的字段
     */
    public void setTaskId(String value);

    /**
     * Getter for <code>train.t_message_record.f_task_id</code>. 发送短信同步返回内容中包含的字段
     */
    public String getTaskId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMessageRecord
     */
    public void from(IMessageRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMessageRecord
     */
    public <E extends IMessageRecord> E into(E into);
}
