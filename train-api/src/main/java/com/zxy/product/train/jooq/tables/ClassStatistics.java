/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassStatisticsRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassStatistics extends TableImpl<ClassStatisticsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_statistics</code>
     */
    public static final ClassStatistics CLASS_STATISTICS = new ClassStatistics();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassStatisticsRecord> getRecordType() {
        return ClassStatisticsRecord.class;
    }

    /**
     * The column <code>train.t_class_statistics.f_id</code>.
     */
    public final TableField<ClassStatisticsRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_statistics.f_content</code>. 题目
     */
    public final TableField<ClassStatisticsRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "题目");

    /**
     * The column <code>train.t_class_statistics.f_satisfaction</code>. 满意
     */
    public final TableField<ClassStatisticsRecord, Integer> SATISFACTION = createField("f_satisfaction", org.jooq.impl.SQLDataType.INTEGER, this, "满意");

    /**
     * The column <code>train.t_class_statistics.f_basic_satisfaction</code>. 基本满意
     */
    public final TableField<ClassStatisticsRecord, Integer> BASIC_SATISFACTION = createField("f_basic_satisfaction", org.jooq.impl.SQLDataType.INTEGER, this, "基本满意");

    /**
     * The column <code>train.t_class_statistics.f_commonly</code>. 一般
     */
    public final TableField<ClassStatisticsRecord, Integer> COMMONLY = createField("f_commonly", org.jooq.impl.SQLDataType.INTEGER, this, "一般");

    /**
     * The column <code>train.t_class_statistics.f_dissatisfied</code>. 不满意
     */
    public final TableField<ClassStatisticsRecord, Integer> DISSATISFIED = createField("f_dissatisfied", org.jooq.impl.SQLDataType.INTEGER, this, "不满意");

    /**
     * The column <code>train.t_class_statistics.f_very_dissatisfied</code>. 很不满意
     */
    public final TableField<ClassStatisticsRecord, Integer> VERY_DISSATISFIED = createField("f_very_dissatisfied", org.jooq.impl.SQLDataType.INTEGER, this, "很不满意");

    /**
     * The column <code>train.t_class_statistics.f_count</code>. 合计（个）
     */
    public final TableField<ClassStatisticsRecord, Integer> COUNT = createField("f_count", org.jooq.impl.SQLDataType.INTEGER, this, "合计（个）");

    /**
     * The column <code>train.t_class_statistics.f_recommended_number</code>. 推荐数（个）
     */
    public final TableField<ClassStatisticsRecord, Integer> RECOMMENDED_NUMBER = createField("f_recommended_number", org.jooq.impl.SQLDataType.INTEGER, this, "推荐数（个）");

    /**
     * The column <code>train.t_class_statistics.f_teacher_satisfaction</code>. 课程师资满意率（%）
     */
    public final TableField<ClassStatisticsRecord, String> TEACHER_SATISFACTION = createField("f_teacher_satisfaction", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "课程师资满意率（%）");

    /**
     * The column <code>train.t_class_statistics.f_satisfied_rate</code>. 满意（%）
     */
    public final TableField<ClassStatisticsRecord, String> SATISFIED_RATE = createField("f_satisfied_rate", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "满意（%）");

    /**
     * The column <code>train.t_class_statistics.f_basic_satisfaction_rate</code>. 基本满意（%）
     */
    public final TableField<ClassStatisticsRecord, String> BASIC_SATISFACTION_RATE = createField("f_basic_satisfaction_rate", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "基本满意（%）");

    /**
     * The column <code>train.t_class_statistics.f_commonly_rate</code>. 一般（%）
     */
    public final TableField<ClassStatisticsRecord, String> COMMONLY_RATE = createField("f_commonly_rate", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "一般（%）");

    /**
     * The column <code>train.t_class_statistics.f_dissatisfied_rate</code>. 不满意（%）
     */
    public final TableField<ClassStatisticsRecord, String> DISSATISFIED_RATE = createField("f_dissatisfied_rate", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "不满意（%）");

    /**
     * The column <code>train.t_class_statistics.f_very_dissatisfied_rate</code>. 很不满意（%）
     */
    public final TableField<ClassStatisticsRecord, String> VERY_DISSATISFIED_RATE = createField("f_very_dissatisfied_rate", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "很不满意（%）");

    /**
     * The column <code>train.t_class_statistics.f_satisfaction_rate</code>. 满意率（%）
     */
    public final TableField<ClassStatisticsRecord, String> SATISFACTION_RATE = createField("f_satisfaction_rate", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "满意率（%）");

    /**
     * The column <code>train.t_class_statistics.f_recommendation_rate</code>. 推荐率（%）
     */
    public final TableField<ClassStatisticsRecord, String> RECOMMENDATION_RATE = createField("f_recommendation_rate", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "推荐率（%）");

    /**
     * The column <code>train.t_class_statistics.f_order</code>. 排序
     */
    public final TableField<ClassStatisticsRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_class_statistics.f_class_id</code>. 班级id
     */
    public final TableField<ClassStatisticsRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级id");

    /**
     * The column <code>train.t_class_statistics.f_satisfaction_dimension</code>. 1第一部分2第二部分
     */
    public final TableField<ClassStatisticsRecord, Integer> SATISFACTION_DIMENSION = createField("f_satisfaction_dimension", org.jooq.impl.SQLDataType.INTEGER, this, "1第一部分2第二部分");

    /**
     * Create a <code>train.t_class_statistics</code> table reference
     */
    public ClassStatistics() {
        this("t_class_statistics", null);
    }

    /**
     * Create an aliased <code>train.t_class_statistics</code> table reference
     */
    public ClassStatistics(String alias) {
        this(alias, CLASS_STATISTICS);
    }

    private ClassStatistics(String alias, Table<ClassStatisticsRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassStatistics(String alias, Table<ClassStatisticsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassStatisticsRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_STATISTICS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassStatisticsRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassStatisticsRecord>>asList(Keys.KEY_T_CLASS_STATISTICS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassStatistics as(String alias) {
        return new ClassStatistics(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassStatistics rename(String name) {
        return new ClassStatistics(name, null);
    }
}
