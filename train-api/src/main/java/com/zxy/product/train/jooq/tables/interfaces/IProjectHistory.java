/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 历史培训计划表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IProjectHistory extends Serializable {

    /**
     * Setter for <code>train.t_project_history.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_project_history.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_project_history.f_name</code>. 计划名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_project_history.f_name</code>. 计划名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_project_history.f_code</code>. MIS编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_project_history.f_code</code>. MIS编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_project_history.f_contact_member_id</code>. 需求单位联系人
     */
    public void setContactMemberId(String value);

    /**
     * Getter for <code>train.t_project_history.f_contact_member_id</code>. 需求单位联系人
     */
    public String getContactMemberId();

    /**
     * Setter for <code>train.t_project_history.f_organization_id</code>. 培训需求单位
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_project_history.f_organization_id</code>. 培训需求单位
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_project_history.f_contact_phone</code>. 联系电话
     */
    public void setContactPhone(String value);

    /**
     * Getter for <code>train.t_project_history.f_contact_phone</code>. 联系电话
     */
    public String getContactPhone();

    /**
     * Setter for <code>train.t_project_history.f_contact_email</code>. 联系邮箱
     */
    public void setContactEmail(String value);

    /**
     * Getter for <code>train.t_project_history.f_contact_email</code>. 联系邮箱
     */
    public String getContactEmail();

    /**
     * Setter for <code>train.t_project_history.f_plan_year</code>. 计划年月
     */
    public void setPlanYear(Long value);

    /**
     * Getter for <code>train.t_project_history.f_plan_year</code>. 计划年月
     */
    public Long getPlanYear();

    /**
     * Setter for <code>train.t_project_history.f_amount</code>. 计划人数
     */
    public void setAmount(Integer value);

    /**
     * Getter for <code>train.t_project_history.f_amount</code>. 计划人数
     */
    public Integer getAmount();

    /**
     * Setter for <code>train.t_project_history.f_object</code>. 培训对象
     */
    public void setObject(String value);

    /**
     * Getter for <code>train.t_project_history.f_object</code>. 培训对象
     */
    public String getObject();

    /**
     * Setter for <code>train.t_project_history.f_days</code>. 计划培训天数
     */
    public void setDays(Integer value);

    /**
     * Getter for <code>train.t_project_history.f_days</code>. 计划培训天数
     */
    public Integer getDays();

    /**
     * Setter for <code>train.t_project_history.f_type_id</code>. 培训类型
     */
    public void setTypeId(String value);

    /**
     * Getter for <code>train.t_project_history.f_type_id</code>. 培训类型
     */
    public String getTypeId();

    /**
     * Setter for <code>train.t_project_history.f_address</code>. 培训地点
     */
    public void setAddress(String value);

    /**
     * Getter for <code>train.t_project_history.f_address</code>. 培训地点
     */
    public String getAddress();

    /**
     * Setter for <code>train.t_project_history.f_status</code>. 状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_project_history.f_status</code>. 状态
     */
    public Integer getStatus();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IProjectHistory
     */
    public void from(IProjectHistory from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IProjectHistory
     */
    public <E extends IProjectHistory> E into(E into);
}
