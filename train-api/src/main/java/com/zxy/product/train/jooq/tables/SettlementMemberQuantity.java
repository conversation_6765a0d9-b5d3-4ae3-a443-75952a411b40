/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SettlementMemberQuantityRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训班结算人数配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SettlementMemberQuantity extends TableImpl<SettlementMemberQuantityRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_settlement_member_quantity</code>
     */
    public static final SettlementMemberQuantity SETTLEMENT_MEMBER_QUANTITY = new SettlementMemberQuantity();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SettlementMemberQuantityRecord> getRecordType() {
        return SettlementMemberQuantityRecord.class;
    }

    /**
     * The column <code>train.t_settlement_member_quantity.f_id</code>. ID
     */
    public final TableField<SettlementMemberQuantityRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_settlement_member_quantity.f_class_id</code>. 班级id
     */
    public final TableField<SettlementMemberQuantityRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "班级id");

    /**
     * The column <code>train.t_settlement_member_quantity.f_date</code>. 培训日期
     */
    public final TableField<SettlementMemberQuantityRecord, Long> DATE = createField("f_date", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "培训日期");

    /**
     * The column <code>train.t_settlement_member_quantity.f_quantity</code>. 培训人数
     */
    public final TableField<SettlementMemberQuantityRecord, Integer> QUANTITY = createField("f_quantity", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "培训人数");

    /**
     * The column <code>train.t_settlement_member_quantity.f_create_time</code>. 创建时间
     */
    public final TableField<SettlementMemberQuantityRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_settlement_member_quantity</code> table reference
     */
    public SettlementMemberQuantity() {
        this("t_settlement_member_quantity", null);
    }

    /**
     * Create an aliased <code>train.t_settlement_member_quantity</code> table reference
     */
    public SettlementMemberQuantity(String alias) {
        this(alias, SETTLEMENT_MEMBER_QUANTITY);
    }

    private SettlementMemberQuantity(String alias, Table<SettlementMemberQuantityRecord> aliased) {
        this(alias, aliased, null);
    }

    private SettlementMemberQuantity(String alias, Table<SettlementMemberQuantityRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训班结算人数配置表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SettlementMemberQuantityRecord> getPrimaryKey() {
        return Keys.KEY_T_SETTLEMENT_MEMBER_QUANTITY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SettlementMemberQuantityRecord>> getKeys() {
        return Arrays.<UniqueKey<SettlementMemberQuantityRecord>>asList(Keys.KEY_T_SETTLEMENT_MEMBER_QUANTITY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementMemberQuantity as(String alias) {
        return new SettlementMemberQuantity(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SettlementMemberQuantity rename(String name) {
        return new SettlementMemberQuantity(name, null);
    }
}
