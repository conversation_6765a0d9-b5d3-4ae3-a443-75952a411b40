/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.TaskRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 作业表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Task extends TableImpl<TaskRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_task</code>
     */
    public static final Task TASK = new Task();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TaskRecord> getRecordType() {
        return TaskRecord.class;
    }

    /**
     * The column <code>train.t_task.f_id</code>. 主键
     */
    public final TableField<TaskRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_task.f_name</code>. 作业名称
     */
    public final TableField<TaskRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "作业名称");

    /**
     * The column <code>train.t_task.f_start_time</code>. 开始时间
     */
    public final TableField<TaskRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * The column <code>train.t_task.f_end_time</code>. 结束时间
     */
    public final TableField<TaskRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "结束时间");

    /**
     * The column <code>train.t_task.f_explain</code>. 作业说明
     */
    public final TableField<TaskRecord, String> EXPLAIN = createField("f_explain", org.jooq.impl.SQLDataType.CLOB, this, "作业说明");

    /**
     * The column <code>train.t_task.f_class_id</code>. 班级ID
     */
    public final TableField<TaskRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_task.f_create_time</code>. 创建时间
     */
    public final TableField<TaskRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_task.f_create_member_id</code>. 创建人ID
     */
    public final TableField<TaskRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_task.f_delete_flag</code>. 删除状态 0未删除（默认） 1已删除
     */
    public final TableField<TaskRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除状态 0未删除（默认） 1已删除");

    /**
     * Create a <code>train.t_task</code> table reference
     */
    public Task() {
        this("t_task", null);
    }

    /**
     * Create an aliased <code>train.t_task</code> table reference
     */
    public Task(String alias) {
        this(alias, TASK);
    }

    private Task(String alias, Table<TaskRecord> aliased) {
        this(alias, aliased, null);
    }

    private Task(String alias, Table<TaskRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "作业表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TaskRecord> getPrimaryKey() {
        return Keys.KEY_T_TASK_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TaskRecord>> getKeys() {
        return Arrays.<UniqueKey<TaskRecord>>asList(Keys.KEY_T_TASK_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Task as(String alias) {
        return new Task(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Task rename(String name) {
        return new Task(name, null);
    }
}
