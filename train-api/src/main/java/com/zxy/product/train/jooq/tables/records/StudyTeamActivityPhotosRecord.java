/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamActivityPhotos;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamActivityPhotos;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 团队学习班-活动相册资料表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamActivityPhotosRecord extends UpdatableRecordImpl<StudyTeamActivityPhotosRecord> implements Record5<String, String, String, String, Long>, IStudyTeamActivityPhotos {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_activity_id</code>. 活动id
     */
    @Override
    public void setActivityId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_activity_id</code>. 活动id
     */
    @Override
    public String getActivityId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_img_path</code>. 相册路径
     */
    @Override
    public void setImgPath(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_img_path</code>. 相册路径
     */
    @Override
    public String getImgPath() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_upload_member_id</code>. 上传人
     */
    @Override
    public void setUploadMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_upload_member_id</code>. 上传人
     */
    @Override
    public String getUploadMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_activity_photos.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_photos.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, String, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, String, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS.ACTIVITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS.IMG_PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS.UPLOAD_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getActivityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getImgPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getUploadMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityPhotosRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityPhotosRecord value2(String value) {
        setActivityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityPhotosRecord value3(String value) {
        setImgPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityPhotosRecord value4(String value) {
        setUploadMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityPhotosRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityPhotosRecord values(String value1, String value2, String value3, String value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamActivityPhotos from) {
        setId(from.getId());
        setActivityId(from.getActivityId());
        setImgPath(from.getImgPath());
        setUploadMemberId(from.getUploadMemberId());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamActivityPhotos> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamActivityPhotosRecord
     */
    public StudyTeamActivityPhotosRecord() {
        super(StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS);
    }

    /**
     * Create a detached, initialised StudyTeamActivityPhotosRecord
     */
    public StudyTeamActivityPhotosRecord(String id, String activityId, String imgPath, String uploadMemberId, Long createTime) {
        super(StudyTeamActivityPhotos.STUDY_TEAM_ACTIVITY_PHOTOS);

        set(0, id);
        set(1, activityId);
        set(2, imgPath);
        set(3, uploadMemberId);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityPhotosEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityPhotosEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityPhotosEntity)source;
        pojo.into(this);
        return true;
    }
}
