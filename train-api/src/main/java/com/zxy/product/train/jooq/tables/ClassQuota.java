/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassQuotaRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassQuota extends TableImpl<ClassQuotaRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_quota</code>
     */
    public static final ClassQuota CLASS_QUOTA = new ClassQuota();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassQuotaRecord> getRecordType() {
        return ClassQuotaRecord.class;
    }

    /**
     * The column <code>train.t_class_quota.f_id</code>.
     */
    public final TableField<ClassQuotaRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_quota.f_class_id</code>. 培训班ID
     */
    public final TableField<ClassQuotaRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训班ID");

    /**
     * The column <code>train.t_class_quota.f_type</code>. 类型（1整体配额  2分省配额）
     */
    public final TableField<ClassQuotaRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类型（1整体配额  2分省配额）");

    /**
     * The column <code>train.t_class_quota.f_is_auto_approve</code>. 是否自动审批（0否  1是）
     */
    public final TableField<ClassQuotaRecord, Integer> IS_AUTO_APPROVE = createField("f_is_auto_approve", org.jooq.impl.SQLDataType.INTEGER, this, "是否自动审批（0否  1是）");

    /**
     * The column <code>train.t_class_quota.f_create_member</code>. 创建人
     */
    public final TableField<ClassQuotaRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_quota.f_create_time</code>. 创建时间
     */
    public final TableField<ClassQuotaRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_class_quota</code> table reference
     */
    public ClassQuota() {
        this("t_class_quota", null);
    }

    /**
     * Create an aliased <code>train.t_class_quota</code> table reference
     */
    public ClassQuota(String alias) {
        this(alias, CLASS_QUOTA);
    }

    private ClassQuota(String alias, Table<ClassQuotaRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassQuota(String alias, Table<ClassQuotaRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassQuotaRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_QUOTA_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassQuotaRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassQuotaRecord>>asList(Keys.KEY_T_CLASS_QUOTA_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuota as(String alias) {
        return new ClassQuota(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassQuota rename(String name) {
        return new ClassQuota(name, null);
    }
}
