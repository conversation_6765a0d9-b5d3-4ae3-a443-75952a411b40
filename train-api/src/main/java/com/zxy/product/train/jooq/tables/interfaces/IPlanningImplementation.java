/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 策划实施表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPlanningImplementation extends Serializable {

    /**
     * Setter for <code>train.t_planning_implementation.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_planning_implementation.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_planning_implementation.f_project_id</code>. 培训计划id
     */
    public void setProjectId(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_project_id</code>. 培训计划id
     */
    public String getProjectId();

    /**
     * Setter for <code>train.t_planning_implementation.f_business_id</code>. 关联培训项目表id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_business_id</code>. 关联培训项目表id
     */
    public String getBusinessId();

    /**
     * Setter for <code>train.t_planning_implementation.f_progress_details</code>. 进度详情（1未启动、2需求沟通、3方案设计、4资源建设、5实施中、6已完成、7取消、8待定）
     */
    public void setProgressDetails(Integer value);

    /**
     * Getter for <code>train.t_planning_implementation.f_progress_details</code>. 进度详情（1未启动、2需求沟通、3方案设计、4资源建设、5实施中、6已完成、7取消、8待定）
     */
    public Integer getProgressDetails();

    /**
     * Setter for <code>train.t_planning_implementation.f_initiation_date</code>. 开始实施日期
     */
    public void setInitiationDate(Long value);

    /**
     * Getter for <code>train.t_planning_implementation.f_initiation_date</code>. 开始实施日期
     */
    public Long getInitiationDate();

    /**
     * Setter for <code>train.t_planning_implementation.f_finish_date</code>. 完成日期
     */
    public void setFinishDate(Long value);

    /**
     * Getter for <code>train.t_planning_implementation.f_finish_date</code>. 完成日期
     */
    public Long getFinishDate();

    /**
     * Setter for <code>train.t_planning_implementation.f_embodiment_id</code>. 实施方式Id
     */
    public void setEmbodimentId(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_embodiment_id</code>. 实施方式Id
     */
    public String getEmbodimentId();

    /**
     * Setter for <code>train.t_planning_implementation.f_embodiment_address</code>. 实施地点
     */
    public void setEmbodimentAddress(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_embodiment_address</code>. 实施地点
     */
    public String getEmbodimentAddress();

    /**
     * Setter for <code>train.t_planning_implementation.f_plan_planning_id</code>. 方案策划情况Id
     */
    public void setPlanPlanningId(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_plan_planning_id</code>. 方案策划情况Id
     */
    public String getPlanPlanningId();

    /**
     * Setter for <code>train.t_planning_implementation.f_teacher_num</code>. 实施师资数量
     */
    public void setTeacherNum(Integer value);

    /**
     * Getter for <code>train.t_planning_implementation.f_teacher_num</code>. 实施师资数量
     */
    public Integer getTeacherNum();

    /**
     * Setter for <code>train.t_planning_implementation.f_course_num</code>. 电子课程采纳量
     */
    public void setCourseNum(Integer value);

    /**
     * Getter for <code>train.t_planning_implementation.f_course_num</code>. 电子课程采纳量
     */
    public Integer getCourseNum();

    /**
     * Setter for <code>train.t_planning_implementation.f_content</code>. 备注
     */
    public void setContent(String value);

    /**
     * Getter for <code>train.t_planning_implementation.f_content</code>. 备注
     */
    public String getContent();

    /**
     * Setter for <code>train.t_planning_implementation.f_source</code>. 来源（0：同步班级管理 1：自建）
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>train.t_planning_implementation.f_source</code>. 来源（0：同步班级管理 1：自建）
     */
    public Integer getSource();

    /**
     * Setter for <code>train.t_planning_implementation.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_planning_implementation.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_planning_implementation.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_planning_implementation.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_planning_implementation.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_planning_implementation.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPlanningImplementation
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.IPlanningImplementation from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPlanningImplementation
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.IPlanningImplementation> E into(E into);
}
