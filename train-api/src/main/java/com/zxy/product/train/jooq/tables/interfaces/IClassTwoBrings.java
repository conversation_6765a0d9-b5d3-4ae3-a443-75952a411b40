/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassTwoBrings extends Serializable {

    /**
     * Setter for <code>train.t_class_two_brings.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_two_brings.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_class_two_brings.f_class_id</code>. 培训班ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_class_id</code>. 培训班ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_two_brings.f_title1</code>. 标题1
     */
    public void setTitle1(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_title1</code>. 标题1
     */
    public String getTitle1();

    /**
     * Setter for <code>train.t_class_two_brings.f_content1</code>. 内容1
     */
    public void setContent1(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_content1</code>. 内容1
     */
    public String getContent1();

    /**
     * Setter for <code>train.t_class_two_brings.f_title2</code>. 标题2
     */
    public void setTitle2(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_title2</code>. 标题2
     */
    public String getTitle2();

    /**
     * Setter for <code>train.t_class_two_brings.f_content2</code>. 内容2
     */
    public void setContent2(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_content2</code>. 内容2
     */
    public String getContent2();

    /**
     * Setter for <code>train.t_class_two_brings.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_two_brings.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_two_brings.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_two_brings.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassTwoBrings
     */
    public void from(IClassTwoBrings from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassTwoBrings
     */
    public <E extends IClassTwoBrings> E into(E into);
}
