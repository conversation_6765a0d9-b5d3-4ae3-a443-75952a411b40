/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassResource extends Serializable {

    /**
     * Setter for <code>train.t_class_resource.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_resource.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_resource.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_resource.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_resource.f_rest_room</code>. 客房
     */
    public void setRestRoom(String value);

    /**
     * Getter for <code>train.t_class_resource.f_rest_room</code>. 客房
     */
    public String getRestRoom();

    /**
     * Setter for <code>train.t_class_resource.f_dining_room</code>. 餐厅
     */
    public void setDiningRoom(String value);

    /**
     * Getter for <code>train.t_class_resource.f_dining_room</code>. 餐厅
     */
    public String getDiningRoom();

    /**
     * Setter for <code>train.t_class_resource.f_classroom</code>. 教室
     */
    public void setClassroom(String value);

    /**
     * Getter for <code>train.t_class_resource.f_classroom</code>. 教室
     */
    public String getClassroom();

    /**
     * Setter for <code>train.t_class_resource.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_class_resource.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_class_resource.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_class_resource.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_class_resource.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_class_resource.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public Integer getDeleteFlag();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassResource
     */
    public void from(IClassResource from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassResource
     */
    public <E extends IClassResource> E into(E into);
}
