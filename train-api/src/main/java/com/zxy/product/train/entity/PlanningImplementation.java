package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.PlanningImplementationEntity;

public class PlanningImplementation extends PlanningImplementationEntity {

    private static final long serialVersionUID = -7391380295626617606L;

    // 未实施
    public static final Integer UNIMPLEMENTED = 1;
    //实施中
    public static final Integer IN_OPERATION = 2;
    //已实施
    public static final Integer IMPLEMENTED = 3;
    //未启动
    public static final Integer NOT_STARTED = 1;
    // 策划实施中的实施中状态
    public static final Integer IN_OPERATION_V2 = 5;
    //已完成
    public static final Integer COMPLETED = 6;
    //取消
    public static final Integer CANCEL = 7;

    /**
     * 删除状态
     * 0未删除(默认) 1已删除
     */
    public static final int DELETE_NO = 0;
    public static final int DELETE_YES = 1;

    private String planPlaningName;

    private String embodimentName;


    public String getPlanPlaningName() {
        return planPlaningName;
    }

    public void setPlanPlaningName(String planPlaningName) {
        this.planPlaningName = planPlaningName;
    }

    public String getEmbodimentName() {
        return embodimentName;
    }

    public void setEmbodimentName(String embodimentName) {
        this.embodimentName = embodimentName;
    }
}
