package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.OrganizationEntity;

/**
 * <AUTHOR>
 *
 */
public class Organization extends OrganizationEntity {

    /**
     *
     */
    private static final long serialVersionUID = 4661050558792921560L;

    public static final String INCLUDE_KEY = "includeKey";
    public static final String NOT_INCLUDE_KEY = "notIncludeKey";

    public static final Integer STATUS_ENABLED = 1;
    public static final Integer STATUS_DISABLED = 2;
    public static final Integer LEVEL_HEAD = 2; // 公司
    private String childFind; //1 勾选发现 2 半选择状态
    private String organizationName; //所属机构名称
    private String companyName;
    private String memberId;//为成员管理所用
    private boolean isParent;

    public String getChildFind() {
        return childFind;
    }

    public void setChildFind(String childFind) {
        this.childFind = childFind;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public boolean getIsParent() {
        return isParent;
    }

    public void setIsParent(boolean isParent) {
        this.isParent = isParent;
    }
}
