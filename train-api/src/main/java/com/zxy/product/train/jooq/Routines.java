/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq;


import com.zxy.product.train.jooq.routines.BatchUpdateOrsIds_728;
import com.zxy.product.train.jooq.routines.InsertTClassDetail;
import com.zxy.product.train.jooq.routines.InsertTClassGroup;
import com.zxy.product.train.jooq.routines.InsertTClassInfo;

import javax.annotation.Generated;

import org.jooq.Configuration;


/**
 * Convenience access to all stored procedures and functions in train
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.5"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Routines {

    /**
     * Call <code>train.batch_update_ors_ids_728</code>
     */
    public static void batchUpdateOrsIds_728(Configuration configuration) {
        BatchUpdateOrsIds_728 p = new BatchUpdateOrsIds_728();

        p.execute(configuration);
    }

    /**
     * Call <code>train.insert_t_class_detail</code>
     */
    public static void insertTClassDetail(Configuration configuration) {
        InsertTClassDetail p = new InsertTClassDetail();

        p.execute(configuration);
    }

    /**
     * Call <code>train.insert_t_class_group</code>
     */
    public static void insertTClassGroup(Configuration configuration) {
        InsertTClassGroup p = new InsertTClassGroup();

        p.execute(configuration);
    }

    /**
     * Call <code>train.insert_t_class_info</code>
     */
    public static void insertTClassInfo(Configuration configuration) {
        InsertTClassInfo p = new InsertTClassInfo();

        p.execute(configuration);
    }
}
