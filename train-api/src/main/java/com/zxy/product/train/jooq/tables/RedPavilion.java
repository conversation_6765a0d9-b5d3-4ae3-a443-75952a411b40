/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.RedPavilionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 红色展馆信息
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RedPavilion extends TableImpl<RedPavilionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_red_pavilion</code>
     */
    public static final RedPavilion RED_PAVILION = new RedPavilion();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RedPavilionRecord> getRecordType() {
        return RedPavilionRecord.class;
    }

    /**
     * The column <code>train.t_red_pavilion.f_id</code>.
     */
    public final TableField<RedPavilionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_red_pavilion.f_province</code>. 省份
     */
    public final TableField<RedPavilionRecord, String> PROVINCE = createField("f_province", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "省份");

    /**
     * The column <code>train.t_red_pavilion.f_unit</code>. 单位
     */
    public final TableField<RedPavilionRecord, String> UNIT = createField("f_unit", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "单位");

    /**
     * The column <code>train.t_red_pavilion.f_base_name</code>. 教育基地名称
     */
    public final TableField<RedPavilionRecord, String> BASE_NAME = createField("f_base_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "教育基地名称");

    /**
     * The column <code>train.t_red_pavilion.f_location</code>. 所在地
     */
    public final TableField<RedPavilionRecord, String> LOCATION = createField("f_location", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所在地");

    /**
     * The column <code>train.t_red_pavilion.f_pavilion_name</code>. 展馆名称
     */
    public final TableField<RedPavilionRecord, String> PAVILION_NAME = createField("f_pavilion_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "展馆名称");

    /**
     * The column <code>train.t_red_pavilion.f_image_url</code>. 缩略图路径
     */
    public final TableField<RedPavilionRecord, String> IMAGE_URL = createField("f_image_url", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "缩略图路径");

    /**
     * The column <code>train.t_red_pavilion.f_url</code>. 地址
     */
    public final TableField<RedPavilionRecord, String> URL = createField("f_url", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "地址");

    /**
     * The column <code>train.t_red_pavilion.f_type</code>. 1:VR 2:主页 3:视频
     */
    public final TableField<RedPavilionRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1:VR 2:主页 3:视频");

    /**
     * The column <code>train.t_red_pavilion.f_note</code>. 备注
     */
    public final TableField<RedPavilionRecord, String> NOTE = createField("f_note", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>train.t_red_pavilion.f_create_time</code>. 创建时间
     */
    public final TableField<RedPavilionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>train.t_red_pavilion</code> table reference
     */
    public RedPavilion() {
        this("t_red_pavilion", null);
    }

    /**
     * Create an aliased <code>train.t_red_pavilion</code> table reference
     */
    public RedPavilion(String alias) {
        this(alias, RED_PAVILION);
    }

    private RedPavilion(String alias, Table<RedPavilionRecord> aliased) {
        this(alias, aliased, null);
    }

    private RedPavilion(String alias, Table<RedPavilionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "红色展馆信息");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<RedPavilionRecord> getPrimaryKey() {
        return Keys.KEY_T_RED_PAVILION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<RedPavilionRecord>> getKeys() {
        return Arrays.<UniqueKey<RedPavilionRecord>>asList(Keys.KEY_T_RED_PAVILION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedPavilion as(String alias) {
        return new RedPavilion(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public RedPavilion rename(String name) {
        return new RedPavilion(name, null);
    }
}
