/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamAchievementReply;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamAchievementReply;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 团队学习班-学习成果回复表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamAchievementReplyRecord extends UpdatableRecordImpl<StudyTeamAchievementReplyRecord> implements Record12<String, String, String, String, String, String, Integer, Integer, Integer, Long, String, Integer>, IStudyTeamAchievementReply {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_parent_id</code>. 上级id
     */
    @Override
    public void setParentId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_parent_id</code>. 上级id
     */
    @Override
    public String getParentId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_comment_id</code>. 评论id
     */
    @Override
    public void setCommentId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_comment_id</code>. 评论id
     */
    @Override
    public String getCommentId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_to_member_id</code>. 被回复的用户id
     */
    @Override
    public void setToMemberId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_to_member_id</code>. 被回复的用户id
     */
    @Override
    public String getToMemberId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_content</code>. 回复内容
     */
    @Override
    public void setContent(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_content</code>. 回复内容
     */
    @Override
    public String getContent() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_hide</code>. 隐藏(1是，0否)
     */
    @Override
    public void setHide(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_hide</code>. 隐藏(1是，0否)
     */
    @Override
    public Integer getHide() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_praise_count</code>. 点赞数
     */
    @Override
    public void setPraiseCount(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_praise_count</code>. 点赞数
     */
    @Override
    public Integer getPraiseCount() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_organization_id</code>. 所属组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_organization_id</code>. 所属组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(10);
    }

    /**
     * Setter for <code>train.t_study_team_achievement_reply.f_level</code>. 1回复了讨论 2回复了回复
     */
    @Override
    public void setLevel(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_study_team_achievement_reply.f_level</code>. 1回复了讨论 2回复了回复
     */
    @Override
    public Integer getLevel() {
        return (Integer) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, String, String, String, Integer, Integer, Integer, Long, String, Integer> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, String, String, String, String, String, Integer, Integer, Integer, Long, String, Integer> valuesRow() {
        return (Row12) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.PARENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.COMMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.TO_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.CONTENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.HIDE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.PRAISE_COUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY.LEVEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getParentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCommentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getToMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getContent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getHide();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getPraiseCount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getLevel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value2(String value) {
        setParentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value3(String value) {
        setCommentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value5(String value) {
        setToMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value6(String value) {
        setContent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value7(Integer value) {
        setHide(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value8(Integer value) {
        setPraiseCount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value9(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value10(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value11(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord value12(Integer value) {
        setLevel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReplyRecord values(String value1, String value2, String value3, String value4, String value5, String value6, Integer value7, Integer value8, Integer value9, Long value10, String value11, Integer value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamAchievementReply from) {
        setId(from.getId());
        setParentId(from.getParentId());
        setCommentId(from.getCommentId());
        setMemberId(from.getMemberId());
        setToMemberId(from.getToMemberId());
        setContent(from.getContent());
        setHide(from.getHide());
        setPraiseCount(from.getPraiseCount());
        setDeleteFlag(from.getDeleteFlag());
        setCreateTime(from.getCreateTime());
        setOrganizationId(from.getOrganizationId());
        setLevel(from.getLevel());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamAchievementReply> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamAchievementReplyRecord
     */
    public StudyTeamAchievementReplyRecord() {
        super(StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY);
    }

    /**
     * Create a detached, initialised StudyTeamAchievementReplyRecord
     */
    public StudyTeamAchievementReplyRecord(String id, String parentId, String commentId, String memberId, String toMemberId, String content, Integer hide, Integer praiseCount, Integer deleteFlag, Long createTime, String organizationId, Integer level) {
        super(StudyTeamAchievementReply.STUDY_TEAM_ACHIEVEMENT_REPLY);

        set(0, id);
        set(1, parentId);
        set(2, commentId);
        set(3, memberId);
        set(4, toMemberId);
        set(5, content);
        set(6, hide);
        set(7, praiseCount);
        set(8, deleteFlag);
        set(9, createTime);
        set(10, organizationId);
        set(11, level);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementReplyEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementReplyEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementReplyEntity)source;
        pojo.into(this);
        return true;
    }
}
