/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudentDetailTotalRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudentDetailTotal extends TableImpl<StudentDetailTotalRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_student_detail_total</code>
     */
    public static final StudentDetailTotal STUDENT_DETAIL_TOTAL = new StudentDetailTotal();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudentDetailTotalRecord> getRecordType() {
        return StudentDetailTotalRecord.class;
    }

    /**
     * The column <code>train.t_student_detail_total.f_id</code>.
     */
    public final TableField<StudentDetailTotalRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_student_detail_total.f_name</code>. 结算公司
     */
    public final TableField<StudentDetailTotalRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "结算公司");

    /**
     * The column <code>train.t_student_detail_total.f_settlement_company_code</code>. 结算公司组织编码
     */
    public final TableField<StudentDetailTotalRecord, String> SETTLEMENT_COMPANY_CODE = createField("f_settlement_company_code", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "结算公司组织编码");

    /**
     * The column <code>train.t_student_detail_total.f_year</code>. 年份
     */
    public final TableField<StudentDetailTotalRecord, String> YEAR = createField("f_year", org.jooq.impl.SQLDataType.VARCHAR.length(4).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "年份");

    /**
     * The column <code>train.t_student_detail_total.f_january</code>. 一月
     */
    public final TableField<StudentDetailTotalRecord, Double> JANUARY = createField("f_january", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "一月");

    /**
     * The column <code>train.t_student_detail_total.f_march</code>. 三月
     */
    public final TableField<StudentDetailTotalRecord, Double> MARCH = createField("f_march", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "三月");

    /**
     * The column <code>train.t_student_detail_total.f_february</code>. 二月
     */
    public final TableField<StudentDetailTotalRecord, Double> FEBRUARY = createField("f_february", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "二月");

    /**
     * The column <code>train.t_student_detail_total.f_december</code>. 十二月
     */
    public final TableField<StudentDetailTotalRecord, Double> DECEMBER = createField("f_december", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "十二月");

    /**
     * The column <code>train.t_student_detail_total.f_november</code>. 十一月
     */
    public final TableField<StudentDetailTotalRecord, Double> NOVEMBER = createField("f_november", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "十一月");

    /**
     * The column <code>train.t_student_detail_total.f_october</code>. 十月
     */
    public final TableField<StudentDetailTotalRecord, Double> OCTOBER = createField("f_october", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "十月");

    /**
     * The column <code>train.t_student_detail_total.f_september</code>. 九月
     */
    public final TableField<StudentDetailTotalRecord, Double> SEPTEMBER = createField("f_september", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "九月");

    /**
     * The column <code>train.t_student_detail_total.f_august</code>. 八月
     */
    public final TableField<StudentDetailTotalRecord, Double> AUGUST = createField("f_august", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "八月");

    /**
     * The column <code>train.t_student_detail_total.f_july</code>. 七月
     */
    public final TableField<StudentDetailTotalRecord, Double> JULY = createField("f_july", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "七月");

    /**
     * The column <code>train.t_student_detail_total.f_june</code>. 六月
     */
    public final TableField<StudentDetailTotalRecord, Double> JUNE = createField("f_june", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "六月");

    /**
     * The column <code>train.t_student_detail_total.f_may</code>. 五月
     */
    public final TableField<StudentDetailTotalRecord, Double> MAY = createField("f_may", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "五月");

    /**
     * The column <code>train.t_student_detail_total.f_april</code>. 四月
     */
    public final TableField<StudentDetailTotalRecord, Double> APRIL = createField("f_april", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "四月");

    /**
     * The column <code>train.t_student_detail_total.f_sort</code>. 排序
     */
    public final TableField<StudentDetailTotalRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * The column <code>train.t_student_detail_total.f_total</code>. 总计
     */
    public final TableField<StudentDetailTotalRecord, Double> TOTAL = createField("f_total", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "总计");

    /**
     * The column <code>train.t_student_detail_total.f_create_time</code>. 创建时间
     */
    public final TableField<StudentDetailTotalRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>train.t_student_detail_total</code> table reference
     */
    public StudentDetailTotal() {
        this("t_student_detail_total", null);
    }

    /**
     * Create an aliased <code>train.t_student_detail_total</code> table reference
     */
    public StudentDetailTotal(String alias) {
        this(alias, STUDENT_DETAIL_TOTAL);
    }

    private StudentDetailTotal(String alias, Table<StudentDetailTotalRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudentDetailTotal(String alias, Table<StudentDetailTotalRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudentDetailTotalRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDENT_DETAIL_TOTAL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudentDetailTotalRecord>> getKeys() {
        return Arrays.<UniqueKey<StudentDetailTotalRecord>>asList(Keys.KEY_T_STUDENT_DETAIL_TOTAL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetailTotal as(String alias) {
        return new StudentDetailTotal(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudentDetailTotal rename(String name) {
        return new StudentDetailTotal(name, null);
    }
}
