package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberSignEntity;

/**
 * 学习团队成员签到实体类
 *
 * <AUTHOR>
 * @date 2021/4/14/0014 15:52
 */
public class StudyTeamMemberSign extends StudyTeamMemberSignEntity {
    private static final long serialVersionUID = 3679437324560605642L;

    /**
     * 签到状态
     * 1--正常；
     * 2--迟到
     * 3--早退
     * 4--迟到&早退
     * 5--未签到
     * 6--已签到
     * 7--已签退
     */
    public static final int STATUS_NORMAL = 1;
    // public static final int STATUS_LATE = 2;
    // public static final int STATUS_LEAVE_EARLY = 3;
    // public static final int STATUS_LATE_ARRIVAL = 4;
    public static final int STATUS_NOT_SIGN = 5;
    public static final int STATUS_SIGNED = 6;
    public static final int STATUS_LEAVEED = 7;

    /**
     * 时长确认状态
     * 0--未确认
     * 1--确认中
     * 2--已确认
     */
    public static final int CONFIRM_STATUS_UNCONFIRMED = 0;
    public static final int CONFIRM_STATUS_CONFIRMING = 1;
    public static final int CONFIRM_STATUS_CONFIRMED = 2;

    /**
     * 是否为领学人，0 否 1是
     */
    public static final int BUTTON_DISPLAY = 1;
    public static final int BUTTON_NO_DISPLAY = 0;

    /**
     * 角色
     */
    public static final int ROLE_IS_LEADER = 1;
    public static final int ROLE_IS_CURRENT = 2;
    public static final int ROLE_IS_ADMINISTRATOR = 3;

    private String memberId;
    /**
     * 是否展示确认学时按钮，0否；1是
     */
    private int flag;
    /**
     * 签到接口签到状态
     * 1--签到
     * 2--签退
     */
    private int signStatus;

    /**
     * 角色 ,1=领学人,2=成员,3=管理员
     */
    private Integer role;

    private Integer leaderStatus;
    private Integer currentStatus;

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public Integer getLeaderStatus() {
        return leaderStatus;
    }

    public void setLeaderStatus(Integer leaderStatus) {
        this.leaderStatus = leaderStatus;
    }

    public Integer getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(Integer currentStatus) {
        this.currentStatus = currentStatus;
    }


    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public int getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(int signStatus) {
        this.signStatus = signStatus;
    }
}
