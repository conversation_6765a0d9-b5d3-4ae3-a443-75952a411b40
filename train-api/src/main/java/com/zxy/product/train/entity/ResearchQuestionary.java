package com.zxy.product.train.entity;

import com.zxy.product.train.dto.ResearchAnswerRecordMap;
import com.zxy.product.train.jooq.tables.pojos.ResearchQuestionaryEntity;

import java.math.BigDecimal;
import java.util.List;

public class ResearchQuestionary extends ResearchQuestionaryEntity{

	/**
	 * lulu 统计与评估 调研预评估实体类
	 */
	private static final long serialVersionUID = -1039130117479117345L;

	public static final String URI = "exam/research-activity";

	/**
	 * 调研活动
	 */
	public static final Integer TYPE_RESEARCH_ACTIVITY = 1;
	/**
	 * 评估问卷
	 */
	public static final Integer TYPE_EVALUATE_QUESTIONARY = 2;
	/**
	 * 调研问卷
	 */
	public static final Integer TYPE_RESEARCH_QUESTIONARY = 3;
	/** 满意度问卷 */
	public static final Integer TYPE_SATISFACTION_QUESTIONARY = 4;
	/** 四度问卷 */
	public static final Integer TYPE_FOUR_DEGREES_QUESTIONARY = 5;
	/** 能力习问卷 */
	public static final Integer TYPE_ABILITY_LEARNING_QUESTIONARY = 6;
	/** 领导问卷 */
	public static final Integer TYPE_LEADER_QUESTIONARY = 7;
	/** 新满意度问卷 */
	public static final Integer TYPE_NEW_SATISFACTION_QUESTIONARY = 8;

	/**
	 * 未发布
	 */
	public static final Integer STATUS_NO_PUBLISH = 1;

	/**
	 * 未开始
	 */
	public static final Integer STATUS_NO_START = 2;

	/**
	 * 进行中
	 */
	public static final Integer STATUS_PROGRESS = 3;

	/**
	 * 已结束
	 */
	public static final Integer STATUS_OVER = 4;

	/**
	 * 已撤销
	 */
	public static final Integer STATUS_CANCEL = 5;


	/**
	 * 复制 名称 后缀
	 */
	public static final String COPY_PREX = "(copy)";

	/**
	 * 调研 题库ID
	 */
	public static final String RESEARCH_QUESTIONARY_QUESTION_DEPOT_ID = "RESEARCH_QUESTIONARY_QUESTION_DEPOT";

	/**
	 * 模板代码 催办
	 */
	public static final String TEMPLATE_CODE_URGE = "doc_audit_pass";

	/**
	 * 推送个人中心
	 */
	public static final Integer PERSON_CENTER_PUSH = 1;

	/**
	 * 导入调研模板
	 */
	public static final String TEMPLATE_INDEX = "序号";

	public static final Integer ANSWER_PAPER_RULE = 1;

	public static final String TEMPLATE_ORGANIZATION = "所属部门编码(必填)";

	public static final String TEMPLATE_DIMENSION_NAME = "维度名称";

	public static final String TEMPLATE_QUESTION_TYPE = "调研试题类型(必填)";

	public static final String TEMPLATE_QUESTION_OPTION = "选项（用'|'隔开）";

	public static final String TEMPLATE_QUESTION_CONTENT = "调研试题信息(必填)";

	public static final String NAME = "满意度问卷(学员)";
	
	public static final String NAME_COMPETENCY = "能力习得问卷";
	
	public static final String LEADER_QUESTIONNAIR = "学员上级领导问卷";

	private Organization organization;

	private Member publishMember;

	private Organization publishOrganization;

	private List<ResearchAnswerRecordMap> researchAnswerRecordMaps;
	private Integer joinNumber;

	private ResearchAnswerRecord researchAnswerRecord;
	private ResearchRecord researchRecord;
	private Integer num;
	private String researchRecordId;

	private String className;

	private Integer commitStatus;
	
	private Integer commitStatuss;
	
	private Integer commitStatusn;

	private Integer isPartyCadre;//是否是党干部进修班 0否 1是

	private String rId;

	private BigDecimal score;

	public String getrId() {
		return rId;
	}

	public void setrId(String rId) {
		this.rId = rId;
	}

	public BigDecimal getScore() {
		return score;
	}

	public void setScore(BigDecimal score) {
		this.score = score;
	}

	public Integer getCommitStatuss() {
		return commitStatuss;
	}
	public void setCommitStatuss(Integer commitStatuss) {
		this.commitStatuss = commitStatuss;
	}
	public Integer getCommitStatusn() {
		return commitStatusn;
	}
	public void setCommitStatusn(Integer commitStatusn) {
		this.commitStatusn = commitStatusn;
	}
	public String getClassName() {
		return className;
	}
	public void setClassName(String className) {
		this.className = className;
	}
	public Integer getCommitStatus() {
		return commitStatus;
	}
	public void setCommitStatus(Integer commitStatus) {
		this.commitStatus = commitStatus;
	}
	public String getResearchRecordId() {
		return researchRecordId;
	}
	public void setResearchRecordId(String researchRecordId) {
		this.researchRecordId = researchRecordId;
	}
	public List<ResearchAnswerRecordMap> getResearchAnswerRecordMaps() {
		return researchAnswerRecordMaps;
	}
	public void setResearchAnswerRecordMaps(List<ResearchAnswerRecordMap> researchAnswerRecordMaps) {
		this.researchAnswerRecordMaps = researchAnswerRecordMaps;
	}
	private List<Dimension> dimensions;

	public Organization getOrganization() {
		return organization;
	}
	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	public Member getPublishMember() {
		return publishMember;
	}
	public void setPublishMember(Member publishMember) {
		this.publishMember = publishMember;
	}
	public Organization getPublishOrganization() {
		return publishOrganization;
	}
	public void setPublishOrganization(Organization publishOrganization) {
		this.publishOrganization = publishOrganization;
	}
	public List<Dimension> getDimensions() {
		return dimensions;
	}
	public void setDimensions(List<Dimension> dimensions) {
		this.dimensions = dimensions;
	}
	public Integer getJoinNumber() {
		return joinNumber;
	}
	public void setJoinNumber(Integer joinNumber) {
		this.joinNumber = joinNumber;
	}

	public Integer getNum() {
		return num;
	}
	public void setNum(Integer num) {
		this.num = num;
	}
	public ResearchAnswerRecord getResearchAnswerRecord() {
		return researchAnswerRecord;
	}
	public void setResearchAnswerRecord(ResearchAnswerRecord researchAnswerRecord) {
		this.researchAnswerRecord = researchAnswerRecord;
	}
	public ResearchRecord getResearchRecord() {
		return researchRecord;
	}
	public void setResearchRecord(ResearchRecord researchRecord) {
		this.researchRecord = researchRecord;
	}

	public Integer getIsPartyCadre() {
		return isPartyCadre;
	}

	public void setIsPartyCadre(Integer isPartyCadre) {
		this.isPartyCadre = isPartyCadre;
	}
}
