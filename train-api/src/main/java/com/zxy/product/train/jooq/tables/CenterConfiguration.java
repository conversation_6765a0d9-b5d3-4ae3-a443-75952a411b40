/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CenterConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CenterConfiguration extends TableImpl<CenterConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_center_configuration</code>
     */
    public static final CenterConfiguration CENTER_CONFIGURATION = new CenterConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CenterConfigurationRecord> getRecordType() {
        return CenterConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_center_configuration.f_id</code>. 主键
     */
    public final TableField<CenterConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_center_configuration.f_configuration_id</code>. 关联配置表ID
     */
    public final TableField<CenterConfigurationRecord, String> CONFIGURATION_ID = createField("f_configuration_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联配置表ID");

    /**
     * The column <code>train.t_center_configuration.f_type_id</code>. 关联类型ID
     */
    public final TableField<CenterConfigurationRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型ID");

    /**
     * The column <code>train.t_center_configuration.f_member_id</code>. 人员ID
     */
    public final TableField<CenterConfigurationRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "人员ID");

    /**
     * The column <code>train.t_center_configuration.f_sort</code>. 排序
     */
    public final TableField<CenterConfigurationRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_center_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<CenterConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_center_configuration.f_create_member</code>. 创建人ID
     */
    public final TableField<CenterConfigurationRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_center_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<CenterConfigurationRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_center_configuration</code> table reference
     */
    public CenterConfiguration() {
        this("t_center_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_center_configuration</code> table reference
     */
    public CenterConfiguration(String alias) {
        this(alias, CENTER_CONFIGURATION);
    }

    private CenterConfiguration(String alias, Table<CenterConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private CenterConfiguration(String alias, Table<CenterConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CenterConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_CENTER_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CenterConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<CenterConfigurationRecord>>asList(Keys.KEY_T_CENTER_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CenterConfiguration as(String alias) {
        return new CenterConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CenterConfiguration rename(String name) {
        return new CenterConfiguration(name, null);
    }
}
