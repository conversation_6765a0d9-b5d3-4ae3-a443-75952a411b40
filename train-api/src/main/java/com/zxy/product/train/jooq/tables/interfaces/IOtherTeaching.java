/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 其他教学教研记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IOtherTeaching extends Serializable {

    /**
     * Setter for <code>train.t_other_teaching.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_other_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public void setLecturerId(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public String getLecturerId();

    /**
     * Setter for <code>train.t_other_teaching.f_course_name</code>. 教研名称
     */
    public void setCourseName(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_course_name</code>. 教研名称
     */
    public String getCourseName();

    /**
     * Setter for <code>train.t_other_teaching.f_teaching_type</code>. 教学教研类型1：授课；2：课程开发；3：电子课件开发；4：案例开发；5：业务专题研究；6：内训工作支撑
     */
    public void setTeachingType(Integer value);

    /**
     * Getter for <code>train.t_other_teaching.f_teaching_type</code>. 教学教研类型1：授课；2：课程开发；3：电子课件开发；4：案例开发；5：业务专题研究；6：内训工作支撑
     */
    public Integer getTeachingType();

    /**
     * Setter for <code>train.t_other_teaching.f_activity_level</code>. 活动级别
     */
    public void setActivityLevel(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_activity_level</code>. 活动级别
     */
    public String getActivityLevel();

    /**
     * Setter for <code>train.t_other_teaching.f_start_time</code>. 参与开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_other_teaching.f_start_time</code>. 参与开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_other_teaching.f_end_time</code>. 参与结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_other_teaching.f_end_time</code>. 参与结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_other_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public void setSatisfiedDegree(Double value);

    /**
     * Getter for <code>train.t_other_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public Double getSatisfiedDegree();

    /**
     * Setter for <code>train.t_other_teaching.f_status</code>. 是否通过0：是1：否
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_other_teaching.f_status</code>. 是否通过0：是1：否
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_other_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    public void setSatisfiedEvaluate(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    public String getSatisfiedEvaluate();

    /**
     * Setter for <code>train.t_other_teaching.f_other_instructions</code>. 其他情况说明
     */
    public void setOtherInstructions(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_other_instructions</code>. 其他情况说明
     */
    public String getOtherInstructions();

    /**
     * Setter for <code>train.t_other_teaching.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_other_teaching.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_other_teaching.f_approval_time</code>. 审核时间
     */
    public void setApprovalTime(Long value);

    /**
     * Getter for <code>train.t_other_teaching.f_approval_time</code>. 审核时间
     */
    public Long getApprovalTime();

    /**
     * Setter for <code>train.t_other_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    public void setApprovalStatus(Integer value);

    /**
     * Getter for <code>train.t_other_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    public Integer getApprovalStatus();

    /**
     * Setter for <code>train.t_other_teaching.f_organization_id</code>. 归属部门
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_organization_id</code>. 归属部门
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_other_teaching.f_create_member</code>. 创建ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_create_member</code>. 创建ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_other_teaching.f_approval_member</code>. 审核人ID
     */
    public void setApprovalMember(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_approval_member</code>. 审核人ID
     */
    public String getApprovalMember();

    /**
     * Setter for <code>train.t_other_teaching.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_other_teaching.f_lecturer_name</code>. 讲师名称
     */
    public void setLecturerName(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_lecturer_name</code>. 讲师名称
     */
    public String getLecturerName();

    /**
     * Setter for <code>train.t_other_teaching.f_source</code>. 0：讲师自己申请 1：管理员添加
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>train.t_other_teaching.f_source</code>. 0：讲师自己申请 1：管理员添加
     */
    public Integer getSource();

    /**
     * Setter for <code>train.t_other_teaching.f_course_type</code>. 分类（同面授课程分类）
     */
    public void setCourseType(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_course_type</code>. 分类（同面授课程分类）
     */
    public String getCourseType();

    /**
     * Setter for <code>train.t_other_teaching.f_reference_time</code>. 时长(小时)
     */
    public void setReferenceTime(Double value);

    /**
     * Getter for <code>train.t_other_teaching.f_reference_time</code>. 时长(小时)
     */
    public Double getReferenceTime();

    /**
     * Setter for <code>train.t_other_teaching.f_object_oriented</code>. 面向对象
     */
    public void setObjectOriented(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_object_oriented</code>. 面向对象
     */
    public String getObjectOriented();

    /**
     * Setter for <code>train.t_other_teaching.f_description</code>. 简介
     */
    public void setDescription(String value);

    /**
     * Getter for <code>train.t_other_teaching.f_description</code>. 简介
     */
    public String getDescription();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IOtherTeaching
     */
    public void from(IOtherTeaching from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IOtherTeaching
     */
    public <E extends IOtherTeaching> E into(E into);
}
