package com.zxy.product.train.content;

import com.zxy.common.base.exception.Code;

public enum ErrorCode implements Code {

    NoMuchAvailable(60101, "Have no much available room"),
    SaveThemeError(60102, "Save theme error"),
    SaveConfigurationValueError(60103, "Save configurationValue error"),
    SaveOfflineCourseError(60104, "Save offline course error"),
    SaveOnlineCourseError(60105, "Save online course error"),
    InvalidStartTime(60106, "Invalid start time"),
    InvalidEndTime(60107, "Invalid end time"),
    CouldNotFindClassroom(60108, "Could not find classroom by this code"),
    InvalidPhoneNumber(60109, "手机号码有误"),
    InvalidIdCard(60110, "Invalid id card"),
    InvalidNumber(60111, "Invalid number"),
    SaveTraineeGroupError(60110, "Save trainee group error"),
    SaveOptionError(60111, "Save option error"),
    SaveClassTaskError(60112, "Save class task error"),
    SaveClassQuotaError(60113, "Save class quota error"),
    ProjectCodeingRequired(61102, "project code import is required"),
    ProjectCodeingRepetition(61103, "project code import is not repeat"),
    DemandSideRequired(61104, "DemandSide import is required"),
    DemandMemberRequired(61105, "员工编号为必填"),
    ProjectAddressRequired(61106, "培训地点为必填"),
    ProjectTypeRequired(61107, "培训类型为必填"),
    CanNotFindProjectAddress(61108, "找不到该培训地点"),
    CanNotFindProjectType(61109, "找不到该培训类型"),
    ProjectNameingRequired(61110, "计划名称为必填"),
    ProjectNameLength(61111, "计划名称长度最大为70"),
    CanNotFindOrganization(60905, "找不到该组织"),
    CanNotFindMember(60906, "找不到该员工"),
    ResearchHadFinished(60907, "research  has finished "),
    ResearchNoStart(60908, " research no start"),
    ResearchRecordError(60909, "research record error"),
    ResearchEndedReply(60910, "research ended reply"),
    ProjectCodeLength(61112, "ProjectCode import is not greater than 10"),
    ProjectObject(61113, "ProjectObject import is required"),
    ProjectObjectLength(61114, "ProjectObject import is not greater than 500"),
    OrganizationCodeRequired(61115, "组织编码为必填"),
    PhoneRequired(61148, "需求方联系人电话必填"),
    InvalidEmail(61116, "邮箱格式不对"),
    InvalidDate(61117, "日期格式不对"),
    InvalidInteger(61118, "Number Integer format"),
    InvalidIntegerLenght1(61119, "最小值为1"),
    InvalidIntegerLenght3(61120, "Number import is not less than 3"),
    DateRequired(61121, "计划日期为必填"),
    Lenght1Required(61122, "培训人数必填"),
    ProjectCodeRepeat(61123, "计划编码重复"),
    EmailRequired(61124, "需求方联系人邮箱必填"),
    LecturerMobileRepeat(61125, "讲师电话已存在"),
    LecturerRepeat(61126, "LecturerRepeat"),
    DegreeNumber(61127, "所填项必须为整数"),
    CostNumber(61128, "课时费(课酬)最多保留两位小数"),
    CanNotFindCourseCategoryCode(61129, "Can Not Find Course CategoryCode"),
    CourseCategoryCodeRequired(61130, "课程分类序列为必填"),
    CourseNameRequired(61131, "课程名称为必填"),
    ObjRequired(61132, "授课对象为必填"),
    KeywordRequired(61134, "关键词为必填"),
    LecturerIDRequired(61135, "讲师为必填"),
    CourseDurationRequired(61136, "课程时长为必填"),
    CourseRewardRequired(61137, "CourseReward Required"),
    LecturerNameRequired(61138, "讲师名称为必填"),
    LecturerPhoneNumberRepeat(61139, "讲师电话已存在"),
    MemberNameRequired(61140, "员工编号为必填"),
    LecturerLevelRequired(61141, "讲师级别为必填"),
    ProjectDaysRequired(61142, "计划时间为必填"),
    ProjectDaysMin3(61143, "计划时间最小为3天"),
    TrainObjectRequired(61144, "培训对象为必填"),
    MemberCardFormatError(61145, "身份证号码有误"),
    BankCardFormatError(61146, "银行卡号有误"),
    MinimumNumberOfTrainee(61147, "最小学员数必须小于最大学员数"),
    CostTypeRequired(61149, "CostType Required"),
    MemberMobileRepeat(61150, "Member Mobile Repeat"),
    NoSuchPhoneNumber(61151, "No such phone number in system"),
    IdentifyingCodeError(61152, "Identifying code error"),
    ResourceOccupied(61153, "Resource occupied"),
    IdentifyingCodeTimeout(61154, "Identifying code timeout"),
    CanNotFindLevel(61155, "找不到该讲师级别"),
    SatisfyNumber(61156, "课程满意度需在0到10之间最多保留一位小数"),
    TimeLong(61157, "课程时长最多保留一位小数"),
    Num(61158, "满意度需在0到10之间最多保留一位小数"),
    ImportNullFile(61159, "导入文档不能为空"),
    EXCEL_NOTSUPPORT(61160, "文档不符合规范"),
    ImportContentOverSize(61161, "import question content over size"),
    CheckRowTooLong(61162, "导入数据过多不能超过1000条"),
    ProjectCostRequired(61163, "培训级别为必填"),
    CanNotFindProjectCost(61164, "找不到该培训级别"),
    OccupyEnough(61165, "配额已满或已添加过该员工"),
    CheckCatalageExists(61166, "目录编码不存在"),
    ProjectCodeWrong (61167, "计划编码格式不对"),
    LengthMin16Max21 (61168, "银行卡号位数在16位与21位之间"),
    CanNotFindLabel (61169, "讲师标签不存在"),
    SexError (61170, "性别导入有误"),
    CooperativeVature (61171, "合作性质导入有误"),
    BusTimeRepetition(61178,"Different bus hours should not overlap"),

    EmployeeDisable (61172, "该员工已被禁用"),
    LecturerListRepeat(61173, "导入数据中存在相同的讲师电话，请确认后重新导入"),
    MemberListRepeat(61174, "导入数据中存在相同的员工编号，请确认后重新导入"),
    CheckSexFail(61175, "性别范围不正确"),
    CheckCooperationFail(61176, "合作性质范围不正确"),
    ErrorStartTime(61177, "Error start time"),
    ErrorEndTime(61179, "Error end time"),
    NumberLengthError(61180, "Error number length"),
    ExcelNoRows(61181, "Excel no rows!"),
    InvalidExcelTemplate(61182, "Invalid excel template"),
    LecturerLvevlRepeat(61183, "讲师级别重复"),
    ExcelRowsRepeatCode(61184, "导入表格存在相同的code"),
    ExcelRowsRepeatLevel(61185, "导入表格存在相同的讲师级别"),
    DegreeChinese(61186, "所填项必须为汉字"),
    TaskMemberIsNotNull(61187, "No details of the operation can be downloaded without data"),
    SettlementNameRepeat(61188, "结算单位名称重复"),
    SettlementContactsRepeat(61189, "结算单位往来字段重复"),
    StartTimeCanNotEqualEndTime(61190, "Start Time Can Not Equal End Time"),
    CanNotFindSettlementConfig(61191, "找不到该结算单位"),
    CanNotFindLecturer(61192, "找不到该讲师"),
    CanNotDeleteEvaluate(61193, "已有学员参加，无法被删除"),
    ResearchSubmitRepeatedly(61194, "Can not submit repeatedly"),
    NoSourceToDownLoad(61195, "没有可下载的资源"),
    MemberPhoneNumberNull(61196, "该账号手机号为空"),
    LecturerMemberIdRepeat(61197, "讲师已存在"),
    InvalidLectureDate(61198, "Invalid lecture date"),
    FillInTheLecturerType(61200,"FillInTheLecturerType"),
    FillOutTheNameOfThe(61201,"FillOutTheNameOfThe"),
    InputLecturerPhone(61202,"InputLecturerPhone"),
    NoBody(61203,"NoBody"),
    LecturerTypeRequired(61204, "Lecturer Type Required"),
    LecturerAttributeRequired(61205, "Lecturer Attribute Required"),
    LecturerCourseConfigRequired(61206, "Lecturer Course Config Required"),
    LecturerCourseConfigUniqe(61207, "Lecturer Course Config Uniqe"),
    LecturerCourseConfigParentNotFound(61208, "Lecturer Course Config Parent Not Found"),
    CourseAttributeRequired(61209, "Course Attribute Required"),
    TeachingActivityIdRequired(61210, "Teaching Activity Id Required"),
    TeachingActivityIdNotMatch(61211, "Teaching Activity Id Not Match"),
    TeachingActivityBaseError(61212, "Teaching Activity Base Error"),
    TeachingActivityCoefficientError(61213, "Teaching Activity Coefficient Error"),
    TeachingActivitySatisfiedError(61214, "Teaching Activity Satisfied Error"),
	CourseNameMatch(61203,"课程名称已存在"),
	CourseAttributeRemarkTooLong(61215, "Course Attribute Required"),
	CollectionProgrammeConfigMechanismNameRequired(61215, "Collection Programme Config Mechanism Name Required"),
	CollectionProgrammeConfigProgrammeNameRequired(61216, "Collection Programme Config Programme Name Required"),
	CollectionProgrammeConfigMechanismContactRequired(61217, "Collection Programme Config Mechanism Contact Required"),
	CollectionProgrammeConfigMechanismContactNumberRequired(61218, "Collection Programme Config Mechanism Contact Number Required"),
	CollectionProgrammeConfigMechanismContactEmailRequired(61219, "Collection Programme Config Mechanism Contact Email Required"),
	CourseArrtMatch(61301,"课程属性必填"),
	IsCourseArrtMatch(61302,"课程属性必须是集采课程"),
	CourseCompanyMatch(61303,"课程所属机构必填"),
	NotCourseCompanyMatch(61307,"请选择课程机构"),
	NotCoursePlanMatch(61340,"请选择课程所属方案"),
	CoursePlanMatch(61304,"课程所属方案必填"),
	CourseOrganizationMatch(61305,"归属部门编码必填"),
	CourseTypeMatch(61306,"课程分类必填"),
	NotCourseTypeMatch(61308,"请现选择课程分类"),
	NotCourseOrganizationMatch(61309,"非法归属部门编码"),
	NotCourseMemberMatch(61310,"请查看集采方案联系人"),
	NotCoursePhoneMatch(61311,"请查看集采方案联系人电话"),
	NotCourseEmailMatch(61312,"请查看集采方案联系人邮箱"),
	IsShareMatch(61313,"是否共享必填"),
	SelectIsShareMatch(61314,"请选择是否共享"),
	CourseTimeMatch(61315,"参考时长格式有误"),
	CourseNameRepeatMatch(61316,"集采课程名称已存在"),
	AdpetLectuercCourseMatch(61500,"该课程擅长讲师已存在"),
	LecturerAdeptCourseRepeat(61317, "讲师课程擅长记录重复"),
    CanNotFindAttribute(61318, "找不到该讲师属性"),
    CanNotFindCourseConfig(61319, "找不到该讲师专业序列"),
    LecturerTypeError(61320, "Lecturer Type Error"),
    LecturerLabelRequired(61321, "Lecturer Lable Required"),
    CourseTypePathLimit4(61322, "Course Type Max Depth Is Four"),
    LecturerAttributeNameAlreadyExists(61323, "Lecturer Attribute Name Already Exists"),
    LevelCodeAlreadyExists(61324, "Level Code Already Exists"),
    LevelNameAlreadyExists(61325, "Level Name Already Exists"),
    SequenceCodeAlreadyExists(61326, "Sequence Code Already Exists"),
	SequenceNameAlreadyExists(61327, "Sequence Name Already Exists"),
	FieldNameAlreadyExists(61328, "Field Name Already Exists"),
	CourseAttributeNameAlreadyExists(61329, "Course Attribute Name Already Exists"),
	CollectionProgrameNameAlreadyExists(61330, "Collection Programe Name Already Exists"),
	TeachingActivityLevelNameAlreadyExists(61331, "Teaching Activity Level Name Already Exists"),
	TeachingActivityLevelNameRequired(61332, "Teaching Activity Id Required"),
	AuthenticationTimeNotNULL(61333, "Authentication Time Not NULL"),
	OrganizationNameNotNULL(61334, "Organization name Not NULL"),
	InstitutionalContactsNotNULL(61335, "Institutional contacts Not NULL"),
	InstitutionalContactTelephoneNotNULL(61336, "Institutional Contact Telephone Not NULL"),
    MemberIdCardListRepeat(61337, "导入数据中存在相同的身份证号，请确认后重新导入"),
    NotImportsOtherClassifications(61338, "Imports are not allowed under other classifications"),
    NotImportsClassCode(61339, "培训班编码不可为空"),
    NotImportsClearingCorp(61341, "结算公司编码不可为空"),
    StudentCourseNameMatch(61342,"课程已存在"),
    OrganizationNameNotExists(61351, "组织编码错误"),
    CourseThemeTimeRepetition(61346,"主题时间重复"),
    OverlapTime(61344,"跨周时间过长"),
    InTimeClass(61345,"主题时间不在班级范围内"),
    LecturerIDCardMustFill(61347,"讲师身份证号码必填"),
    LecturerFieldMaxFive(61348,"讲师领域最多五个"),
    CheckRowTooMaxLong(61343, "导入数据过多不能超过10000条"),
    QuestionnaireMaxExport(61349, "此班级成员已超出200人，请联系管理员导出"),
    LecturerFieldMaxTen(61350,"讲师领域文字过长"),

    CourseUnitPriceFormat(61360,"集采方案课程单价格式错误"),
    CourseUnitPriceRange(61361,"集采方案课程单价不在规定范围内"),
    NotHaveLecturerReleaseAuthority(61362,"您没有该操作权限"),
	ActivityTypeIsNonexistent(61363,"教学教研活动类型不存在"),
	ActivityTimeFormatError(61364,"时间格式错误"),
	ActivityLevelIsMandatory(61365,"活动级别不存在"),
	CourseTypeIsMandatory(61366,"活动分类不存在"),
	ActivityRecordLengthFormatOrSizeError(61367,"活动记录时长格式或大小错误"),
	ActivityRecordIsConsideredMandatory(61368,"活动记录审核认定为必填"),
	ActivityRecordSatisfactionRequired(61369,"活动记录满意度必填"),
	ActivityRecordSatisfactionError(61370,"活动记录满意度数值错误"),
	LecturerIsNotInternalTrainer(61371,"讲师不是内训师无法导入教学教研活动"),
	CourseNameIsNonexistent(61372,"课程不存在"),
    CanNotFindConfig(61373, "找不到该单位"),
    TimeRangesFromZeroToNinety_Nine(61374, "时长必须在0~99之间"),
    NumberMustBeBetweenOneAndNine(61375, "人数必须在1~999之间"),
    AmountMustBeBetweenZeroAndOneHundredThousand(61376, "金额必须在0~10000之间"),
    SatisfactionMustBeBetweenZeroToOneHundred(61377, "满意度必须在0~100之间"),
    ExcessiveAmountOfExportedData(61378, "导出数据量超过2000条"),
    KeepTwoDecimalPlaces(61379, "最多保留两位小数"),

    CompletedQuestionnaire(61401, "已完成问卷无法修改"),
    InformalClassMember(61402, "非正式成员无法填写问卷"),
    IdentifyingCodeNotSend(61403, "验证码错误,请填写正确的手机验证码"),

    StudyTeamNotExist(61501, "该团队学习班不存在"),
    NotStudyTeamMember(61502, "您不是该团队人员"),
    NoPermissionToOperate(61503, "您无权执行此操作"),
    ConvertCaptainError(61504, "该人员不能转换为团长"),
    ActivityBeginTimeEditError(61505, "活动开始后不允许修改活动开始时间"),
    StudyTeamActivityNotExist(61506, "该活动不存在"),
    StudyTeamActivityTimeError(61507, "活动时间不得小于30分钟，不得跨天"),
    StudyTeamMemberAuditWaiting(61508, "报名审核中"),
    ActivityTaskBeginTimeDeleteError(61509, "活动开始后无法删除课程"),
    ActivityTaskNotExist(61510, "活动任务不存在"),
    ActivityBeginTimeDeleteError(61511, "活动开始后无法删除"),
    StudyTeamNotCaptainOrAssistant(61512, "非团队长或者助理，没有操作权限"),
    StudyTeamActivityNotEnd(61513, "学习活动未结束，不能确认学时"),
    TeamLeaderCanNotDelete(61514, "领学人以及团队长不可以删除"),
    StudyTeamConfirmedCreditsBeOverdue(61515, "活动结束已超过7天，无法再确认时长"),
    CanNotDeleteSelf(61517, "无法进行移除本人操作"),
    MemberIsExist(61518, "学员已存在"),
    StudyAchievementDeleteNotCaptainOrSelf(61519, "非本人、团队长或者助理，无权删除学习成果"),
    MemberNameNotMatchFullName(61520,"该员工编号和姓名不匹配"),
    MemberNameDuplicateInExcel(61521,"该员工编号在表格中重复"),
    MemberNotExist(61522, "该学员不存在"),
    StudyTeamActivityAttachmentOverSize(61523, "资料上传超过上限"),
    ActivityTaskIsExist(61524, "已经添加过该课程"),
    ElseMemberCanNotDelete(61525, "该成员不是其他成员，不可以删除"),
    PhotoNotExist(61526, "该相册照片不存在"),
    IfNotPleasTryAgain(61527, "您扫描的非本活动二维码，请重新扫码"),
    ResourceNotExist(61528, "该资源已不存在"),
    NoPermissionToCourse(61529, "您暂无添加此课程的权限"),
    UnableToFindCorrespondingOrg(61530,"根据memberID无法找到对应的组织信息,获取不到领学人学习时长!"),
    ActivityONCourse(61531,"活动没有课程"),
    NotQueryToLeaderMemberId(61532,"未查询到领学人的用户id"),
    TeamTypeIsWrong(61533,"类型错误"),
    TeamLeaderNotBelongClass(61534,"团队长超出班级归属范围内"),
    TeamOrgNotOverGrant(61535,"归属部门超出权限范围"),
    MemberCodeOrOrgCodeIsWrong(61536,"员工编号/组织编码错误"),
    SignConfirmedCreditsBeOverdue(61537, "活动结束已超过7天，无法签到"),
    TeamIsRepeat(61538,"团队学习班已存在"),
    SignTimeOutOfRang(61549, "仅能在活动开始前15分钟~活动结束后15分钟内扫码签到"),
    LengthExceedsLimit(61541,"长度超过限制"),
    NameExists(61544,"名称存在"),
    GradesLengthExceedsLimit(61540,"成绩长度超过限制"),
    DescLengthExceedsLimit(61541,"备注长度超过限制"),
    NameLengthExceedsLimit(61542,"名称长度超过限制"),
    NonClassMembers(61543,"非班级成员"),
    GradesExists(61545,"成绩存在"),
    LicensePlateNumberFormatError(61539,"车牌号格式错误"),
    SameTimeHaveAttendanceMachine(61548,"考勤签到时间重复"),
    TrainingNameExists(61550,"培训名称已存在"),
    ExceedingMaximumLength(62100,"采纳师资数量/电子课程采纳量格式错误，最多填写4位数字"),
    TheImplementationAlreadyAssociated(62101,"数据已关联,无法删除"),
    TrainMonthRequired(62102,"培训月份为必填"),
    TrainYearRequired(62103,"培训年份为必填"),
    TrainYearMustBe4Length(62104,"培训年份为4位整数"),
    CanNotFindPlanDepartment(62105, "找不到该策划部门"),
    CanNotFindImplementDepartment(62106, "找不到该实施部门"),
    BigTypeRequired(62107, "培训大类为必填"),
    MISCodeRequired(62108, "MIS编码为必填"),
    BigTypeNotExists(62110, "培训大类不存在"),
    SmallTypeNotExists(62111, "培训小类不存在"),
    BigTypeNotContainsThis(62112, "培训大类下不包含该培训小类"),
    KeyProjectRequired(62113, "是否重点为必填"),
    KeyProjectNotMatch(62114, "是否重点填写值不匹配"),
    ProjectNatureRequired(62115, "项目性质为必填"),
    ProjectNatureNotMatch(62116, "项目性质填写值不匹配"),
    Max4Length(62117, "最多填写4位数"),
    PlanNotExists(62118, "方案策划情况不存在"),
    ImplementNotExists(62121, "实施方式不存在"),
    ProgressDetailsNotExists(62119, "进度详情不存在"),
    PlanMemberNotExistsOrganization(62120, "填写人员与策划部门不对应"),
    ThereIsNoHalfDaySignInForTheClass(62122,"没有可修改的签到活动"),
    GetIMTokenError(62123,"获取IM群聊Token失败"),
    AddChatGroupMemberFailed(62124,"添加群聊成员失败"),
    DelChatGroupMemberFailed(62125,"删除群聊成员失败"),
    IMTokenError(62126,"IM群聊Token错误"),
    CreateChatGroupFailed(62127,"创建群聊失败"),
    ThePermissionVerificationOrTimeVerificationFailed(62128,"权限校验或时间校验失败，无法发送短信"),
    TheSpecifiedInformationIsNotSpecified(62129,"未填写指定内容"),
    NumberRangesFromOneToThreeThousand(62130, "必须在1~3000之间"),
    MemberCodeError(62131, "员工编号填写错误"),
    TraineeMemberIsEmpty(62132,"当前培训班下无正式成员"),
    TraineeSignCodeIsError(62133,"报名码不正确"),
    OnlyYesNoNrNull(62134,"格式错误，只能填写是、否或空值")
    ;



    private final int code;

    ErrorCode(int code, String desc) {
        this.code = code;
    }
    @Override
    public int getCode() {
        return code;
    }

    public static ErrorCode getByCode(int code) {
        for(ErrorCode ec : ErrorCode.values()) {
            if (ec.getCode() == code) {
                return ec;
            }
        }
        return null;
    }
}
