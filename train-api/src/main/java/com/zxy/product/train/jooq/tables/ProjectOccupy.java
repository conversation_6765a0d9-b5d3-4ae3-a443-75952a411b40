/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ProjectOccupyRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProjectOccupy extends TableImpl<ProjectOccupyRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_project_occupy</code>
     */
    public static final ProjectOccupy PROJECT_OCCUPY = new ProjectOccupy();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProjectOccupyRecord> getRecordType() {
        return ProjectOccupyRecord.class;
    }

    /**
     * The column <code>train.t_project_occupy.f_id</code>. 主键
     */
    public final TableField<ProjectOccupyRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_project_occupy.f_year</code>. 年份
     */
    public final TableField<ProjectOccupyRecord, Integer> YEAR = createField("f_year", org.jooq.impl.SQLDataType.INTEGER, this, "年份");

    /**
     * The column <code>train.t_project_occupy.f_month</code>. 月份
     */
    public final TableField<ProjectOccupyRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER, this, "月份");

    /**
     * The column <code>train.t_project_occupy.f_day</code>. 日期
     */
    public final TableField<ProjectOccupyRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER, this, "日期");

    /**
     * The column <code>train.t_project_occupy.f_date</code>. 日期时间戳
     */
    public final TableField<ProjectOccupyRecord, Long> DATE = createField("f_date", org.jooq.impl.SQLDataType.BIGINT, this, "日期时间戳");

    /**
     * The column <code>train.t_project_occupy.f_default</code>. 默认资源数
     */
    public final TableField<ProjectOccupyRecord, Integer> DEFAULT = createField("f_default", org.jooq.impl.SQLDataType.INTEGER, this, "默认资源数");

    /**
     * The column <code>train.t_project_occupy.f_available</code>. 可用资源数
     */
    public final TableField<ProjectOccupyRecord, Integer> AVAILABLE = createField("f_available", org.jooq.impl.SQLDataType.INTEGER, this, "可用资源数");

    /**
     * The column <code>train.t_project_occupy.f_create_time</code>. 创建时间
     */
    public final TableField<ProjectOccupyRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_project_occupy.f_create_member</code>. 创建人
     */
    public final TableField<ProjectOccupyRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_project_occupy.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ProjectOccupyRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_project_occupy</code> table reference
     */
    public ProjectOccupy() {
        this("t_project_occupy", null);
    }

    /**
     * Create an aliased <code>train.t_project_occupy</code> table reference
     */
    public ProjectOccupy(String alias) {
        this(alias, PROJECT_OCCUPY);
    }

    private ProjectOccupy(String alias, Table<ProjectOccupyRecord> aliased) {
        this(alias, aliased, null);
    }

    private ProjectOccupy(String alias, Table<ProjectOccupyRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ProjectOccupyRecord> getPrimaryKey() {
        return Keys.KEY_T_PROJECT_OCCUPY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ProjectOccupyRecord>> getKeys() {
        return Arrays.<UniqueKey<ProjectOccupyRecord>>asList(Keys.KEY_T_PROJECT_OCCUPY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProjectOccupy as(String alias) {
        return new ProjectOccupy(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ProjectOccupy rename(String name) {
        return new ProjectOccupy(name, null);
    }
}
