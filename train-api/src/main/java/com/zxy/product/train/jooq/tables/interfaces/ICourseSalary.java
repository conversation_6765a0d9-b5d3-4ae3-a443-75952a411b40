/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseSalary extends Serializable {

    /**
     * Setter for <code>train.t_course_salary.f_id</code>. 表id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_course_salary.f_id</code>. 表id
     */
    public String getId();

    /**
     * Setter for <code>train.t_course_salary.f_lecturer_card</code>. 讲师身份证
     */
    public void setLecturerCard(String value);

    /**
     * Getter for <code>train.t_course_salary.f_lecturer_card</code>. 讲师身份证
     */
    public String getLecturerCard();

    /**
     * Setter for <code>train.t_course_salary.f_lecturer_bank_name</code>. 讲师银行名称
     */
    public void setLecturerBankName(String value);

    /**
     * Getter for <code>train.t_course_salary.f_lecturer_bank_name</code>. 讲师银行名称
     */
    public String getLecturerBankName();

    /**
     * Setter for <code>train.t_course_salary.f_lecturer_bank_card</code>. 讲师银行卡号
     */
    public void setLecturerBankCard(String value);

    /**
     * Getter for <code>train.t_course_salary.f_lecturer_bank_card</code>. 讲师银行卡号
     */
    public String getLecturerBankCard();

    /**
     * Setter for <code>train.t_course_salary.f_lecture_id</code>. 讲师id
     */
    public void setLectureId(String value);

    /**
     * Getter for <code>train.t_course_salary.f_lecture_id</code>. 讲师id
     */
    public String getLectureId();

    /**
     * Setter for <code>train.t_course_salary.f_paid_pay</code>. 实付薪酬
     */
    public void setPaidPay(Double value);

    /**
     * Getter for <code>train.t_course_salary.f_paid_pay</code>. 实付薪酬
     */
    public Double getPaidPay();

    /**
     * Setter for <code>train.t_course_salary.f_pay</code>. 酬金
     */
    public void setPay(Double value);

    /**
     * Getter for <code>train.t_course_salary.f_pay</code>. 酬金
     */
    public Double getPay();

    /**
     * Setter for <code>train.t_course_salary.f_tax</code>. 税金
     */
    public void setTax(Double value);

    /**
     * Getter for <code>train.t_course_salary.f_tax</code>. 税金
     */
    public Double getTax();

    /**
     * Setter for <code>train.t_course_salary.f_course_id</code>. 课程id
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_course_salary.f_course_id</code>. 课程id
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_course_salary.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_course_salary.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_course_salary.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_course_salary.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_course_salary.f_service_charge</code>. 服务费比例
     */
    public void setServiceCharge(Double value);

    /**
     * Getter for <code>train.t_course_salary.f_service_charge</code>. 服务费比例
     */
    public Double getServiceCharge();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseSalary
     */
    public void from(ICourseSalary from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseSalary
     */
    public <E extends ICourseSalary> E into(E into);
}
