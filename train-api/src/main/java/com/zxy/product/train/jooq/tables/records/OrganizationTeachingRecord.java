/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.OrganizationTeaching;
import com.zxy.product.train.jooq.tables.interfaces.IOrganizationTeaching;

import javax.annotation.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 各单位授课记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationTeachingRecord extends UpdatableRecordImpl<OrganizationTeachingRecord> implements IOrganizationTeaching {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_organization_teaching.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_course_id</code>. 课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_course_id</code>. 课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_course_name</code>. 授课名称
     */
    @Override
    public void setCourseName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_course_name</code>. 授课名称
     */
    @Override
    public String getCourseName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    @Override
    public void setLecturerId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    @Override
    public String getLecturerId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    @Override
    public void setLecturerName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    @Override
    public String getLecturerName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    @Override
    public void setLecturerPost(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    @Override
    public String getLecturerPost() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_reference_dollars</code>. 参考课酬(元)
     */
    @Override
    public void setReferenceDollars(Double value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_reference_dollars</code>. 参考课酬(元)
     */
    @Override
    public Double getReferenceDollars() {
        return (Double) get(6);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_course_duration</code>. 课程时长
     */
    @Override
    public void setCourseDuration(Double value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_course_duration</code>. 课程时长
     */
    @Override
    public Double getCourseDuration() {
        return (Double) get(7);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_organization_name</code>. 主办部门
     */
    @Override
    public void setOrganizationName(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_organization_name</code>. 主办部门
     */
    @Override
    public String getOrganizationName() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_end_date</code>. 授课结束日期
     */
    @Override
    public void setEndDate(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_end_date</code>. 授课结束日期
     */
    @Override
    public Long getEndDate() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_start_date</code>. 授课起止日期
     */
    @Override
    public void setStartDate(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_start_date</code>. 授课起止日期
     */
    @Override
    public Long getStartDate() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_class_name</code>. 培训班名称
     */
    @Override
    public void setClassName(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_class_name</code>. 培训班名称
     */
    @Override
    public String getClassName() {
        return (String) get(11);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_class_member</code>. 培训班联系人
     */
    @Override
    public void setClassMember(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_class_member</code>. 培训班联系人
     */
    @Override
    public String getClassMember() {
        return (String) get(12);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_object</code>. 授课对象
     */
    @Override
    public void setObject(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_object</code>. 授课对象
     */
    @Override
    public String getObject() {
        return (String) get(13);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_attachment_id</code>. 附件id
     */
    @Override
    public void setAttachmentId(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_attachment_id</code>. 附件id
     */
    @Override
    public String getAttachmentId() {
        return (String) get(14);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_member_count</code>. 授课人数
     */
    @Override
    public void setMemberCount(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_member_count</code>. 授课人数
     */
    @Override
    public Integer getMemberCount() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_pay</code>. 实付(元)
     */
    @Override
    public void setPay(Double value) {
        set(16, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_pay</code>. 实付(元)
     */
    @Override
    public Double getPay() {
        return (Double) get(16);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_tax</code>. 税金(元)
     */
    @Override
    public void setTax(Double value) {
        set(17, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_tax</code>. 税金(元)
     */
    @Override
    public Double getTax() {
        return (Double) get(17);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_remuneration</code>. 酬金
     */
    @Override
    public void setRemuneration(Double value) {
        set(18, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_remuneration</code>. 酬金
     */
    @Override
    public Double getRemuneration() {
        return (Double) get(18);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    @Override
    public void setSatisfiedDegree(Double value) {
        set(19, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    @Override
    public Double getSatisfiedDegree() {
        return (Double) get(19);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    @Override
    public void setSatisfiedEvaluate(String value) {
        set(20, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_satisfied_evaluate</code>. 满意度评价
     */
    @Override
    public String getSatisfiedEvaluate() {
        return (String) get(20);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_attachment_name</code>. 附件名称
     */
    @Override
    public void setAttachmentName(String value) {
        set(21, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_attachment_name</code>. 附件名称
     */
    @Override
    public String getAttachmentName() {
        return (String) get(21);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_teaching_type</code>. 教学教研类型
     */
    @Override
    public void setTeachingType(String value) {
        set(22, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_teaching_type</code>. 教学教研类型
     */
    @Override
    public String getTeachingType() {
        return (String) get(22);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(23, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(23);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_approval_time</code>. 审核时间
     */
    @Override
    public void setApprovalTime(Long value) {
        set(24, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_approval_time</code>. 审核时间
     */
    @Override
    public Long getApprovalTime() {
        return (Long) get(24);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    @Override
    public void setApprovalStatus(Integer value) {
        set(25, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_approval_status</code>. 是否通过0：待审核1：通过2：拒绝
     */
    @Override
    public Integer getApprovalStatus() {
        return (Integer) get(25);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_organization_id</code>. 归属部门
     */
    @Override
    public void setOrganizationId(String value) {
        set(26, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_organization_id</code>. 归属部门
     */
    @Override
    public String getOrganizationId() {
        return (String) get(26);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_create_member</code>. 创建ID
     */
    @Override
    public void setCreateMember(String value) {
        set(27, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_create_member</code>. 创建ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(27);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_approval_member</code>. 审核人ID
     */
    @Override
    public void setApprovalMember(String value) {
        set(28, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_approval_member</code>. 审核人ID
     */
    @Override
    public String getApprovalMember() {
        return (String) get(28);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_remark</code>. 备注
     */
    @Override
    public void setRemark(String value) {
        set(29, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_remark</code>. 备注
     */
    @Override
    public String getRemark() {
        return (String) get(29);
    }

    /**
     * Setter for <code>train.t_organization_teaching.f_source</code>. 0：讲师自己申请 1：集采登记
     */
    @Override
    public void setSource(Integer value) {
        set(30, value);
    }

    /**
     * Getter for <code>train.t_organization_teaching.f_source</code>. 0：讲师自己申请 1：集采登记
     */
    @Override
    public Integer getSource() {
        return (Integer) get(30);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOrganizationTeaching from) {
        setId(from.getId());
        setCourseId(from.getCourseId());
        setCourseName(from.getCourseName());
        setLecturerId(from.getLecturerId());
        setLecturerName(from.getLecturerName());
        setLecturerPost(from.getLecturerPost());
        setReferenceDollars(from.getReferenceDollars());
        setCourseDuration(from.getCourseDuration());
        setOrganizationName(from.getOrganizationName());
        setEndDate(from.getEndDate());
        setStartDate(from.getStartDate());
        setClassName(from.getClassName());
        setClassMember(from.getClassMember());
        setObject(from.getObject());
        setAttachmentId(from.getAttachmentId());
        setMemberCount(from.getMemberCount());
        setPay(from.getPay());
        setTax(from.getTax());
        setRemuneration(from.getRemuneration());
        setSatisfiedDegree(from.getSatisfiedDegree());
        setSatisfiedEvaluate(from.getSatisfiedEvaluate());
        setAttachmentName(from.getAttachmentName());
        setTeachingType(from.getTeachingType());
        setCreateTime(from.getCreateTime());
        setApprovalTime(from.getApprovalTime());
        setApprovalStatus(from.getApprovalStatus());
        setOrganizationId(from.getOrganizationId());
        setCreateMember(from.getCreateMember());
        setApprovalMember(from.getApprovalMember());
        setRemark(from.getRemark());
        setSource(from.getSource());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOrganizationTeaching> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OrganizationTeachingRecord
     */
    public OrganizationTeachingRecord() {
        super(OrganizationTeaching.ORGANIZATION_TEACHING);
    }

    /**
     * Create a detached, initialised OrganizationTeachingRecord
     */
    public OrganizationTeachingRecord(String id, String courseId, String courseName, String lecturerId, String lecturerName, String lecturerPost, Double referenceDollars, Double courseDuration, String organizationName, Long endDate, Long startDate, String className, String classMember, String object, String attachmentId, Integer memberCount, Double pay, Double tax, Double remuneration, Double satisfiedDegree, String satisfiedEvaluate, String attachmentName, String teachingType, Long createTime, Long approvalTime, Integer approvalStatus, String organizationId, String createMember, String approvalMember, String remark, Integer source) {
        super(OrganizationTeaching.ORGANIZATION_TEACHING);

        set(0, id);
        set(1, courseId);
        set(2, courseName);
        set(3, lecturerId);
        set(4, lecturerName);
        set(5, lecturerPost);
        set(6, referenceDollars);
        set(7, courseDuration);
        set(8, organizationName);
        set(9, endDate);
        set(10, startDate);
        set(11, className);
        set(12, classMember);
        set(13, object);
        set(14, attachmentId);
        set(15, memberCount);
        set(16, pay);
        set(17, tax);
        set(18, remuneration);
        set(19, satisfiedDegree);
        set(20, satisfiedEvaluate);
        set(21, attachmentName);
        set(22, teachingType);
        set(23, createTime);
        set(24, approvalTime);
        set(25, approvalStatus);
        set(26, organizationId);
        set(27, createMember);
        set(28, approvalMember);
        set(29, remark);
        set(30, source);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.OrganizationTeachingEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.OrganizationTeachingEntity pojo = (com.zxy.product.train.jooq.tables.pojos.OrganizationTeachingEntity)source;
        pojo.into(this);
        return true;
    }
}
