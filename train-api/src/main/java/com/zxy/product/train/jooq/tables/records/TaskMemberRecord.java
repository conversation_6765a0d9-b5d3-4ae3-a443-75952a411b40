/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.TaskMember;
import com.zxy.product.train.jooq.tables.interfaces.ITaskMember;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户提交作业详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TaskMemberRecord extends UpdatableRecordImpl<TaskMemberRecord> implements Record9<String, String, Long, String, String, String, String, String, Integer>, ITaskMember {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_task_member.f_id</code>. 表ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_id</code>. 表ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_task_member.f_member_id</code>. 提交人
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_member_id</code>. 提交人
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_task_member.f_create_time</code>. 提交作业时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_create_time</code>. 提交作业时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>train.t_task_member.f_task_id</code>. 作业ID
     */
    @Override
    public void setTaskId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_task_id</code>. 作业ID
     */
    @Override
    public String getTaskId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_task_member.f_name</code>. 附件名称
     */
    @Override
    public void setName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_name</code>. 附件名称
     */
    @Override
    public String getName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_task_member.f_description</code>. 附件描述
     */
    @Override
    public void setDescription(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_description</code>. 附件描述
     */
    @Override
    public String getDescription() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_task_member.f_attachment_type</code>. 附件类型
     */
    @Override
    public void setAttachmentType(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_attachment_type</code>. 附件类型
     */
    @Override
    public String getAttachmentType() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_task_member.f_attachment_id</code>. 附件ID
     */
    @Override
    public void setAttachmentId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_attachment_id</code>. 附件ID
     */
    @Override
    public String getAttachmentId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_task_member.f_state</code>. 状态 1未提交 2审核中  3合格  4不合格
     */
    @Override
    public void setState(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_task_member.f_state</code>. 状态 1未提交 2审核中  3合格  4不合格
     */
    @Override
    public Integer getState() {
        return (Integer) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Long, String, String, String, String, String, Integer> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Long, String, String, String, String, String, Integer> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return TaskMember.TASK_MEMBER.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return TaskMember.TASK_MEMBER.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field3() {
        return TaskMember.TASK_MEMBER.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return TaskMember.TASK_MEMBER.TASK_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return TaskMember.TASK_MEMBER.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return TaskMember.TASK_MEMBER.DESCRIPTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return TaskMember.TASK_MEMBER.ATTACHMENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return TaskMember.TASK_MEMBER.ATTACHMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return TaskMember.TASK_MEMBER.STATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value3() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getTaskId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getDescription();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getAttachmentType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getAttachmentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getState();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value3(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value4(String value) {
        setTaskId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value5(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value6(String value) {
        setDescription(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value7(String value) {
        setAttachmentType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value8(String value) {
        setAttachmentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord value9(Integer value) {
        setState(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TaskMemberRecord values(String value1, String value2, Long value3, String value4, String value5, String value6, String value7, String value8, Integer value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ITaskMember from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCreateTime(from.getCreateTime());
        setTaskId(from.getTaskId());
        setName(from.getName());
        setDescription(from.getDescription());
        setAttachmentType(from.getAttachmentType());
        setAttachmentId(from.getAttachmentId());
        setState(from.getState());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ITaskMember> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached TaskMemberRecord
     */
    public TaskMemberRecord() {
        super(TaskMember.TASK_MEMBER);
    }

    /**
     * Create a detached, initialised TaskMemberRecord
     */
    public TaskMemberRecord(String id, String memberId, Long createTime, String taskId, String name, String description, String attachmentType, String attachmentId, Integer state) {
        super(TaskMember.TASK_MEMBER);

        set(0, id);
        set(1, memberId);
        set(2, createTime);
        set(3, taskId);
        set(4, name);
        set(5, description);
        set(6, attachmentType);
        set(7, attachmentId);
        set(8, state);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.TaskMemberEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.TaskMemberEntity pojo = (com.zxy.product.train.jooq.tables.pojos.TaskMemberEntity)source;
        pojo.into(this);
        return true;
    }
}
