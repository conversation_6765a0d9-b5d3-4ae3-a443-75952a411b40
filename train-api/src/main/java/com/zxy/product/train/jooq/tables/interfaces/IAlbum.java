/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAlbum extends Serializable {

    /**
     * Setter for <code>train.t_album.f_id</code>. 表ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_album.f_id</code>. 表ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_album.f_name</code>. 照片名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_album.f_name</code>. 照片名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_album.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_album.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_album.f_attachment_id</code>. attachment_id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_album.f_attachment_id</code>. attachment_id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_album.f_delete_flag</code>. 删除状态 0未删除（默认） 1已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>train.t_album.f_delete_flag</code>. 删除状态 0未删除（默认） 1已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>train.t_album.f_author</code>. 作者
     */
    public void setAuthor(String value);

    /**
     * Getter for <code>train.t_album.f_author</code>. 作者
     */
    public String getAuthor();

    /**
     * Setter for <code>train.t_album.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_album.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_album.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_album.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_album.f_thumbnail_id</code>. 缩略图id
     */
    public void setThumbnailId(String value);

    /**
     * Getter for <code>train.t_album.f_thumbnail_id</code>. 缩略图id
     */
    public String getThumbnailId();

    /**
     * Setter for <code>train.t_album.f_path</code>. 图片地址
     */
    public void setPath(String value);

    /**
     * Getter for <code>train.t_album.f_path</code>. 图片地址
     */
    public String getPath();

    /**
     * Setter for <code>train.t_album.f_thumbnail_path</code>. 缩略图地址
     */
    public void setThumbnailPath(String value);

    /**
     * Getter for <code>train.t_album.f_thumbnail_path</code>. 缩略图地址
     */
    public String getThumbnailPath();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAlbum
     */
    public void from(IAlbum from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAlbum
     */
    public <E extends IAlbum> E into(E into);
}
