/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILevel extends Serializable {

    /**
     * Setter for <code>train.t_level.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_level.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_level.f_name</code>. 名称 
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_level.f_name</code>. 名称 
     */
    public String getName();

    /**
     * Setter for <code>train.t_level.f_upper_level</code>. 上一级别
     */
    public void setUpperLevel(String value);

    /**
     * Getter for <code>train.t_level.f_upper_level</code>. 上一级别
     */
    public String getUpperLevel();

    /**
     * Setter for <code>train.t_level.f_organization_id</code>. 所属组织
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_level.f_organization_id</code>. 所属组织
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_level.f_code</code>. 编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_level.f_code</code>. 编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_level.f_type</code>. 类型 0 内部讲师  1 外部讲师
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_level.f_type</code>. 类型 0 内部讲师  1 外部讲师
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_level.f_teach_cost</code>. 课时费
     */
    public void setTeachCost(Double value);

    /**
     * Getter for <code>train.t_level.f_teach_cost</code>. 课时费
     */
    public Double getTeachCost();

    /**
     * Setter for <code>train.t_level.f_teach_avg</code>. 授课平均满意度
     */
    public void setTeachAvg(Double value);

    /**
     * Getter for <code>train.t_level.f_teach_avg</code>. 授课平均满意度
     */
    public Double getTeachAvg();

    /**
     * Setter for <code>train.t_level.f_teach_count</code>. 授课课时
     */
    public void setTeachCount(Double value);

    /**
     * Getter for <code>train.t_level.f_teach_count</code>. 授课课时
     */
    public Double getTeachCount();

    /**
     * Setter for <code>train.t_level.f_dev_count</code>. 课时开发数
     */
    public void setDevCount(Integer value);

    /**
     * Getter for <code>train.t_level.f_dev_count</code>. 课时开发数
     */
    public Integer getDevCount();

    /**
     * Setter for <code>train.t_level.f_description</code>. 描述
     */
    public void setDescription(String value);

    /**
     * Getter for <code>train.t_level.f_description</code>. 描述
     */
    public String getDescription();

    /**
     * Setter for <code>train.t_level.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_level.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_level.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_level.f_order</code>. 排序
     */
    public Integer getOrder();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILevel
     */
    public void from(ILevel from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILevel
     */
    public <E extends ILevel> E into(E into);
}
