package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassstaffClassEntity;

public class ClassstaffClass extends ClassstaffClassEntity {
	private static final long serialVersionUID = 2366155791115278193L;

	public static final int DELETE_FLASE = 0; 				// 删除状态：未删除
	public static final int DELETE_TRUE = 1; 				// 删除状态：已删除
	public static final int TYPE_STAFF = 0;					// 班务类型：0普通班务人员
	public static final int TYPE_MASTER = 1;				// 班务类型：1班主任
	public static final int MAX_COUNT = 15; 				// 单个班级最多拥有的班务人数
	public static final int DEFAULT_SORT = 1; 		   	// 默认排序
	public static final int MASTER_SORT = 1; 		   		// 班主任排序
	public static final String DEFAULT_CALLNAME = "教学助理"; 	// 默认称呼
	public static final String MASTER_CALLNAME = "班主任";		// 班主任称呼
	public static final int ADD = 1;//新增
	public static final int UPDATE = 0;//修改
	public static final int DELETE = -1;//删除

	private Member member;
	private String classTeacher;
	private String organizationName;//部门
	private String companyName;//公司
	private Integer organizationLevel;
	private String path;
	private String phoneNumber;
	private String memberName;


	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getClassTeacher() {
		return classTeacher;
	}

	public void setClassTeacher(String classTeacher) {
		this.classTeacher = classTeacher;
	}

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getOrganizationLevel() {
        return organizationLevel;
    }

    public void setOrganizationLevel(Integer organizationLevel) {
        this.organizationLevel = organizationLevel;
    }

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
}
