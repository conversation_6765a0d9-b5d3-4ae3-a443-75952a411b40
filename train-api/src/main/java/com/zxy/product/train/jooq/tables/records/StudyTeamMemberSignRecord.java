/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamMemberSign;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamMemberSign;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学习团队成员签到表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamMemberSignRecord extends UpdatableRecordImpl<StudyTeamMemberSignRecord> implements Record9<String, String, String, Long, Long, Integer, Integer, String, Long>, IStudyTeamMemberSign {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_member_sign.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_activity_id</code>. 团队活动id
     */
    @Override
    public void setActivityId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_activity_id</code>. 团队活动id
     */
    @Override
    public String getActivityId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_team_member_id</code>. 团队成员id
     */
    @Override
    public void setTeamMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_team_member_id</code>. 团队成员id
     */
    @Override
    public String getTeamMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_sign_in_time</code>. 签到时间
     */
    @Override
    public void setSignInTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_sign_in_time</code>. 签到时间
     */
    @Override
    public Long getSignInTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_sign_off_time</code>. 签退时间
     */
    @Override
    public void setSignOffTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_sign_off_time</code>. 签退时间
     */
    @Override
    public Long getSignOffTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_status</code>. 状态 1-正常 2-迟到 3-早退 4-迟到&amp;早退
     */
    @Override
    public void setStatus(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_status</code>. 状态 1-正常 2-迟到 3-早退 4-迟到&amp;早退
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_confirm_status</code>. 确认时长状态 0-未确认 1-确认中 2-已确认
     */
    @Override
    public void setConfirmStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_confirm_status</code>. 确认时长状态 0-未确认 1-确认中 2-已确认
     */
    @Override
    public Integer getConfirmStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_remark</code>. 备注
     */
    @Override
    public void setRemark(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_remark</code>. 备注
     */
    @Override
    public String getRemark() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, Long, Long, Integer, Integer, String, Long> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, Long, Long, Integer, Integer, String, Long> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.ACTIVITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.TEAM_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.SIGN_IN_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.SIGN_OFF_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.CONFIRM_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.REMARK;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getActivityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getTeamMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getSignInTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getSignOffTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getConfirmStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getRemark();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value2(String value) {
        setActivityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value3(String value) {
        setTeamMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value4(Long value) {
        setSignInTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value5(Long value) {
        setSignOffTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value6(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value7(Integer value) {
        setConfirmStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value8(String value) {
        setRemark(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord value9(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignRecord values(String value1, String value2, String value3, Long value4, Long value5, Integer value6, Integer value7, String value8, Long value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamMemberSign from) {
        setId(from.getId());
        setActivityId(from.getActivityId());
        setTeamMemberId(from.getTeamMemberId());
        setSignInTime(from.getSignInTime());
        setSignOffTime(from.getSignOffTime());
        setStatus(from.getStatus());
        setConfirmStatus(from.getConfirmStatus());
        setRemark(from.getRemark());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamMemberSign> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamMemberSignRecord
     */
    public StudyTeamMemberSignRecord() {
        super(StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN);
    }

    /**
     * Create a detached, initialised StudyTeamMemberSignRecord
     */
    public StudyTeamMemberSignRecord(String id, String activityId, String teamMemberId, Long signInTime, Long signOffTime, Integer status, Integer confirmStatus, String remark, Long createTime) {
        super(StudyTeamMemberSign.STUDY_TEAM_MEMBER_SIGN);

        set(0, id);
        set(1, activityId);
        set(2, teamMemberId);
        set(3, signInTime);
        set(4, signOffTime);
        set(5, status);
        set(6, confirmStatus);
        set(7, remark);
        set(8, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberSignEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberSignEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberSignEntity)source;
        pojo.into(this);
        return true;
    }
}
