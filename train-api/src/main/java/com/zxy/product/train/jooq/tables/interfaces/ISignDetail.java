/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 签到详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISignDetail extends Serializable {

    /**
     * Setter for <code>train.t_sign_detail.f_id</code>. 表ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_sign_detail.f_id</code>. 表ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_sign_detail.f_member_id</code>. 员工ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_sign_detail.f_member_id</code>. 员工ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_sign_detail.f_sign_date</code>. 签到时间
     */
    public void setSignDate(Long value);

    /**
     * Getter for <code>train.t_sign_detail.f_sign_date</code>. 签到时间
     */
    public Long getSignDate();

    /**
     * Setter for <code>train.t_sign_detail.f_state</code>. 状态 0全部 1正常 2迟到 3未签到 4请假
     */
    public void setState(Integer value);

    /**
     * Getter for <code>train.t_sign_detail.f_state</code>. 状态 0全部 1正常 2迟到 3未签到 4请假
     */
    public Integer getState();

    /**
     * Setter for <code>train.t_sign_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_sign_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_sign_detail.f_sign_id</code>. 签到表ID
     */
    public void setSignId(String value);

    /**
     * Getter for <code>train.t_sign_detail.f_sign_id</code>. 签到表ID
     */
    public String getSignId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISignDetail
     */
    public void from(ISignDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISignDetail
     */
    public <E extends ISignDetail> E into(E into);
}
