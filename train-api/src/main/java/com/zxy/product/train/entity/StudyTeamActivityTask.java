package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityTaskEntity;

/**
 * 学习团队活动任务实体类
 *
 * <AUTHOR>
 * @date 2021/4/14/0014 15:50
 */
public class StudyTeamActivityTask extends StudyTeamActivityTaskEntity {
    private static final long serialVersionUID = 5697151979315203074L;


    /**
     * 业务类型
     * 1.课程
     */
    public static final int BUSINESS_TYPE_COURSE = 1;

    private String courseName;
    private String courseTime;
    private Integer courseStatus;
    private String coverPath;
    private String courseId;


    /**
     * 课程编码
     * @return
     */
    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    /**
     * 封面路径
     * @return
     */
    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    /**
     * 课程名称
     *
     * @return
     */
    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    /**
     * 课程总时长
     *
     * @return
     */
    public String getCourseTime() {
        return courseTime;
    }

    public void setCourseTime(String courseTime) {
        this.courseTime = courseTime;
    }

    /**
     * 课程学习状态
     *
     * @return
     */
    public Integer getCourseStatus() {
        return courseStatus;
    }

    public void setCourseStatus(Integer courseStatus) {
        this.courseStatus = courseStatus;
    }
}
