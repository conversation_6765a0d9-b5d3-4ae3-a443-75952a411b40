/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 课程目录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseCategory extends Serializable {

    /**
     * Setter for <code>train.t_course_category.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_course_category.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_course_category.f_name</code>. 名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_course_category.f_name</code>. 名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_course_category.f_code</code>. 编号
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_course_category.f_code</code>. 编号
     */
    public String getCode();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseCategory
     */
    public void from(ICourseCategory from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseCategory
     */
    public <E extends ICourseCategory> E into(E into);
}
