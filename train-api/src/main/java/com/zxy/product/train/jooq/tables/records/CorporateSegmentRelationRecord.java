/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.CorporateSegmentRelation;
import com.zxy.product.train.jooq.tables.interfaces.ICorporateSegmentRelation;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 公司段对应关系
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CorporateSegmentRelationRecord extends UpdatableRecordImpl<CorporateSegmentRelationRecord> implements Record5<String, String, String, String, Integer>, ICorporateSegmentRelation {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_organization_name</code>. 公司名称
     */
    @Override
    public void setOrganizationName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_organization_name</code>. 公司名称
     */
    @Override
    public String getOrganizationName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_intercourse_section</code>. 往来段
     */
    @Override
    public void setIntercourseSection(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_intercourse_section</code>. 往来段
     */
    @Override
    public String getIntercourseSection() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_organization_id</code>. 机构ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_organization_id</code>. 机构ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_order</code>.
     */
    @Override
    public void setOrder(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_order</code>.
     */
    @Override
    public Integer getOrder() {
        return (Integer) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, String, Integer> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, String, Integer> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION.ORGANIZATION_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION.INTERCOURSE_SECTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION.ORDER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getOrganizationName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getIntercourseSection();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getOrder();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorporateSegmentRelationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorporateSegmentRelationRecord value2(String value) {
        setOrganizationName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorporateSegmentRelationRecord value3(String value) {
        setIntercourseSection(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorporateSegmentRelationRecord value4(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorporateSegmentRelationRecord value5(Integer value) {
        setOrder(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CorporateSegmentRelationRecord values(String value1, String value2, String value3, String value4, Integer value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICorporateSegmentRelation from) {
        setId(from.getId());
        setOrganizationName(from.getOrganizationName());
        setIntercourseSection(from.getIntercourseSection());
        setOrganizationId(from.getOrganizationId());
        setOrder(from.getOrder());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICorporateSegmentRelation> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CorporateSegmentRelationRecord
     */
    public CorporateSegmentRelationRecord() {
        super(CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION);
    }

    /**
     * Create a detached, initialised CorporateSegmentRelationRecord
     */
    public CorporateSegmentRelationRecord(String id, String organizationName, String intercourseSection, String organizationId, Integer order) {
        super(CorporateSegmentRelation.CORPORATE_SEGMENT_RELATION);

        set(0, id);
        set(1, organizationName);
        set(2, intercourseSection);
        set(3, organizationId);
        set(4, order);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.CorporateSegmentRelationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.CorporateSegmentRelationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.CorporateSegmentRelationEntity)source;
        pojo.into(this);
        return true;
    }
}
