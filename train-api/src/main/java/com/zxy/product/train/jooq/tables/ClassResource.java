/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassResourceRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassResource extends TableImpl<ClassResourceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_resource</code>
     */
    public static final ClassResource CLASS_RESOURCE = new ClassResource();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassResourceRecord> getRecordType() {
        return ClassResourceRecord.class;
    }

    /**
     * The column <code>train.t_class_resource.f_id</code>. 表id
     */
    public final TableField<ClassResourceRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_resource.f_class_id</code>. 班级ID
     */
    public final TableField<ClassResourceRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_resource.f_rest_room</code>. 客房
     */
    public final TableField<ClassResourceRecord, String> REST_ROOM = createField("f_rest_room", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "客房");

    /**
     * The column <code>train.t_class_resource.f_dining_room</code>. 餐厅
     */
    public final TableField<ClassResourceRecord, String> DINING_ROOM = createField("f_dining_room", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "餐厅");

    /**
     * The column <code>train.t_class_resource.f_classroom</code>. 教室
     */
    public final TableField<ClassResourceRecord, String> CLASSROOM = createField("f_classroom", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "教室");

    /**
     * The column <code>train.t_class_resource.f_create_time</code>. 创建时间
     */
    public final TableField<ClassResourceRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_resource.f_create_member</code>. 创建人
     */
    public final TableField<ClassResourceRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_resource.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassResourceRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_class_resource</code> table reference
     */
    public ClassResource() {
        this("t_class_resource", null);
    }

    /**
     * Create an aliased <code>train.t_class_resource</code> table reference
     */
    public ClassResource(String alias) {
        this(alias, CLASS_RESOURCE);
    }

    private ClassResource(String alias, Table<ClassResourceRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassResource(String alias, Table<ClassResourceRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassResourceRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_RESOURCE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassResourceRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassResourceRecord>>asList(Keys.KEY_T_CLASS_RESOURCE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResource as(String alias) {
        return new ClassResource(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassResource rename(String name) {
        return new ClassResource(name, null);
    }
}
