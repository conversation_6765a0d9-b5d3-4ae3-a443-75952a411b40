package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.LecturerAttributeEntity;

public class LecturerAttribute extends LecturerAttributeEntity{

	private static final long serialVersionUID = 1868046825233943925L;
	
	private String fullName;

	/** 内部讲师标识 */
	public static final Integer INSIDE_TYPE_ID = 0;
	/** 外部讲师标识 */
	public static final Integer EXTERNAL_TYPE_ID = 1;

	/** 内训师 */
	public static final String ATTRIBUTE_INTERNAL_TRAINER = "1";
	/** 普通内部讲师 */
	public static final String ATTRIBUTE_INTERNAL_GENERAL = "2";
	/** 外部机构讲师 */
	public static final String ATTRIBUTE_EXTERNAL_INSTITUTIONS = "3";
	/** 外部个人讲师 */
	public static final String ATTRIBUTE_EXTERNAL_PERSONAL = "4";

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

}
