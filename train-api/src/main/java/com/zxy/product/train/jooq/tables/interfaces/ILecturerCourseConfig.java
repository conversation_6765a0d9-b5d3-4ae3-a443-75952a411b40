/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 讲师专业序列/讲师课程库课程分类配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILecturerCourseConfig extends Serializable {

    /**
     * Setter for <code>train.t_lecturer_course_config.f_id</code>. 系统ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_id</code>. 系统ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_name</code>. 专业序列/课程分类名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_name</code>. 专业序列/课程分类名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_parent_id</code>. 上级序列ID【顶级为null】
     */
    public void setParentId(String value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_parent_id</code>. 上级序列ID【顶级为null】
     */
    public String getParentId();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_type</code>. 类型【0：讲师专业序列；1：课程分类】
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_type</code>. 类型【0：讲师专业序列；1：课程分类】
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_code</code>. 课程分类/专业序列的目录编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_code</code>. 课程分类/专业序列的目录编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_create_member_id</code>. 创建人ID
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_create_member_id</code>. 创建人ID
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_path</code>. 保存向上的所有id
     */
    public void setPath(String value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_path</code>. 保存向上的所有id
     */
    public String getPath();

    /**
     * Setter for <code>train.t_lecturer_course_config.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_lecturer_course_config.f_sort</code>. 排序
     */
    public Integer getSort();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILecturerCourseConfig
     */
    public void from(ILecturerCourseConfig from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILecturerCourseConfig
     */
    public <E extends ILecturerCourseConfig> E into(E into);
}
