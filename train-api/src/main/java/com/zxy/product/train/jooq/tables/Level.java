/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LevelRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Level extends TableImpl<LevelRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_level</code>
     */
    public static final Level LEVEL = new Level();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LevelRecord> getRecordType() {
        return LevelRecord.class;
    }

    /**
     * The column <code>train.t_level.f_id</code>. ID
     */
    public final TableField<LevelRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_level.f_name</code>. 名称 
     */
    public final TableField<LevelRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(20), this, "名称 ");

    /**
     * The column <code>train.t_level.f_upper_level</code>. 上一级别
     */
    public final TableField<LevelRecord, String> UPPER_LEVEL = createField("f_upper_level", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "上一级别");

    /**
     * The column <code>train.t_level.f_organization_id</code>. 所属组织
     */
    public final TableField<LevelRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所属组织");

    /**
     * The column <code>train.t_level.f_code</code>. 编码
     */
    public final TableField<LevelRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "编码");

    /**
     * The column <code>train.t_level.f_type</code>. 类型 0 内部讲师  1 外部讲师
     */
    public final TableField<LevelRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类型 0 内部讲师  1 外部讲师");

    /**
     * The column <code>train.t_level.f_teach_cost</code>. 课时费
     */
    public final TableField<LevelRecord, Double> TEACH_COST = createField("f_teach_cost", org.jooq.impl.SQLDataType.DOUBLE, this, "课时费");

    /**
     * The column <code>train.t_level.f_teach_avg</code>. 授课平均满意度
     */
    public final TableField<LevelRecord, Double> TEACH_AVG = createField("f_teach_avg", org.jooq.impl.SQLDataType.FLOAT, this, "授课平均满意度");

    /**
     * The column <code>train.t_level.f_teach_count</code>. 授课课时
     */
    public final TableField<LevelRecord, Double> TEACH_COUNT = createField("f_teach_count", org.jooq.impl.SQLDataType.DOUBLE, this, "授课课时");

    /**
     * The column <code>train.t_level.f_dev_count</code>. 课时开发数
     */
    public final TableField<LevelRecord, Integer> DEV_COUNT = createField("f_dev_count", org.jooq.impl.SQLDataType.INTEGER, this, "课时开发数");

    /**
     * The column <code>train.t_level.f_description</code>. 描述
     */
    public final TableField<LevelRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(1000), this, "描述");

    /**
     * The column <code>train.t_level.f_create_time</code>. 创建时间
     */
    public final TableField<LevelRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_level.f_order</code>. 排序
     */
    public final TableField<LevelRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * Create a <code>train.t_level</code> table reference
     */
    public Level() {
        this("t_level", null);
    }

    /**
     * Create an aliased <code>train.t_level</code> table reference
     */
    public Level(String alias) {
        this(alias, LEVEL);
    }

    private Level(String alias, Table<LevelRecord> aliased) {
        this(alias, aliased, null);
    }

    private Level(String alias, Table<LevelRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LevelRecord> getPrimaryKey() {
        return Keys.KEY_T_LEVEL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LevelRecord>> getKeys() {
        return Arrays.<UniqueKey<LevelRecord>>asList(Keys.KEY_T_LEVEL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Level as(String alias) {
        return new Level(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Level rename(String name) {
        return new Level(name, null);
    }
}
