package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.TraineeEntity;

public class Trainee extends TraineeEntity {
	/**
	 *
	 */
	private static final long serialVersionUID = -6640107682235760596L;

	public static final int TRAINEE_TYPE_FORMAL=0; // 学员类型：0正式
	public static final int TRAINEE_TYPE_INFORMAL=1; // 学员类型：1非正式

	public static final int AUDIT_WAIT = 0;	//审核状态：待审核
	public static final int AUDIT_AGREE = 1;//审核状态：同意
	public static final int AUDIT_REFUSE = 2;//审核状态：拒绝
	public static final int AUDIT_REFUSE_LIMIT = 8;//审核状态：配额满拒绝
	public static final Integer FINANCE_ONE = 1;//财务纠偏添加状态
	public static final Integer FINANCE_TWO = 2;//财务纠偏删除状态
	public static final int CONSTANT_ONE = 1;//常量
	public static final int CONSTANT_ZERO = 0; //常量
	public static final int DELETE_FLASE=0;	//删除状态：未删除
	public static final int DELETE_TRUE=1;	//删除状态：已删除
	public static final int SOURCE_SIGNUP=0; // 成员来源：报名
    public static final int SOURCE_MANUAL=1; // 成员来源：手工添加
    public static final String CACHE_TRAINEE_STUDY_TIME_KEY = "trainee-study-time-key";
    public static final String CACHE_TRAINEE_MEMBER_COURSE_KEY = "trainee-member-course-key";
	public static final Integer FINISH_STATUS_NOT_START = 0;//完成状态-未开始
	public static final Integer FINISH_STATUS_NOT_FINISH = 1;//完成状态-学习中
	public static final Integer FINISH_STATUS_FINISHED = 2;//完成状态-已完成
	public static final Integer MAINLAND_PERSONNEL = 1;  // 内陆人员
	public static final Integer NON_MAINLAND_PERSONNEL = 0;  // 非内陆人员

	private String memberName;			//人员编码
	private String memberFullName;		//人员全名
	private String phone;				//电话号码
	private Integer num;				//问卷中的条数
	private String organizationName;	// 部门名称
	private String companyName;			// 公司名称
	private String nationName;			// 民族
	private Member member;				// 人员对象
	private TraineeGroup traineeGroup;	// 分组对象
	private String courseName;//学习详情中课程名称
	private Long startTime; //开始时间
	private Long endTime;//结束时间
	private String studyTotalTimeString; //学习时长（String）
	private String settlementMonth; //结算日期
	private String financeMonth;// 纠偏月份
	private Integer classStatus;//班级状态
	private Long studyTotalTime;     	// 学习时长（学习详情）
	private Integer studyStatus; //学习状态
	private Integer finishStatus; //完成状态 空:全部 0-未开始 1-学习中 2-已完成
	private String finishStatusStr; //完成状态 空:全部 0-未开始 1-学习中 2-已完成
	private String studyProgress; //学习进度
	private String themeStatus; //主题状态
	private Integer organizationOrder;	// 单位排序
	private String settleOrganizationName;//结算单位的名称
	private Integer organizationLevel;	// 组织的等级
	private String projectName;//班级名称
	private String projectOrg;//需求方
	private String jobName;//职务名称
	private String misCode;
	private String memberIds;//学员ids
	private String courseIds;//在线课程ids
	private String levelName;//职级名称
	private String companyId;//机构ID
	private Integer memberNum;//人员个数
	private Integer index;//用于导出表中的序号
	private String projectId;//计划id
	private Integer fdqSum; //班级中四度问卷是否有人提交
	private Integer aaqSum; //班级中能力习得问卷是否有人提交
	private Integer slqSum; //班级中领导问卷是否有人提交
	private Integer messageRecordStatus; //0.未发送（“/”）、1.回执返回状态“发送成功、3.回执返回状态“发送失败”、4接口调用成功、5接口调用失败

	public static final int NO_COMMIT_QUESTIONARY = 0; // 是否提交满意度问卷：0未提交

	public static final int YES_COMMIT_QUESTIONARY = 1; // 是否提交满意度问卷：1已提交

	public static final String WAIT_GROUP = "0"; // 默认分组,待分组"0"
	public static final int DEFAULT_SORT = 1; // 默认排序

	public static final int ADD = 1;//新增
	public static final int UPDATE = 0;//修改
	public static final int DELETE = -1;//删除
	private String path;//组织path

	//未报到
	public static final Integer NO_SHOW = 0;
	//已报到
	public static final Integer SHOW = 1;

	private String cardId;
	private String className;

	private Integer row;

	public String getSettlementMonth() {
		return settlementMonth;
	}

	public String getProjectOrg() {
		return projectOrg;
	}

	public void setProjectOrg(String projectOrg) {
		this.projectOrg = projectOrg;
	}

	public void setSettlementMonth(String settlementMonth) {
		this.settlementMonth = settlementMonth;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getMemberIds() {
		return memberIds;
	}

	public void setMemberIds(String memberIds) {
		this.memberIds = memberIds;
	}

	public String getCourseIds() {
		return courseIds;
	}

	public void setCourseIds(String courseIds) {
		this.courseIds = courseIds;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public String getMemberFullName() {
		return memberFullName;
	}

	public Integer getFdqSum() {
		return fdqSum;
	}

	public Integer getAaqSum() {
		return aaqSum;
	}

	public Integer getSlqSum() {
		return slqSum;
	}

	public void setMemberFullName(String memberFullName) {
		this.memberFullName = memberFullName;
	}

	public void setFdqSum(Integer fdqSum) {
		this.fdqSum = fdqSum;
	}

	public void setAaqSum(Integer aaqSum) {
		this.aaqSum = aaqSum;
	}

	public void setSlqSum(Integer slqSum) {
		this.slqSum = slqSum;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    public String getJobName() {
        return jobName;
    }
    public void setJobName(String jobName) {
        this.jobName = jobName;
    }
    public String getSettleOrganizationName() {
        return settleOrganizationName;
    }
    public void setSettleOrganizationName(String settleOrganizationName) {
        this.settleOrganizationName = settleOrganizationName;
    }
    public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getNationName() {
		return nationName;
	}

	public void setNationName(String nationName) {
		this.nationName = nationName;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public TraineeGroup getTraineeGroup() {
		return traineeGroup;
	}

	public void setTraineeGroup(TraineeGroup traineeGroup) {
		this.traineeGroup = traineeGroup;
	}
    public Long getStartTime() {
        return startTime;
    }
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    public Long getEndTime() {
        return endTime;
    }
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    public Integer getClassStatus() {
        return classStatus;
    }
    public void setClassStatus(Integer classStatus) {
        this.classStatus = classStatus;
    }

	public Long getStudyTotalTime() {
		return studyTotalTime;
	}

	public void setStudyTotalTime(Long studyTotalTime) {
		this.studyTotalTime = studyTotalTime;
	}

	public Integer getOrganizationOrder() {
		return organizationOrder;
	}

	public void setOrganizationOrder(Integer organizationOrder) {
		this.organizationOrder = organizationOrder;
	}

	public String getLevelName() {
		return levelName;
	}

	public void setLevelName(String levelName) {
		this.levelName = levelName;
	}

	public Integer getOrganizationLevel() {
		return organizationLevel;
	}

	public void setOrganizationLevel(Integer organizationLevel) {
		this.organizationLevel = organizationLevel;
	}

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public Integer getMemberNum() {
        return memberNum;
    }

    public void setMemberNum(Integer memberNum) {
        this.memberNum = memberNum;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

	public String getMisCode() {
		return misCode;
	}

	public void setMisCode(String misCode) {
		this.misCode = misCode;
	}

	public String getFinanceMonth() {
		return financeMonth;
	}

	public void setFinanceMonth(String financeMonth) {
		this.financeMonth = financeMonth;
	}

	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getStudyTotalTimeString() {
		return studyTotalTimeString;
	}

	public void setStudyTotalTimeString(String studyTotalTimeString) {
		this.studyTotalTimeString = studyTotalTimeString;
	}
	public Integer getStudyStatus() {
		return studyStatus;
	}

	public void setStudyStatus(Integer studyStatus) {
		this.studyStatus = studyStatus;
	}

	public String getThemeStatus() {
		return themeStatus;
	}

	public void setThemeStatus(String themeStatus) {
		this.themeStatus = themeStatus;
	}

	public String getStudyProgress() {
		return studyProgress;
	}

	public void setStudyProgress(String studyProgress) {
		this.studyProgress = studyProgress;
	}

	public Integer getFinishStatus() {
		return finishStatus;
	}

	public void setFinishStatus(Integer finishStatus) {
		this.finishStatus = finishStatus;
	}

	public String getFinishStatusStr() {
		return finishStatusStr;
	}

	public void setFinishStatusStr(String finishStatusStr) {
		this.finishStatusStr = finishStatusStr;
	}

    public Integer getMessageRecordStatus() {
        return messageRecordStatus;
    }

    public void setMessageRecordStatus(Integer messageRecordStatus) {
        this.messageRecordStatus = messageRecordStatus;
    }

	public String getCardId() {
		return cardId;
	}

	public void setCardId(String cardId) {
		this.cardId = cardId;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public Integer getRow() {
		return row;
	}

	public void setRow(Integer row) {
		this.row = row;
	}
}