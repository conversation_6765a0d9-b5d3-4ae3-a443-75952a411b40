/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.PlanningImplementationRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 策划实施表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PlanningImplementation extends TableImpl<PlanningImplementationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_planning_implementation</code>
     */
    public static final PlanningImplementation PLANNING_IMPLEMENTATION = new PlanningImplementation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PlanningImplementationRecord> getRecordType() {
        return PlanningImplementationRecord.class;
    }

    /**
     * The column <code>train.t_planning_implementation.f_id</code>. 主键
     */
    public final TableField<PlanningImplementationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_planning_implementation.f_class_id</code>. 班级id
     */
    public final TableField<PlanningImplementationRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "班级id");

    /**
     * The column <code>train.t_planning_implementation.f_project_id</code>. 培训计划id
     */
    public final TableField<PlanningImplementationRecord, String> PROJECT_ID = createField("f_project_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "培训计划id");

    /**
     * The column <code>train.t_planning_implementation.f_business_id</code>. 关联培训项目表id
     */
    public final TableField<PlanningImplementationRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "关联培训项目表id");

    /**
     * The column <code>train.t_planning_implementation.f_progress_details</code>. 进度详情（1未启动、2需求沟通、3方案设计、4资源建设、5实施中、6已完成、7取消、8待定）
     */
    public final TableField<PlanningImplementationRecord, Integer> PROGRESS_DETAILS = createField("f_progress_details", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "进度详情（1未启动、2需求沟通、3方案设计、4资源建设、5实施中、6已完成、7取消、8待定）");

    /**
     * The column <code>train.t_planning_implementation.f_initiation_date</code>. 开始实施日期
     */
    public final TableField<PlanningImplementationRecord, Long> INITIATION_DATE = createField("f_initiation_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "开始实施日期");

    /**
     * The column <code>train.t_planning_implementation.f_finish_date</code>. 完成日期
     */
    public final TableField<PlanningImplementationRecord, Long> FINISH_DATE = createField("f_finish_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "完成日期");

    /**
     * The column <code>train.t_planning_implementation.f_embodiment_id</code>. 实施方式Id
     */
    public final TableField<PlanningImplementationRecord, String> EMBODIMENT_ID = createField("f_embodiment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "实施方式Id");

    /**
     * The column <code>train.t_planning_implementation.f_embodiment_address</code>. 实施地点
     */
    public final TableField<PlanningImplementationRecord, String> EMBODIMENT_ADDRESS = createField("f_embodiment_address", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "实施地点");

    /**
     * The column <code>train.t_planning_implementation.f_plan_planning_id</code>. 方案策划情况Id
     */
    public final TableField<PlanningImplementationRecord, String> PLAN_PLANNING_ID = createField("f_plan_planning_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "方案策划情况Id");

    /**
     * The column <code>train.t_planning_implementation.f_teacher_num</code>. 实施师资数量
     */
    public final TableField<PlanningImplementationRecord, Integer> TEACHER_NUM = createField("f_teacher_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "实施师资数量");

    /**
     * The column <code>train.t_planning_implementation.f_course_num</code>. 电子课程采纳量
     */
    public final TableField<PlanningImplementationRecord, Integer> COURSE_NUM = createField("f_course_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "电子课程采纳量");

    /**
     * The column <code>train.t_planning_implementation.f_content</code>. 备注
     */
    public final TableField<PlanningImplementationRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>train.t_planning_implementation.f_source</code>. 来源（0：同步班级管理 1：自建）
     */
    public final TableField<PlanningImplementationRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源（0：同步班级管理 1：自建）");

    /**
     * The column <code>train.t_planning_implementation.f_create_time</code>. 创建时间
     */
    public final TableField<PlanningImplementationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_planning_implementation.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public final TableField<PlanningImplementationRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态：0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_planning_implementation.f_modify_date</code>. 修改时间
     */
    public final TableField<PlanningImplementationRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>train.t_planning_implementation</code> table reference
     */
    public PlanningImplementation() {
        this("t_planning_implementation", null);
    }

    /**
     * Create an aliased <code>train.t_planning_implementation</code> table reference
     */
    public PlanningImplementation(String alias) {
        this(alias, PLANNING_IMPLEMENTATION);
    }

    private PlanningImplementation(String alias, Table<PlanningImplementationRecord> aliased) {
        this(alias, aliased, null);
    }

    private PlanningImplementation(String alias, Table<PlanningImplementationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "策划实施表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PlanningImplementationRecord> getPrimaryKey() {
        return Keys.KEY_T_PLANNING_IMPLEMENTATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PlanningImplementationRecord>> getKeys() {
        return Arrays.<UniqueKey<PlanningImplementationRecord>>asList(Keys.KEY_T_PLANNING_IMPLEMENTATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PlanningImplementation as(String alias) {
        return new PlanningImplementation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PlanningImplementation rename(String name) {
        return new PlanningImplementation(name, null);
    }
}
