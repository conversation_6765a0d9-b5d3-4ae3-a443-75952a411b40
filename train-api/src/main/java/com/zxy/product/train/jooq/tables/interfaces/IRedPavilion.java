/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 红色展馆信息
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IRedPavilion extends Serializable {

    /**
     * Setter for <code>train.t_red_pavilion.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_red_pavilion.f_province</code>. 省份
     */
    public void setProvince(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_province</code>. 省份
     */
    public String getProvince();

    /**
     * Setter for <code>train.t_red_pavilion.f_unit</code>. 单位
     */
    public void setUnit(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_unit</code>. 单位
     */
    public String getUnit();

    /**
     * Setter for <code>train.t_red_pavilion.f_base_name</code>. 教育基地名称
     */
    public void setBaseName(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_base_name</code>. 教育基地名称
     */
    public String getBaseName();

    /**
     * Setter for <code>train.t_red_pavilion.f_location</code>. 所在地
     */
    public void setLocation(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_location</code>. 所在地
     */
    public String getLocation();

    /**
     * Setter for <code>train.t_red_pavilion.f_pavilion_name</code>. 展馆名称
     */
    public void setPavilionName(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_pavilion_name</code>. 展馆名称
     */
    public String getPavilionName();

    /**
     * Setter for <code>train.t_red_pavilion.f_image_url</code>. 缩略图路径
     */
    public void setImageUrl(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_image_url</code>. 缩略图路径
     */
    public String getImageUrl();

    /**
     * Setter for <code>train.t_red_pavilion.f_url</code>. 地址
     */
    public void setUrl(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_url</code>. 地址
     */
    public String getUrl();

    /**
     * Setter for <code>train.t_red_pavilion.f_type</code>. 1:VR 2:主页 3:视频
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_red_pavilion.f_type</code>. 1:VR 2:主页 3:视频
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_red_pavilion.f_note</code>. 备注
     */
    public void setNote(String value);

    /**
     * Getter for <code>train.t_red_pavilion.f_note</code>. 备注
     */
    public String getNote();

    /**
     * Setter for <code>train.t_red_pavilion.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_red_pavilion.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IRedPavilion
     */
    public void from(IRedPavilion from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IRedPavilion
     */
    public <E extends IRedPavilion> E into(E into);
}
