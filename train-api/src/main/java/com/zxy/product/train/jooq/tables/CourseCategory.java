/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CourseCategoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 课程目录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseCategory extends TableImpl<CourseCategoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_course_category</code>
     */
    public static final CourseCategory COURSE_CATEGORY = new CourseCategory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseCategoryRecord> getRecordType() {
        return CourseCategoryRecord.class;
    }

    /**
     * The column <code>train.t_course_category.f_id</code>. 主键
     */
    public final TableField<CourseCategoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_course_category.f_name</code>. 名称
     */
    public final TableField<CourseCategoryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "名称");

    /**
     * The column <code>train.t_course_category.f_code</code>. 编号
     */
    public final TableField<CourseCategoryRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "编号");

    /**
     * Create a <code>train.t_course_category</code> table reference
     */
    public CourseCategory() {
        this("t_course_category", null);
    }

    /**
     * Create an aliased <code>train.t_course_category</code> table reference
     */
    public CourseCategory(String alias) {
        this(alias, COURSE_CATEGORY);
    }

    private CourseCategory(String alias, Table<CourseCategoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseCategory(String alias, Table<CourseCategoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "课程目录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseCategoryRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_CATEGORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseCategoryRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseCategoryRecord>>asList(Keys.KEY_T_COURSE_CATEGORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseCategory as(String alias) {
        return new CourseCategory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseCategory rename(String name) {
        return new CourseCategory(name, null);
    }
}
