/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 作业审核人关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITaskReviewer extends Serializable {

    /**
     * Setter for <code>train.t_task_reviewer.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_task_reviewer.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_task_reviewer.f_approval_member_id</code>. 审核人id
     */
    public void setApprovalMemberId(String value);

    /**
     * Getter for <code>train.t_task_reviewer.f_approval_member_id</code>. 审核人id
     */
    public String getApprovalMemberId();

    /**
     * Setter for <code>train.t_task_reviewer.f_task_id</code>. 作业id
     */
    public void setTaskId(String value);

    /**
     * Getter for <code>train.t_task_reviewer.f_task_id</code>. 作业id
     */
    public String getTaskId();

    /**
     * Setter for <code>train.t_task_reviewer.f_create_member_id</code>. 创建人id
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_task_reviewer.f_create_member_id</code>. 创建人id
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_task_reviewer.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_task_reviewer.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITaskReviewer
     */
    public void from(ITaskReviewer from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITaskReviewer
     */
    public <E extends ITaskReviewer> E into(E into);
}
