package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.QuestionEntity;

public class Question extends QuestionEntity{

	/**
	 * 题型表实体  lulu
	 *
	 */
	private static final long serialVersionUID = -6498675902080185794L;

	private List<QuestionAttr> questionAttrs;

    /**
     * 调研问卷 维度ID
     */
    private String dimensionId;

    /**
     * 调研问卷 排序
     */

	public List<QuestionAttr> getQuestionAttrs() {
		return questionAttrs;
	}

	public void setQuestionAttrs(List<QuestionAttr> questionAttrs) {
		this.questionAttrs = questionAttrs;
	}

	public String getDimensionId() {
		return dimensionId;
	}

	public void setDimensionId(String dimensionId) {
		this.dimensionId = dimensionId;
	}

}
