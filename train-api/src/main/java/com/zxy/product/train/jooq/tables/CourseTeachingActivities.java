/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CourseTeachingActivitiesRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 教研教学活动配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseTeachingActivities extends TableImpl<CourseTeachingActivitiesRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_course_teaching_activities</code>
     */
    public static final CourseTeachingActivities COURSE_TEACHING_ACTIVITIES = new CourseTeachingActivities();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseTeachingActivitiesRecord> getRecordType() {
        return CourseTeachingActivitiesRecord.class;
    }

    /**
     * The column <code>train.t_course_teaching_activities.f_id</code>. 系统ID
     */
    public final TableField<CourseTeachingActivitiesRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_course_teaching_activities.f_activity_id</code>. 活动ID
     */
    public final TableField<CourseTeachingActivitiesRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(64).nullable(false), this, "活动ID");

    /**
     * The column <code>train.t_course_teaching_activities.f_activity_name</code>. 活动名称
     */
    public final TableField<CourseTeachingActivitiesRecord, String> ACTIVITY_NAME = createField("f_activity_name", org.jooq.impl.SQLDataType.VARCHAR.length(64), this, "活动名称");

    /**
     * The column <code>train.t_course_teaching_activities.f_base</code>. 基数
     */
    public final TableField<CourseTeachingActivitiesRecord, Double> BASE = createField("f_base", org.jooq.impl.SQLDataType.FLOAT.nullable(false), this, "基数");

    /**
     * The column <code>train.t_course_teaching_activities.f_level_name</code>. 级别名称
     */
    public final TableField<CourseTeachingActivitiesRecord, String> LEVEL_NAME = createField("f_level_name", org.jooq.impl.SQLDataType.VARCHAR.length(64).nullable(false), this, "级别名称");

    /**
     * The column <code>train.t_course_teaching_activities.f_coefficient</code>. 系数
     */
    public final TableField<CourseTeachingActivitiesRecord, Double> COEFFICIENT = createField("f_coefficient", org.jooq.impl.SQLDataType.FLOAT.nullable(false), this, "系数");

    /**
     * The column <code>train.t_course_teaching_activities.f_satisfied_degree</code>. 满意度要求
     */
    public final TableField<CourseTeachingActivitiesRecord, Double> SATISFIED_DEGREE = createField("f_satisfied_degree", org.jooq.impl.SQLDataType.FLOAT.nullable(false), this, "满意度要求");

    /**
     * The column <code>train.t_course_teaching_activities.f_organization_id</code>. 组织ID
     */
    public final TableField<CourseTeachingActivitiesRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "组织ID");

    /**
     * The column <code>train.t_course_teaching_activities.f_create_member_id</code>. 创建人ID
     */
    public final TableField<CourseTeachingActivitiesRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "创建人ID");

    /**
     * The column <code>train.t_course_teaching_activities.f_create_time</code>. 添加时间
     */
    public final TableField<CourseTeachingActivitiesRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "添加时间");

    /**
     * Create a <code>train.t_course_teaching_activities</code> table reference
     */
    public CourseTeachingActivities() {
        this("t_course_teaching_activities", null);
    }

    /**
     * Create an aliased <code>train.t_course_teaching_activities</code> table reference
     */
    public CourseTeachingActivities(String alias) {
        this(alias, COURSE_TEACHING_ACTIVITIES);
    }

    private CourseTeachingActivities(String alias, Table<CourseTeachingActivitiesRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseTeachingActivities(String alias, Table<CourseTeachingActivitiesRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "教研教学活动配置");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseTeachingActivitiesRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_TEACHING_ACTIVITIES_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseTeachingActivitiesRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseTeachingActivitiesRecord>>asList(Keys.KEY_T_COURSE_TEACHING_ACTIVITIES_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseTeachingActivities as(String alias) {
        return new CourseTeachingActivities(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseTeachingActivities rename(String name) {
        return new CourseTeachingActivities(name, null);
    }
}
