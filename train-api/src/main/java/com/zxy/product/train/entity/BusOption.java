package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.BusOptionEntity;

/**
 * l<PERSON><PERSON>an
 *
 */

public class BusOption extends BusOptionEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = 9116585841560732625L;
	private boolean selected;//是否选中    true的时候表示选中状态

	private int count;//选项统计数

	private String optionName;//选项名称
	private String memberId;

	public int getCount() {
		return count;
	}

	public String getOptionName() {
		return optionName;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public void setOptionName(String optionName) {
		this.optionName = optionName;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }
}
