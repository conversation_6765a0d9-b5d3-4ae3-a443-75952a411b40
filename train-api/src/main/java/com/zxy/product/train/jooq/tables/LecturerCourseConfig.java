/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LecturerCourseConfigRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 讲师专业序列/讲师课程库课程分类配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerCourseConfig extends TableImpl<LecturerCourseConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_lecturer_course_config</code>
     */
    public static final LecturerCourseConfig LECTURER_COURSE_CONFIG = new LecturerCourseConfig();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LecturerCourseConfigRecord> getRecordType() {
        return LecturerCourseConfigRecord.class;
    }

    /**
     * The column <code>train.t_lecturer_course_config.f_id</code>. 系统ID
     */
    public final TableField<LecturerCourseConfigRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_lecturer_course_config.f_name</code>. 专业序列/课程分类名称
     */
    public final TableField<LecturerCourseConfigRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).nullable(false), this, "专业序列/课程分类名称");

    /**
     * The column <code>train.t_lecturer_course_config.f_parent_id</code>. 上级序列ID【顶级为null】
     */
    public final TableField<LecturerCourseConfigRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "上级序列ID【顶级为null】");

    /**
     * The column <code>train.t_lecturer_course_config.f_type</code>. 类型【0：讲师专业序列；1：课程分类】
     */
    public final TableField<LecturerCourseConfigRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "类型【0：讲师专业序列；1：课程分类】");

    /**
     * The column <code>train.t_lecturer_course_config.f_code</code>. 课程分类/专业序列的目录编码
     */
    public final TableField<LecturerCourseConfigRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(24), this, "课程分类/专业序列的目录编码");

    /**
     * The column <code>train.t_lecturer_course_config.f_create_member_id</code>. 创建人ID
     */
    public final TableField<LecturerCourseConfigRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "创建人ID");

    /**
     * The column <code>train.t_lecturer_course_config.f_create_time</code>. 创建时间
     */
    public final TableField<LecturerCourseConfigRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_lecturer_course_config.f_organization_id</code>. 组织ID
     */
    public final TableField<LecturerCourseConfigRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "组织ID");

    /**
     * The column <code>train.t_lecturer_course_config.f_path</code>. 保存向上的所有id
     */
    public final TableField<LecturerCourseConfigRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(500).nullable(false), this, "保存向上的所有id");

    /**
     * The column <code>train.t_lecturer_course_config.f_sort</code>. 排序
     */
    public final TableField<LecturerCourseConfigRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * Create a <code>train.t_lecturer_course_config</code> table reference
     */
    public LecturerCourseConfig() {
        this("t_lecturer_course_config", null);
    }

    /**
     * Create an aliased <code>train.t_lecturer_course_config</code> table reference
     */
    public LecturerCourseConfig(String alias) {
        this(alias, LECTURER_COURSE_CONFIG);
    }

    private LecturerCourseConfig(String alias, Table<LecturerCourseConfigRecord> aliased) {
        this(alias, aliased, null);
    }

    private LecturerCourseConfig(String alias, Table<LecturerCourseConfigRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "讲师专业序列/讲师课程库课程分类配置");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LecturerCourseConfigRecord> getPrimaryKey() {
        return Keys.KEY_T_LECTURER_COURSE_CONFIG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LecturerCourseConfigRecord>> getKeys() {
        return Arrays.<UniqueKey<LecturerCourseConfigRecord>>asList(Keys.KEY_T_LECTURER_COURSE_CONFIG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerCourseConfig as(String alias) {
        return new LecturerCourseConfig(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LecturerCourseConfig rename(String name) {
        return new LecturerCourseConfig(name, null);
    }
}
