/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 讲师属性配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILecturerAttribute extends Serializable {

    /**
     * Setter for <code>train.t_lecturer_attribute.f_id</code>. 系统ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_id</code>. 系统ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_lecturer_attribute.f_type_id</code>. 分类ID固定值【0：内部讲师；1：外部讲师】
     */
    public void setTypeId(Integer value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_type_id</code>. 分类ID固定值【0：内部讲师；1：外部讲师】
     */
    public Integer getTypeId();

    /**
     * Setter for <code>train.t_lecturer_attribute.f_attribute_name</code>. 属性名称
     */
    public void setAttributeName(String value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_attribute_name</code>. 属性名称
     */
    public String getAttributeName();

    /**
     * Setter for <code>train.t_lecturer_attribute.f_create_member_id</code>. 创建人ID
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_create_member_id</code>. 创建人ID
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_lecturer_attribute.f_exit_member_id</code>. 最后修改人ID
     */
    public void setExitMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_exit_member_id</code>. 最后修改人ID
     */
    public String getExitMemberId();

    /**
     * Setter for <code>train.t_lecturer_attribute.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_lecturer_attribute.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_lecturer_attribute.f_update_time</code>. 最后修改时间
     */
    public void setUpdateTime(Long value);

    /**
     * Getter for <code>train.t_lecturer_attribute.f_update_time</code>. 最后修改时间
     */
    public Long getUpdateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILecturerAttribute
     */
    public void from(ILecturerAttribute from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILecturerAttribute
     */
    public <E extends ILecturerAttribute> E into(E into);
}
