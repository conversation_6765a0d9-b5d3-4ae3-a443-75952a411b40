/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.AcademicStatisticsInfoRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AcademicStatisticsInfo extends TableImpl<AcademicStatisticsInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_academic_statistics_info</code>
     */
    public static final AcademicStatisticsInfo ACADEMIC_STATISTICS_INFO = new AcademicStatisticsInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AcademicStatisticsInfoRecord> getRecordType() {
        return AcademicStatisticsInfoRecord.class;
    }

    /**
     * The column <code>train.t_academic_statistics_info.f_id</code>. 表id
     */
    public final TableField<AcademicStatisticsInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_academic_statistics_info.f_type</code>. 业务类型（1实施培训班数量、2培训学员人次、3培训学员人数）
     */
    public final TableField<AcademicStatisticsInfoRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "业务类型（1实施培训班数量、2培训学员人次、3培训学员人数）");

    /**
     * The column <code>train.t_academic_statistics_info.f_implementation_year</code>. 年份
     */
    public final TableField<AcademicStatisticsInfoRecord, Integer> IMPLEMENTATION_YEAR = createField("f_implementation_year", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "年份");

    /**
     * The column <code>train.t_academic_statistics_info.f_total_count</code>. 总数
     */
    public final TableField<AcademicStatisticsInfoRecord, Long> TOTAL_COUNT = createField("f_total_count", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "总数");

    /**
     * The column <code>train.t_academic_statistics_info.f_create_time</code>. 创建时间
     */
    public final TableField<AcademicStatisticsInfoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>train.t_academic_statistics_info</code> table reference
     */
    public AcademicStatisticsInfo() {
        this("t_academic_statistics_info", null);
    }

    /**
     * Create an aliased <code>train.t_academic_statistics_info</code> table reference
     */
    public AcademicStatisticsInfo(String alias) {
        this(alias, ACADEMIC_STATISTICS_INFO);
    }

    private AcademicStatisticsInfo(String alias, Table<AcademicStatisticsInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private AcademicStatisticsInfo(String alias, Table<AcademicStatisticsInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AcademicStatisticsInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_ACADEMIC_STATISTICS_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AcademicStatisticsInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<AcademicStatisticsInfoRecord>>asList(Keys.KEY_T_ACADEMIC_STATISTICS_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AcademicStatisticsInfo as(String alias) {
        return new AcademicStatisticsInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AcademicStatisticsInfo rename(String name) {
        return new AcademicStatisticsInfo(name, null);
    }
}
