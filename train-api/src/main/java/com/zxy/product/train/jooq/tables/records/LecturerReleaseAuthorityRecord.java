/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.LecturerReleaseAuthority;
import com.zxy.product.train.jooq.tables.interfaces.ILecturerReleaseAuthority;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 讲师发布权限人员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerReleaseAuthorityRecord extends UpdatableRecordImpl<LecturerReleaseAuthorityRecord> implements Record6<String, String, String, Short, Long, String>, ILecturerReleaseAuthority {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_member_id</code>. 用户ID
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_member_id</code>. 用户ID
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_name</code>. 人员编号
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_name</code>. 人员编号
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Short value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Short getDeleteFlag() {
        return (Short) get(3);
    }

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_lecturer_release_authority.f_create_member_id</code>. 创建人
     */
    @Override
    public void setCreateMemberId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_lecturer_release_authority.f_create_member_id</code>. 创建人
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, Short, Long, String> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, Short, Long, String> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Short> field4() {
        return LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Short value4() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthorityRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthorityRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthorityRecord value3(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthorityRecord value4(Short value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthorityRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthorityRecord value6(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerReleaseAuthorityRecord values(String value1, String value2, String value3, Short value4, Long value5, String value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILecturerReleaseAuthority from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setName(from.getName());
        setDeleteFlag(from.getDeleteFlag());
        setCreateTime(from.getCreateTime());
        setCreateMemberId(from.getCreateMemberId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILecturerReleaseAuthority> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LecturerReleaseAuthorityRecord
     */
    public LecturerReleaseAuthorityRecord() {
        super(LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY);
    }

    /**
     * Create a detached, initialised LecturerReleaseAuthorityRecord
     */
    public LecturerReleaseAuthorityRecord(String id, String memberId, String name, Short deleteFlag, Long createTime, String createMemberId) {
        super(LecturerReleaseAuthority.LECTURER_RELEASE_AUTHORITY);

        set(0, id);
        set(1, memberId);
        set(2, name);
        set(3, deleteFlag);
        set(4, createTime);
        set(5, createMemberId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.LecturerReleaseAuthorityEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.LecturerReleaseAuthorityEntity pojo = (com.zxy.product.train.jooq.tables.pojos.LecturerReleaseAuthorityEntity)source;
        pojo.into(this);
        return true;
    }
}
