/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Configuration extends TableImpl<ConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_configuration</code>
     */
    public static final Configuration CONFIGURATION = new Configuration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ConfigurationRecord> getRecordType() {
        return ConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_configuration.f_id</code>. 主键
     */
    public final TableField<ConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_configuration.f_configuration_name</code>. 配置项名称
     */
    public final TableField<ConfigurationRecord, String> CONFIGURATION_NAME = createField("f_configuration_name", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "配置项名称");

    /**
     * The column <code>train.t_configuration.f_type</code>. 配置类型（1费用类型2培训类型3培训补贴类型4人员类型5班级类型6教室信息7额度配置8常用版务人员9
     */
    public final TableField<ConfigurationRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "配置类型（1费用类型2培训类型3培训补贴类型4人员类型5班级类型6教室信息7额度配置8常用版务人员9");

    /**
     * The column <code>train.t_configuration.f_organization_id</code>. 所属部门
     */
    public final TableField<ConfigurationRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所属部门");

    /**
     * The column <code>train.t_configuration.f_configuration_amount</code>. 配置数
     */
    public final TableField<ConfigurationRecord, Integer> CONFIGURATION_AMOUNT = createField("f_configuration_amount", org.jooq.impl.SQLDataType.INTEGER, this, "配置数");

    /**
     * The column <code>train.t_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<ConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_configuration.f_create_member</code>. 创建人ID
     */
    public final TableField<ConfigurationRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ConfigurationRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_configuration</code> table reference
     */
    public Configuration() {
        this("t_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_configuration</code> table reference
     */
    public Configuration(String alias) {
        this(alias, CONFIGURATION);
    }

    private Configuration(String alias, Table<ConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private Configuration(String alias, Table<ConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<ConfigurationRecord>>asList(Keys.KEY_T_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Configuration as(String alias) {
        return new Configuration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Configuration rename(String name) {
        return new Configuration(name, null);
    }
}
