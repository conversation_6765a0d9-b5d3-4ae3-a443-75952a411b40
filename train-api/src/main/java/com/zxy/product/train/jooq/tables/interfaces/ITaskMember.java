/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 用户提交作业详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITaskMember extends Serializable {

    /**
     * Setter for <code>train.t_task_member.f_id</code>. 表ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_task_member.f_id</code>. 表ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_task_member.f_member_id</code>. 提交人
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_task_member.f_member_id</code>. 提交人
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_task_member.f_create_time</code>. 提交作业时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_task_member.f_create_time</code>. 提交作业时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_task_member.f_task_id</code>. 作业ID
     */
    public void setTaskId(String value);

    /**
     * Getter for <code>train.t_task_member.f_task_id</code>. 作业ID
     */
    public String getTaskId();

    /**
     * Setter for <code>train.t_task_member.f_name</code>. 附件名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_task_member.f_name</code>. 附件名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_task_member.f_description</code>. 附件描述
     */
    public void setDescription(String value);

    /**
     * Getter for <code>train.t_task_member.f_description</code>. 附件描述
     */
    public String getDescription();

    /**
     * Setter for <code>train.t_task_member.f_attachment_type</code>. 附件类型
     */
    public void setAttachmentType(String value);

    /**
     * Getter for <code>train.t_task_member.f_attachment_type</code>. 附件类型
     */
    public String getAttachmentType();

    /**
     * Setter for <code>train.t_task_member.f_attachment_id</code>. 附件ID
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_task_member.f_attachment_id</code>. 附件ID
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_task_member.f_state</code>. 状态 1未提交 2审核中  3合格  4不合格
     */
    public void setState(Integer value);

    /**
     * Getter for <code>train.t_task_member.f_state</code>. 状态 1未提交 2审核中  3合格  4不合格
     */
    public Integer getState();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITaskMember
     */
    public void from(ITaskMember from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITaskMember
     */
    public <E extends ITaskMember> E into(E into);
}
