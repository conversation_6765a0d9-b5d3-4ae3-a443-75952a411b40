/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.GroupConfigurationValueRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GroupConfigurationValue extends TableImpl<GroupConfigurationValueRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_group_configuration_value</code>
     */
    public static final GroupConfigurationValue GROUP_CONFIGURATION_VALUE = new GroupConfigurationValue();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GroupConfigurationValueRecord> getRecordType() {
        return GroupConfigurationValueRecord.class;
    }

    /**
     * The column <code>train.t_group_configuration_value.f_id</code>. 主键
     */
    public final TableField<GroupConfigurationValueRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_group_configuration_value.f_organization_id</code>. 单位名称
     */
    public final TableField<GroupConfigurationValueRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "单位名称");

    /**
     * The column <code>train.t_group_configuration_value.f_group_id</code>. 关联分组配置ID
     */
    public final TableField<GroupConfigurationValueRecord, String> GROUP_ID = createField("f_group_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联分组配置ID");

    /**
     * The column <code>train.t_group_configuration_value.f_sort</code>. 排序
     */
    public final TableField<GroupConfigurationValueRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_group_configuration_value.f_create_time</code>. 创建时间
     */
    public final TableField<GroupConfigurationValueRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_group_configuration_value.f_create_member</code>. 创建人ID
     */
    public final TableField<GroupConfigurationValueRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_group_configuration_value.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<GroupConfigurationValueRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_group_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    public final TableField<GroupConfigurationValueRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "当前节点的所有父节点");

    /**
     * The column <code>train.t_group_configuration_value.f_short_name</code>. 短名称
     */
    public final TableField<GroupConfigurationValueRecord, String> SHORT_NAME = createField("f_short_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "短名称");

    /**
     * Create a <code>train.t_group_configuration_value</code> table reference
     */
    public GroupConfigurationValue() {
        this("t_group_configuration_value", null);
    }

    /**
     * Create an aliased <code>train.t_group_configuration_value</code> table reference
     */
    public GroupConfigurationValue(String alias) {
        this(alias, GROUP_CONFIGURATION_VALUE);
    }

    private GroupConfigurationValue(String alias, Table<GroupConfigurationValueRecord> aliased) {
        this(alias, aliased, null);
    }

    private GroupConfigurationValue(String alias, Table<GroupConfigurationValueRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GroupConfigurationValueRecord> getPrimaryKey() {
        return Keys.KEY_T_GROUP_CONFIGURATION_VALUE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GroupConfigurationValueRecord>> getKeys() {
        return Arrays.<UniqueKey<GroupConfigurationValueRecord>>asList(Keys.KEY_T_GROUP_CONFIGURATION_VALUE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationValue as(String alias) {
        return new GroupConfigurationValue(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GroupConfigurationValue rename(String name) {
        return new GroupConfigurationValue(name, null);
    }
}
