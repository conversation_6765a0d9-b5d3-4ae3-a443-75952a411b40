/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudentHistoryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 历史学员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudentHistory extends TableImpl<StudentHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_student_history</code>
     */
    public static final StudentHistory STUDENT_HISTORY = new StudentHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudentHistoryRecord> getRecordType() {
        return StudentHistoryRecord.class;
    }

    /**
     * The column <code>train.t_student_history.f_id</code>.
     */
    public final TableField<StudentHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_student_history.f_class_id</code>. 班级ID
     */
    public final TableField<StudentHistoryRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_student_history.f_person_id</code>. 用户ID
     */
    public final TableField<StudentHistoryRecord, String> PERSON_ID = createField("f_person_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "用户ID");

    /**
     * The column <code>train.t_student_history.f_type</code>. 学员类型:0N
     */
    public final TableField<StudentHistoryRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "学员类型:0N");

    /**
     * The column <code>train.t_student_history.f_phone</code>. 手机号码
     */
    public final TableField<StudentHistoryRecord, String> PHONE = createField("f_phone", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "手机号码");

    /**
     * The column <code>train.t_student_history.f_email</code>. 邮箱
     */
    public final TableField<StudentHistoryRecord, String> EMAIL = createField("f_email", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "邮箱");

    /**
     * The column <code>train.t_student_history.f_sex</code>. 性别:0男,1女
     */
    public final TableField<StudentHistoryRecord, Integer> SEX = createField("f_sex", org.jooq.impl.SQLDataType.INTEGER, this, "性别:0男,1女");

    /**
     * The column <code>train.t_student_history.f_nation</code>. 民族
     */
    public final TableField<StudentHistoryRecord, String> NATION = createField("f_nation", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "民族");

    /**
     * The column <code>train.t_student_history.f_remark</code>. 备注
     */
    public final TableField<StudentHistoryRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "备注");

    /**
     * The column <code>train.t_student_history.f_status</code>. 审核状态:1N,2W,3Y
     */
    public final TableField<StudentHistoryRecord, String> STATUS = createField("f_status", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "审核状态:1N,2W,3Y");

    /**
     * The column <code>train.t_student_history.f_sort</code>. 排序
     */
    public final TableField<StudentHistoryRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * Create a <code>train.t_student_history</code> table reference
     */
    public StudentHistory() {
        this("t_student_history", null);
    }

    /**
     * Create an aliased <code>train.t_student_history</code> table reference
     */
    public StudentHistory(String alias) {
        this(alias, STUDENT_HISTORY);
    }

    private StudentHistory(String alias, Table<StudentHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudentHistory(String alias, Table<StudentHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "历史学员表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudentHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDENT_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudentHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<StudentHistoryRecord>>asList(Keys.KEY_T_STUDENT_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentHistory as(String alias) {
        return new StudentHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudentHistory rename(String name) {
        return new StudentHistory(name, null);
    }
}
