/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassQuestionnaireTotalRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 新版满意度问卷数据表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassQuestionnaireTotal extends TableImpl<ClassQuestionnaireTotalRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_questionnaire_total</code>
     */
    public static final ClassQuestionnaireTotal CLASS_QUESTIONNAIRE_TOTAL = new ClassQuestionnaireTotal();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassQuestionnaireTotalRecord> getRecordType() {
        return ClassQuestionnaireTotalRecord.class;
    }

    /**
     * The column <code>train.t_class_questionnaire_total.f_id</code>. ID
     */
    public final TableField<ClassQuestionnaireTotalRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_class_questionnaire_total.f_class_id</code>. 班级ID
     */
    public final TableField<ClassQuestionnaireTotalRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_questionnaire_total.f_questionnaire_id</code>. 问卷ID
     */
    public final TableField<ClassQuestionnaireTotalRecord, String> QUESTIONNAIRE_ID = createField("f_questionnaire_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "问卷ID");

    /**
     * The column <code>train.t_class_questionnaire_total.f_course_id</code>. 课程ID
     */
    public final TableField<ClassQuestionnaireTotalRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "课程ID");

    /**
     * The column <code>train.t_class_questionnaire_total.f_question_id</code>. 问题ID
     */
    public final TableField<ClassQuestionnaireTotalRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "问题ID");

    /**
     * The column <code>train.t_class_questionnaire_total.f_answer</code>. 答案
     */
    public final TableField<ClassQuestionnaireTotalRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "答案");

    /**
     * The column <code>train.t_class_questionnaire_total.f_number</code>. 作答人数
     */
    public final TableField<ClassQuestionnaireTotalRecord, Integer> NUMBER = createField("f_number", org.jooq.impl.SQLDataType.INTEGER, this, "作答人数");

    /**
     * Create a <code>train.t_class_questionnaire_total</code> table reference
     */
    public ClassQuestionnaireTotal() {
        this("t_class_questionnaire_total", null);
    }

    /**
     * Create an aliased <code>train.t_class_questionnaire_total</code> table reference
     */
    public ClassQuestionnaireTotal(String alias) {
        this(alias, CLASS_QUESTIONNAIRE_TOTAL);
    }

    private ClassQuestionnaireTotal(String alias, Table<ClassQuestionnaireTotalRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassQuestionnaireTotal(String alias, Table<ClassQuestionnaireTotalRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "新版满意度问卷数据表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassQuestionnaireTotalRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_QUESTIONNAIRE_TOTAL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassQuestionnaireTotalRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassQuestionnaireTotalRecord>>asList(Keys.KEY_T_CLASS_QUESTIONNAIRE_TOTAL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassQuestionnaireTotal as(String alias) {
        return new ClassQuestionnaireTotal(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassQuestionnaireTotal rename(String name) {
        return new ClassQuestionnaireTotal(name, null);
    }
}
