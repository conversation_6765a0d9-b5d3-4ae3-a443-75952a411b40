/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamAchievementReplyRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 团队学习班-学习成果回复表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamAchievementReply extends TableImpl<StudyTeamAchievementReplyRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_achievement_reply</code>
     */
    public static final StudyTeamAchievementReply STUDY_TEAM_ACHIEVEMENT_REPLY = new StudyTeamAchievementReply();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamAchievementReplyRecord> getRecordType() {
        return StudyTeamAchievementReplyRecord.class;
    }

    /**
     * The column <code>train.t_study_team_achievement_reply.f_id</code>. 主键
     */
    public final TableField<StudyTeamAchievementReplyRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_parent_id</code>. 上级id
     */
    public final TableField<StudyTeamAchievementReplyRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "上级id");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_comment_id</code>. 评论id
     */
    public final TableField<StudyTeamAchievementReplyRecord, String> COMMENT_ID = createField("f_comment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "评论id");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_member_id</code>. 用户id
     */
    public final TableField<StudyTeamAchievementReplyRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "用户id");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_to_member_id</code>. 被回复的用户id
     */
    public final TableField<StudyTeamAchievementReplyRecord, String> TO_MEMBER_ID = createField("f_to_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "被回复的用户id");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_content</code>. 回复内容
     */
    public final TableField<StudyTeamAchievementReplyRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.VARCHAR.length(3000).nullable(false), this, "回复内容");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_hide</code>. 隐藏(1是，0否)
     */
    public final TableField<StudyTeamAchievementReplyRecord, Integer> HIDE = createField("f_hide", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "隐藏(1是，0否)");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_praise_count</code>. 点赞数
     */
    public final TableField<StudyTeamAchievementReplyRecord, Integer> PRAISE_COUNT = createField("f_praise_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "点赞数");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_delete_flag</code>. 删除状态(0未删除，1已删除)
     */
    public final TableField<StudyTeamAchievementReplyRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态(0未删除，1已删除)");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamAchievementReplyRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_organization_id</code>. 所属组织id
     */
    public final TableField<StudyTeamAchievementReplyRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "所属组织id");

    /**
     * The column <code>train.t_study_team_achievement_reply.f_level</code>. 1回复了讨论 2回复了回复
     */
    public final TableField<StudyTeamAchievementReplyRecord, Integer> LEVEL = createField("f_level", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "1回复了讨论 2回复了回复");

    /**
     * Create a <code>train.t_study_team_achievement_reply</code> table reference
     */
    public StudyTeamAchievementReply() {
        this("t_study_team_achievement_reply", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_achievement_reply</code> table reference
     */
    public StudyTeamAchievementReply(String alias) {
        this(alias, STUDY_TEAM_ACHIEVEMENT_REPLY);
    }

    private StudyTeamAchievementReply(String alias, Table<StudyTeamAchievementReplyRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamAchievementReply(String alias, Table<StudyTeamAchievementReplyRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "团队学习班-学习成果回复表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamAchievementReplyRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_ACHIEVEMENT_REPLY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamAchievementReplyRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamAchievementReplyRecord>>asList(Keys.KEY_T_STUDY_TEAM_ACHIEVEMENT_REPLY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamAchievementReply as(String alias) {
        return new StudyTeamAchievementReply(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamAchievementReply rename(String name) {
        return new StudyTeamAchievementReply(name, null);
    }
}
