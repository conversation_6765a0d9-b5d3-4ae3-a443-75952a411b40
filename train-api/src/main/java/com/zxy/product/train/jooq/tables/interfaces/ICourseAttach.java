/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseAttach extends Serializable {

    /**
     * Setter for <code>train.t_course_attach.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_course_attach.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_course_attach.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_course_attach.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_course_attach.f_attach_id</code>. 附件ID
     */
    public void setAttachId(String value);

    /**
     * Getter for <code>train.t_course_attach.f_attach_id</code>. 附件ID
     */
    public String getAttachId();

    /**
     * Setter for <code>train.t_course_attach.f_attach_type</code>. 文件类型
     */
    public void setAttachType(String value);

    /**
     * Getter for <code>train.t_course_attach.f_attach_type</code>. 文件类型
     */
    public String getAttachType();

    /**
     * Setter for <code>train.t_course_attach.f_attach_name</code>. 文件名称
     */
    public void setAttachName(String value);

    /**
     * Getter for <code>train.t_course_attach.f_attach_name</code>. 文件名称
     */
    public String getAttachName();

    /**
     * Setter for <code>train.t_course_attach.f_create_member</code>. 创建人
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_course_attach.f_create_member</code>. 创建人
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_course_attach.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_course_attach.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseAttach
     */
    public void from(ICourseAttach from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseAttach
     */
    public <E extends ICourseAttach> E into(E into);
}
