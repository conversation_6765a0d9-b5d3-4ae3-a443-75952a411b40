package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ResearchAnswerRecordEntity;

public class ResearchAnswerRecord extends ResearchAnswerRecordEntity{

	/**
	 * lulu 统计与评估 评估记录
	 */
	private static final long serialVersionUID = 6564132849482676028L;

	private Member member;

	private Organization organization;

	private Long sTime;//开始时间

	private Long suTime;//提交时间

	private Integer sumScore;//总分

	private String rId;//评估记录ID
	
	private String organizationId;//评估记录ID

	private String type;//问题类型
	
	private String type_branch;//问题类型分组
	
	private Integer status;//问卷作答状态

	private ResearchRecord researchRecord;//调研评估记录实体
	private String mName;
	private String oName;
	private String phone;
	private String questionName;
	private Short questionType;

	public static final String DEGREE_OF_SATISACTION_ANSWER = "satisfaction-degree-question-answer";//方问卷答案缓存的key值

	public String getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(String organizationId) {
		this.organizationId = organizationId;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public Long getsTime() {
		return sTime;
	}

	public void setsTime(Long sTime) {
		this.sTime = sTime;
	}

	public Long getSuTime() {
		return suTime;
	}

	public void setSuTime(Long suTime) {
		this.suTime = suTime;
	}

	public Integer getSumScore() {
		return sumScore;
	}

	public void setSumScore(Integer sumScore) {
		this.sumScore = sumScore;
	}

	public ResearchRecord getResearchRecord() {
		return researchRecord;
	}

	public void setResearchRecord(ResearchRecord researchRecord) {
		this.researchRecord = researchRecord;
	}

	public String getrId() {
		return rId;
	}

	public void setrId(String rId) {
		this.rId = rId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getType_branch() {
		return type_branch;
	}

	public void setType_branch(String type_branch) {
		this.type_branch = type_branch;
	}

	public String getmName() {
		return mName;
	}

	public void setmName(String mName) {
		this.mName = mName;
	}

	public String getoName() {
		return oName;
	}

	public void setoName(String oName) {
		this.oName = oName;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getQuestionName() {
		return questionName;
	}

	public void setQuestionName(String questionName) {
		this.questionName = questionName;
	}

	public Short getQuestionType() {
		return questionType;
	}

	public void setQuestionType(Short questionType) {
		this.questionType = questionType;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

}
