/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.BusDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 班车详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BusDetail extends TableImpl<BusDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_bus_detail</code>
     */
    public static final BusDetail BUS_DETAIL = new BusDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BusDetailRecord> getRecordType() {
        return BusDetailRecord.class;
    }

    /**
     * The column <code>train.t_bus_detail.f_id</code>. 主键
     */
    public final TableField<BusDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_bus_detail.f_member_id</code>. 员工ID
     */
    public final TableField<BusDetailRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "员工ID");

    /**
     * The column <code>train.t_bus_detail.f_option_id</code>. 登记信息(选项主题表ID)
     */
    public final TableField<BusDetailRecord, String> OPTION_ID = createField("f_option_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "登记信息(选项主题表ID)");

    /**
     * Create a <code>train.t_bus_detail</code> table reference
     */
    public BusDetail() {
        this("t_bus_detail", null);
    }

    /**
     * Create an aliased <code>train.t_bus_detail</code> table reference
     */
    public BusDetail(String alias) {
        this(alias, BUS_DETAIL);
    }

    private BusDetail(String alias, Table<BusDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private BusDetail(String alias, Table<BusDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "班车详情表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<BusDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_BUS_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<BusDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<BusDetailRecord>>asList(Keys.KEY_T_BUS_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusDetail as(String alias) {
        return new BusDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public BusDetail rename(String name) {
        return new BusDetail(name, null);
    }
}
