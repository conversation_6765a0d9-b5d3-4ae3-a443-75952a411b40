package com.zxy.product.train.content;

import java.util.HashMap;
import java.util.Map;

public enum ProgressDetailEnum {
    notStart("未启动", 1),
    requireCommunication("需求沟通", 2),
    schemeDesign("方案设计", 3),
    resourceBuild("资源建设", 4),
    inOperation("实施中", 5),
    finished("已完成", 6),
    cancel("取消", 7);

    private Integer code;
    private String value;

    ProgressDetailEnum(String value,int code){
        this.code = code;
        this.value = value;
    }

    public static Map<String, Integer> progressDetailMap = new HashMap<>(18);

    static {
        for (ProgressDetailEnum type : values()) {
            progressDetailMap.put(type.getValue(), type.getCode());
        }
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
