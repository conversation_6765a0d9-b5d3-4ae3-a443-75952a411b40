/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学院授课记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICollegeTeaching extends Serializable {

    /**
     * Setter for <code>train.t_college_teaching.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_college_teaching.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_college_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public void setLecturerId(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_lecturer_id</code>. 课程讲师ID
     */
    public String getLecturerId();

    /**
     * Setter for <code>train.t_college_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    public void setLecturerName(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_lecturer_name</code>. 课程讲师姓名
     */
    public String getLecturerName();

    /**
     * Setter for <code>train.t_college_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    public void setLecturerPost(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_lecturer_post</code>. 课程讲师职务
     */
    public String getLecturerPost();

    /**
     * Setter for <code>train.t_college_teaching.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_college_teaching.f_course_date</code>. 上课日期
     */
    public void setCourseDate(Long value);

    /**
     * Getter for <code>train.t_college_teaching.f_course_date</code>. 上课日期
     */
    public Long getCourseDate();

    /**
     * Setter for <code>train.t_college_teaching.f_organization_name</code>. 主办部门
     */
    public void setOrganizationName(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_organization_name</code>. 主办部门
     */
    public String getOrganizationName();

    /**
     * Setter for <code>train.t_college_teaching.f_class_name</code>. 培训班名称
     */
    public void setClassName(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_class_name</code>. 培训班名称
     */
    public String getClassName();

    /**
     * Setter for <code>train.t_college_teaching.f_class_member</code>. 培训班联系人
     */
    public void setClassMember(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_class_member</code>. 培训班联系人
     */
    public String getClassMember();

    /**
     * Setter for <code>train.t_college_teaching.f_course_duration</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public void setCourseDuration(Double value);

    /**
     * Getter for <code>train.t_college_teaching.f_course_duration</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public Double getCourseDuration();

    /**
     * Setter for <code>train.t_college_teaching.f_obj</code>. 授课对象
     */
    public void setObj(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_obj</code>. 授课对象
     */
    public String getObj();

    /**
     * Setter for <code>train.t_college_teaching.f_attachment_id</code>. 附件id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_college_teaching.f_attachment_id</code>. 附件id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_college_teaching.f_obj_num</code>. 授课人数
     */
    public void setObjNum(Integer value);

    /**
     * Getter for <code>train.t_college_teaching.f_obj_num</code>. 授课人数
     */
    public Integer getObjNum();

    /**
     * Setter for <code>train.t_college_teaching.f_tax</code>. 税金
     */
    public void setTax(Double value);

    /**
     * Getter for <code>train.t_college_teaching.f_tax</code>. 税金
     */
    public Double getTax();

    /**
     * Setter for <code>train.t_college_teaching.f_pay</code>. 实付
     */
    public void setPay(Double value);

    /**
     * Getter for <code>train.t_college_teaching.f_pay</code>. 实付
     */
    public Double getPay();

    /**
     * Setter for <code>train.t_college_teaching.f_course_reward</code>. 课酬
     */
    public void setCourseReward(Double value);

    /**
     * Getter for <code>train.t_college_teaching.f_course_reward</code>. 课酬
     */
    public Double getCourseReward();

    /**
     * Setter for <code>train.t_college_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public void setSatisfiedDegree(Double value);

    /**
     * Getter for <code>train.t_college_teaching.f_satisfied_degree</code>. 平均满意度(十分制，小数点后保留一位)
     */
    public Double getSatisfiedDegree();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICollegeTeaching
     */
    public void from(ICollegeTeaching from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICollegeTeaching
     */
    public <E extends ICollegeTeaching> E into(E into);
}
