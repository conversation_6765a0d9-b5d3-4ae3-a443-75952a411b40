/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.LimitDefaultConfiguration;
import com.zxy.product.train.jooq.tables.interfaces.ILimitDefaultConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record2;
import org.jooq.Row2;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LimitDefaultConfigurationRecord extends UpdatableRecordImpl<LimitDefaultConfigurationRecord> implements Record2<String, Integer>, ILimitDefaultConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_limit_default_configuration.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_limit_default_configuration.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_limit_default_configuration.f_default_value</code>. 默认值
     */
    @Override
    public void setDefaultValue(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_limit_default_configuration.f_default_value</code>. 默认值
     */
    @Override
    public Integer getDefaultValue() {
        return (Integer) get(1);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record2 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row2<String, Integer> fieldsRow() {
        return (Row2) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row2<String, Integer> valuesRow() {
        return (Row2) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION.DEFAULT_VALUE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getDefaultValue();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitDefaultConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitDefaultConfigurationRecord value2(Integer value) {
        setDefaultValue(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitDefaultConfigurationRecord values(String value1, Integer value2) {
        value1(value1);
        value2(value2);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILimitDefaultConfiguration from) {
        setId(from.getId());
        setDefaultValue(from.getDefaultValue());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILimitDefaultConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LimitDefaultConfigurationRecord
     */
    public LimitDefaultConfigurationRecord() {
        super(LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION);
    }

    /**
     * Create a detached, initialised LimitDefaultConfigurationRecord
     */
    public LimitDefaultConfigurationRecord(String id, Integer defaultValue) {
        super(LimitDefaultConfiguration.LIMIT_DEFAULT_CONFIGURATION);

        set(0, id);
        set(1, defaultValue);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.LimitDefaultConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.LimitDefaultConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.LimitDefaultConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
