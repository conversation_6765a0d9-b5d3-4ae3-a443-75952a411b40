/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamActivityTaskRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习活动-任务关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamActivityTask extends TableImpl<StudyTeamActivityTaskRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_activity_task</code>
     */
    public static final StudyTeamActivityTask STUDY_TEAM_ACTIVITY_TASK = new StudyTeamActivityTask();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamActivityTaskRecord> getRecordType() {
        return StudyTeamActivityTaskRecord.class;
    }

    /**
     * The column <code>train.t_study_team_activity_task.f_id</code>. ID
     */
    public final TableField<StudyTeamActivityTaskRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_study_team_activity_task.f_activity_id</code>. 活动id
     */
    public final TableField<StudyTeamActivityTaskRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "活动id");

    /**
     * The column <code>train.t_study_team_activity_task.f_business_id</code>. 业务id
     */
    public final TableField<StudyTeamActivityTaskRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "业务id");

    /**
     * The column <code>train.t_study_team_activity_task.f_business_type</code>. 业务类型：1.课程
     */
    public final TableField<StudyTeamActivityTaskRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "业务类型：1.课程");

    /**
     * The column <code>train.t_study_team_activity_task.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamActivityTaskRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team_activity_task</code> table reference
     */
    public StudyTeamActivityTask() {
        this("t_study_team_activity_task", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_activity_task</code> table reference
     */
    public StudyTeamActivityTask(String alias) {
        this(alias, STUDY_TEAM_ACTIVITY_TASK);
    }

    private StudyTeamActivityTask(String alias, Table<StudyTeamActivityTaskRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamActivityTask(String alias, Table<StudyTeamActivityTaskRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习活动-任务关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamActivityTaskRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_ACTIVITY_TASK_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamActivityTaskRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamActivityTaskRecord>>asList(Keys.KEY_T_STUDY_TEAM_ACTIVITY_TASK_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityTask as(String alias) {
        return new StudyTeamActivityTask(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamActivityTask rename(String name) {
        return new StudyTeamActivityTask(name, null);
    }
}
