/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ResearchQuestionaryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchQuestionary extends TableImpl<ResearchQuestionaryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_research_questionary</code>
     */
    public static final ResearchQuestionary RESEARCH_QUESTIONARY = new ResearchQuestionary();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchQuestionaryRecord> getRecordType() {
        return ResearchQuestionaryRecord.class;
    }

    /**
     * The column <code>train.t_research_questionary.f_id</code>. 主键
     */
    public final TableField<ResearchQuestionaryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_research_questionary.f_cover_id</code>. 封面
     */
    public final TableField<ResearchQuestionaryRecord, String> COVER_ID = createField("f_cover_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "封面");

    /**
     * The column <code>train.t_research_questionary.f_organization_id</code>. 所属组织
     */
    public final TableField<ResearchQuestionaryRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所属组织");

    /**
     * The column <code>train.t_research_questionary.f_publish_organization_id</code>. 发布部门
     */
    public final TableField<ResearchQuestionaryRecord, String> PUBLISH_ORGANIZATION_ID = createField("f_publish_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "发布部门");

    /**
     * The column <code>train.t_research_questionary.f_publish_member_id</code>. 发布人
     */
    public final TableField<ResearchQuestionaryRecord, String> PUBLISH_MEMBER_ID = createField("f_publish_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "发布人");

    /**
     * The column <code>train.t_research_questionary.f_start_time</code>. 开始时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "开始时间");

    /**
     * The column <code>train.t_research_questionary.f_end_time</code>. 结束时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "结束时间");

    /**
     * The column <code>train.t_research_questionary.f_is_anonymity</code>. 是否匿名 1：是 0： 否
     */
    public final TableField<ResearchQuestionaryRecord, Integer> IS_ANONYMITY = createField("f_is_anonymity", org.jooq.impl.SQLDataType.INTEGER, this, "是否匿名 1：是 0： 否");

    /**
     * The column <code>train.t_research_questionary.f_permit_view_count</code>. 是否允许查看统计结果 1:是 0： 否
     */
    public final TableField<ResearchQuestionaryRecord, Integer> PERMIT_VIEW_COUNT = createField("f_permit_view_count", org.jooq.impl.SQLDataType.INTEGER, this, "是否允许查看统计结果 1:是 0： 否");

    /**
     * The column <code>train.t_research_questionary.f_questionary_detail</code>. 问卷须知
     */
    public final TableField<ResearchQuestionaryRecord, String> QUESTIONARY_DETAIL = createField("f_questionary_detail", org.jooq.impl.SQLDataType.VARCHAR.length(3000), this, "问卷须知");

    /**
     * The column <code>train.t_research_questionary.f_create_time</code>. 创建时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_research_questionary.f_name</code>. 名字
     */
    public final TableField<ResearchQuestionaryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "名字");

    /**
     * The column <code>train.t_research_questionary.f_type</code>. 类型 1：调研 2：评估问卷
     */
    public final TableField<ResearchQuestionaryRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "类型 1：调研 2：评估问卷");

    /**
     * The column <code>train.t_research_questionary.f_status</code>. 1:已发布；0：未发布
     */
    public final TableField<ResearchQuestionaryRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "1:已发布；0：未发布");

    /**
     * The column <code>train.t_research_questionary.f_publish_time</code>. 发布时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> PUBLISH_TIME = createField("f_publish_time", org.jooq.impl.SQLDataType.BIGINT, this, "发布时间");

    /**
     * The column <code>train.t_research_questionary.f_push_personal_center</code>.
     */
    public final TableField<ResearchQuestionaryRecord, Integer> PUSH_PERSONAL_CENTER = createField("f_push_personal_center", org.jooq.impl.SQLDataType.INTEGER, this, "");

    /**
     * The column <code>train.t_research_questionary.f_class_id</code>. 班级ID
     */
    public final TableField<ResearchQuestionaryRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_research_questionary.f_answer_paper_rule</code>. 答卷方式 1:一页多题 2：一页一题
     */
    public final TableField<ResearchQuestionaryRecord, Integer> ANSWER_PAPER_RULE = createField("f_answer_paper_rule", org.jooq.impl.SQLDataType.INTEGER, this, "答卷方式 1:一页多题 2：一页一题");

    /**
     * The column <code>train.t_research_questionary.f_member_id</code>. 领导id
     */
    public final TableField<ResearchQuestionaryRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "领导id");

    /**
     * The column <code>train.t_research_questionary.f_is_ensemble</code>. type=8时生效 0：周满意度 1：整体满意度
     */
    public final TableField<ResearchQuestionaryRecord, Integer> IS_ENSEMBLE = createField("f_is_ensemble", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "type=8时生效 0：周满意度 1：整体满意度");

    /**
     * The column <code>train.t_research_questionary.f_class_theme_id</code>. 周主题id
     */
    public final TableField<ResearchQuestionaryRecord, String> CLASS_THEME_ID = createField("f_class_theme_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "周主题id");

    /**
     * Create a <code>train.t_research_questionary</code> table reference
     */
    public ResearchQuestionary() {
        this("t_research_questionary", null);
    }

    /**
     * Create an aliased <code>train.t_research_questionary</code> table reference
     */
    public ResearchQuestionary(String alias) {
        this(alias, RESEARCH_QUESTIONARY);
    }

    private ResearchQuestionary(String alias, Table<ResearchQuestionaryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchQuestionary(String alias, Table<ResearchQuestionaryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchQuestionaryRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_QUESTIONARY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchQuestionaryRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchQuestionaryRecord>>asList(Keys.KEY_T_RESEARCH_QUESTIONARY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionary as(String alias) {
        return new ResearchQuestionary(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchQuestionary rename(String name) {
        return new ResearchQuestionary(name, null);
    }
}
