/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassInfoRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassInfo extends TableImpl<ClassInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_info</code>
     */
    public static final ClassInfo CLASS_INFO = new ClassInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassInfoRecord> getRecordType() {
        return ClassInfoRecord.class;
    }

    /**
     * The column <code>train.t_class_info.f_id</code>. 表id
     */
    public final TableField<ClassInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_class_info.f_project_id</code>. 计划id
     */
    public final TableField<ClassInfoRecord, String> PROJECT_ID = createField("f_project_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "计划id");

    /**
     * The column <code>train.t_class_info.f_class_teacher_phone</code>. 班主任电话
     */
    public final TableField<ClassInfoRecord, String> CLASS_TEACHER_PHONE = createField("f_class_teacher_phone", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班主任电话");

    /**
     * The column <code>train.t_class_info.f_class_teacher</code>. 班主任
     */
    public final TableField<ClassInfoRecord, String> CLASS_TEACHER = createField("f_class_teacher", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班主任");

    /**
     * The column <code>train.t_class_info.f_arrive_date</code>. 报到日
     */
    public final TableField<ClassInfoRecord, Long> ARRIVE_DATE = createField("f_arrive_date", org.jooq.impl.SQLDataType.BIGINT, this, "报到日");

    /**
     * The column <code>train.t_class_info.f_return_date</code>. 返程日
     */
    public final TableField<ClassInfoRecord, Long> RETURN_DATE = createField("f_return_date", org.jooq.impl.SQLDataType.BIGINT, this, "返程日");

    /**
     * The column <code>train.t_class_info.f_is_outside</code>. 是否外部举办（0否，1是）
     */
    public final TableField<ClassInfoRecord, Integer> IS_OUTSIDE = createField("f_is_outside", org.jooq.impl.SQLDataType.INTEGER, this, "是否外部举办（0否，1是）");

    /**
     * The column <code>train.t_class_info.f_survey_type</code>. 需求调研方式
     */
    public final TableField<ClassInfoRecord, String> SURVEY_TYPE = createField("f_survey_type", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "需求调研方式");

    /**
     * The column <code>train.t_class_info.f_target</code>. 培训目标
     */
    public final TableField<ClassInfoRecord, String> TARGET = createField("f_target", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "培训目标");

    /**
     * The column <code>train.t_class_info.f_class_info_type</code>. 班级类别
     */
    public final TableField<ClassInfoRecord, String> CLASS_INFO_TYPE = createField("f_class_info_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级类别");

    /**
     * The column <code>train.t_class_info.f_student_type</code>. 人员类别
     */
    public final TableField<ClassInfoRecord, String> STUDENT_TYPE = createField("f_student_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "人员类别");

    /**
     * The column <code>train.t_class_info.f_simple_type</code>. 补贴类型
     */
    public final TableField<ClassInfoRecord, String> SIMPLE_TYPE = createField("f_simple_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "补贴类型");

    /**
     * The column <code>train.t_class_info.f_is_plan</code>. 是否计划内
     */
    public final TableField<ClassInfoRecord, Integer> IS_PLAN = createField("f_is_plan", org.jooq.impl.SQLDataType.INTEGER, this, "是否计划内");

    /**
     * The column <code>train.t_class_info.f_status</code>. 班级状态（1未实施、2实施中、3已实施）
     */
    public final TableField<ClassInfoRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "班级状态（1未实施、2实施中、3已实施）");

    /**
     * The column <code>train.t_class_info.f_confirm</code>. 是否提交（0否 1是）
     */
    public final TableField<ClassInfoRecord, Integer> CONFIRM = createField("f_confirm", org.jooq.impl.SQLDataType.INTEGER, this, "是否提交（0否 1是）");

    /**
     * The column <code>train.t_class_info.f_group_id</code>. 分组id
     */
    public final TableField<ClassInfoRecord, String> GROUP_ID = createField("f_group_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "分组id");

    /**
     * The column <code>train.t_class_info.f_short_name</code>. 短名称
     */
    public final TableField<ClassInfoRecord, String> SHORT_NAME = createField("f_short_name", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "短名称");

    /**
     * The column <code>train.t_class_info.f_group_order</code>. 分组排序
     */
    public final TableField<ClassInfoRecord, Integer> GROUP_ORDER = createField("f_group_order", org.jooq.impl.SQLDataType.INTEGER, this, "分组排序");

    /**
     * The column <code>train.t_class_info.f_create_time</code>. 创建时间
     */
    public final TableField<ClassInfoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_info.f_create_member</code>. 创建人
     */
    public final TableField<ClassInfoRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_info.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassInfoRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_info.f_implementation_year</code>. 实施年
     */
    public final TableField<ClassInfoRecord, Integer> IMPLEMENTATION_YEAR = createField("f_implementation_year", org.jooq.impl.SQLDataType.INTEGER, this, "实施年");

    /**
     * The column <code>train.t_class_info.f_implementation_month</code>. 实施月
     */
    public final TableField<ClassInfoRecord, Integer> IMPLEMENTATION_MONTH = createField("f_implementation_month", org.jooq.impl.SQLDataType.INTEGER, this, "实施月");

    /**
     * The column <code>train.t_class_info.f_member_satisfaction</code>. 学员满意率
     */
    public final TableField<ClassInfoRecord, Double> MEMBER_SATISFACTION = createField("f_member_satisfaction", org.jooq.impl.SQLDataType.FLOAT, this, "学员满意率");

    /**
     * The column <code>train.t_class_info.f_class_satisfaction</code>. 课程满意率
     */
    public final TableField<ClassInfoRecord, Double> CLASS_SATISFACTION = createField("f_class_satisfaction", org.jooq.impl.SQLDataType.FLOAT, this, "课程满意率");

    /**
     * The column <code>train.t_class_info.f_trainee_num</code>. 实际参训人数
     */
    public final TableField<ClassInfoRecord, Integer> TRAINEE_NUM = createField("f_trainee_num", org.jooq.impl.SQLDataType.INTEGER, this, "实际参训人数");

    /**
     * The column <code>train.t_class_info.f_submit_num</code>. 满意度问卷提交数
     */
    public final TableField<ClassInfoRecord, Integer> SUBMIT_NUM = createField("f_submit_num", org.jooq.impl.SQLDataType.INTEGER, this, "满意度问卷提交数");

    /**
     * The column <code>train.t_class_info.f_notice</code>. 是否发布 1：已发布
     */
    public final TableField<ClassInfoRecord, Integer> NOTICE = createField("f_notice", org.jooq.impl.SQLDataType.INTEGER, this, "是否发布 1：已发布");

    /**
     * The column <code>train.t_class_info.f_resource_status</code>. 预定资源状态
     */
    public final TableField<ClassInfoRecord, Integer> RESOURCE_STATUS = createField("f_resource_status", org.jooq.impl.SQLDataType.INTEGER, this, "预定资源状态");

    /**
     * The column <code>train.t_class_info.f_totality_satisfied</code>. 总体满意度
     */
    public final TableField<ClassInfoRecord, Double> TOTALITY_SATISFIED = createField("f_totality_satisfied", org.jooq.impl.SQLDataType.DOUBLE, this, "总体满意度");

    /**
     * The column <code>train.t_class_info.f_course_satisfied</code>. 课程满意度
     */
    public final TableField<ClassInfoRecord, Double> COURSE_SATISFIED = createField("f_course_satisfied", org.jooq.impl.SQLDataType.DOUBLE, this, "课程满意度");

    /**
     * The column <code>train.t_class_info.f_organization_id</code>. 需求单位
     */
    public final TableField<ClassInfoRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "需求单位");

    /**
     * The column <code>train.t_class_info.f_project_source</code>. 培训来源 1：集团级 2：省级
     */
    public final TableField<ClassInfoRecord, Integer> PROJECT_SOURCE = createField("f_project_source", org.jooq.impl.SQLDataType.INTEGER, this, "培训来源 1：集团级 2：省级");

    /**
     * The column <code>train.t_class_info.f_sort</code>. 排序
     */
    public final TableField<ClassInfoRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_class_info.f_is_overproof</code>. 课酬是否超标：0,全部超标；1，单门课酬超标；2，课酬总额超标
     */
    public final TableField<ClassInfoRecord, Integer> IS_OVERPROOF = createField("f_is_overproof", org.jooq.impl.SQLDataType.INTEGER, this, "课酬是否超标：0,全部超标；1，单门课酬超标；2，课酬总额超标");

    /**
     * The column <code>train.t_class_info.f_four_degrees_submit_num</code>. 四度问卷提交数
     */
    public final TableField<ClassInfoRecord, Integer> FOUR_DEGREES_SUBMIT_NUM = createField("f_four_degrees_submit_num", org.jooq.impl.SQLDataType.INTEGER, this, "四度问卷提交数");

    /**
     * The column <code>train.t_class_info.f_ability_submit_num</code>. 能力习得问卷提交数
     */
    public final TableField<ClassInfoRecord, Integer> ABILITY_SUBMIT_NUM = createField("f_ability_submit_num", org.jooq.impl.SQLDataType.INTEGER, this, "能力习得问卷提交数");

    /**
     * The column <code>train.t_class_info.f_superior_leadership_submit_num</code>. 上级领导提交数
     */
    public final TableField<ClassInfoRecord, Integer> SUPERIOR_LEADERSHIP_SUBMIT_NUM = createField("f_superior_leadership_submit_num", org.jooq.impl.SQLDataType.INTEGER, this, "上级领导提交数");

    /**
     * The column <code>train.t_class_info.f_questionnaire_status</code>. 问卷状态
     */
    public final TableField<ClassInfoRecord, Integer> QUESTIONNAIRE_STATUS = createField("f_questionnaire_status", org.jooq.impl.SQLDataType.INTEGER, this, "问卷状态");

    /**
     * The column <code>train.t_class_info.f_course_salary</code>. 是否确认课酬
     */
    public final TableField<ClassInfoRecord, Integer> COURSE_SALARY = createField("f_course_salary", org.jooq.impl.SQLDataType.INTEGER, this, "是否确认课酬");

    /**
     * The column <code>train.t_class_info.f_special_class</code>. 是否为特殊班级(0为正常，1为特殊)
     */
    public final TableField<ClassInfoRecord, Integer> SPECIAL_CLASS = createField("f_special_class", org.jooq.impl.SQLDataType.INTEGER, this, "是否为特殊班级(0为正常，1为特殊)");

    /**
     * The column <code>train.t_class_info.f_class_level</code>. 培训班级别【null,0：普通班级；1：高管班】
     */
    public final TableField<ClassInfoRecord, Integer> CLASS_LEVEL = createField("f_class_level", org.jooq.impl.SQLDataType.INTEGER, this, "培训班级别【null,0：普通班级；1：高管班】");

    /**
     * Create a <code>train.t_class_info</code> table reference
     */
    public ClassInfo() {
        this("t_class_info", null);
    }

    /**
     * Create an aliased <code>train.t_class_info</code> table reference
     */
    public ClassInfo(String alias) {
        this(alias, CLASS_INFO);
    }

    private ClassInfo(String alias, Table<ClassInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassInfo(String alias, Table<ClassInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassInfoRecord>>asList(Keys.KEY_T_CLASS_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassInfo as(String alias) {
        return new ClassInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassInfo rename(String name) {
        return new ClassInfo(name, null);
    }
}
