/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 讲师擅长课程列表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILecturerAdeptCourse extends Serializable {

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_id</code>. 系统ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_id</code>. 系统ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_lecturer_id</code>. 讲师ID
     */
    public void setLecturerId(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_lecturer_id</code>. 讲师ID
     */
    public String getLecturerId();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_course_id</code>. 所擅长的课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_course_id</code>. 所擅长的课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_reference_time</code>. 参考时长
     */
    public void setReferenceTime(Double value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_reference_time</code>. 参考时长
     */
    public Double getReferenceTime();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_reference_remuneration</code>. 参考课酬(元)
     */
    public void setReferenceRemuneration(Double value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_reference_remuneration</code>. 参考课酬(元)
     */
    public Double getReferenceRemuneration();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_course_url</code>. 授课视频URL
     */
    public void setCourseUrl(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_course_url</code>. 授课视频URL
     */
    public String getCourseUrl();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_attachment_id</code>. 附件ID
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_attachment_id</code>. 附件ID
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_lecturer_adept_course.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_lecturer_adept_course.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILecturerAdeptCourse
     */
    public void from(ILecturerAdeptCourse from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILecturerAdeptCourse
     */
    public <E extends ILecturerAdeptCourse> E into(E into);
}
