/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassSignupInfoRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassSignupInfo extends TableImpl<ClassSignupInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_signup_info</code>
     */
    public static final ClassSignupInfo CLASS_SIGNUP_INFO = new ClassSignupInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassSignupInfoRecord> getRecordType() {
        return ClassSignupInfoRecord.class;
    }

    /**
     * The column <code>train.t_class_signup_info.f_id</code>.
     */
    public final TableField<ClassSignupInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_signup_info.f_class_id</code>. 培训班ID
     */
    public final TableField<ClassSignupInfoRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训班ID");

    /**
     * The column <code>train.t_class_signup_info.f_is_open</code>. 是否开放报名（0否 1是）
     */
    public final TableField<ClassSignupInfoRecord, Integer> IS_OPEN = createField("f_is_open", org.jooq.impl.SQLDataType.INTEGER, this, "是否开放报名（0否 1是）");

    /**
     * The column <code>train.t_class_signup_info.f_start_time</code>. 报名开始时间
     */
    public final TableField<ClassSignupInfoRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT, this, "报名开始时间");

    /**
     * The column <code>train.t_class_signup_info.f_end_time</code>. 报名结束时间
     */
    public final TableField<ClassSignupInfoRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT, this, "报名结束时间");

    /**
     * The column <code>train.t_class_signup_info.f_signup_code</code>. 报名码
     */
    public final TableField<ClassSignupInfoRecord, String> SIGNUP_CODE = createField("f_signup_code", org.jooq.impl.SQLDataType.VARCHAR.length(20), this, "报名码");

    /**
     * The column <code>train.t_class_signup_info.f_using_two_brings</code>. 是否启用2个带来（0否 1是）
     */
    public final TableField<ClassSignupInfoRecord, Integer> USING_TWO_BRINGS = createField("f_using_two_brings", org.jooq.impl.SQLDataType.INTEGER, this, "是否启用2个带来（0否 1是）");

    /**
     * The column <code>train.t_class_signup_info.f_question1</code>. 问题1
     */
    public final TableField<ClassSignupInfoRecord, String> QUESTION1 = createField("f_question1", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "问题1");

    /**
     * The column <code>train.t_class_signup_info.f_question2</code>. 问题2
     */
    public final TableField<ClassSignupInfoRecord, String> QUESTION2 = createField("f_question2", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "问题2");

    /**
     * The column <code>train.t_class_signup_info.f_signup_url</code>. 报名链接
     */
    public final TableField<ClassSignupInfoRecord, String> SIGNUP_URL = createField("f_signup_url", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "报名链接");

    /**
     * The column <code>train.t_class_signup_info.f_create_member</code>. 创建人
     */
    public final TableField<ClassSignupInfoRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_signup_info.f_create_time</code>. 创建时间
     */
    public final TableField<ClassSignupInfoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_signup_info.f_face_attendance</code>. 是否人脸考勤（0否 1是）
     */
    public final TableField<ClassSignupInfoRecord, Integer> FACE_ATTENDANCE = createField("f_face_attendance", org.jooq.impl.SQLDataType.INTEGER, this, "是否人脸考勤（0否 1是）");

    /**
     * Create a <code>train.t_class_signup_info</code> table reference
     */
    public ClassSignupInfo() {
        this("t_class_signup_info", null);
    }

    /**
     * Create an aliased <code>train.t_class_signup_info</code> table reference
     */
    public ClassSignupInfo(String alias) {
        this(alias, CLASS_SIGNUP_INFO);
    }

    private ClassSignupInfo(String alias, Table<ClassSignupInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassSignupInfo(String alias, Table<ClassSignupInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassSignupInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_SIGNUP_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassSignupInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassSignupInfoRecord>>asList(Keys.KEY_T_CLASS_SIGNUP_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassSignupInfo as(String alias) {
        return new ClassSignupInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassSignupInfo rename(String name) {
        return new ClassSignupInfo(name, null);
    }
}
