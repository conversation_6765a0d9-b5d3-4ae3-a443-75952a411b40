/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IQuestionAttr extends Serializable {

    /**
     * Setter for <code>train.t_question_attr.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_question_attr.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_question_attr.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_question_attr.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_question_attr.f_type</code>. 类型
     */
    public void setType(String value);

    /**
     * Getter for <code>train.t_question_attr.f_type</code>. 类型
     */
    public String getType();

    /**
     * Setter for <code>train.t_question_attr.f_name</code>. 属性名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_question_attr.f_name</code>. 属性名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_question_attr.f_value</code>. 属性值
     */
    public void setValue(String value);

    /**
     * Getter for <code>train.t_question_attr.f_value</code>. 属性值
     */
    public String getValue();

    /**
     * Setter for <code>train.t_question_attr.f_question_id</code>. 所属试题
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>train.t_question_attr.f_question_id</code>. 所属试题
     */
    public String getQuestionId();

    /**
     * Setter for <code>train.t_question_attr.f_score</code>. 评估问卷的题目选项分数
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>train.t_question_attr.f_score</code>. 评估问卷的题目选项分数
     */
    public Integer getScore();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IQuestionAttr
     */
    public void from(IQuestionAttr from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IQuestionAttr
     */
    public <E extends IQuestionAttr> E into(E into);
}
