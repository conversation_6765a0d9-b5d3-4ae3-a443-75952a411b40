/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 作业附件关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ITaskAttach extends Serializable {

    /**
     * Setter for <code>train.t_task_attach.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_task_attach.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_task_attach.f_task_id</code>. 作业ID
     */
    public void setTaskId(String value);

    /**
     * Getter for <code>train.t_task_attach.f_task_id</code>. 作业ID
     */
    public String getTaskId();

    /**
     * Setter for <code>train.t_task_attach.f_attachment_id</code>. 附件ID
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_task_attach.f_attachment_id</code>. 附件ID
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_task_attach.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_task_attach.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    /**
     * Setter for <code>train.t_task_attach.f_attachment_type</code>. 类型
     */
    public void setAttachmentType(String value);

    /**
     * Getter for <code>train.t_task_attach.f_attachment_type</code>. 类型
     */
    public String getAttachmentType();

    /**
     * Setter for <code>train.t_task_attach.f_create_member_id</code>. 创建人id
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>train.t_task_attach.f_create_member_id</code>. 创建人id
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>train.t_task_attach.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_task_attach.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ITaskAttach
     */
    public void from(ITaskAttach from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ITaskAttach
     */
    public <E extends ITaskAttach> E into(E into);
}
