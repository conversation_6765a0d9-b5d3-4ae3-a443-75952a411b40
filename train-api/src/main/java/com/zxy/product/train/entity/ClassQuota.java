package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.ClassQuotaEntity;

public class ClassQuota extends ClassQuotaEntity {

	/**
	 * Created by 田聪 on 2017/02/24
	 */
	private static final long serialVersionUID = 1159488851906586479L;


	public static final int TYPE_WHOLE = 1;		// 配额类型：1整体配额
	public static final int TYPE_DISPER = 2;	// 配额类型：2分省配额
	
	public static final int MANUAL_APPROVE = 0;	// 是否自动审批： 0否
	public static final int AUTO_APPROVE = 1;	// 是否自动审批： 1是
	
	private Integer amount;	// 计划人数
	private Integer signUpNumber; // 报名人数
	private List<Trainee> trainees;//人员列表详情
	private List<ClassQuotaDetail> classQuotaDetails; // 配额详情
	/**
	 * fc
	 */
	private String className;//班级名称为审计提供

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public Integer getSignUpNumber() {
		return signUpNumber;
	}

	public void setSignUpNumber(Integer signUpNumber) {
		this.signUpNumber = signUpNumber;
	}

	public List<ClassQuotaDetail> getClassQuotaDetails() {
		return classQuotaDetails;
	}

	public void setClassQuotaDetails(List<ClassQuotaDetail> classQuotaDetails) {
		this.classQuotaDetails = classQuotaDetails;
	}

	public List<Trainee> getTrainees() {
		return trainees;
	}

	public void setTrainees(List<Trainee> trainees) {
		this.trainees = trainees;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}
}
