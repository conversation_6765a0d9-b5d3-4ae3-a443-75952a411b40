package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassRequiredEntity;

/**
 * Created by chun on 2017/2/24.
 */
public class ClassRequired extends ClassRequiredEntity{

    private static final long serialVersionUID = -4804591401859013792L;

    public static final int DELETE_FLASE=0;	//删除状态：未删除
    public static final int DELETE_TRUE=1;	//删除状态，已删除
    public static final int REQ_STATUS = 0; //必修课状态

    private String themeName;//主题名称
    private String status;// 返回班级详情页状态语
    private Integer day;
    private int config;//是否必修 0:是;1:否

    public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

	public int getConfig() {
		return config;
	}

	public void setConfig(int config) {
		this.config = config;
	}

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
