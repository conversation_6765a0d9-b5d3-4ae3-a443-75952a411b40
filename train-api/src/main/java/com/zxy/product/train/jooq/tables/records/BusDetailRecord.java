/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.BusDetail;
import com.zxy.product.train.jooq.tables.interfaces.IBusDetail;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record3;
import org.jooq.Row3;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 班车详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BusDetailRecord extends UpdatableRecordImpl<BusDetailRecord> implements Record3<String, String, String>, IBusDetail {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_bus_detail.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_bus_detail.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_bus_detail.f_member_id</code>. 员工ID
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_bus_detail.f_member_id</code>. 员工ID
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_bus_detail.f_option_id</code>. 登记信息(选项主题表ID)
     */
    @Override
    public void setOptionId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_bus_detail.f_option_id</code>. 登记信息(选项主题表ID)
     */
    @Override
    public String getOptionId() {
        return (String) get(2);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record3 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row3<String, String, String> fieldsRow() {
        return (Row3) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row3<String, String, String> valuesRow() {
        return (Row3) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return BusDetail.BUS_DETAIL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return BusDetail.BUS_DETAIL.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return BusDetail.BUS_DETAIL.OPTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getOptionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusDetailRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusDetailRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusDetailRecord value3(String value) {
        setOptionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusDetailRecord values(String value1, String value2, String value3) {
        value1(value1);
        value2(value2);
        value3(value3);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IBusDetail from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setOptionId(from.getOptionId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IBusDetail> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached BusDetailRecord
     */
    public BusDetailRecord() {
        super(BusDetail.BUS_DETAIL);
    }

    /**
     * Create a detached, initialised BusDetailRecord
     */
    public BusDetailRecord(String id, String memberId, String optionId) {
        super(BusDetail.BUS_DETAIL);

        set(0, id);
        set(1, memberId);
        set(2, optionId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.BusDetailEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.BusDetailEntity pojo = (com.zxy.product.train.jooq.tables.pojos.BusDetailEntity)source;
        pojo.into(this);
        return true;
    }
}
