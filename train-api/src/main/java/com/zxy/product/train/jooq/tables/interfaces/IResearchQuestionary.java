/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IResearchQuestionary extends Serializable {

    /**
     * Setter for <code>train.t_research_questionary.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_research_questionary.f_cover_id</code>. 封面
     */
    public void setCoverId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_cover_id</code>. 封面
     */
    public String getCoverId();

    /**
     * Setter for <code>train.t_research_questionary.f_organization_id</code>. 所属组织
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_organization_id</code>. 所属组织
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_research_questionary.f_publish_organization_id</code>. 发布部门
     */
    public void setPublishOrganizationId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_publish_organization_id</code>. 发布部门
     */
    public String getPublishOrganizationId();

    /**
     * Setter for <code>train.t_research_questionary.f_publish_member_id</code>. 发布人
     */
    public void setPublishMemberId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_publish_member_id</code>. 发布人
     */
    public String getPublishMemberId();

    /**
     * Setter for <code>train.t_research_questionary.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_research_questionary.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_research_questionary.f_end_time</code>. 结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_research_questionary.f_end_time</code>. 结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_research_questionary.f_is_anonymity</code>. 是否匿名 1：是 0： 否
     */
    public void setIsAnonymity(Integer value);

    /**
     * Getter for <code>train.t_research_questionary.f_is_anonymity</code>. 是否匿名 1：是 0： 否
     */
    public Integer getIsAnonymity();

    /**
     * Setter for <code>train.t_research_questionary.f_permit_view_count</code>. 是否允许查看统计结果 1:是 0： 否
     */
    public void setPermitViewCount(Integer value);

    /**
     * Getter for <code>train.t_research_questionary.f_permit_view_count</code>. 是否允许查看统计结果 1:是 0： 否
     */
    public Integer getPermitViewCount();

    /**
     * Setter for <code>train.t_research_questionary.f_questionary_detail</code>. 问卷须知
     */
    public void setQuestionaryDetail(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_questionary_detail</code>. 问卷须知
     */
    public String getQuestionaryDetail();

    /**
     * Setter for <code>train.t_research_questionary.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_research_questionary.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_research_questionary.f_name</code>. 名字
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_name</code>. 名字
     */
    public String getName();

    /**
     * Setter for <code>train.t_research_questionary.f_type</code>. 类型 1：调研 2：评估问卷
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_research_questionary.f_type</code>. 类型 1：调研 2：评估问卷
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_research_questionary.f_status</code>. 1:已发布；0：未发布
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_research_questionary.f_status</code>. 1:已发布；0：未发布
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_research_questionary.f_publish_time</code>. 发布时间
     */
    public void setPublishTime(Long value);

    /**
     * Getter for <code>train.t_research_questionary.f_publish_time</code>. 发布时间
     */
    public Long getPublishTime();

    /**
     * Setter for <code>train.t_research_questionary.f_push_personal_center</code>.
     */
    public void setPushPersonalCenter(Integer value);

    /**
     * Getter for <code>train.t_research_questionary.f_push_personal_center</code>.
     */
    public Integer getPushPersonalCenter();

    /**
     * Setter for <code>train.t_research_questionary.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_research_questionary.f_answer_paper_rule</code>. 答卷方式 1:一页多题 2：一页一题
     */
    public void setAnswerPaperRule(Integer value);

    /**
     * Getter for <code>train.t_research_questionary.f_answer_paper_rule</code>. 答卷方式 1:一页多题 2：一页一题
     */
    public Integer getAnswerPaperRule();

    /**
     * Setter for <code>train.t_research_questionary.f_member_id</code>. 领导id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_member_id</code>. 领导id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_research_questionary.f_is_ensemble</code>. type=8时生效 0：周满意度 1：整体满意度
     */
    public void setIsEnsemble(Integer value);

    /**
     * Getter for <code>train.t_research_questionary.f_is_ensemble</code>. type=8时生效 0：周满意度 1：整体满意度
     */
    public Integer getIsEnsemble();

    /**
     * Setter for <code>train.t_research_questionary.f_class_theme_id</code>. 周主题id
     */
    public void setClassThemeId(String value);

    /**
     * Getter for <code>train.t_research_questionary.f_class_theme_id</code>. 周主题id
     */
    public String getClassThemeId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IResearchQuestionary
     */
    public void from(IResearchQuestionary from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IResearchQuestionary
     */
    public <E extends IResearchQuestionary> E into(E into);
}
