/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IDimensionQuestion extends Serializable {

    /**
     * Setter for <code>train.t_dimension_question.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_dimension_question.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_dimension_question.f_create_time</code>. 时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_dimension_question.f_create_time</code>. 时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_dimension_question.f_dimension_id</code>. 维度
     */
    public void setDimensionId(String value);

    /**
     * Getter for <code>train.t_dimension_question.f_dimension_id</code>. 维度
     */
    public String getDimensionId();

    /**
     * Setter for <code>train.t_dimension_question.f_question_id</code>. 题目
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>train.t_dimension_question.f_question_id</code>. 题目
     */
    public String getQuestionId();

    /**
     * Setter for <code>train.t_dimension_question.f_score</code>. 分数
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>train.t_dimension_question.f_score</code>. 分数
     */
    public Integer getScore();

    /**
     * Setter for <code>train.t_dimension_question.f_idea</code>. 意见
     */
    public void setIdea(String value);

    /**
     * Getter for <code>train.t_dimension_question.f_idea</code>. 意见
     */
    public String getIdea();

    /**
     * Setter for <code>train.t_dimension_question.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_dimension_question.f_order</code>. 排序
     */
    public Integer getOrder();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IDimensionQuestion
     */
    public void from(IDimensionQuestion from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IDimensionQuestion
     */
    public <E extends IDimensionQuestion> E into(E into);
}
