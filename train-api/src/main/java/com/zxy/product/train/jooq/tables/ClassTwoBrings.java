/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassTwoBringsRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassTwoBrings extends TableImpl<ClassTwoBringsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_two_brings</code>
     */
    public static final ClassTwoBrings CLASS_TWO_BRINGS = new ClassTwoBrings();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassTwoBringsRecord> getRecordType() {
        return ClassTwoBringsRecord.class;
    }

    /**
     * The column <code>train.t_class_two_brings.f_id</code>.
     */
    public final TableField<ClassTwoBringsRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_two_brings.f_member_id</code>. 人员id
     */
    public final TableField<ClassTwoBringsRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "人员id");

    /**
     * The column <code>train.t_class_two_brings.f_class_id</code>. 培训班ID
     */
    public final TableField<ClassTwoBringsRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "培训班ID");

    /**
     * The column <code>train.t_class_two_brings.f_title1</code>. 标题1
     */
    public final TableField<ClassTwoBringsRecord, String> TITLE1 = createField("f_title1", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "标题1");

    /**
     * The column <code>train.t_class_two_brings.f_content1</code>. 内容1
     */
    public final TableField<ClassTwoBringsRecord, String> CONTENT1 = createField("f_content1", org.jooq.impl.SQLDataType.VARCHAR.length(5000), this, "内容1");

    /**
     * The column <code>train.t_class_two_brings.f_title2</code>. 标题2
     */
    public final TableField<ClassTwoBringsRecord, String> TITLE2 = createField("f_title2", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "标题2");

    /**
     * The column <code>train.t_class_two_brings.f_content2</code>. 内容2
     */
    public final TableField<ClassTwoBringsRecord, String> CONTENT2 = createField("f_content2", org.jooq.impl.SQLDataType.VARCHAR.length(5000), this, "内容2");

    /**
     * The column <code>train.t_class_two_brings.f_create_member</code>. 创建人
     */
    public final TableField<ClassTwoBringsRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_two_brings.f_create_time</code>. 创建时间
     */
    public final TableField<ClassTwoBringsRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_class_two_brings</code> table reference
     */
    public ClassTwoBrings() {
        this("t_class_two_brings", null);
    }

    /**
     * Create an aliased <code>train.t_class_two_brings</code> table reference
     */
    public ClassTwoBrings(String alias) {
        this(alias, CLASS_TWO_BRINGS);
    }

    private ClassTwoBrings(String alias, Table<ClassTwoBringsRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassTwoBrings(String alias, Table<ClassTwoBringsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassTwoBringsRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_TWO_BRINGS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassTwoBringsRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassTwoBringsRecord>>asList(Keys.KEY_T_CLASS_TWO_BRINGS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassTwoBrings as(String alias) {
        return new ClassTwoBrings(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassTwoBrings rename(String name) {
        return new ClassTwoBrings(name, null);
    }
}
