/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.TrainChatGroupInfo;
import com.zxy.product.train.jooq.tables.interfaces.ITrainChatGroupInfo;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 培训班群聊表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TrainChatGroupInfoRecord extends UpdatableRecordImpl<TrainChatGroupInfoRecord> implements Record8<String, String, String, String, String, String, Long, Timestamp>, ITrainChatGroupInfo {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_train_chat_group_info.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_train_chat_group_info.f_class_id</code>. 班级ID
     */
    @Override
    public void setClassId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_class_id</code>. 班级ID
     */
    @Override
    public String getClassId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_train_chat_group_info.f_chat_id</code>. 群聊ID
     */
    @Override
    public void setChatId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_chat_id</code>. 群聊ID
     */
    @Override
    public String getChatId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_train_chat_group_info.f_conversion_id</code>. 会话ID
     */
    @Override
    public void setConversionId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_conversion_id</code>. 会话ID
     */
    @Override
    public String getConversionId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_train_chat_group_info.f_chat_name</code>. 群聊名称
     */
    @Override
    public void setChatName(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_chat_name</code>. 群聊名称
     */
    @Override
    public String getChatName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_train_chat_group_info.f_member_id</code>. 班主任ID
     */
    @Override
    public void setMemberId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_member_id</code>. 班主任ID
     */
    @Override
    public String getMemberId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_train_chat_group_info.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>train.t_train_chat_group_info.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_train_chat_group_info.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, String, String, Long, Timestamp> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row8<String, String, String, String, String, String, Long, Timestamp> valuesRow() {
        return (Row8) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.CHAT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.CONVERSION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.CHAT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field8() {
        return TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getChatId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getConversionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getChatName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value8() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value2(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value3(String value) {
        setChatId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value4(String value) {
        setConversionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value5(String value) {
        setChatName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value6(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value7(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord value8(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TrainChatGroupInfoRecord values(String value1, String value2, String value3, String value4, String value5, String value6, Long value7, Timestamp value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ITrainChatGroupInfo from) {
        setId(from.getId());
        setClassId(from.getClassId());
        setChatId(from.getChatId());
        setConversionId(from.getConversionId());
        setChatName(from.getChatName());
        setMemberId(from.getMemberId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ITrainChatGroupInfo> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached TrainChatGroupInfoRecord
     */
    public TrainChatGroupInfoRecord() {
        super(TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO);
    }

    /**
     * Create a detached, initialised TrainChatGroupInfoRecord
     */
    public TrainChatGroupInfoRecord(String id, String classId, String chatId, String conversionId, String chatName, String memberId, Long createTime, Timestamp modifyDate) {
        super(TrainChatGroupInfo.TRAIN_CHAT_GROUP_INFO);

        set(0, id);
        set(1, classId);
        set(2, chatId);
        set(3, conversionId);
        set(4, chatName);
        set(5, memberId);
        set(6, createTime);
        set(7, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.TrainChatGroupInfoEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.TrainChatGroupInfoEntity pojo = (com.zxy.product.train.jooq.tables.pojos.TrainChatGroupInfoEntity)source;
        pojo.into(this);
        return true;
    }
}
