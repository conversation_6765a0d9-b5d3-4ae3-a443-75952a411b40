/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.DemandSideOrganizationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 需求方枚举表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DemandSideOrganization extends TableImpl<DemandSideOrganizationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_demand_side_organization</code>
     */
    public static final DemandSideOrganization DEMAND_SIDE_ORGANIZATION = new DemandSideOrganization();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DemandSideOrganizationRecord> getRecordType() {
        return DemandSideOrganizationRecord.class;
    }

    /**
     * The column <code>train.t_demand_side_organization.f_id</code>. 组织id
     */
    public final TableField<DemandSideOrganizationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "组织id");

    /**
     * The column <code>train.t_demand_side_organization.f_create_time</code>. 创建时间
     */
    public final TableField<DemandSideOrganizationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_demand_side_organization.f_name</code>. 组织名称
     */
    public final TableField<DemandSideOrganizationRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "组织名称");

    /**
     * The column <code>train.t_demand_side_organization.f_short_name</code>. 组织短名称
     */
    public final TableField<DemandSideOrganizationRecord, String> SHORT_NAME = createField("f_short_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "组织短名称");

    /**
     * The column <code>train.t_demand_side_organization.f_code</code>. 组织编码
     */
    public final TableField<DemandSideOrganizationRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "组织编码");

    /**
     * The column <code>train.t_demand_side_organization.f_ihr_code</code>. ihr新组织编码
     */
    public final TableField<DemandSideOrganizationRecord, String> IHR_CODE = createField("f_ihr_code", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "ihr新组织编码");

    /**
     * The column <code>train.t_demand_side_organization.f_parent_id</code>. 上级组织
     */
    public final TableField<DemandSideOrganizationRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "上级组织");

    /**
     * The column <code>train.t_demand_side_organization.f_path</code>. 当前节点的所有父节点
     */
    public final TableField<DemandSideOrganizationRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "当前节点的所有父节点");

    /**
     * The column <code>train.t_demand_side_organization.f_cmcc_level</code>. 公司分类(移动项目)
     */
    public final TableField<DemandSideOrganizationRecord, Integer> CMCC_LEVEL = createField("f_cmcc_level", org.jooq.impl.SQLDataType.INTEGER, this, "公司分类(移动项目)");

    /**
     * The column <code>train.t_demand_side_organization.f_level</code>. 组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)
     */
    public final TableField<DemandSideOrganizationRecord, Integer> LEVEL = createField("f_level", org.jooq.impl.SQLDataType.INTEGER, this, "组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)");

    /**
     * The column <code>train.t_demand_side_organization.f_order</code>. 排序
     */
    public final TableField<DemandSideOrganizationRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_demand_side_organization.f_cmcc_attribute</code>. 组织机构属性(移动项目)
     */
    public final TableField<DemandSideOrganizationRecord, String> CMCC_ATTRIBUTE = createField("f_cmcc_attribute", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "组织机构属性(移动项目)");

    /**
     * The column <code>train.t_demand_side_organization.f_cmcc_category</code>. 组织机构类型(移动项目)
     */
    public final TableField<DemandSideOrganizationRecord, String> CMCC_CATEGORY = createField("f_cmcc_category", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "组织机构类型(移动项目)");

    /**
     * The column <code>train.t_demand_side_organization.f_status</code>. 组织状态
     */
    public final TableField<DemandSideOrganizationRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER, this, "组织状态");

    /**
     * The column <code>train.t_demand_side_organization.f_company_id</code>. 所属机构
     */
    public final TableField<DemandSideOrganizationRecord, String> COMPANY_ID = createField("f_company_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "所属机构");

    /**
     * The column <code>train.t_demand_side_organization.f_site_id</code>. 站点id
     */
    public final TableField<DemandSideOrganizationRecord, String> SITE_ID = createField("f_site_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "站点id");

    /**
     * The column <code>train.t_demand_side_organization.f_mis_code</code>. MIS省公司简称，用于同步数据
     */
    public final TableField<DemandSideOrganizationRecord, String> MIS_CODE = createField("f_mis_code", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "MIS省公司简称，用于同步数据");

    /**
     * The column <code>train.t_demand_side_organization.f_depth</code>. 表示当前组织深度
     */
    public final TableField<DemandSideOrganizationRecord, Integer> DEPTH = createField("f_depth", org.jooq.impl.SQLDataType.INTEGER, this, "表示当前组织深度");

    /**
     * The column <code>train.t_demand_side_organization.f_type</code>.
     */
    public final TableField<DemandSideOrganizationRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "");

    /**
     * The column <code>train.t_demand_side_organization.f_area_code</code>. 区号
     */
    public final TableField<DemandSideOrganizationRecord, String> AREA_CODE = createField("f_area_code", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "区号");

    /**
     * Create a <code>train.t_demand_side_organization</code> table reference
     */
    public DemandSideOrganization() {
        this("t_demand_side_organization", null);
    }

    /**
     * Create an aliased <code>train.t_demand_side_organization</code> table reference
     */
    public DemandSideOrganization(String alias) {
        this(alias, DEMAND_SIDE_ORGANIZATION);
    }

    private DemandSideOrganization(String alias, Table<DemandSideOrganizationRecord> aliased) {
        this(alias, aliased, null);
    }

    private DemandSideOrganization(String alias, Table<DemandSideOrganizationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "需求方枚举表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<DemandSideOrganizationRecord> getPrimaryKey() {
        return Keys.KEY_T_DEMAND_SIDE_ORGANIZATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<DemandSideOrganizationRecord>> getKeys() {
        return Arrays.<UniqueKey<DemandSideOrganizationRecord>>asList(Keys.KEY_T_DEMAND_SIDE_ORGANIZATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DemandSideOrganization as(String alias) {
        return new DemandSideOrganization(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DemandSideOrganization rename(String name) {
        return new DemandSideOrganization(name, null);
    }
}
