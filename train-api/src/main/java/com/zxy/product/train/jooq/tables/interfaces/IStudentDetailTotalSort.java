/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学员明细规则排序表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudentDetailTotalSort extends Serializable {

    /**
     * Setter for <code>train.t_student_detail_total_sort.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_student_detail_total_sort.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_student_detail_total_sort.f_short_name</code>. 短名称
     */
    public void setShortName(String value);

    /**
     * Getter for <code>train.t_student_detail_total_sort.f_short_name</code>. 短名称
     */
    public String getShortName();

    /**
     * Setter for <code>train.t_student_detail_total_sort.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_student_detail_total_sort.f_sort</code>. 排序
     */
    public Integer getSort();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudentDetailTotalSort
     */
    public void from(IStudentDetailTotalSort from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudentDetailTotalSort
     */
    public <E extends IStudentDetailTotalSort> E into(E into);
}
