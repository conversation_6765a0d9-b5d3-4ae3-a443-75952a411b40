package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.F2fCourseLibraryEntity;

public class OfflineCourseLibrary extends F2fCourseLibraryEntity{


	private static final long serialVersionUID = -9026317959773260861L;
	public static final Integer SHARE_YES = 0;
	public static final Integer SHARE_NO = 1;
	public static final String URI = "activity/lecturer/offline";
	
	private List<CollegeTeaching> collegeTeachings;
	private List<OrganizationTeaching> organizationTeachings;
	private List<LecturerAdeptCourse> lecturerAdeptCourse;
	private CollectionProgrammeConfig programmeConfig;
	private String createMemberName;
	private String organizationName;
    private String createDate;
	private String updateDate;
	private Double courseUnitPrice;
	private List<CourseAttach> courseAttachList;
	
	private Boolean  isGrant;
	
	public Boolean getIsGrant() {
		return isGrant;
	}
	public void setIsGrant(Boolean isGrant) {
		this.isGrant = isGrant;
	}
	public List<CourseAttach> getCourseAttachList() {
		return courseAttachList;
	}
	public void setCourseAttachList(List<CourseAttach> courseAttachList) {
		this.courseAttachList = courseAttachList;
	}
	public CollectionProgrammeConfig getProgrammeConfig() {
		return programmeConfig;
	}
	public void setProgrammeConfig(CollectionProgrammeConfig programmeConfig) {
		this.programmeConfig = programmeConfig;
	}
	
	private String courseAttributesName;
	
	public String getCreateDate() {
		return createDate;
	}
	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}
	public String getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(String updateDate) {
		this.updateDate = updateDate;
	}
	public String getSequenceName() {
		return sequenceName;
	}
	public void setSequenceName(String sequenceName) {
		this.sequenceName = sequenceName;
	}
	private String sequenceName;  //课程分类名称

	public String getOrganizationName() {
		return organizationName;
	}
	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}
	public String getCreateMemberName() {
		return createMemberName;
	}
	public void setCreateMemberName(String createMemberName) {
		this.createMemberName = createMemberName;
	}
	public String getUpdateMemberName() {
		return updateMemberName;
	}
	public void setUpdateMemberName(String updateMemberName) {
		this.updateMemberName = updateMemberName;
	}
	private String updateMemberName;
	
	public List<LecturerAdeptCourse> getLecturerAdeptCourse() {
		return lecturerAdeptCourse;
	}
	public void setLecturerAdeptCourse(List<LecturerAdeptCourse> lecturerAdeptCourse) {
		this.lecturerAdeptCourse = lecturerAdeptCourse;
	}
	public List<CollegeTeaching> getCollegeTeachings() {
		return collegeTeachings;
	}
	public void setCollegeTeachings(List<CollegeTeaching> collegeTeachings) {
		this.collegeTeachings = collegeTeachings;
	}
	public List<OrganizationTeaching> getOrganizationTeachings() {
		return organizationTeachings;
	}
	public void setOrganizationTeachings(List<OrganizationTeaching> organizationTeachings) {
		this.organizationTeachings = organizationTeachings;
	}
	public String getCourseAttributesName() {
		return courseAttributesName;
	}
	public void setCourseAttributesName(String courseAttributesName) {
		this.courseAttributesName = courseAttributesName;
	}
	public Double getCourseUnitPrice() {
		return courseUnitPrice;
	}
	public void setCourseUnitPrice(Double courseUnitPrice) {
		this.courseUnitPrice = courseUnitPrice;
	}
	

}
