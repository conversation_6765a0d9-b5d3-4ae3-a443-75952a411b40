/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 策划实施关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPlanningImplementationRelated extends Serializable {

    /**
     * Setter for <code>train.t_planning_implementation_related.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_planning_implementation_related.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_planning_implementation_related.f_business_id</code>. 关联业务id
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>train.t_planning_implementation_related.f_business_id</code>. 关联业务id
     */
    public String getBusinessId();

    /**
     * Setter for <code>train.t_planning_implementation_related.f_organization_id</code>. 部门id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_planning_implementation_related.f_organization_id</code>. 部门id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_planning_implementation_related.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_planning_implementation_related.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_planning_implementation_related.f_type</code>. 来源（2：策划 1：实施）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_planning_implementation_related.f_type</code>. 来源（2：策划 1：实施）
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_planning_implementation_related.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_planning_implementation_related.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_planning_implementation_related.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>train.t_planning_implementation_related.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPlanningImplementationRelated
     */
    public void from(com.zxy.product.train.jooq.tables.interfaces.IPlanningImplementationRelated from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPlanningImplementationRelated
     */
    public <E extends com.zxy.product.train.jooq.tables.interfaces.IPlanningImplementationRelated> E into(E into);
}
