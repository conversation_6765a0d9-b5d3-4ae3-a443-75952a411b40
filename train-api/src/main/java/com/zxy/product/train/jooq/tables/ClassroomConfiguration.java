/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassroomConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassroomConfiguration extends TableImpl<ClassroomConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_classroom_configuration</code>
     */
    public static final ClassroomConfiguration CLASSROOM_CONFIGURATION = new ClassroomConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassroomConfigurationRecord> getRecordType() {
        return ClassroomConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_classroom_configuration.f_id</code>. 主键
     */
    public final TableField<ClassroomConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_classroom_configuration.f_configuration_id</code>. 关联配置表ID
     */
    public final TableField<ClassroomConfigurationRecord, String> CONFIGURATION_ID = createField("f_configuration_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联配置表ID");

    /**
     * The column <code>train.t_classroom_configuration.f_type_id</code>. 关联类型ID
     */
    public final TableField<ClassroomConfigurationRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER, this, "关联类型ID");

    /**
     * The column <code>train.t_classroom_configuration.f_classroom</code>.
     */
    public final TableField<ClassroomConfigurationRecord, String> CLASSROOM = createField("f_classroom", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "");

    /**
     * The column <code>train.t_classroom_configuration.f_classroom_coding</code>. 教室编号
     */
    public final TableField<ClassroomConfigurationRecord, String> CLASSROOM_CODING = createField("f_classroom_coding", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "教室编号");

    /**
     * The column <code>train.t_classroom_configuration.f_remark</code>. 备注
     */
    public final TableField<ClassroomConfigurationRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "备注");

    /**
     * The column <code>train.t_classroom_configuration.f_sort</code>. 排序
     */
    public final TableField<ClassroomConfigurationRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_classroom_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<ClassroomConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_classroom_configuration.f_create_member</code>. 创建人ID
     */
    public final TableField<ClassroomConfigurationRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_classroom_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassroomConfigurationRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * Create a <code>train.t_classroom_configuration</code> table reference
     */
    public ClassroomConfiguration() {
        this("t_classroom_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_classroom_configuration</code> table reference
     */
    public ClassroomConfiguration(String alias) {
        this(alias, CLASSROOM_CONFIGURATION);
    }

    private ClassroomConfiguration(String alias, Table<ClassroomConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassroomConfiguration(String alias, Table<ClassroomConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassroomConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASSROOM_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassroomConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassroomConfigurationRecord>>asList(Keys.KEY_T_CLASSROOM_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfiguration as(String alias) {
        return new ClassroomConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassroomConfiguration rename(String name) {
        return new ClassroomConfiguration(name, null);
    }
}
