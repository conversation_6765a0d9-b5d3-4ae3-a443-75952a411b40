/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ClassroomConfiguration;
import com.zxy.product.train.jooq.tables.interfaces.IClassroomConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassroomConfigurationRecord extends UpdatableRecordImpl<ClassroomConfigurationRecord> implements Record10<String, String, Integer, String, String, String, Integer, Long, String, Integer>, IClassroomConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_classroom_configuration.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public void setConfigurationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public String getConfigurationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public void setTypeId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public Integer getTypeId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_classroom</code>.
     */
    @Override
    public void setClassroom(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_classroom</code>.
     */
    @Override
    public String getClassroom() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_classroom_coding</code>. 教室编号
     */
    @Override
    public void setClassroomCoding(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_classroom_coding</code>. 教室编号
     */
    @Override
    public String getClassroomCoding() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_remark</code>. 备注
     */
    @Override
    public void setRemark(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_remark</code>. 备注
     */
    @Override
    public String getRemark() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_classroom_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_classroom_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, Integer, String, String, String, Integer, Long, String, Integer> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, Integer, String, String, String, Integer, Long, String, Integer> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.CONFIGURATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.TYPE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.CLASSROOM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.CLASSROOM_CODING;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.REMARK;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field8() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return ClassroomConfiguration.CLASSROOM_CONFIGURATION.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getConfigurationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getTypeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getClassroom();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getClassroomCoding();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getRemark();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value8() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value2(String value) {
        setConfigurationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value3(Integer value) {
        setTypeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value4(String value) {
        setClassroom(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value5(String value) {
        setClassroomCoding(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value6(String value) {
        setRemark(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value7(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value8(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value9(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord value10(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassroomConfigurationRecord values(String value1, String value2, Integer value3, String value4, String value5, String value6, Integer value7, Long value8, String value9, Integer value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IClassroomConfiguration from) {
        setId(from.getId());
        setConfigurationId(from.getConfigurationId());
        setTypeId(from.getTypeId());
        setClassroom(from.getClassroom());
        setClassroomCoding(from.getClassroomCoding());
        setRemark(from.getRemark());
        setSort(from.getSort());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IClassroomConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ClassroomConfigurationRecord
     */
    public ClassroomConfigurationRecord() {
        super(ClassroomConfiguration.CLASSROOM_CONFIGURATION);
    }

    /**
     * Create a detached, initialised ClassroomConfigurationRecord
     */
    public ClassroomConfigurationRecord(String id, String configurationId, Integer typeId, String classroom, String classroomCoding, String remark, Integer sort, Long createTime, String createMember, Integer deleteFlag) {
        super(ClassroomConfiguration.CLASSROOM_CONFIGURATION);

        set(0, id);
        set(1, configurationId);
        set(2, typeId);
        set(3, classroom);
        set(4, classroomCoding);
        set(5, remark);
        set(6, sort);
        set(7, createTime);
        set(8, createMember);
        set(9, deleteFlag);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ClassroomConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ClassroomConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ClassroomConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
