/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SolutionConfigurationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 智慧教务-配置情况
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SolutionConfiguration extends TableImpl<SolutionConfigurationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_solution_configuration</code>
     */
    public static final SolutionConfiguration SOLUTION_CONFIGURATION = new SolutionConfiguration();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SolutionConfigurationRecord> getRecordType() {
        return SolutionConfigurationRecord.class;
    }

    /**
     * The column <code>train.t_solution_configuration.f_id</code>.
     */
    public final TableField<SolutionConfigurationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_solution_configuration.f_name</code>. 名称
     */
    public final TableField<SolutionConfigurationRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "名称");

    /**
     * The column <code>train.t_solution_configuration.f_code</code>. 编码
     */
    public final TableField<SolutionConfigurationRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "编码");

    /**
     * The column <code>train.t_solution_configuration.f_order</code>. 排序
     */
    public final TableField<SolutionConfigurationRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * The column <code>train.t_solution_configuration.f_create_time</code>. 创建时间
     */
    public final TableField<SolutionConfigurationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_solution_configuration.f_create_member_id</code>. 创建人
     */
    public final TableField<SolutionConfigurationRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>train.t_solution_configuration.f_type</code>. 0=方案策划情况配置,1=实施方式配置
     */
    public final TableField<SolutionConfigurationRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0=方案策划情况配置,1=实施方式配置");

    /**
     * Create a <code>train.t_solution_configuration</code> table reference
     */
    public SolutionConfiguration() {
        this("t_solution_configuration", null);
    }

    /**
     * Create an aliased <code>train.t_solution_configuration</code> table reference
     */
    public SolutionConfiguration(String alias) {
        this(alias, SOLUTION_CONFIGURATION);
    }

    private SolutionConfiguration(String alias, Table<SolutionConfigurationRecord> aliased) {
        this(alias, aliased, null);
    }

    private SolutionConfiguration(String alias, Table<SolutionConfigurationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "智慧教务-配置情况");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SolutionConfigurationRecord> getPrimaryKey() {
        return Keys.KEY_T_SOLUTION_CONFIGURATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SolutionConfigurationRecord>> getKeys() {
        return Arrays.<UniqueKey<SolutionConfigurationRecord>>asList(Keys.KEY_T_SOLUTION_CONFIGURATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SolutionConfiguration as(String alias) {
        return new SolutionConfiguration(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SolutionConfiguration rename(String name) {
        return new SolutionConfiguration(name, null);
    }
}
