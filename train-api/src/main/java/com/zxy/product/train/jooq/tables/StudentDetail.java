/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudentDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudentDetail extends TableImpl<StudentDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_student_detail</code>
     */
    public static final StudentDetail STUDENT_DETAIL = new StudentDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudentDetailRecord> getRecordType() {
        return StudentDetailRecord.class;
    }

    /**
     * The column <code>train.t_student_detail.f_id</code>.
     */
    public final TableField<StudentDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_student_detail.f_code</code>. MIS编码
     */
    public final TableField<StudentDetailRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "MIS编码");

    /**
     * The column <code>train.t_student_detail.f_name</code>. 班级名称
     */
    public final TableField<StudentDetailRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班级名称");

    /**
     * The column <code>train.t_student_detail.f_organization_name</code>. 主办部门
     */
    public final TableField<StudentDetailRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "主办部门");

    /**
     * The column <code>train.t_student_detail.f_train_type</code>. 培训类型
     */
    public final TableField<StudentDetailRecord, String> TRAIN_TYPE = createField("f_train_type", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "培训类型");

    /**
     * The column <code>train.t_student_detail.f_student_name</code>. 姓名
     */
    public final TableField<StudentDetailRecord, String> STUDENT_NAME = createField("f_student_name", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "姓名");

    /**
     * The column <code>train.t_student_detail.f_student_code</code>. 员工编号
     */
    public final TableField<StudentDetailRecord, String> STUDENT_CODE = createField("f_student_code", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工编号");

    /**
     * The column <code>train.t_student_detail.f_student_nation</code>. 民族
     */
    public final TableField<StudentDetailRecord, String> STUDENT_NATION = createField("f_student_nation", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "民族");

    /**
     * The column <code>train.t_student_detail.f_student_sex</code>. 性别
     */
    public final TableField<StudentDetailRecord, String> STUDENT_SEX = createField("f_student_sex", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "性别");

    /**
     * The column <code>train.t_student_detail.f_student_company</code>. 公司
     */
    public final TableField<StudentDetailRecord, String> STUDENT_COMPANY = createField("f_student_company", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "公司");

    /**
     * The column <code>train.t_student_detail.f_student_department</code>. 部门
     */
    public final TableField<StudentDetailRecord, String> STUDENT_DEPARTMENT = createField("f_student_department", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "部门");

    /**
     * The column <code>train.t_student_detail.f_student_job</code>. 职位
     */
    public final TableField<StudentDetailRecord, String> STUDENT_JOB = createField("f_student_job", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "职位");

    /**
     * The column <code>train.t_student_detail.f_student_phone</code>. 手机
     */
    public final TableField<StudentDetailRecord, String> STUDENT_PHONE = createField("f_student_phone", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "手机");

    /**
     * The column <code>train.t_student_detail.f_student_email</code>. 邮箱
     */
    public final TableField<StudentDetailRecord, String> STUDENT_EMAIL = createField("f_student_email", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "邮箱");

    /**
     * The column <code>train.t_student_detail.f_month</code>. 月份
     */
    public final TableField<StudentDetailRecord, String> MONTH = createField("f_month", org.jooq.impl.SQLDataType.VARCHAR.length(2).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "月份");

    /**
     * The column <code>train.t_student_detail.f_day</code>. 培训天数
     */
    public final TableField<StudentDetailRecord, Double> DAY = createField("f_day", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "培训天数");

    /**
     * The column <code>train.t_student_detail.f_settlement_company</code>. 结算公司
     */
    public final TableField<StudentDetailRecord, String> SETTLEMENT_COMPANY = createField("f_settlement_company", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "结算公司");

    /**
     * The column <code>train.t_student_detail.f_settlement_company_code</code>. 结算公司组织编码
     */
    public final TableField<StudentDetailRecord, String> SETTLEMENT_COMPANY_CODE = createField("f_settlement_company_code", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "结算公司组织编码");

    /**
     * The column <code>train.t_student_detail.f_year</code>. 年份
     */
    public final TableField<StudentDetailRecord, String> YEAR = createField("f_year", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "年份");

    /**
     * The column <code>train.t_student_detail.f_create_member</code>. 创建人
     */
    public final TableField<StudentDetailRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>train.t_student_detail.f_create_time</code>. 创建时间
     */
    public final TableField<StudentDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>train.t_student_detail</code> table reference
     */
    public StudentDetail() {
        this("t_student_detail", null);
    }

    /**
     * Create an aliased <code>train.t_student_detail</code> table reference
     */
    public StudentDetail(String alias) {
        this(alias, STUDENT_DETAIL);
    }

    private StudentDetail(String alias, Table<StudentDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudentDetail(String alias, Table<StudentDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudentDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDENT_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudentDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<StudentDetailRecord>>asList(Keys.KEY_T_STUDENT_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudentDetail as(String alias) {
        return new StudentDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudentDetail rename(String name) {
        return new StudentDetail(name, null);
    }
}
