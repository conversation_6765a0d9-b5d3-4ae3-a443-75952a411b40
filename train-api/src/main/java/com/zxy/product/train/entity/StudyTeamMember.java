package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberEntity;

/**
 * 学习团队成员实体类
 *
 * <AUTHOR>
 * @date 2021/4/14/0014 15:51
 */
public class StudyTeamMember extends StudyTeamMemberEntity {
    private static final long serialVersionUID = 5875521885262088824L;

    /**
     * 审核状态:0待审核 1通过 2拒绝
     */
    public static final Integer AUDIT_STATUS_WAIT = 0;
    public static final Integer AUDIT_STATUS_PASS = 1;
    public static final Integer AUDIT_STATUS_REFUSE = 2;

    /**
     * 人员角色 1-团队长 2-助理 3-正式人员  4-其他人员 5-历史人员
     */
    public static final int ROLE_CAPTAIN = 1;
    public static final int ROLE_ASSISTANT = 2;
    public static final int ROLE_FORMAL = 3;
    public static final int ROLE_ELSE = 4;
    public static final int ROLE_HISTORY = 5;

     private StudyTeamMemberSign teamMemberSign;

     private boolean needNotice;
     /**
     * 用户信息
     */
    private Member member;

    public StudyTeamMemberSign getTeamMemberSign() {
        return teamMemberSign;
    }

    public void setTeamMemberSign(StudyTeamMemberSign teamMemberSign) {
        this.teamMemberSign = teamMemberSign;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public boolean isNeedNotice() {
        return needNotice;
    }

    public void setNeedNotice(boolean needNotice) {
        this.needNotice = needNotice;
    }
}
