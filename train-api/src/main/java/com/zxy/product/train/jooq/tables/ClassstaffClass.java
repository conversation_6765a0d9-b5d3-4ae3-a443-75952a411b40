/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassstaffClassRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 培训班级和班务人员关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassstaffClass extends TableImpl<ClassstaffClassRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_classstaff_class</code>
     */
    public static final ClassstaffClass CLASSSTAFF_CLASS = new ClassstaffClass();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassstaffClassRecord> getRecordType() {
        return ClassstaffClassRecord.class;
    }

    /**
     * The column <code>train.t_classstaff_class.f_id</code>. 表id
     */
    public final TableField<ClassstaffClassRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_classstaff_class.f_member_id</code>. 班务人员id
     */
    public final TableField<ClassstaffClassRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班务人员id");

    /**
     * The column <code>train.t_classstaff_class.f_type</code>. 班务人员类型： 0.班务人员 1.班主任
     */
    public final TableField<ClassstaffClassRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "班务人员类型： 0.班务人员 1.班主任");

    /**
     * The column <code>train.t_classstaff_class.f_call_name</code>. 称呼
     */
    public final TableField<ClassstaffClassRecord, String> CALL_NAME = createField("f_call_name", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "称呼");

    /**
     * The column <code>train.t_classstaff_class.f_delete_flag</code>. 删除状态：0未删除(默认) 1已删除
     */
    public final TableField<ClassstaffClassRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态：0未删除(默认) 1已删除");

    /**
     * The column <code>train.t_classstaff_class.f_class_id</code>. 班级id
     */
    public final TableField<ClassstaffClassRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级id");

    /**
     * The column <code>train.t_classstaff_class.f_create_time</code>. 创建时间
     */
    public final TableField<ClassstaffClassRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_classstaff_class.f_sort</code>. 排序
     */
    public final TableField<ClassstaffClassRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * Create a <code>train.t_classstaff_class</code> table reference
     */
    public ClassstaffClass() {
        this("t_classstaff_class", null);
    }

    /**
     * Create an aliased <code>train.t_classstaff_class</code> table reference
     */
    public ClassstaffClass(String alias) {
        this(alias, CLASSSTAFF_CLASS);
    }

    private ClassstaffClass(String alias, Table<ClassstaffClassRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassstaffClass(String alias, Table<ClassstaffClassRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训班级和班务人员关联表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassstaffClassRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASSSTAFF_CLASS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassstaffClassRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassstaffClassRecord>>asList(Keys.KEY_T_CLASSSTAFF_CLASS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassstaffClass as(String alias) {
        return new ClassstaffClass(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassstaffClass rename(String name) {
        return new ClassstaffClass(name, null);
    }
}
