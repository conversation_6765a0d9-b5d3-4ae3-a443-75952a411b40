/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamMemberSignLogRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习团队成员签到表流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamMemberSignLog extends TableImpl<StudyTeamMemberSignLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_member_sign_log</code>
     */
    public static final StudyTeamMemberSignLog STUDY_TEAM_MEMBER_SIGN_LOG = new StudyTeamMemberSignLog();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamMemberSignLogRecord> getRecordType() {
        return StudyTeamMemberSignLogRecord.class;
    }

    /**
     * The column <code>train.t_study_team_member_sign_log.f_id</code>. ID
     */
    public final TableField<StudyTeamMemberSignLogRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_study_team_member_sign_log.f_activity_id</code>. 团队活动id
     */
    public final TableField<StudyTeamMemberSignLogRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队活动id");

    /**
     * The column <code>train.t_study_team_member_sign_log.f_team_member_id</code>. 团队成员id
     */
    public final TableField<StudyTeamMemberSignLogRecord, String> TEAM_MEMBER_ID = createField("f_team_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队成员id");

    /**
     * The column <code>train.t_study_team_member_sign_log.f_sign_time</code>. 签到时间
     */
    public final TableField<StudyTeamMemberSignLogRecord, Long> SIGN_TIME = createField("f_sign_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "签到时间");

    /**
     * The column <code>train.t_study_team_member_sign_log.f_sign_type</code>. 签到类型 1-签到 2-签退
     */
    public final TableField<StudyTeamMemberSignLogRecord, Integer> SIGN_TYPE = createField("f_sign_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "签到类型 1-签到 2-签退");

    /**
     * The column <code>train.t_study_team_member_sign_log.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamMemberSignLogRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team_member_sign_log</code> table reference
     */
    public StudyTeamMemberSignLog() {
        this("t_study_team_member_sign_log", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_member_sign_log</code> table reference
     */
    public StudyTeamMemberSignLog(String alias) {
        this(alias, STUDY_TEAM_MEMBER_SIGN_LOG);
    }

    private StudyTeamMemberSignLog(String alias, Table<StudyTeamMemberSignLogRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamMemberSignLog(String alias, Table<StudyTeamMemberSignLogRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习团队成员签到表流水表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamMemberSignLogRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_MEMBER_SIGN_LOG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamMemberSignLogRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamMemberSignLogRecord>>asList(Keys.KEY_T_STUDY_TEAM_MEMBER_SIGN_LOG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLog as(String alias) {
        return new StudyTeamMemberSignLog(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamMemberSignLog rename(String name) {
        return new StudyTeamMemberSignLog(name, null);
    }
}
