package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassCourseHistoryEntity;

/**
 * 历史日程安排
 * @ClassName: ClassCourseHistory
 * @author: Acong
 * @date: 2018年1月26日
 */
public class ClassCourseHistory extends ClassCourseHistoryEntity {

	private static final long serialVersionUID = -5607650487162424780L;

    private Double paidPay;
    private Double pay;
    private Double tax;
    private String projectName;
    private String organizationName;
    private String classRoomName;
    private Double courseDuration;
	public Double getPaidPay() {
		return paidPay;
	}
	public void setPaidPay(Double paidPay) {
		this.paidPay = paidPay;
	}
	public Double getPay() {
		return pay;
	}
	public void setPay(Double pay) {
		this.pay = pay;
	}
	public Double getTax() {
		return tax;
	}
	public void setTax(Double tax) {
		this.tax = tax;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getOrganizationName() {
		return organizationName;
	}
	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}
	public String getClassRoomName() {
		return classRoomName;
	}
	public void setClassRoomName(String classRoomName) {
		this.classRoomName = classRoomName;
	}
	public Double getCourseDuration() {
		return courseDuration;
	}
	public void setCourseDuration(Double courseDuration) {
		this.courseDuration = courseDuration;
	}
}
