/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMessageDetail extends Serializable {

    /**
     * Setter for <code>train.t_message_detail.f_id</code>. id
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_message_detail.f_id</code>. id
     */
    public String getId();

    /**
     * Setter for <code>train.t_message_detail.f_message_record_id</code>. 短信表主键
     */
    public void setMessageRecordId(String value);

    /**
     * Getter for <code>train.t_message_detail.f_message_record_id</code>. 短信表主键
     */
    public String getMessageRecordId();

    /**
     * Setter for <code>train.t_message_detail.f_recipient</code>. 收件人
     */
    public void setRecipient(String value);

    /**
     * Getter for <code>train.t_message_detail.f_recipient</code>. 收件人
     */
    public String getRecipient();

    /**
     * Setter for <code>train.t_message_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_message_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMessageDetail
     */
    public void from(IMessageDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMessageDetail
     */
    public <E extends IMessageDetail> E into(E into);
}
