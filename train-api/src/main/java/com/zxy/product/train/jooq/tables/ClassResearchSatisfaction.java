/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassResearchSatisfactionRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;


/**
 * 培训班课程问卷满意度结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassResearchSatisfaction extends TableImpl<ClassResearchSatisfactionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_research_satisfaction</code>
     */
    public static final ClassResearchSatisfaction CLASS_RESEARCH_SATISFACTION = new ClassResearchSatisfaction();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassResearchSatisfactionRecord> getRecordType() {
        return ClassResearchSatisfactionRecord.class;
    }

    /**
     * The column <code>train.t_class_research_satisfaction.f_id</code>. 主键id
     */
    public final TableField<ClassResearchSatisfactionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键id");

    /**
     * The column <code>train.t_class_research_satisfaction.f_class_id</code>. 班级id
     */
    public final TableField<ClassResearchSatisfactionRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "班级id");

    /**
     * The column <code>train.t_class_research_satisfaction.f_overall_satisfaction</code>. 总体满意度
     */
    public final TableField<ClassResearchSatisfactionRecord, Integer> OVERALL_SATISFACTION = createField("f_overall_satisfaction", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "总体满意度");

    /**
     * The column <code>train.t_class_research_satisfaction.f_course_average_satisfaction</code>. 课程总体满意度均值
     */
    public final TableField<ClassResearchSatisfactionRecord, Integer> COURSE_AVERAGE_SATISFACTION = createField("f_course_average_satisfaction", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "课程总体满意度均值");

    /**
     * The column <code>train.t_class_research_satisfaction.f_create_time</code>. 创建时间
     */
    public final TableField<ClassResearchSatisfactionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_class_research_satisfaction.f_update_time</code>. 修改时间
     */
    public final TableField<ClassResearchSatisfactionRecord, Long> UPDATE_TIME = createField("f_update_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "修改时间");

    /**
     * The column <code>train.t_class_research_satisfaction.f_modify_date</code>. 修改时间
     */
    public final TableField<ClassResearchSatisfactionRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>train.t_class_research_satisfaction</code> table reference
     */
    public ClassResearchSatisfaction() {
        this("t_class_research_satisfaction", null);
    }

    /**
     * Create an aliased <code>train.t_class_research_satisfaction</code> table reference
     */
    public ClassResearchSatisfaction(String alias) {
        this(alias, CLASS_RESEARCH_SATISFACTION);
    }

    private ClassResearchSatisfaction(String alias, Table<ClassResearchSatisfactionRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassResearchSatisfaction(String alias, Table<ClassResearchSatisfactionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "培训班课程问卷满意度结果表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassResearchSatisfactionRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_RESEARCH_SATISFACTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassResearchSatisfactionRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassResearchSatisfactionRecord>>asList(Keys.KEY_T_CLASS_RESEARCH_SATISFACTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassResearchSatisfaction as(String alias) {
        return new ClassResearchSatisfaction(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassResearchSatisfaction rename(String name) {
        return new ClassResearchSatisfaction(name, null);
    }
}
