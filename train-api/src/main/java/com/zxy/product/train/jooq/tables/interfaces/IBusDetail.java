/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 班车详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IBusDetail extends Serializable {

    /**
     * Setter for <code>train.t_bus_detail.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_bus_detail.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_bus_detail.f_member_id</code>. 员工ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_bus_detail.f_member_id</code>. 员工ID
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_bus_detail.f_option_id</code>. 登记信息(选项主题表ID)
     */
    public void setOptionId(String value);

    /**
     * Getter for <code>train.t_bus_detail.f_option_id</code>. 登记信息(选项主题表ID)
     */
    public String getOptionId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IBusDetail
     */
    public void from(IBusDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IBusDetail
     */
    public <E extends IBusDetail> E into(E into);
}
