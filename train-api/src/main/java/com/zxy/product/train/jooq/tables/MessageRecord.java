/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.MessageRecordRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MessageRecord extends TableImpl<MessageRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_message_record</code>
     */
    public static final MessageRecord MESSAGE_RECORD = new MessageRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MessageRecordRecord> getRecordType() {
        return MessageRecordRecord.class;
    }

    /**
     * The column <code>train.t_message_record.f_id</code>. 表id
     */
    public final TableField<MessageRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_message_record.f_class_id</code>. 班级id
     */
    public final TableField<MessageRecordRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班级id");

    /**
     * The column <code>train.t_message_record.f_type</code>. 类型：1 学员管理短信； 2班级问卷短信； 3培训计划短信
     */
    public final TableField<MessageRecordRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "类型：1 学员管理短信； 2班级问卷短信； 3培训计划短信");

    /**
     * The column <code>train.t_message_record.f_sender</code>. 发送人id
     */
    public final TableField<MessageRecordRecord, String> SENDER = createField("f_sender", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发送人id");

    /**
     * The column <code>train.t_message_record.f_receiver</code>. 接收人id，逗号隔开
     */
    public final TableField<MessageRecordRecord, String> RECEIVER = createField("f_receiver", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "接收人id，逗号隔开");

    /**
     * The column <code>train.t_message_record.f_content</code>. 短信内容
     */
    public final TableField<MessageRecordRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "短信内容");

    /**
     * The column <code>train.t_message_record.f_create_time</code>. 创建时间
     */
    public final TableField<MessageRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_message_record.f_organization</code>. 发件人组织
     */
    public final TableField<MessageRecordRecord, String> ORGANIZATION = createField("f_organization", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发件人组织");

    /**
     * The column <code>train.t_message_record.f_status</code>. 0.未发送（“/”）、1.回执返回状态“发送成功、3.回执返回状态“发送失败”、4接口调用成功、5接口调用失败
     */
    public final TableField<MessageRecordRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "0.未发送（“/”）、1.回执返回状态“发送成功、3.回执返回状态“发送失败”、4接口调用成功、5接口调用失败");

    /**
     * The column <code>train.t_message_record.f_task_id</code>. 发送短信同步返回内容中包含的字段
     */
    public final TableField<MessageRecordRecord, String> TASK_ID = createField("f_task_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发送短信同步返回内容中包含的字段");

    /**
     * Create a <code>train.t_message_record</code> table reference
     */
    public MessageRecord() {
        this("t_message_record", null);
    }

    /**
     * Create an aliased <code>train.t_message_record</code> table reference
     */
    public MessageRecord(String alias) {
        this(alias, MESSAGE_RECORD);
    }

    private MessageRecord(String alias, Table<MessageRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private MessageRecord(String alias, Table<MessageRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MessageRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_MESSAGE_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MessageRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<MessageRecordRecord>>asList(Keys.KEY_T_MESSAGE_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MessageRecord as(String alias) {
        return new MessageRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MessageRecord rename(String name) {
        return new MessageRecord(name, null);
    }
}
