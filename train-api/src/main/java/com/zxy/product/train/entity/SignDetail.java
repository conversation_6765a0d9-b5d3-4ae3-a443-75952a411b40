package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.SignDetailEntity;

/**
 * <PERSON><PERSON><PERSON>an
 *
 */
public class SignDetail extends SignDetailEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = 5258178558674252879L;
	public static final Integer ALL = 0;//全部
	public static final Integer AUDITED = 1;//正常
	public static final Integer LATE = 2;//迟到
	public static final Integer NO_SIGN = 3;//未签到
	public static final Integer LEAVE = 4;//请假
	public static final Integer SIGNED = 5;//已经签到
	public static final Integer NOT_START = 6;//签到未开始
	public static final Integer END = 7;//签到已结束
	public static final Integer ERROR = 8;//签到已失效
	public static final Integer ILLEGAL = 9;//非培训班人员

	private Member member;// 人员对象

	private Sign sign;//SIGN对象

	private Leave signLeave; //signLeave对象

	private Integer count;//页面count

	private Integer flag;//标识，看是否已经请假过

	private String classId; //查询成员电话所用

	public Integer getFlag() {
		return flag;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Sign getSign() {
		return sign;
	}

	public void setSign(Sign sign) {
		this.sign = sign;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Leave getSignLeave() {
		return signLeave;
	}

	public void setSignLeave(Leave signLeave) {
		this.signLeave = signLeave;
	}

	public String getClassId() {
		return classId;
	}

	public void setClassId(String classId) {
		this.classId = classId;
	}
}
