package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ProjectEntity;

public class Project extends ProjectEntity {

	/*
	 * Created by 田聪 on 2017/02/08
	 */
	private static final long serialVersionUID = 4106220048115253401L;

	public static final int ADDRESS_MOVE = 1;	//　移动党校
	public static final int ADDRESS_SOUTH = 2;	// 南方基地

	public static final int STATUS_RESERVE = 1;	// 待预定
	public static final int STATUS_APPROVAL = 2;// 待审核
	public static final int STATUS_AGREE = 3;	// 同意申请
	public static final int STATUS_RESOURCES_FULL = 4; // 资源已满
	public static final int STATUS_REFUSE = 5; // 拒绝

	public static final int DELETE_FLASE=0;	//删除状态：未删除
	public static final int DELETE_TRUE=1;	//删除状态，已删除

	private Integer auditStatus;

	private ClassInfo classInfo;

	private ProjectApproval projectApproval;

	private String contactMemberName;

	private String organizationName;

	private String teacherName;

	private String classRoomName;

	private Member member;

	private ConfigurationValue trainType;

	private ConfigurationValue studentType;

	private ConfigurationValue simpleType;

	private ConfigurationValue trainAddress;

	private ClassResource classResource;

	private String suggestion;

	private String classType;

	private int classStatus;

	private String projectMonth;

	private Integer traineeNum;//学员数量

	private String projectType;//培训类别

	private String projectLevel;//培训级别

	private String classroomConfig;//教室
	private String restaurantsConfig;//餐厅
	private String guestroomsConfig;//客房
	private String addressName;//地点
	private String special;//特殊班级

	private Integer implementationMonth;//实施月
	private String orgName;//主办方
	private String  aesContactPhone;// 加密的联系人电话
	private String  aesContactEmail;// 加密的联系人邮箱

	public Integer getImplementationMonth() {
		return implementationMonth;
	}

	public void setImplementationMonth(Integer implementationMonth) {
		this.implementationMonth = implementationMonth;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}


	public String getSpecial() {
		return special;
	}

	public void setSpecial(String special) {
		this.special = special;
	}

	public String getAddressName() {
		return addressName;
	}

	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}

	public String getClassroomConfig() {
		return classroomConfig;
	}

	public void setClassroomConfig(String classroomConfig) {
		this.classroomConfig = classroomConfig;
	}

	public String getRestaurantsConfig() {
		return restaurantsConfig;
	}

	public void setRestaurantsConfig(String restaurantsConfig) {
		this.restaurantsConfig = restaurantsConfig;
	}

	public String getGuestroomsConfig() {
		return guestroomsConfig;
	}

	public void setGuestroomsConfig(String guestroomsConfig) {
		this.guestroomsConfig = guestroomsConfig;
	}

	public String getProjectType() {
		return projectType;
	}

	public void setProjectType(String projectType) {
		this.projectType = projectType;
	}

	public String getProjectLevel() {
		return projectLevel;
	}

	public void setProjectLevel(String projectLevel) {
		this.projectLevel = projectLevel;
	}

	public String getClassRoomName() {
		return classRoomName;
	}

	public void setClassRoomName(String classRoomName) {
		this.classRoomName = classRoomName;
	}

	public Integer getTraineeNum() {
		return traineeNum;
	}

	public void setTraineeNum(Integer traineeNum) {
		this.traineeNum = traineeNum;
	}

	public String getProjectMonth() {
		return projectMonth;
	}

	public void setProjectMonth(String projectMonth) {
		this.projectMonth = projectMonth;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public int getClassStatus() {
		return classStatus;
	}

	public void setClassStatus(int classStatus) {
		this.classStatus = classStatus;
	}

	public String getClassType() {
		return classType;
	}

	public void setClassType(String classType) {
		this.classType = classType;
	}

	public String getSuggestion() {
		return suggestion;
	}

	public void setSuggestion(String suggestion) {
		this.suggestion = suggestion;
	}

	public ClassResource getClassResource() {
		return classResource;
	}

	public void setClassResource(ClassResource classResource) {
		this.classResource = classResource;
	}

	public ConfigurationValue getTrainAddress() {
		return trainAddress;
	}

	public void setTrainAddress(ConfigurationValue trainAddress) {
		this.trainAddress = trainAddress;
	}

	private Organization organization;

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public ConfigurationValue getTrainType() {
		return trainType;
	}

	public void setTrainType(ConfigurationValue trainType) {
		this.trainType = trainType;
	}

	public ConfigurationValue getStudentType() {
		return studentType;
	}

	public void setStudentType(ConfigurationValue studentType) {
		this.studentType = studentType;
	}

	public ConfigurationValue getSimpleType() {
		return simpleType;
	}

	public void setSimpleType(ConfigurationValue simpleType) {
		this.simpleType = simpleType;
	}
	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public ClassInfo getClassInfo() {
		return classInfo;
	}

	public void setClassInfo(ClassInfo classInfo) {
		this.classInfo = classInfo;
	}

	public ProjectApproval getProjectApproval() {
		return projectApproval;
	}

	public void setProjectApproval(ProjectApproval projectApproval) {
		this.projectApproval = projectApproval;
	}

	public String getContactMemberName() {
		return contactMemberName;
	}

	public void setContactMemberName(String contactMemberName) {
		this.contactMemberName = contactMemberName;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getTeacherName() {
		return teacherName;
	}

	public void setTeacherName(String teacherName) {
		this.teacherName = teacherName;
	}

	public String getAesContactPhone() {
		return aesContactPhone;
	}

	public void setAesContactPhone(String aesContactPhone) {
		this.aesContactPhone = aesContactPhone;
	}

	public String getAesContactEmail() {
		return aesContactEmail;
	}

	public void setAesContactEmail(String aesContactEmail) {
		this.aesContactEmail = aesContactEmail;
	}
}
