/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LecturerAttributeRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 讲师属性配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LecturerAttribute extends TableImpl<LecturerAttributeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_lecturer_attribute</code>
     */
    public static final LecturerAttribute LECTURER_ATTRIBUTE = new LecturerAttribute();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LecturerAttributeRecord> getRecordType() {
        return LecturerAttributeRecord.class;
    }

    /**
     * The column <code>train.t_lecturer_attribute.f_id</code>. 系统ID
     */
    public final TableField<LecturerAttributeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_lecturer_attribute.f_type_id</code>. 分类ID固定值【0：内部讲师；1：外部讲师】
     */
    public final TableField<LecturerAttributeRecord, Integer> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "分类ID固定值【0：内部讲师；1：外部讲师】");

    /**
     * The column <code>train.t_lecturer_attribute.f_attribute_name</code>. 属性名称
     */
    public final TableField<LecturerAttributeRecord, String> ATTRIBUTE_NAME = createField("f_attribute_name", org.jooq.impl.SQLDataType.VARCHAR.length(64).nullable(false), this, "属性名称");

    /**
     * The column <code>train.t_lecturer_attribute.f_create_member_id</code>. 创建人ID
     */
    public final TableField<LecturerAttributeRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "创建人ID");

    /**
     * The column <code>train.t_lecturer_attribute.f_exit_member_id</code>. 最后修改人ID
     */
    public final TableField<LecturerAttributeRecord, String> EXIT_MEMBER_ID = createField("f_exit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "最后修改人ID");

    /**
     * The column <code>train.t_lecturer_attribute.f_organization_id</code>. 组织ID
     */
    public final TableField<LecturerAttributeRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "组织ID");

    /**
     * The column <code>train.t_lecturer_attribute.f_create_time</code>. 创建时间
     */
    public final TableField<LecturerAttributeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_lecturer_attribute.f_update_time</code>. 最后修改时间
     */
    public final TableField<LecturerAttributeRecord, Long> UPDATE_TIME = createField("f_update_time", org.jooq.impl.SQLDataType.BIGINT, this, "最后修改时间");

    /**
     * Create a <code>train.t_lecturer_attribute</code> table reference
     */
    public LecturerAttribute() {
        this("t_lecturer_attribute", null);
    }

    /**
     * Create an aliased <code>train.t_lecturer_attribute</code> table reference
     */
    public LecturerAttribute(String alias) {
        this(alias, LECTURER_ATTRIBUTE);
    }

    private LecturerAttribute(String alias, Table<LecturerAttributeRecord> aliased) {
        this(alias, aliased, null);
    }

    private LecturerAttribute(String alias, Table<LecturerAttributeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "讲师属性配置表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LecturerAttributeRecord> getPrimaryKey() {
        return Keys.KEY_T_LECTURER_ATTRIBUTE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LecturerAttributeRecord>> getKeys() {
        return Arrays.<UniqueKey<LecturerAttributeRecord>>asList(Keys.KEY_T_LECTURER_ATTRIBUTE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LecturerAttribute as(String alias) {
        return new LecturerAttribute(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LecturerAttribute rename(String name) {
        return new LecturerAttribute(name, null);
    }
}
