/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMember extends Serializable {

    /**
     * Setter for <code>train.t_member.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_member.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_member.f_name</code>. 人员名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_member.f_name</code>. 人员名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_member.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_member.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_member.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_member.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_member.f_full_name</code>. 姓名
     */
    public void setFullName(String value);

    /**
     * Getter for <code>train.t_member.f_full_name</code>. 姓名
     */
    public String getFullName();

    /**
     * Setter for <code>train.t_member.f_ihr_code</code>. ihr新员工编码
     */
    public void setIhrCode(String value);

    /**
     * Getter for <code>train.t_member.f_ihr_code</code>. ihr新员工编码
     */
    public String getIhrCode();

    /**
     * Setter for <code>train.t_member.f_major_position_id</code>. 主岗
     */
    public void setMajorPositionId(String value);

    /**
     * Getter for <code>train.t_member.f_major_position_id</code>. 主岗
     */
    public String getMajorPositionId();

    /**
     * Setter for <code>train.t_member.f_job_id</code>. ihr新职务id
     */
    public void setJobId(String value);

    /**
     * Getter for <code>train.t_member.f_job_id</code>. ihr新职务id
     */
    public String getJobId();

    /**
     * Setter for <code>train.t_member.f_email</code>.
     */
    public void setEmail(String value);

    /**
     * Getter for <code>train.t_member.f_email</code>.
     */
    public String getEmail();

    /**
     * Setter for <code>train.t_member.f_phone_number</code>. 手机号
     */
    public void setPhoneNumber(String value);

    /**
     * Getter for <code>train.t_member.f_phone_number</code>. 手机号
     */
    public String getPhoneNumber();

    /**
     * Setter for <code>train.t_member.f_status</code>. 人员状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_member.f_status</code>. 人员状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_member.f_sex</code>. 性别
     */
    public void setSex(Integer value);

    /**
     * Getter for <code>train.t_member.f_sex</code>. 性别
     */
    public Integer getSex();

    /**
     * Setter for <code>train.t_member.f_head_portrait</code>. 用户头像id
     */
    public void setHeadPortrait(String value);

    /**
     * Getter for <code>train.t_member.f_head_portrait</code>. 用户头像id
     */
    public String getHeadPortrait();

    /**
     * Setter for <code>train.t_member.f_ethnicity_id</code>. 民族id
     */
    public void setEthnicityId(String value);

    /**
     * Getter for <code>train.t_member.f_ethnicity_id</code>. 民族id
     */
    public String getEthnicityId();

    /**
     * Setter for <code>train.t_member.f_head_portrait_path</code>. 头像图片路径
     */
    public void setHeadPortraitPath(String value);

    /**
     * Getter for <code>train.t_member.f_head_portrait_path</code>. 头像图片路径
     */
    public String getHeadPortraitPath();

    /**
     * Setter for <code>train.t_member.f_company_id</code>.
     */
    public void setCompanyId(String value);

    /**
     * Getter for <code>train.t_member.f_company_id</code>.
     */
    public String getCompanyId();

    /**
     * Setter for <code>train.t_member.f_incumbency_status</code>. 在职状态
     */
    public void setIncumbencyStatus(String value);

    /**
     * Getter for <code>train.t_member.f_incumbency_status</code>. 在职状态
     */
    public String getIncumbencyStatus();

    /**
     * Setter for <code>train.t_member.f_credential_value</code>. 身份证号
     */
    public void setCredentialValue(String value);

    /**
     * Getter for <code>train.t_member.f_credential_value</code>. 身份证号
     */
    public String getCredentialValue();

    /**
     * Setter for <code>train.t_member.f_entry_date</code>. 参加移动时间
     */
    public void setEntryDate(Long value);

    /**
     * Getter for <code>train.t_member.f_entry_date</code>. 参加移动时间
     */
    public Long getEntryDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMember
     */
    public void from(IMember from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMember
     */
    public <E extends IMember> E into(E into);
}
