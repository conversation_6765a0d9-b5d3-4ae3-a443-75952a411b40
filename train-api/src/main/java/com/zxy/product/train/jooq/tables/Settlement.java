/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SettlementRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Settlement extends TableImpl<SettlementRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_settlement</code>
     */
    public static final Settlement SETTLEMENT = new Settlement();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SettlementRecord> getRecordType() {
        return SettlementRecord.class;
    }

    /**
     * The column <code>train.t_settlement.f_id</code>. 表id
     */
    public final TableField<SettlementRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "表id");

    /**
     * The column <code>train.t_settlement.f_people_number</code>. 结算人数
     */
    public final TableField<SettlementRecord, Integer> PEOPLE_NUMBER = createField("f_people_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "结算人数");

    /**
     * The column <code>train.t_settlement.f_day_number</code>. 结算天数
     */
    public final TableField<SettlementRecord, Double> DAY_NUMBER = createField("f_day_number", org.jooq.impl.SQLDataType.FLOAT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.FLOAT)), this, "结算天数");

    /**
     * The column <code>train.t_settlement.f_people_day</code>. 结算人日
     */
    public final TableField<SettlementRecord, Double> PEOPLE_DAY = createField("f_people_day", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "结算人日");

    /**
     * The column <code>train.t_settlement.f_explain</code>. 结算说明
     */
    public final TableField<SettlementRecord, String> EXPLAIN = createField("f_explain", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "结算说明");

    /**
     * The column <code>train.t_settlement.f_class_id</code>. 班级id
     */
    public final TableField<SettlementRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "班级id");

    /**
     * The column <code>train.t_settlement.f_attend_days</code>. 上课天数
     */
    public final TableField<SettlementRecord, Double> ATTEND_DAYS = createField("f_attend_days", org.jooq.impl.SQLDataType.DOUBLE.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.DOUBLE)), this, "上课天数");

    /**
     * The column <code>train.t_settlement.f_create_time</code>. 创建时间
     */
    public final TableField<SettlementRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>train.t_settlement.f_create_mouth</code>. 结算月份
     */
    public final TableField<SettlementRecord, String> CREATE_MOUTH = createField("f_create_mouth", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "结算月份");

    /**
     * The column <code>train.t_settlement.f_is_receipt</code>. 是否开票
     */
    public final TableField<SettlementRecord, Integer> IS_RECEIPT = createField("f_is_receipt", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否开票");

    /**
     * The column <code>train.t_settlement.f_train_day_num</code>. 培训天数
     */
    public final TableField<SettlementRecord, Double> TRAIN_DAY_NUM = createField("f_train_day_num", org.jooq.impl.SQLDataType.FLOAT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.FLOAT)), this, "培训天数");

    /**
     * The column <code>train.t_settlement.f_finance_month</code>. 财务结算月份
     */
    public final TableField<SettlementRecord, String> FINANCE_MONTH = createField("f_finance_month", org.jooq.impl.SQLDataType.VARCHAR.length(2).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "财务结算月份");

    /**
     * The column <code>train.t_settlement.f_settlement_quantity</code>. 结算人数(根据培训人数培训日期计算而得，新)
     */
    public final TableField<SettlementRecord, Integer> SETTLEMENT_QUANTITY = createField("f_settlement_quantity", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "结算人数(根据培训人数培训日期计算而得，新)");

    /**
     * Create a <code>train.t_settlement</code> table reference
     */
    public Settlement() {
        this("t_settlement", null);
    }

    /**
     * Create an aliased <code>train.t_settlement</code> table reference
     */
    public Settlement(String alias) {
        this(alias, SETTLEMENT);
    }

    private Settlement(String alias, Table<SettlementRecord> aliased) {
        this(alias, aliased, null);
    }

    private Settlement(String alias, Table<SettlementRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SettlementRecord> getPrimaryKey() {
        return Keys.KEY_T_SETTLEMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SettlementRecord>> getKeys() {
        return Arrays.<UniqueKey<SettlementRecord>>asList(Keys.KEY_T_SETTLEMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Settlement as(String alias) {
        return new Settlement(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Settlement rename(String name) {
        return new Settlement(name, null);
    }
}
