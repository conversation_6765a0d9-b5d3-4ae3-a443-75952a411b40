/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamLeaderConfirmDetail;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamLeaderConfirmDetail;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学习活动-课程领学人学习时间
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamLeaderConfirmDetailRecord extends UpdatableRecordImpl<StudyTeamLeaderConfirmDetailRecord> implements Record4<String, String, Long, Long>, IStudyTeamLeaderConfirmDetail {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_activity_task_id</code>. 任务id
     */
    @Override
    public void setActivityTaskId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_activity_task_id</code>. 任务id
     */
    @Override
    public String getActivityTaskId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_study_time</code>. 学习时间
     */
    @Override
    public void setStudyTime(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_study_time</code>. 学习时间
     */
    @Override
    public Long getStudyTime() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_leader_confirm_detail.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_leader_confirm_detail.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record4 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, Long, Long> fieldsRow() {
        return (Row4) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row4<String, String, Long, Long> valuesRow() {
        return (Row4) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL.ACTIVITY_TASK_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field3() {
        return StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL.STUDY_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getActivityTaskId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value3() {
        return getStudyTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamLeaderConfirmDetailRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamLeaderConfirmDetailRecord value2(String value) {
        setActivityTaskId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamLeaderConfirmDetailRecord value3(Long value) {
        setStudyTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamLeaderConfirmDetailRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamLeaderConfirmDetailRecord values(String value1, String value2, Long value3, Long value4) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamLeaderConfirmDetail from) {
        setId(from.getId());
        setActivityTaskId(from.getActivityTaskId());
        setStudyTime(from.getStudyTime());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamLeaderConfirmDetail> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamLeaderConfirmDetailRecord
     */
    public StudyTeamLeaderConfirmDetailRecord() {
        super(StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL);
    }

    /**
     * Create a detached, initialised StudyTeamLeaderConfirmDetailRecord
     */
    public StudyTeamLeaderConfirmDetailRecord(String id, String activityTaskId, Long studyTime, Long createTime) {
        super(StudyTeamLeaderConfirmDetail.STUDY_TEAM_LEADER_CONFIRM_DETAIL);

        set(0, id);
        set(1, activityTaskId);
        set(2, studyTime);
        set(3, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamLeaderConfirmDetailEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamLeaderConfirmDetailEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamLeaderConfirmDetailEntity)source;
        pojo.into(this);
        return true;
    }
}
