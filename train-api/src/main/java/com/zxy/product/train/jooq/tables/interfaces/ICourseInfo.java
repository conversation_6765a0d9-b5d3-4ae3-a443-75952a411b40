/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 在线课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseInfo extends Serializable {

    /**
     * Setter for <code>train.t_course_info.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_course_info.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_course_info.f_name</code>. 课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_course_info.f_name</code>. 课程名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_course_info.f_status</code>. 状态（0：未发布，1：已发布，2：取消发布，3：测试中）
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_course_info.f_status</code>. 状态（0：未发布，1：已发布，2：取消发布，3：测试中）
     */
    public Integer getStatus();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseInfo
     */
    public void from(ICourseInfo from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseInfo
     */
    public <E extends ICourseInfo> E into(E into);
}
