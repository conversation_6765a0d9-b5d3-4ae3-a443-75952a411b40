/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.HomeLecturerRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HomeLecturer extends TableImpl<HomeLecturerRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_home_lecturer</code>
     */
    public static final HomeLecturer HOME_LECTURER = new HomeLecturer();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<HomeLecturerRecord> getRecordType() {
        return HomeLecturerRecord.class;
    }

    /**
     * The column <code>train.t_home_lecturer.f_id</code>. ID
     */
    public final TableField<HomeLecturerRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_home_lecturer.f_lecture_id</code>. 关联的讲师id 
     */
    public final TableField<HomeLecturerRecord, String> LECTURE_ID = createField("f_lecture_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联的讲师id ");

    /**
     * The column <code>train.t_home_lecturer.f_module_config_id</code>. 关联的配置模块id
     */
    public final TableField<HomeLecturerRecord, String> MODULE_CONFIG_ID = createField("f_module_config_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联的配置模块id");

    /**
     * The column <code>train.t_home_lecturer.f_sort</code>. 排序好
     */
    public final TableField<HomeLecturerRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序好");

    /**
     * The column <code>train.t_home_lecturer.f_create_time</code>. 创建时间
     */
    public final TableField<HomeLecturerRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_home_lecturer</code> table reference
     */
    public HomeLecturer() {
        this("t_home_lecturer", null);
    }

    /**
     * Create an aliased <code>train.t_home_lecturer</code> table reference
     */
    public HomeLecturer(String alias) {
        this(alias, HOME_LECTURER);
    }

    private HomeLecturer(String alias, Table<HomeLecturerRecord> aliased) {
        this(alias, aliased, null);
    }

    private HomeLecturer(String alias, Table<HomeLecturerRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<HomeLecturerRecord> getPrimaryKey() {
        return Keys.KEY_T_HOME_LECTURER_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<HomeLecturerRecord>> getKeys() {
        return Arrays.<UniqueKey<HomeLecturerRecord>>asList(Keys.KEY_T_HOME_LECTURER_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HomeLecturer as(String alias) {
        return new HomeLecturer(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public HomeLecturer rename(String name) {
        return new HomeLecturer(name, null);
    }
}
