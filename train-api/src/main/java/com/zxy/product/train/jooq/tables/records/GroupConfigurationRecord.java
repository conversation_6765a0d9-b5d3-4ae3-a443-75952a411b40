/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.GroupConfiguration;
import com.zxy.product.train.jooq.tables.interfaces.IGroupConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GroupConfigurationRecord extends UpdatableRecordImpl<GroupConfigurationRecord> implements Record7<String, String, Integer, String, Long, String, Integer>, IGroupConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_group_configuration.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_group_configuration.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_group_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public void setConfigurationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_group_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public String getConfigurationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_group_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public void setTypeId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_group_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public Integer getTypeId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_group_configuration.f_group_name</code>. 分组名称
     */
    @Override
    public void setGroupName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_group_configuration.f_group_name</code>. 分组名称
     */
    @Override
    public String getGroupName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_group_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_group_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_group_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_group_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_group_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_group_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, String, Long, String, Integer> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, String, Long, String, Integer> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return GroupConfiguration.GROUP_CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return GroupConfiguration.GROUP_CONFIGURATION.CONFIGURATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return GroupConfiguration.GROUP_CONFIGURATION.TYPE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return GroupConfiguration.GROUP_CONFIGURATION.GROUP_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return GroupConfiguration.GROUP_CONFIGURATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return GroupConfiguration.GROUP_CONFIGURATION.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return GroupConfiguration.GROUP_CONFIGURATION.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getConfigurationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getTypeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getGroupName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord value2(String value) {
        setConfigurationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord value3(Integer value) {
        setTypeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord value4(String value) {
        setGroupName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord value6(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord value7(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GroupConfigurationRecord values(String value1, String value2, Integer value3, String value4, Long value5, String value6, Integer value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IGroupConfiguration from) {
        setId(from.getId());
        setConfigurationId(from.getConfigurationId());
        setTypeId(from.getTypeId());
        setGroupName(from.getGroupName());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IGroupConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached GroupConfigurationRecord
     */
    public GroupConfigurationRecord() {
        super(GroupConfiguration.GROUP_CONFIGURATION);
    }

    /**
     * Create a detached, initialised GroupConfigurationRecord
     */
    public GroupConfigurationRecord(String id, String configurationId, Integer typeId, String groupName, Long createTime, String createMember, Integer deleteFlag) {
        super(GroupConfiguration.GROUP_CONFIGURATION);

        set(0, id);
        set(1, configurationId);
        set(2, typeId);
        set(3, groupName);
        set(4, createTime);
        set(5, createMember);
        set(6, deleteFlag);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.GroupConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.GroupConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.GroupConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
