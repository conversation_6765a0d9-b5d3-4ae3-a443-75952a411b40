/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassStatistics extends Serializable {

    /**
     * Setter for <code>train.t_class_statistics.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_statistics.f_content</code>. 题目
     */
    public void setContent(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_content</code>. 题目
     */
    public String getContent();

    /**
     * Setter for <code>train.t_class_statistics.f_satisfaction</code>. 满意
     */
    public void setSatisfaction(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_satisfaction</code>. 满意
     */
    public Integer getSatisfaction();

    /**
     * Setter for <code>train.t_class_statistics.f_basic_satisfaction</code>. 基本满意
     */
    public void setBasicSatisfaction(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_basic_satisfaction</code>. 基本满意
     */
    public Integer getBasicSatisfaction();

    /**
     * Setter for <code>train.t_class_statistics.f_commonly</code>. 一般
     */
    public void setCommonly(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_commonly</code>. 一般
     */
    public Integer getCommonly();

    /**
     * Setter for <code>train.t_class_statistics.f_dissatisfied</code>. 不满意
     */
    public void setDissatisfied(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_dissatisfied</code>. 不满意
     */
    public Integer getDissatisfied();

    /**
     * Setter for <code>train.t_class_statistics.f_very_dissatisfied</code>. 很不满意
     */
    public void setVeryDissatisfied(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_very_dissatisfied</code>. 很不满意
     */
    public Integer getVeryDissatisfied();

    /**
     * Setter for <code>train.t_class_statistics.f_count</code>. 合计（个）
     */
    public void setCount(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_count</code>. 合计（个）
     */
    public Integer getCount();

    /**
     * Setter for <code>train.t_class_statistics.f_recommended_number</code>. 推荐数（个）
     */
    public void setRecommendedNumber(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_recommended_number</code>. 推荐数（个）
     */
    public Integer getRecommendedNumber();

    /**
     * Setter for <code>train.t_class_statistics.f_teacher_satisfaction</code>. 课程师资满意率（%）
     */
    public void setTeacherSatisfaction(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_teacher_satisfaction</code>. 课程师资满意率（%）
     */
    public String getTeacherSatisfaction();

    /**
     * Setter for <code>train.t_class_statistics.f_satisfied_rate</code>. 满意（%）
     */
    public void setSatisfiedRate(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_satisfied_rate</code>. 满意（%）
     */
    public String getSatisfiedRate();

    /**
     * Setter for <code>train.t_class_statistics.f_basic_satisfaction_rate</code>. 基本满意（%）
     */
    public void setBasicSatisfactionRate(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_basic_satisfaction_rate</code>. 基本满意（%）
     */
    public String getBasicSatisfactionRate();

    /**
     * Setter for <code>train.t_class_statistics.f_commonly_rate</code>. 一般（%）
     */
    public void setCommonlyRate(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_commonly_rate</code>. 一般（%）
     */
    public String getCommonlyRate();

    /**
     * Setter for <code>train.t_class_statistics.f_dissatisfied_rate</code>. 不满意（%）
     */
    public void setDissatisfiedRate(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_dissatisfied_rate</code>. 不满意（%）
     */
    public String getDissatisfiedRate();

    /**
     * Setter for <code>train.t_class_statistics.f_very_dissatisfied_rate</code>. 很不满意（%）
     */
    public void setVeryDissatisfiedRate(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_very_dissatisfied_rate</code>. 很不满意（%）
     */
    public String getVeryDissatisfiedRate();

    /**
     * Setter for <code>train.t_class_statistics.f_satisfaction_rate</code>. 满意率（%）
     */
    public void setSatisfactionRate(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_satisfaction_rate</code>. 满意率（%）
     */
    public String getSatisfactionRate();

    /**
     * Setter for <code>train.t_class_statistics.f_recommendation_rate</code>. 推荐率（%）
     */
    public void setRecommendationRate(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_recommendation_rate</code>. 推荐率（%）
     */
    public String getRecommendationRate();

    /**
     * Setter for <code>train.t_class_statistics.f_order</code>. 排序
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_order</code>. 排序
     */
    public Integer getOrder();

    /**
     * Setter for <code>train.t_class_statistics.f_class_id</code>. 班级id
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_statistics.f_class_id</code>. 班级id
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_statistics.f_satisfaction_dimension</code>. 1第一部分2第二部分
     */
    public void setSatisfactionDimension(Integer value);

    /**
     * Getter for <code>train.t_class_statistics.f_satisfaction_dimension</code>. 1第一部分2第二部分
     */
    public Integer getSatisfactionDimension();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassStatistics
     */
    public void from(IClassStatistics from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassStatistics
     */
    public <E extends IClassStatistics> E into(E into);
}
