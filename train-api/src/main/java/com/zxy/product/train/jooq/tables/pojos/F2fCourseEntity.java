/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.train.jooq.tables.interfaces.IF2fCourse;

import javax.annotation.Generated;


/**
 * 面授课程库表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class F2fCourseEntity extends BaseEntity implements IF2fCourse {

    private static final long serialVersionUID = 1L;

    private String  name;
    private String  sequence;
    private String  obj;
    private String  keyword;
    private String  lecturer;
    private Double  courseDuration;
    private Double  courseReward;
    private Double  satisfiedDegree;
    private String  teachMethod;
    private Integer traineeNumber1;
    private Integer traineeNumber2;
    private String  summary;
    private String  remark;
    private String  target;
    private String  outline;
    private String  institution;
    private String  contacts;
    private String  contactsTel;
    private String  contactsEmail;
    private Double  pay;
    private Double  tax;
    private Integer deleteFlag;
    private String  createMember;
    private Integer type;
    private String  classId;
    private Long    courseDate;
    private String  bankUser;
    private String  bankIdentity;
    private String  bank;
    private String  bankCard;
    private String  startTime;
    private String  endTime;
    private Integer status;
    private String  attachmentId;
    private String  attachmentName;

    public F2fCourseEntity() {}

    public F2fCourseEntity(F2fCourseEntity value) {
        this.name = value.name;
        this.sequence = value.sequence;
        this.obj = value.obj;
        this.keyword = value.keyword;
        this.lecturer = value.lecturer;
        this.courseDuration = value.courseDuration;
        this.courseReward = value.courseReward;
        this.satisfiedDegree = value.satisfiedDegree;
        this.teachMethod = value.teachMethod;
        this.traineeNumber1 = value.traineeNumber1;
        this.traineeNumber2 = value.traineeNumber2;
        this.summary = value.summary;
        this.remark = value.remark;
        this.target = value.target;
        this.outline = value.outline;
        this.institution = value.institution;
        this.contacts = value.contacts;
        this.contactsTel = value.contactsTel;
        this.contactsEmail = value.contactsEmail;
        this.pay = value.pay;
        this.tax = value.tax;
        this.deleteFlag = value.deleteFlag;
        this.createMember = value.createMember;
        this.type = value.type;
        this.classId = value.classId;
        this.courseDate = value.courseDate;
        this.bankUser = value.bankUser;
        this.bankIdentity = value.bankIdentity;
        this.bank = value.bank;
        this.bankCard = value.bankCard;
        this.startTime = value.startTime;
        this.endTime = value.endTime;
        this.status = value.status;
        this.attachmentId = value.attachmentId;
        this.attachmentName = value.attachmentName;
    }

    public F2fCourseEntity(
        String  id,
        String  name,
        String  sequence,
        String  obj,
        String  keyword,
        String  lecturer,
        Double  courseDuration,
        Double  courseReward,
        Double  satisfiedDegree,
        String  teachMethod,
        Integer traineeNumber1,
        Integer traineeNumber2,
        String  summary,
        String  remark,
        String  target,
        String  outline,
        String  institution,
        String  contacts,
        String  contactsTel,
        String  contactsEmail,
        Long    createTime,
        Double  pay,
        Double  tax,
        Integer deleteFlag,
        String  createMember,
        Integer type,
        String  classId,
        Long    courseDate,
        String  bankUser,
        String  bankIdentity,
        String  bank,
        String  bankCard,
        String  startTime,
        String  endTime,
        Integer status,
        String  attachmentId,
        String  attachmentName
    ) {
        super.setId(id);
        this.name = name;
        this.sequence = sequence;
        this.obj = obj;
        this.keyword = keyword;
        this.lecturer = lecturer;
        this.courseDuration = courseDuration;
        this.courseReward = courseReward;
        this.satisfiedDegree = satisfiedDegree;
        this.teachMethod = teachMethod;
        this.traineeNumber1 = traineeNumber1;
        this.traineeNumber2 = traineeNumber2;
        this.summary = summary;
        this.remark = remark;
        this.target = target;
        this.outline = outline;
        this.institution = institution;
        this.contacts = contacts;
        this.contactsTel = contactsTel;
        this.contactsEmail = contactsEmail;
        super.setCreateTime(createTime);
        this.pay = pay;
        this.tax = tax;
        this.deleteFlag = deleteFlag;
        this.createMember = createMember;
        this.type = type;
        this.classId = classId;
        this.courseDate = courseDate;
        this.bankUser = bankUser;
        this.bankIdentity = bankIdentity;
        this.bank = bank;
        this.bankCard = bankCard;
        this.startTime = startTime;
        this.endTime = endTime;
        this.status = status;
        this.attachmentId = attachmentId;
        this.attachmentName = attachmentName;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getSequence() {
        return this.sequence;
    }

    @Override
    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    @Override
    public String getObj() {
        return this.obj;
    }

    @Override
    public void setObj(String obj) {
        this.obj = obj;
    }

    @Override
    public String getKeyword() {
        return this.keyword;
    }

    @Override
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String getLecturer() {
        return this.lecturer;
    }

    @Override
    public void setLecturer(String lecturer) {
        this.lecturer = lecturer;
    }

    @Override
    public Double getCourseDuration() {
        return this.courseDuration;
    }

    @Override
    public void setCourseDuration(Double courseDuration) {
        this.courseDuration = courseDuration;
    }

    @Override
    public Double getCourseReward() {
        return this.courseReward;
    }

    @Override
    public void setCourseReward(Double courseReward) {
        this.courseReward = courseReward;
    }

    @Override
    public Double getSatisfiedDegree() {
        return this.satisfiedDegree;
    }

    @Override
    public void setSatisfiedDegree(Double satisfiedDegree) {
        this.satisfiedDegree = satisfiedDegree;
    }

    @Override
    public String getTeachMethod() {
        return this.teachMethod;
    }

    @Override
    public void setTeachMethod(String teachMethod) {
        this.teachMethod = teachMethod;
    }

    @Override
    public Integer getTraineeNumber1() {
        return this.traineeNumber1;
    }

    @Override
    public void setTraineeNumber1(Integer traineeNumber1) {
        this.traineeNumber1 = traineeNumber1;
    }

    @Override
    public Integer getTraineeNumber2() {
        return this.traineeNumber2;
    }

    @Override
    public void setTraineeNumber2(Integer traineeNumber2) {
        this.traineeNumber2 = traineeNumber2;
    }

    @Override
    public String getSummary() {
        return this.summary;
    }

    @Override
    public void setSummary(String summary) {
        this.summary = summary;
    }

    @Override
    public String getRemark() {
        return this.remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String getTarget() {
        return this.target;
    }

    @Override
    public void setTarget(String target) {
        this.target = target;
    }

    @Override
    public String getOutline() {
        return this.outline;
    }

    @Override
    public void setOutline(String outline) {
        this.outline = outline;
    }

    @Override
    public String getInstitution() {
        return this.institution;
    }

    @Override
    public void setInstitution(String institution) {
        this.institution = institution;
    }

    @Override
    public String getContacts() {
        return this.contacts;
    }

    @Override
    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    @Override
    public String getContactsTel() {
        return this.contactsTel;
    }

    @Override
    public void setContactsTel(String contactsTel) {
        this.contactsTel = contactsTel;
    }

    @Override
    public String getContactsEmail() {
        return this.contactsEmail;
    }

    @Override
    public void setContactsEmail(String contactsEmail) {
        this.contactsEmail = contactsEmail;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Double getPay() {
        return this.pay;
    }

    @Override
    public void setPay(Double pay) {
        this.pay = pay;
    }

    @Override
    public Double getTax() {
        return this.tax;
    }

    @Override
    public void setTax(Double tax) {
        this.tax = tax;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String getCreateMember() {
        return this.createMember;
    }

    @Override
    public void setCreateMember(String createMember) {
        this.createMember = createMember;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String getClassId() {
        return this.classId;
    }

    @Override
    public void setClassId(String classId) {
        this.classId = classId;
    }

    @Override
    public Long getCourseDate() {
        return this.courseDate;
    }

    @Override
    public void setCourseDate(Long courseDate) {
        this.courseDate = courseDate;
    }

    @Override
    public String getBankUser() {
        return this.bankUser;
    }

    @Override
    public void setBankUser(String bankUser) {
        this.bankUser = bankUser;
    }

    @Override
    public String getBankIdentity() {
        return this.bankIdentity;
    }

    @Override
    public void setBankIdentity(String bankIdentity) {
        this.bankIdentity = bankIdentity;
    }

    @Override
    public String getBank() {
        return this.bank;
    }

    @Override
    public void setBank(String bank) {
        this.bank = bank;
    }

    @Override
    public String getBankCard() {
        return this.bankCard;
    }

    @Override
    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    @Override
    public String getStartTime() {
        return this.startTime;
    }

    @Override
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    @Override
    public String getEndTime() {
        return this.endTime;
    }

    @Override
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getAttachmentId() {
        return this.attachmentId;
    }

    @Override
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    @Override
    public String getAttachmentName() {
        return this.attachmentName;
    }

    @Override
    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("F2fCourseEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(sequence);
        sb.append(", ").append(obj);
        sb.append(", ").append(keyword);
        sb.append(", ").append(lecturer);
        sb.append(", ").append(courseDuration);
        sb.append(", ").append(courseReward);
        sb.append(", ").append(satisfiedDegree);
        sb.append(", ").append(teachMethod);
        sb.append(", ").append(traineeNumber1);
        sb.append(", ").append(traineeNumber2);
        sb.append(", ").append(summary);
        sb.append(", ").append(remark);
        sb.append(", ").append(target);
        sb.append(", ").append(outline);
        sb.append(", ").append(institution);
        sb.append(", ").append(contacts);
        sb.append(", ").append(contactsTel);
        sb.append(", ").append(contactsEmail);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(pay);
        sb.append(", ").append(tax);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(createMember);
        sb.append(", ").append(type);
        sb.append(", ").append(classId);
        sb.append(", ").append(courseDate);
        sb.append(", ").append(bankUser);
        sb.append(", ").append(bankIdentity);
        sb.append(", ").append(bank);
        sb.append(", ").append(bankCard);
        sb.append(", ").append(startTime);
        sb.append(", ").append(endTime);
        sb.append(", ").append(status);
        sb.append(", ").append(attachmentId);
        sb.append(", ").append(attachmentName);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IF2fCourse from) {
        setId(from.getId());
        setName(from.getName());
        setSequence(from.getSequence());
        setObj(from.getObj());
        setKeyword(from.getKeyword());
        setLecturer(from.getLecturer());
        setCourseDuration(from.getCourseDuration());
        setCourseReward(from.getCourseReward());
        setSatisfiedDegree(from.getSatisfiedDegree());
        setTeachMethod(from.getTeachMethod());
        setTraineeNumber1(from.getTraineeNumber1());
        setTraineeNumber2(from.getTraineeNumber2());
        setSummary(from.getSummary());
        setRemark(from.getRemark());
        setTarget(from.getTarget());
        setOutline(from.getOutline());
        setInstitution(from.getInstitution());
        setContacts(from.getContacts());
        setContactsTel(from.getContactsTel());
        setContactsEmail(from.getContactsEmail());
        setCreateTime(from.getCreateTime());
        setPay(from.getPay());
        setTax(from.getTax());
        setDeleteFlag(from.getDeleteFlag());
        setCreateMember(from.getCreateMember());
        setType(from.getType());
        setClassId(from.getClassId());
        setCourseDate(from.getCourseDate());
        setBankUser(from.getBankUser());
        setBankIdentity(from.getBankIdentity());
        setBank(from.getBank());
        setBankCard(from.getBankCard());
        setStartTime(from.getStartTime());
        setEndTime(from.getEndTime());
        setStatus(from.getStatus());
        setAttachmentId(from.getAttachmentId());
        setAttachmentName(from.getAttachmentName());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IF2fCourse> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends F2fCourseEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.train.jooq.tables.records.F2fCourseRecord r = new com.zxy.product.train.jooq.tables.records.F2fCourseRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ID, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.NAME, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.NAME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SEQUENCE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SEQUENCE, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SEQUENCE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.OBJ) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.OBJ, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.OBJ));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.KEYWORD) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.KEYWORD, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.KEYWORD));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.LECTURER) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.LECTURER, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.LECTURER));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_DURATION) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_DURATION, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_DURATION));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_REWARD) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_REWARD, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_REWARD));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SATISFIED_DEGREE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SATISFIED_DEGREE, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SATISFIED_DEGREE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TEACH_METHOD) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TEACH_METHOD, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TEACH_METHOD));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TRAINEE_NUMBER1) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TRAINEE_NUMBER1, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TRAINEE_NUMBER1));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TRAINEE_NUMBER2) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TRAINEE_NUMBER2, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TRAINEE_NUMBER2));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SUMMARY) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SUMMARY, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.SUMMARY));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.REMARK) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.REMARK, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.REMARK));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TARGET) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TARGET, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TARGET));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.OUTLINE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.OUTLINE, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.OUTLINE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.INSTITUTION) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.INSTITUTION, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.INSTITUTION));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS_TEL) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS_TEL, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS_TEL));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS_EMAIL) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS_EMAIL, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CONTACTS_EMAIL));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CREATE_TIME, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.PAY) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.PAY, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.PAY));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TAX) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TAX, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TAX));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.DELETE_FLAG) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.DELETE_FLAG, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.DELETE_FLAG));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CREATE_MEMBER) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CREATE_MEMBER, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CREATE_MEMBER));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TYPE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TYPE, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.TYPE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CLASS_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CLASS_ID, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.CLASS_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_DATE) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_DATE, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.COURSE_DATE));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_USER) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_USER, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_USER));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_IDENTITY) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_IDENTITY, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_IDENTITY));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_CARD) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_CARD, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.BANK_CARD));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.START_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.START_TIME, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.START_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.END_TIME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.END_TIME, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.END_TIME));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.STATUS) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.STATUS, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.STATUS));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ATTACHMENT_ID) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ATTACHMENT_ID, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ATTACHMENT_ID));
                    }
                    if(row.indexOf(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ATTACHMENT_NAME) > -1){
                        r.setValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ATTACHMENT_NAME, record.getValue(com.zxy.product.train.jooq.tables.F2fCourse.F2F_COURSE.ATTACHMENT_NAME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
