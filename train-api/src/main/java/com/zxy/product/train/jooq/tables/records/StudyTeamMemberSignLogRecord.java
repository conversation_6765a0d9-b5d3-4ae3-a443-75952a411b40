/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamMemberSignLog;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamMemberSignLog;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学习团队成员签到表流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamMemberSignLogRecord extends UpdatableRecordImpl<StudyTeamMemberSignLogRecord> implements Record6<String, String, String, Long, Integer, Long>, IStudyTeamMemberSignLog {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_activity_id</code>. 团队活动id
     */
    @Override
    public void setActivityId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_activity_id</code>. 团队活动id
     */
    @Override
    public String getActivityId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_team_member_id</code>. 团队成员id
     */
    @Override
    public void setTeamMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_team_member_id</code>. 团队成员id
     */
    @Override
    public String getTeamMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_sign_time</code>. 签到时间
     */
    @Override
    public void setSignTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_sign_time</code>. 签到时间
     */
    @Override
    public Long getSignTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_sign_type</code>. 签到类型 1-签到 2-签退
     */
    @Override
    public void setSignType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_sign_type</code>. 签到类型 1-签到 2-签退
     */
    @Override
    public Integer getSignType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_study_team_member_sign_log.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_study_team_member_sign_log.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, Long, Integer, Long> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, Long, Integer, Long> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG.ACTIVITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG.TEAM_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG.SIGN_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG.SIGN_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getActivityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getTeamMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getSignTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getSignType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLogRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLogRecord value2(String value) {
        setActivityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLogRecord value3(String value) {
        setTeamMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLogRecord value4(Long value) {
        setSignTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLogRecord value5(Integer value) {
        setSignType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLogRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSignLogRecord values(String value1, String value2, String value3, Long value4, Integer value5, Long value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamMemberSignLog from) {
        setId(from.getId());
        setActivityId(from.getActivityId());
        setTeamMemberId(from.getTeamMemberId());
        setSignTime(from.getSignTime());
        setSignType(from.getSignType());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamMemberSignLog> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamMemberSignLogRecord
     */
    public StudyTeamMemberSignLogRecord() {
        super(StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG);
    }

    /**
     * Create a detached, initialised StudyTeamMemberSignLogRecord
     */
    public StudyTeamMemberSignLogRecord(String id, String activityId, String teamMemberId, Long signTime, Integer signType, Long createTime) {
        super(StudyTeamMemberSignLog.STUDY_TEAM_MEMBER_SIGN_LOG);

        set(0, id);
        set(1, activityId);
        set(2, teamMemberId);
        set(3, signTime);
        set(4, signType);
        set(5, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberSignLogEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberSignLogEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamMemberSignLogEntity)source;
        pojo.into(this);
        return true;
    }
}
