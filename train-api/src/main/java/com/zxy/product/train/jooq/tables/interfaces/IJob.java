/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IJob extends Serializable {

    /**
     * Setter for <code>train.t_job.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_job.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>train.t_job.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_job.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_job.f_name</code>. 职务名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_job.f_name</code>. 职务名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_job.f_code</code>. 职务编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>train.t_job.f_code</code>. 职务编码
     */
    public String getCode();

    /**
     * Setter for <code>train.t_job.f_organization_id</code>. 所属机构
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_job.f_organization_id</code>. 所属机构
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_job.f_status</code>. 职务状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>train.t_job.f_status</code>. 职务状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>train.t_job.f_job_type_id</code>. 职务类别
     */
    public void setJobTypeId(String value);

    /**
     * Getter for <code>train.t_job.f_job_type_id</code>. 职务类别
     */
    public String getJobTypeId();

    /**
     * Setter for <code>train.t_job.f_position_count</code>. 关联职位数
     */
    public void setPositionCount(Integer value);

    /**
     * Getter for <code>train.t_job.f_position_count</code>. 关联职位数
     */
    public Integer getPositionCount();

    /**
     * Setter for <code>train.t_job.f_member_count</code>. 职务人数
     */
    public void setMemberCount(Integer value);

    /**
     * Getter for <code>train.t_job.f_member_count</code>. 职务人数
     */
    public Integer getMemberCount();

    /**
     * Setter for <code>train.t_job.f_mis_code</code>. MIS省份简称
     */
    public void setMisCode(String value);

    /**
     * Getter for <code>train.t_job.f_mis_code</code>. MIS省份简称
     */
    public String getMisCode();

    /**
     * Setter for <code>train.t_job.f_mis_id</code>. MIS同步职务ID
     */
    public void setMisId(String value);

    /**
     * Getter for <code>train.t_job.f_mis_id</code>. MIS同步职务ID
     */
    public String getMisId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IJob
     */
    public void from(IJob from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IJob
     */
    public <E extends IJob> E into(E into);
}
