/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IStudentDetailTotal extends Serializable {

    /**
     * Setter for <code>train.t_student_detail_total.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_student_detail_total.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_student_detail_total.f_name</code>. 结算公司
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_student_detail_total.f_name</code>. 结算公司
     */
    public String getName();

    /**
     * Setter for <code>train.t_student_detail_total.f_settlement_company_code</code>. 结算公司组织编码
     */
    public void setSettlementCompanyCode(String value);

    /**
     * Getter for <code>train.t_student_detail_total.f_settlement_company_code</code>. 结算公司组织编码
     */
    public String getSettlementCompanyCode();

    /**
     * Setter for <code>train.t_student_detail_total.f_year</code>. 年份
     */
    public void setYear(String value);

    /**
     * Getter for <code>train.t_student_detail_total.f_year</code>. 年份
     */
    public String getYear();

    /**
     * Setter for <code>train.t_student_detail_total.f_january</code>. 一月
     */
    public void setJanuary(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_january</code>. 一月
     */
    public Double getJanuary();

    /**
     * Setter for <code>train.t_student_detail_total.f_march</code>. 三月
     */
    public void setMarch(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_march</code>. 三月
     */
    public Double getMarch();

    /**
     * Setter for <code>train.t_student_detail_total.f_february</code>. 二月
     */
    public void setFebruary(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_february</code>. 二月
     */
    public Double getFebruary();

    /**
     * Setter for <code>train.t_student_detail_total.f_december</code>. 十二月
     */
    public void setDecember(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_december</code>. 十二月
     */
    public Double getDecember();

    /**
     * Setter for <code>train.t_student_detail_total.f_november</code>. 十一月
     */
    public void setNovember(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_november</code>. 十一月
     */
    public Double getNovember();

    /**
     * Setter for <code>train.t_student_detail_total.f_october</code>. 十月
     */
    public void setOctober(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_october</code>. 十月
     */
    public Double getOctober();

    /**
     * Setter for <code>train.t_student_detail_total.f_september</code>. 九月
     */
    public void setSeptember(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_september</code>. 九月
     */
    public Double getSeptember();

    /**
     * Setter for <code>train.t_student_detail_total.f_august</code>. 八月
     */
    public void setAugust(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_august</code>. 八月
     */
    public Double getAugust();

    /**
     * Setter for <code>train.t_student_detail_total.f_july</code>. 七月
     */
    public void setJuly(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_july</code>. 七月
     */
    public Double getJuly();

    /**
     * Setter for <code>train.t_student_detail_total.f_june</code>. 六月
     */
    public void setJune(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_june</code>. 六月
     */
    public Double getJune();

    /**
     * Setter for <code>train.t_student_detail_total.f_may</code>. 五月
     */
    public void setMay(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_may</code>. 五月
     */
    public Double getMay();

    /**
     * Setter for <code>train.t_student_detail_total.f_april</code>. 四月
     */
    public void setApril(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_april</code>. 四月
     */
    public Double getApril();

    /**
     * Setter for <code>train.t_student_detail_total.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>train.t_student_detail_total.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>train.t_student_detail_total.f_total</code>. 总计
     */
    public void setTotal(Double value);

    /**
     * Getter for <code>train.t_student_detail_total.f_total</code>. 总计
     */
    public Double getTotal();

    /**
     * Setter for <code>train.t_student_detail_total.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_student_detail_total.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IStudentDetailTotal
     */
    public void from(IStudentDetailTotal from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IStudentDetailTotal
     */
    public <E extends IStudentDetailTotal> E into(E into);
}
