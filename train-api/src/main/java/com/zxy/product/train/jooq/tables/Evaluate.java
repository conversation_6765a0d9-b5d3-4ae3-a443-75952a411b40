/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.EvaluateRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Evaluate extends TableImpl<EvaluateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_evaluate</code>
     */
    public static final Evaluate EVALUATE = new Evaluate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EvaluateRecord> getRecordType() {
        return EvaluateRecord.class;
    }

    /**
     * The column <code>train.t_evaluate.f_id</code>. 主键
     */
    public final TableField<EvaluateRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_evaluate.f_class_id</code>. 班级ID
     */
    public final TableField<EvaluateRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_evaluate.f_method</code>. 考试方式
     */
    public final TableField<EvaluateRecord, String> METHOD = createField("f_method", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "考试方式");

    /**
     * The column <code>train.t_evaluate.f_attachment_id</code>. 附件ID
     */
    public final TableField<EvaluateRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "附件ID");

    /**
     * The column <code>train.t_evaluate.f_result</code>. 考核结果
     */
    public final TableField<EvaluateRecord, String> RESULT = createField("f_result", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "考核结果");

    /**
     * The column <code>train.t_evaluate.f_create_time</code>. 创建时间
     */
    public final TableField<EvaluateRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_evaluate.f_create_member</code>. 创建人ID
     */
    public final TableField<EvaluateRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_evaluate.f_attachment_name</code>. 附件名称
     */
    public final TableField<EvaluateRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "附件名称");

    /**
     * Create a <code>train.t_evaluate</code> table reference
     */
    public Evaluate() {
        this("t_evaluate", null);
    }

    /**
     * Create an aliased <code>train.t_evaluate</code> table reference
     */
    public Evaluate(String alias) {
        this(alias, EVALUATE);
    }

    private Evaluate(String alias, Table<EvaluateRecord> aliased) {
        this(alias, aliased, null);
    }

    private Evaluate(String alias, Table<EvaluateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<EvaluateRecord> getPrimaryKey() {
        return Keys.KEY_T_EVALUATE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<EvaluateRecord>> getKeys() {
        return Arrays.<UniqueKey<EvaluateRecord>>asList(Keys.KEY_T_EVALUATE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Evaluate as(String alias) {
        return new Evaluate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Evaluate rename(String name) {
        return new Evaluate(name, null);
    }
}
