/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IEvaluate extends Serializable {

    /**
     * Setter for <code>train.t_evaluate.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_evaluate.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_evaluate.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_evaluate.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_evaluate.f_method</code>. 考试方式
     */
    public void setMethod(String value);

    /**
     * Getter for <code>train.t_evaluate.f_method</code>. 考试方式
     */
    public String getMethod();

    /**
     * Setter for <code>train.t_evaluate.f_attachment_id</code>. 附件ID
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_evaluate.f_attachment_id</code>. 附件ID
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_evaluate.f_result</code>. 考核结果
     */
    public void setResult(String value);

    /**
     * Getter for <code>train.t_evaluate.f_result</code>. 考核结果
     */
    public String getResult();

    /**
     * Setter for <code>train.t_evaluate.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_evaluate.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_evaluate.f_create_member</code>. 创建人ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_evaluate.f_create_member</code>. 创建人ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_evaluate.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_evaluate.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IEvaluate
     */
    public void from(IEvaluate from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IEvaluate
     */
    public <E extends IEvaluate> E into(E into);
}
