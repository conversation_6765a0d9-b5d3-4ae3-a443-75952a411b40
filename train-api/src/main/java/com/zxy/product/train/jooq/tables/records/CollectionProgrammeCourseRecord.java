/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.CollectionProgrammeCourse;
import com.zxy.product.train.jooq.tables.interfaces.ICollectionProgrammeCourse;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 集采方案详解关联课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CollectionProgrammeCourseRecord extends UpdatableRecordImpl<CollectionProgrammeCourseRecord> implements Record5<String, String, String, Long, Integer>, ICollectionProgrammeCourse {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_collection_programme_course.f_id</code>. 系统ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_course.f_id</code>. 系统ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_collection_programme_course.f_collection_id</code>. 集采方案ID
     */
    @Override
    public void setCollectionId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_course.f_collection_id</code>. 集采方案ID
     */
    @Override
    public String getCollectionId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_collection_programme_course.f_course_id</code>. 课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_course.f_course_id</code>. 课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_collection_programme_course.f_create_time</code>. 关联时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_course.f_create_time</code>. 关联时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>train.t_collection_programme_course.f_type</code>. 关联课程类型：0-在线课程，1-集采课程
     */
    @Override
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_course.f_type</code>. 关联课程类型：0-在线课程，1-集采课程
     */
    @Override
    public Integer getType() {
        return (Integer) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Long, Integer> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, String, Long, Integer> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE.COLLECTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getCollectionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeCourseRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeCourseRecord value2(String value) {
        setCollectionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeCourseRecord value3(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeCourseRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeCourseRecord value5(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeCourseRecord values(String value1, String value2, String value3, Long value4, Integer value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICollectionProgrammeCourse from) {
        setId(from.getId());
        setCollectionId(from.getCollectionId());
        setCourseId(from.getCourseId());
        setCreateTime(from.getCreateTime());
        setType(from.getType());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICollectionProgrammeCourse> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CollectionProgrammeCourseRecord
     */
    public CollectionProgrammeCourseRecord() {
        super(CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE);
    }

    /**
     * Create a detached, initialised CollectionProgrammeCourseRecord
     */
    public CollectionProgrammeCourseRecord(String id, String collectionId, String courseId, Long createTime, Integer type) {
        super(CollectionProgrammeCourse.COLLECTION_PROGRAMME_COURSE);

        set(0, id);
        set(1, collectionId);
        set(2, courseId);
        set(3, createTime);
        set(4, type);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.CollectionProgrammeCourseEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.CollectionProgrammeCourseEntity pojo = (com.zxy.product.train.jooq.tables.pojos.CollectionProgrammeCourseEntity)source;
        pojo.into(this);
        return true;
    }
}
