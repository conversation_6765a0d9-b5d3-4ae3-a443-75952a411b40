/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.CollectionProgrammeConfig;
import com.zxy.product.train.jooq.tables.interfaces.ICollectionProgrammeConfig;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 集采方案配置
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CollectionProgrammeConfigRecord extends UpdatableRecordImpl<CollectionProgrammeConfigRecord> implements Record13<String, String, String, String, String, String, String, String, String, Long, String, String, Double>, ICollectionProgrammeConfig {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_collection_programme_config.f_id</code>. 系统ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_id</code>. 系统ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_name</code>. 集采机构名称
     */
    @Override
    public void setMechanismName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_name</code>. 集采机构名称
     */
    @Override
    public String getMechanismName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_contacts</code>. 集采机构联系人
     */
    @Override
    public void setMechanismContacts(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_contacts</code>. 集采机构联系人
     */
    @Override
    public String getMechanismContacts() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_contacts_number</code>. 集采机构联系人电话 
     */
    @Override
    public void setMechanismContactsNumber(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_contacts_number</code>. 集采机构联系人电话 
     */
    @Override
    public String getMechanismContactsNumber() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_mechanism_contacts_email</code>. 集采机构联系人邮箱 
     */
    @Override
    public void setMechanismContactsEmail(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_mechanism_contacts_email</code>. 集采机构联系人邮箱 
     */
    @Override
    public String getMechanismContactsEmail() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_programme_name</code>. 集采方案名称 
     */
    @Override
    public void setProgrammeName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_programme_name</code>. 集采方案名称 
     */
    @Override
    public String getProgrammeName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_attachment_id</code>. 集采方案附件ID
     */
    @Override
    public void setAttachmentId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_attachment_id</code>. 集采方案附件ID
     */
    @Override
    public String getAttachmentId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_attachment_name</code>. 集采方案附件名称
     */
    @Override
    public void setAttachmentName(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_attachment_name</code>. 集采方案附件名称
     */
    @Override
    public String getAttachmentName() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_create_member_id</code>. 创建人ID
     */
    @Override
    public void setCreateMemberId(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_create_member_id</code>. 创建人ID
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(10);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_attachment_type</code>. 集采方案附件类型
     */
    @Override
    public void setAttachmentType(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_attachment_type</code>. 集采方案附件类型
     */
    @Override
    public String getAttachmentType() {
        return (String) get(11);
    }

    /**
     * Setter for <code>train.t_collection_programme_config.f_course_unit_price</code>. 集采课程单价(元)
     */
    @Override
    public void setCourseUnitPrice(Double value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_collection_programme_config.f_course_unit_price</code>. 集采课程单价(元)
     */
    @Override
    public Double getCourseUnitPrice() {
        return (Double) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, String, String, String, String, String, String, Long, String, String, Double> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, String, String, String, String, String, String, Long, String, String, Double> valuesRow() {
        return (Row13) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.MECHANISM_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.MECHANISM_CONTACTS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.MECHANISM_CONTACTS_NUMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.MECHANISM_CONTACTS_EMAIL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.PROGRAMME_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.ATTACHMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.ATTACHMENT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.ATTACHMENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Double> field13() {
        return CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG.COURSE_UNIT_PRICE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMechanismName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getMechanismContacts();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMechanismContactsNumber();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getMechanismContactsEmail();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getProgrammeName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getAttachmentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getAttachmentName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getAttachmentType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Double value13() {
        return getCourseUnitPrice();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value2(String value) {
        setMechanismName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value3(String value) {
        setMechanismContacts(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value4(String value) {
        setMechanismContactsNumber(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value5(String value) {
        setMechanismContactsEmail(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value6(String value) {
        setProgrammeName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value7(String value) {
        setAttachmentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value8(String value) {
        setAttachmentName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value9(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value10(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value11(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value12(String value) {
        setAttachmentType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord value13(Double value) {
        setCourseUnitPrice(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CollectionProgrammeConfigRecord values(String value1, String value2, String value3, String value4, String value5, String value6, String value7, String value8, String value9, Long value10, String value11, String value12, Double value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICollectionProgrammeConfig from) {
        setId(from.getId());
        setMechanismName(from.getMechanismName());
        setMechanismContacts(from.getMechanismContacts());
        setMechanismContactsNumber(from.getMechanismContactsNumber());
        setMechanismContactsEmail(from.getMechanismContactsEmail());
        setProgrammeName(from.getProgrammeName());
        setAttachmentId(from.getAttachmentId());
        setAttachmentName(from.getAttachmentName());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setCreateMemberId(from.getCreateMemberId());
        setAttachmentType(from.getAttachmentType());
        setCourseUnitPrice(from.getCourseUnitPrice());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICollectionProgrammeConfig> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CollectionProgrammeConfigRecord
     */
    public CollectionProgrammeConfigRecord() {
        super(CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG);
    }

    /**
     * Create a detached, initialised CollectionProgrammeConfigRecord
     */
    public CollectionProgrammeConfigRecord(String id, String mechanismName, String mechanismContacts, String mechanismContactsNumber, String mechanismContactsEmail, String programmeName, String attachmentId, String attachmentName, String organizationId, Long createTime, String createMemberId, String attachmentType, Double courseUnitPrice) {
        super(CollectionProgrammeConfig.COLLECTION_PROGRAMME_CONFIG);

        set(0, id);
        set(1, mechanismName);
        set(2, mechanismContacts);
        set(3, mechanismContactsNumber);
        set(4, mechanismContactsEmail);
        set(5, programmeName);
        set(6, attachmentId);
        set(7, attachmentName);
        set(8, organizationId);
        set(9, createTime);
        set(10, createMemberId);
        set(11, attachmentType);
        set(12, courseUnitPrice);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.CollectionProgrammeConfigEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.CollectionProgrammeConfigEntity pojo = (com.zxy.product.train.jooq.tables.pojos.CollectionProgrammeConfigEntity)source;
        pojo.into(this);
        return true;
    }
}
