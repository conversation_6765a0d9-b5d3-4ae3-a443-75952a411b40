package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudyTeamAchievementEntity;

import java.util.List;

public class StudyTeamAchievement extends StudyTeamAchievementEntity {
    private static final long serialVersionUID = -7042886090814662168L;

    /**
     * 是否置顶
     * 1.是 0.否
     */
    public static final Integer TOP_STATUS_YES = 1;
    public static final Integer TOP_STATUS_NO = 0;

    /**
     * 是否隐藏
     * 1.是 0.否
     */
    public static final Integer TOP_STATUS_HIDE = 1;
    public static final Integer TOP_STATUS_SHOW = 0;

    /**
     * 是否删除
     * 1.是 0.否
     */
    public static final int DELETE_FALSE = 0;
    public static final int DELETE_TRUE = 1;

    /**
     * 是否加精
     * 1.是 0.否
     */
    public static final int ESSENCE_STATUS_YES = 1;
    public static final int ESSENCE_STATUS_NO = 0;

    /**
     * 回复数据
     */
    private List<StudyTeamAchievementReply> replyList;

    /**
     * 人员归属部门
     */
    private Organization memberOrg;

    /**
     * 点赞id
     */
    private String praiseId;

    private String createTimeStr;

    private String memberName;
    private String memberHeadPath;

    /**
     * 评论人
     */
    private Member member;

    /**
     * 所属学习活动
     */
    private StudyTeamActivity studyTeamActivity;

    private String businessName;

    public List<StudyTeamAchievementReply> getReplyList() {
        return replyList;
    }

    public void setReplyList(List<StudyTeamAchievementReply> replyList) {
        this.replyList = replyList;
    }

    public String getPraiseId() {
        return praiseId;
    }

    public void setPraiseId(String praiseId) {
        this.praiseId = praiseId;
    }

    public Organization getMemberOrg() {
        return memberOrg;
    }

    public void setMemberOrg(Organization memberOrg) {
        this.memberOrg = memberOrg;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberHeadPath() {
        return memberHeadPath;
    }

    public void setMemberHeadPath(String memberHeadPath) {
        this.memberHeadPath = memberHeadPath;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public StudyTeamActivity getStudyTeamActivity() {
        return studyTeamActivity;
    }

    public void setStudyTeamActivity(StudyTeamActivity studyTeamActivity) {
        this.studyTeamActivity = studyTeamActivity;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }
}
