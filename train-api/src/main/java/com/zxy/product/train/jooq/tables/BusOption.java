/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.BusOptionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 班车选项主题表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BusOption extends TableImpl<BusOptionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_bus_option</code>
     */
    public static final BusOption BUS_OPTION = new BusOption();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BusOptionRecord> getRecordType() {
        return BusOptionRecord.class;
    }

    /**
     * The column <code>train.t_bus_option.f_id</code>. 主键
     */
    public final TableField<BusOptionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_bus_option.f_name</code>. 选项主题
     */
    public final TableField<BusOptionRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "选项主题");

    /**
     * The column <code>train.t_bus_option.f_date</code>. 选项时间
     */
    public final TableField<BusOptionRecord, Long> DATE = createField("f_date", org.jooq.impl.SQLDataType.BIGINT, this, "选项时间");

    /**
     * The column <code>train.t_bus_option.f_address</code>. 地点
     */
    public final TableField<BusOptionRecord, String> ADDRESS = createField("f_address", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "地点");

    /**
     * The column <code>train.t_bus_option.f_explain</code>. 其他说明
     */
    public final TableField<BusOptionRecord, String> EXPLAIN = createField("f_explain", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "其他说明");

    /**
     * The column <code>train.t_bus_option.f_create_mem</code>. 创建人ID
     */
    public final TableField<BusOptionRecord, String> CREATE_MEM = createField("f_create_mem", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_bus_option.f_create_time</code>. 创建时间
     */
    public final TableField<BusOptionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_bus_option.f_bus_id</code>. 班车ID
     */
    public final TableField<BusOptionRecord, String> BUS_ID = createField("f_bus_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班车ID");

    /**
     * The column <code>train.t_bus_option.f_flag</code>. option排序
     */
    public final TableField<BusOptionRecord, Integer> FLAG = createField("f_flag", org.jooq.impl.SQLDataType.INTEGER, this, "option排序");

    /**
     * Create a <code>train.t_bus_option</code> table reference
     */
    public BusOption() {
        this("t_bus_option", null);
    }

    /**
     * Create an aliased <code>train.t_bus_option</code> table reference
     */
    public BusOption(String alias) {
        this(alias, BUS_OPTION);
    }

    private BusOption(String alias, Table<BusOptionRecord> aliased) {
        this(alias, aliased, null);
    }

    private BusOption(String alias, Table<BusOptionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "班车选项主题表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<BusOptionRecord> getPrimaryKey() {
        return Keys.KEY_T_BUS_OPTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<BusOptionRecord>> getKeys() {
        return Arrays.<UniqueKey<BusOptionRecord>>asList(Keys.KEY_T_BUS_OPTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusOption as(String alias) {
        return new BusOption(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public BusOption rename(String name) {
        return new BusOption(name, null);
    }
}
