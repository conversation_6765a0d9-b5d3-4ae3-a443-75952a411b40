/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.LabelRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Label extends TableImpl<LabelRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_label</code>
     */
    public static final Label LABEL = new Label();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LabelRecord> getRecordType() {
        return LabelRecord.class;
    }

    /**
     * The column <code>train.t_label.f_id</code>. ID
     */
    public final TableField<LabelRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_label.f_name</code>. 名称 
     */
    public final TableField<LabelRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(20), this, "名称 ");

    /**
     * The column <code>train.t_label.f_organization_id</code>. 所属组织
     */
    public final TableField<LabelRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属组织");

    /**
     * The column <code>train.t_label.f_description</code>. 描述
     */
    public final TableField<LabelRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(1000), this, "描述");

    /**
     * The column <code>train.t_label.f_create_member</code>. 创建人
     */
    public final TableField<LabelRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_label.f_create_time</code>. 创建时间
     */
    public final TableField<LabelRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * Create a <code>train.t_label</code> table reference
     */
    public Label() {
        this("t_label", null);
    }

    /**
     * Create an aliased <code>train.t_label</code> table reference
     */
    public Label(String alias) {
        this(alias, LABEL);
    }

    private Label(String alias, Table<LabelRecord> aliased) {
        this(alias, aliased, null);
    }

    private Label(String alias, Table<LabelRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LabelRecord> getPrimaryKey() {
        return Keys.KEY_T_LABEL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LabelRecord>> getKeys() {
        return Arrays.<UniqueKey<LabelRecord>>asList(Keys.KEY_T_LABEL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Label as(String alias) {
        return new Label(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Label rename(String name) {
        return new Label(name, null);
    }
}
