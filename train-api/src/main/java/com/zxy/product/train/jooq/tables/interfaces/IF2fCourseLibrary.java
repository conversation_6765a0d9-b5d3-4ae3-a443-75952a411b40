/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 面授课程库表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IF2fCourseLibrary extends Serializable {

    /**
     * Setter for <code>train.t_f2f_course_library.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>train.t_f2f_course_library.f_name</code>. 课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_name</code>. 课程名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_f2f_course_library.f_sequence</code>. 课程分类/序列
     */
    public void setSequence(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_sequence</code>. 课程分类/序列
     */
    public String getSequence();

    /**
     * Setter for <code>train.t_f2f_course_library.f_obj</code>. 授课对象
     */
    public void setObj(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_obj</code>. 授课对象
     */
    public String getObj();

    /**
     * Setter for <code>train.t_f2f_course_library.f_keyword</code>. 关键词
     */
    public void setKeyword(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_keyword</code>. 关键词
     */
    public String getKeyword();

    /**
     * Setter for <code>train.t_f2f_course_library.f_summary</code>. 课程简介
     */
    public void setSummary(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_summary</code>. 课程简介
     */
    public String getSummary();

    /**
     * Setter for <code>train.t_f2f_course_library.f_remark</code>. 备注
     */
    public void setRemark(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_remark</code>. 备注
     */
    public String getRemark();

    /**
     * Setter for <code>train.t_f2f_course_library.f_target</code>. 课程目标
     */
    public void setTarget(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_target</code>. 课程目标
     */
    public String getTarget();

    /**
     * Setter for <code>train.t_f2f_course_library.f_outline</code>. 课程大纲
     */
    public void setOutline(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_outline</code>. 课程大纲
     */
    public String getOutline();

    /**
     * Setter for <code>train.t_f2f_course_library.f_course_attributes</code>. 课程属性
     */
    public void setCourseAttributes(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_course_attributes</code>. 课程属性
     */
    public String getCourseAttributes();

    /**
     * Setter for <code>train.t_f2f_course_library.f_institution_id</code>. 课程所属机构
     */
    public void setInstitutionId(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_institution_id</code>. 课程所属机构
     */
    public String getInstitutionId();

    /**
     * Setter for <code>train.t_f2f_course_library.f_organization_id</code>. 归属部门
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_organization_id</code>. 归属部门
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_f2f_course_library.f_reference_time</code>. 参考时长
     */
    public void setReferenceTime(Double value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_reference_time</code>. 参考时长
     */
    public Double getReferenceTime();

    /**
     * Setter for <code>train.t_f2f_course_library.f_course_plan</code>. 课程方案
     */
    public void setCoursePlan(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_course_plan</code>. 课程方案
     */
    public String getCoursePlan();

    /**
     * Setter for <code>train.t_f2f_course_library.f_is_share</code>. 是否共享0：是1：否
     */
    public void setIsShare(Integer value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_is_share</code>. 是否共享0：是1：否
     */
    public Integer getIsShare();

    /**
     * Setter for <code>train.t_f2f_course_library.f_is_use</code>. 0：启用1：禁用
     */
    public void setIsUse(Integer value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_is_use</code>. 0：启用1：禁用
     */
    public Integer getIsUse();

    /**
     * Setter for <code>train.t_f2f_course_library.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>train.t_f2f_course_library.f_update_time</code>. 修改时间
     */
    public void setUpdateTime(Long value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_update_time</code>. 修改时间
     */
    public Long getUpdateTime();

    /**
     * Setter for <code>train.t_f2f_course_library.f_create_member</code>. 创建ID
     */
    public void setCreateMember(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_create_member</code>. 创建ID
     */
    public String getCreateMember();

    /**
     * Setter for <code>train.t_f2f_course_library.f_update_member</code>. 修改人ID
     */
    public void setUpdateMember(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_update_member</code>. 修改人ID
     */
    public String getUpdateMember();

    /**
     * Setter for <code>train.t_f2f_course_library.f_attachment_id</code>. 附件ID
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_attachment_id</code>. 附件ID
     */
    public String getAttachmentId();

    /**
     * Setter for <code>train.t_f2f_course_library.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>train.t_f2f_course_library.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IF2fCourseLibrary
     */
    public void from(IF2fCourseLibrary from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IF2fCourseLibrary
     */
    public <E extends IF2fCourseLibrary> E into(E into);
}
