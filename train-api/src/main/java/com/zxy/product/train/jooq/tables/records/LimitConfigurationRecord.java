/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.LimitConfiguration;
import com.zxy.product.train.jooq.tables.interfaces.ILimitConfiguration;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LimitConfigurationRecord extends UpdatableRecordImpl<LimitConfigurationRecord> implements Record9<String, String, Integer, Integer, Integer, Integer, Long, String, Integer>, ILimitConfiguration {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_limit_configuration.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public void setConfigurationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_configuration_id</code>. 关联配置表ID
     */
    @Override
    public String getConfigurationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public void setTypeId(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_type_id</code>. 关联类型ID
     */
    @Override
    public Integer getTypeId() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_year</code>. 年份
     */
    @Override
    public void setYear(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_year</code>. 年份
     */
    @Override
    public Integer getYear() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_month</code>. 月份
     */
    @Override
    public void setMonth(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_month</code>. 月份
     */
    @Override
    public Integer getMonth() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_limit</code>. 额度
     */
    @Override
    public void setLimit(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_limit</code>. 额度
     */
    @Override
    public Integer getLimit() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_limit_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_limit_configuration.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, Integer, Integer, Integer, Long, String, Integer> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, Integer, Integer, Integer, Long, String, Integer> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return LimitConfiguration.LIMIT_CONFIGURATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return LimitConfiguration.LIMIT_CONFIGURATION.CONFIGURATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return LimitConfiguration.LIMIT_CONFIGURATION.TYPE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return LimitConfiguration.LIMIT_CONFIGURATION.YEAR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return LimitConfiguration.LIMIT_CONFIGURATION.MONTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return LimitConfiguration.LIMIT_CONFIGURATION.LIMIT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return LimitConfiguration.LIMIT_CONFIGURATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return LimitConfiguration.LIMIT_CONFIGURATION.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return LimitConfiguration.LIMIT_CONFIGURATION.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getConfigurationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getTypeId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getYear();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getMonth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getLimit();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value2(String value) {
        setConfigurationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value3(Integer value) {
        setTypeId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value4(Integer value) {
        setYear(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value5(Integer value) {
        setMonth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value6(Integer value) {
        setLimit(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value7(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value8(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord value9(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LimitConfigurationRecord values(String value1, String value2, Integer value3, Integer value4, Integer value5, Integer value6, Long value7, String value8, Integer value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILimitConfiguration from) {
        setId(from.getId());
        setConfigurationId(from.getConfigurationId());
        setTypeId(from.getTypeId());
        setYear(from.getYear());
        setMonth(from.getMonth());
        setLimit(from.getLimit());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setDeleteFlag(from.getDeleteFlag());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILimitConfiguration> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LimitConfigurationRecord
     */
    public LimitConfigurationRecord() {
        super(LimitConfiguration.LIMIT_CONFIGURATION);
    }

    /**
     * Create a detached, initialised LimitConfigurationRecord
     */
    public LimitConfigurationRecord(String id, String configurationId, Integer typeId, Integer year, Integer month, Integer limit, Long createTime, String createMember, Integer deleteFlag) {
        super(LimitConfiguration.LIMIT_CONFIGURATION);

        set(0, id);
        set(1, configurationId);
        set(2, typeId);
        set(3, year);
        set(4, month);
        set(5, limit);
        set(6, createTime);
        set(7, createMember);
        set(8, deleteFlag);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.LimitConfigurationEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.LimitConfigurationEntity pojo = (com.zxy.product.train.jooq.tables.pojos.LimitConfigurationEntity)source;
        pojo.into(this);
        return true;
    }
}
