/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.SettlementConfigurationValueRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SettlementConfigurationValue extends TableImpl<SettlementConfigurationValueRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_settlement_configuration_value</code>
     */
    public static final SettlementConfigurationValue SETTLEMENT_CONFIGURATION_VALUE = new SettlementConfigurationValue();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SettlementConfigurationValueRecord> getRecordType() {
        return SettlementConfigurationValueRecord.class;
    }

    /**
     * The column <code>train.t_settlement_configuration_value.f_id</code>. 主键
     */
    public final TableField<SettlementConfigurationValueRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>train.t_settlement_configuration_value.f_organization_id</code>. 关联组织ID
     */
    public final TableField<SettlementConfigurationValueRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "关联组织ID");

    /**
     * The column <code>train.t_settlement_configuration_value.f_settlement_id</code>. 结算表ID
     */
    public final TableField<SettlementConfigurationValueRecord, String> SETTLEMENT_ID = createField("f_settlement_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "结算表ID");

    /**
     * The column <code>train.t_settlement_configuration_value.f_sort</code>. 排序
     */
    public final TableField<SettlementConfigurationValueRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER, this, "排序");

    /**
     * The column <code>train.t_settlement_configuration_value.f_create_time</code>. 创建时间
     */
    public final TableField<SettlementConfigurationValueRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_settlement_configuration_value.f_create_member</code>. 创建人ID
     */
    public final TableField<SettlementConfigurationValueRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人ID");

    /**
     * The column <code>train.t_settlement_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    public final TableField<SettlementConfigurationValueRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "当前节点的所有父节点");

    /**
     * Create a <code>train.t_settlement_configuration_value</code> table reference
     */
    public SettlementConfigurationValue() {
        this("t_settlement_configuration_value", null);
    }

    /**
     * Create an aliased <code>train.t_settlement_configuration_value</code> table reference
     */
    public SettlementConfigurationValue(String alias) {
        this(alias, SETTLEMENT_CONFIGURATION_VALUE);
    }

    private SettlementConfigurationValue(String alias, Table<SettlementConfigurationValueRecord> aliased) {
        this(alias, aliased, null);
    }

    private SettlementConfigurationValue(String alias, Table<SettlementConfigurationValueRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SettlementConfigurationValueRecord> getPrimaryKey() {
        return Keys.KEY_T_SETTLEMENT_CONFIGURATION_VALUE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SettlementConfigurationValueRecord>> getKeys() {
        return Arrays.<UniqueKey<SettlementConfigurationValueRecord>>asList(Keys.KEY_T_SETTLEMENT_CONFIGURATION_VALUE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValue as(String alias) {
        return new SettlementConfigurationValue(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SettlementConfigurationValue rename(String name) {
        return new SettlementConfigurationValue(name, null);
    }
}
