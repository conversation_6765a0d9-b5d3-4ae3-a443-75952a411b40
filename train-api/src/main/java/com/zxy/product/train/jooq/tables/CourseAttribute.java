/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.CourseAttributeRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 课程属性表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseAttribute extends TableImpl<CourseAttributeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_course_attribute</code>
     */
    public static final CourseAttribute COURSE_ATTRIBUTE = new CourseAttribute();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseAttributeRecord> getRecordType() {
        return CourseAttributeRecord.class;
    }

    /**
     * The column <code>train.t_course_attribute.f_id</code>. 系统ID
     */
    public final TableField<CourseAttributeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "系统ID");

    /**
     * The column <code>train.t_course_attribute.f_attribute_name</code>. 课程属性名称
     */
    public final TableField<CourseAttributeRecord, String> ATTRIBUTE_NAME = createField("f_attribute_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "课程属性名称");

    /**
     * The column <code>train.t_course_attribute.f_attribute_remark</code>. 课程属性描述
     */
    public final TableField<CourseAttributeRecord, String> ATTRIBUTE_REMARK = createField("f_attribute_remark", org.jooq.impl.SQLDataType.VARCHAR.length(500).nullable(false), this, "课程属性描述");

    /**
     * The column <code>train.t_course_attribute.f_create_member_id</code>. 创建人ID
     */
    public final TableField<CourseAttributeRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "创建人ID");

    /**
     * The column <code>train.t_course_attribute.f_exit_member_id</code>. 最后修改人ID
     */
    public final TableField<CourseAttributeRecord, String> EXIT_MEMBER_ID = createField("f_exit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "最后修改人ID");

    /**
     * The column <code>train.t_course_attribute.f_organization_id</code>. 组织ID
     */
    public final TableField<CourseAttributeRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "组织ID");

    /**
     * The column <code>train.t_course_attribute.f_create_time</code>. 创建时间
     */
    public final TableField<CourseAttributeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>train.t_course_attribute.f_update_time</code>. 最后修改时间
     */
    public final TableField<CourseAttributeRecord, Long> UPDATE_TIME = createField("f_update_time", org.jooq.impl.SQLDataType.BIGINT, this, "最后修改时间");

    /**
     * Create a <code>train.t_course_attribute</code> table reference
     */
    public CourseAttribute() {
        this("t_course_attribute", null);
    }

    /**
     * Create an aliased <code>train.t_course_attribute</code> table reference
     */
    public CourseAttribute(String alias) {
        this(alias, COURSE_ATTRIBUTE);
    }

    private CourseAttribute(String alias, Table<CourseAttributeRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseAttribute(String alias, Table<CourseAttributeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "课程属性表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseAttributeRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_ATTRIBUTE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseAttributeRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseAttributeRecord>>asList(Keys.KEY_T_COURSE_ATTRIBUTE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseAttribute as(String alias) {
        return new CourseAttribute(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseAttribute rename(String name) {
        return new CourseAttribute(name, null);
    }
}
