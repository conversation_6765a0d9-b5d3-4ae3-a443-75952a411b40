package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.CollegeTeachingEntity;

public class CollegeTeaching extends CollegeTeachingEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8266133300429492649L;
	
	private String LecturerOrganization;
	private String courseName;
	private CourseAttach courseAttach;
	private String projectId;
	private Integer lecturerType;
	private String attributeId;	
	private String attributeName;
	private String institutionId;
	private String lecturerOrganizationId;
	private String courseOrganizationId;
	private Boolean isGrant;
	private String courseSequenceName;
	
	private Integer isShare;
 	

	public Integer getIsShare() {
		return isShare;
	}

	public void setIsShare(Integer isShare) {
		this.isShare = isShare;
	}

	public Boolean getIsGrant() {
		return isGrant;
	}

	public void setIsGrant(Boolean isGrant) {
		this.isGrant = isGrant;
	}

	public String getLecturerOrganizationId() {
		return lecturerOrganizationId;
	}

	public void setLecturerOrganizationId(String lecturerOrganizationId) {
		this.lecturerOrganizationId = lecturerOrganizationId;
	}

	public String getCourseOrganizationId() {
		return courseOrganizationId;
	}

	public void setCourseOrganizationId(String courseOrganizationId) {
		this.courseOrganizationId = courseOrganizationId;
	}

	public String getInstitutionId() {
		return institutionId;
	}

	public void setInstitutionId(String institutionId) {
		this.institutionId = institutionId;
	}

	public String getAttributeId() {
		return attributeId;
	}

	public void setAttributeId(String attributeId) {
		this.attributeId = attributeId;
	}

	public String getAttributeName() {
		return attributeName;
	}

	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}
	
	private List<CourseAttach> courseAttachList;
	
	
	
	public List<CourseAttach> getCourseAttachList() {
		return courseAttachList;
	}

	public void setCourseAttachList(List<CourseAttach> courseAttachList) {
		this.courseAttachList = courseAttachList;
	}

	public Integer getLecturerType() {
		return lecturerType;
	}

	public void setLecturerType(Integer lecturerType) {
		this.lecturerType = lecturerType;
	}

	
	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public CourseAttach getCourseAttach() {
		return courseAttach;
	}

	public void setCourseAttach(CourseAttach courseAttach) {
		this.courseAttach = courseAttach;
	}

	
	public String getCourseName() {
		return courseName;
	}

	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getLecturerOrganization() {
		return LecturerOrganization;
	}

	public void setLecturerOrganization(String lecturerOrganization) {
		LecturerOrganization = lecturerOrganization;
	}

	public String getCourseSequenceName() {
		return courseSequenceName;
	}

	public void setCourseSequenceName(String courseSequenceName) {
		this.courseSequenceName = courseSequenceName;
	}
	

}
