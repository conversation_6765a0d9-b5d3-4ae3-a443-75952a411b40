/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.StudyTeamActivityAttachment;
import com.zxy.product.train.jooq.tables.interfaces.IStudyTeamActivityAttachment;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 团队学习班-活动资料表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamActivityAttachmentRecord extends UpdatableRecordImpl<StudyTeamActivityAttachmentRecord> implements Record6<String, String, String, String, Integer, Long>, IStudyTeamActivityAttachment {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_activity_id</code>. 活动id
     */
    @Override
    public void setActivityId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_activity_id</code>. 活动id
     */
    @Override
    public String getActivityId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_name</code>. 资料名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_name</code>. 资料名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_attachment_id</code>. 资料id
     */
    @Override
    public void setAttachmentId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_attachment_id</code>. 资料id
     */
    @Override
    public String getAttachmentId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_attachment_type</code>. 附件类型，1: 文档, 2: 图片, 4: 压缩文件, 5: 音频, 6: 视频, 7: EPUB电子书, 10: 其它
     */
    @Override
    public void setAttachmentType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_attachment_type</code>. 附件类型，1: 文档, 2: 图片, 4: 压缩文件, 5: 音频, 6: 视频, 7: EPUB电子书, 10: 其它
     */
    @Override
    public Integer getAttachmentType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>train.t_study_team_activity_attachment.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_study_team_activity_attachment.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, String, Integer, Long> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<String, String, String, String, Integer, Long> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT.ACTIVITY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT.ATTACHMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT.ATTACHMENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field6() {
        return StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getActivityId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getAttachmentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getAttachmentType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachmentRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachmentRecord value2(String value) {
        setActivityId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachmentRecord value3(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachmentRecord value4(String value) {
        setAttachmentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachmentRecord value5(Integer value) {
        setAttachmentType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachmentRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamActivityAttachmentRecord values(String value1, String value2, String value3, String value4, Integer value5, Long value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IStudyTeamActivityAttachment from) {
        setId(from.getId());
        setActivityId(from.getActivityId());
        setName(from.getName());
        setAttachmentId(from.getAttachmentId());
        setAttachmentType(from.getAttachmentType());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IStudyTeamActivityAttachment> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached StudyTeamActivityAttachmentRecord
     */
    public StudyTeamActivityAttachmentRecord() {
        super(StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT);
    }

    /**
     * Create a detached, initialised StudyTeamActivityAttachmentRecord
     */
    public StudyTeamActivityAttachmentRecord(String id, String activityId, String name, String attachmentId, Integer attachmentType, Long createTime) {
        super(StudyTeamActivityAttachment.STUDY_TEAM_ACTIVITY_ATTACHMENT);

        set(0, id);
        set(1, activityId);
        set(2, name);
        set(3, attachmentId);
        set(4, attachmentType);
        set(5, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityAttachmentEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityAttachmentEntity pojo = (com.zxy.product.train.jooq.tables.pojos.StudyTeamActivityAttachmentEntity)source;
        pojo.into(this);
        return true;
    }
}
