package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.TrainProjectEntity;

import java.util.List;

public class TrainProject extends TrainProjectEntity {

    private static final long serialVersionUID = 6308513344663491738L;

    //数据同步自培训计划
    public static final Integer SYNCHRONOUS_PROJECT = 0;
    //数据同步自班级
    public static final Integer SYNCHRONOUS_CLASS = 0;
    //自建
    public static final Integer BUILD_ONESELF = 1;
    //校内培训
    public static final Integer ON_CAMPUS_TRAINING = 0;
    //校外培训
    public static final Integer OFF_CAMPUS_TRAINING = 1;
    //在线学习
    public static final Integer ONLINE_LEARNING = 2;
    //非重点
    public static final Integer NON_KEY_PROJECT = 0;
    //重点
    public static final Integer KEY_PROJECT = 1;
    //垂直线条
    public static final Integer VERTICAL_LINE = 0;
    //非垂直线条
    public static final Integer NON_VERTICAL_LINE = 1;

    public static final Integer ENABLE = 0;

    public static final Integer DISABLE = 1;

    // 专题计划类型
    public static final Integer SUBJECT_PLAN = 1;
    // 培训班计划类型
    public static final Integer TRAIN_PLAN = 0;

    private Integer progressDetails;

    private PlanningImplementation planningImplementation;

    private String bigTypeName;  // 培训大类名称

    private String smallTypeName;  // 培训小类名称

    private String orgName;

    private String orgId;

    private String orgCode;

    private Organization organization;

    private String planPlaningName;  // 策划名称

    private String embodimentName;  // 实施名称

    private String planningDepartment; // 策划部门

    private String implementationDepartment; // 实施部门

    private String planningMember;  // 策划人

    private String implementationMember;  // 实施人

    private Integer type;

    private String contactMemberName;

    private List<PlanningImplementationRelated> planningDepartments; // 策划部门

    private List<PlanningImplementationRelated> implementationDepartments; // 实施部门


    public PlanningImplementation getPlanningImplementation() {
        return planningImplementation;
    }

    public void setPlanningImplementation(PlanningImplementation planningImplementation) {
        this.planningImplementation = planningImplementation;
    }

    public void setProgressDetails(Integer progressDetails) {
        this.progressDetails = progressDetails;
    }

    public Integer getProgressDetails() {
        return progressDetails;
    }

    public String getBigTypeName() {
        return bigTypeName;
    }

    public void setBigTypeName(String bigTypeName) {
        this.bigTypeName = bigTypeName;
    }

    public String getSmallTypeName() {
        return smallTypeName;
    }

    public void setSmallTypeName(String smallTypeName) {
        this.smallTypeName = smallTypeName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public String getPlanPlaningName() {
        return planPlaningName;
    }

    public void setPlanPlaningName(String planPlaningName) {
        this.planPlaningName = planPlaningName;
    }

    public String getEmbodimentName() {
        return embodimentName;
    }

    public void setEmbodimentName(String embodimentName) {
        this.embodimentName = embodimentName;
    }

    public String getPlanningDepartment() {
        return planningDepartment;
    }

    public void setPlanningDepartment(String planningDepartment) {
        this.planningDepartment = planningDepartment;
    }

    public String getImplementationDepartment() {
        return implementationDepartment;
    }

    public void setImplementationDepartment(String implementationDepartment) {
        this.implementationDepartment = implementationDepartment;
    }

    public String getPlanningMember() {
        return planningMember;
    }

    public void setPlanningMember(String planningMember) {
        this.planningMember = planningMember;
    }

    public String getImplementationMember() {
        return implementationMember;
    }

    public void setImplementationMember(String implementationMember) {
        this.implementationMember = implementationMember;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContactMemberName() {
        return contactMemberName;
    }

    public void setContactMemberName(String contactMemberName) {
        this.contactMemberName = contactMemberName;
    }
    
    public List<PlanningImplementationRelated> getPlanningDepartments() {
        return planningDepartments;
    }

    public void setPlanningDepartments(List<PlanningImplementationRelated> planningDepartments) {
        this.planningDepartments = planningDepartments;
    }

    public List<PlanningImplementationRelated> getImplementationDepartments() {
        return implementationDepartments;
    }

    public void setImplementationDepartments(List<PlanningImplementationRelated> implementationDepartments) {
        this.implementationDepartments = implementationDepartments;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
}
