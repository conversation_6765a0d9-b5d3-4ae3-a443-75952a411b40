/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 公司段对应关系
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICorporateSegmentRelation extends Serializable {

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_organization_name</code>. 公司名称
     */
    public void setOrganizationName(String value);

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_organization_name</code>. 公司名称
     */
    public String getOrganizationName();

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_intercourse_section</code>. 往来段
     */
    public void setIntercourseSection(String value);

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_intercourse_section</code>. 往来段
     */
    public String getIntercourseSection();

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_organization_id</code>. 机构ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_organization_id</code>. 机构ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>train.t_corporate_segment_relation.f_order</code>.
     */
    public void setOrder(Integer value);

    /**
     * Getter for <code>train.t_corporate_segment_relation.f_order</code>.
     */
    public Integer getOrder();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICorporateSegmentRelation
     */
    public void from(ICorporateSegmentRelation from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICorporateSegmentRelation
     */
    public <E extends ICorporateSegmentRelation> E into(E into);
}
