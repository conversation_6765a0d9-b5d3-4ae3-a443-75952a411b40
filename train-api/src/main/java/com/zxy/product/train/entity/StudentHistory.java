package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.StudentHistoryEntity;

/**
 * 历史学员
 * @ClassName: StudentHistory
 * @author: Acong
 * @date: 2018年1月26日
 */
public class StudentHistory extends StudentHistoryEntity {

	private static final long serialVersionUID = -2435733870810474341L;

	public static final String STATUS_NOT = "1N";	// 状态:未通过
	public static final String STATUS_WAIT = "2W";	// 状态:待审核
	public static final String STATUS_YES = "3Y";	// 状态:通过

	private String memberName;
	private String memberFullName;
	private String organizationName;
	public String getMemberName() {
		return memberName;
	}
	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	public String getMemberFullName() {
		return memberFullName;
	}
	public void setMemberFullName(String memberFullName) {
		this.memberFullName = memberFullName;
	}
	public String getOrganizationName() {
		return organizationName;
	}
	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

}
