package com.zxy.product.train.entity;

import com.zxy.product.train.jooq.tables.pojos.ClassOnlineCourseEntity;

/**
 * 在线课程
 * <AUTHOR>
 *
 */
public class ClassOnlineCourse extends ClassOnlineCourseEntity {

    private static final long serialVersionUID = 8927194033598469403L;

    public static final Integer TYPE_ONLINE = 1;//在线课程
    public static final Integer TYPE_KNOWLEDGE = 2;//知识
    public static final Integer DELETE_THERE = 3;

    public static final Integer DELETE_FALSE = 0;
    public static final Integer DELETE_TRUE = 1;
    public static final String DEFAULT_THEME_ID = "0";

    public static final String CACHE_ONLINE_COURSE_FOR_DETAIL = "class-detail-online-course-key";

    private String themeName;//主题名称

	public String getThemeName() {
        return themeName;
    }

    public void setThemeName(String themeName) {
        this.themeName = themeName;
    }

}
