/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.StudyTeamMemberSignRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习团队成员签到表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class StudyTeamMemberSign extends TableImpl<StudyTeamMemberSignRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_study_team_member_sign</code>
     */
    public static final StudyTeamMemberSign STUDY_TEAM_MEMBER_SIGN = new StudyTeamMemberSign();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<StudyTeamMemberSignRecord> getRecordType() {
        return StudyTeamMemberSignRecord.class;
    }

    /**
     * The column <code>train.t_study_team_member_sign.f_id</code>. ID
     */
    public final TableField<StudyTeamMemberSignRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_study_team_member_sign.f_activity_id</code>. 团队活动id
     */
    public final TableField<StudyTeamMemberSignRecord, String> ACTIVITY_ID = createField("f_activity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队活动id");

    /**
     * The column <code>train.t_study_team_member_sign.f_team_member_id</code>. 团队成员id
     */
    public final TableField<StudyTeamMemberSignRecord, String> TEAM_MEMBER_ID = createField("f_team_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "团队成员id");

    /**
     * The column <code>train.t_study_team_member_sign.f_sign_in_time</code>. 签到时间
     */
    public final TableField<StudyTeamMemberSignRecord, Long> SIGN_IN_TIME = createField("f_sign_in_time", org.jooq.impl.SQLDataType.BIGINT, this, "签到时间");

    /**
     * The column <code>train.t_study_team_member_sign.f_sign_off_time</code>. 签退时间
     */
    public final TableField<StudyTeamMemberSignRecord, Long> SIGN_OFF_TIME = createField("f_sign_off_time", org.jooq.impl.SQLDataType.BIGINT, this, "签退时间");

    /**
     * The column <code>train.t_study_team_member_sign.f_status</code>. 状态 1-正常 2-迟到 3-早退 4-迟到&amp;早退
     */
    public final TableField<StudyTeamMemberSignRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "状态 1-正常 2-迟到 3-早退 4-迟到&早退");

    /**
     * The column <code>train.t_study_team_member_sign.f_confirm_status</code>. 确认时长状态 0-未确认 1-确认中 2-已确认
     */
    public final TableField<StudyTeamMemberSignRecord, Integer> CONFIRM_STATUS = createField("f_confirm_status", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "确认时长状态 0-未确认 1-确认中 2-已确认");

    /**
     * The column <code>train.t_study_team_member_sign.f_remark</code>. 备注
     */
    public final TableField<StudyTeamMemberSignRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("", org.jooq.impl.SQLDataType.VARCHAR)), this, "备注");

    /**
     * The column <code>train.t_study_team_member_sign.f_create_time</code>. 创建时间
     */
    public final TableField<StudyTeamMemberSignRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * Create a <code>train.t_study_team_member_sign</code> table reference
     */
    public StudyTeamMemberSign() {
        this("t_study_team_member_sign", null);
    }

    /**
     * Create an aliased <code>train.t_study_team_member_sign</code> table reference
     */
    public StudyTeamMemberSign(String alias) {
        this(alias, STUDY_TEAM_MEMBER_SIGN);
    }

    private StudyTeamMemberSign(String alias, Table<StudyTeamMemberSignRecord> aliased) {
        this(alias, aliased, null);
    }

    private StudyTeamMemberSign(String alias, Table<StudyTeamMemberSignRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习团队成员签到表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<StudyTeamMemberSignRecord> getPrimaryKey() {
        return Keys.KEY_T_STUDY_TEAM_MEMBER_SIGN_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<StudyTeamMemberSignRecord>> getKeys() {
        return Arrays.<UniqueKey<StudyTeamMemberSignRecord>>asList(Keys.KEY_T_STUDY_TEAM_MEMBER_SIGN_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public StudyTeamMemberSign as(String alias) {
        return new StudyTeamMemberSign(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public StudyTeamMemberSign rename(String name) {
        return new StudyTeamMemberSign(name, null);
    }
}
