/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.AudienceObjectRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 受众对象表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AudienceObject extends TableImpl<AudienceObjectRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_audience_object</code>
     */
    public static final AudienceObject AUDIENCE_OBJECT = new AudienceObject();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AudienceObjectRecord> getRecordType() {
        return AudienceObjectRecord.class;
    }

    /**
     * The column <code>train.t_audience_object.f_id</code>. ID
     */
    public final TableField<AudienceObjectRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>train.t_audience_object.f_business_id</code>. 业务id
     */
    public final TableField<AudienceObjectRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "业务id");

    /**
     * The column <code>train.t_audience_object.f_business_type</code>. 业务类型:1 消息
     */
    public final TableField<AudienceObjectRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER, this, "业务类型:1 消息");

    /**
     * The column <code>train.t_audience_object.f_item_id</code>. 受众项ID
     */
    public final TableField<AudienceObjectRecord, String> ITEM_ID = createField("f_item_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "受众项ID");

    /**
     * The column <code>train.t_audience_object.f_create_time</code>.
     */
    public final TableField<AudienceObjectRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "");

    /**
     * Create a <code>train.t_audience_object</code> table reference
     */
    public AudienceObject() {
        this("t_audience_object", null);
    }

    /**
     * Create an aliased <code>train.t_audience_object</code> table reference
     */
    public AudienceObject(String alias) {
        this(alias, AUDIENCE_OBJECT);
    }

    private AudienceObject(String alias, Table<AudienceObjectRecord> aliased) {
        this(alias, aliased, null);
    }

    private AudienceObject(String alias, Table<AudienceObjectRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "受众对象表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AudienceObjectRecord> getPrimaryKey() {
        return Keys.KEY_T_AUDIENCE_OBJECT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AudienceObjectRecord>> getKeys() {
        return Arrays.<UniqueKey<AudienceObjectRecord>>asList(Keys.KEY_T_AUDIENCE_OBJECT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AudienceObject as(String alias) {
        return new AudienceObject(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AudienceObject rename(String name) {
        return new AudienceObject(name, null);
    }
}
