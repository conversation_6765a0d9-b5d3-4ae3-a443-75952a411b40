/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.ResearchAnswerRecord;
import com.zxy.product.train.jooq.tables.interfaces.IResearchAnswerRecord;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchAnswerRecordRecord extends UpdatableRecordImpl<ResearchAnswerRecordRecord> implements Record15<String, Long, String, String, String, Integer, Integer, String, String, String, String, String, String, String, String>, IResearchAnswerRecord {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_research_answer_record.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_research_record_id</code>.
     */
    @Override
    public void setResearchRecordId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_research_record_id</code>.
     */
    @Override
    public String getResearchRecordId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_question_id</code>.
     */
    @Override
    public void setQuestionId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_question_id</code>.
     */
    @Override
    public String getQuestionId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_answer</code>.
     */
    @Override
    public void setAnswer(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_answer</code>.
     */
    @Override
    public String getAnswer() {
        return (String) get(4);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_is_right</code>.
     */
    @Override
    public void setIsRight(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_is_right</code>.
     */
    @Override
    public Integer getIsRight() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_score</code>.
     */
    @Override
    public void setScore(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_score</code>.
     */
    @Override
    public Integer getScore() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_idea</code>. 意见
     */
    @Override
    public void setIdea(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_idea</code>. 意见
     */
    @Override
    public String getIdea() {
        return (String) get(7);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_member_id</code>. 用户ID
     */
    @Override
    public void setMemberId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_member_id</code>. 用户ID
     */
    @Override
    public String getMemberId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_class_id</code>. 培训班ID
     */
    @Override
    public void setClassId(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_class_id</code>. 培训班ID
     */
    @Override
    public String getClassId() {
        return (String) get(9);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_course_id</code>. 课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_course_id</code>. 课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(10);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_course_name</code>. 课程名称
     */
    @Override
    public void setCourseName(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_course_name</code>. 课程名称
     */
    @Override
    public String getCourseName() {
        return (String) get(11);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_teacher_id</code>. 讲师ID
     */
    @Override
    public void setTeacherId(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_teacher_id</code>. 讲师ID
     */
    @Override
    public String getTeacherId() {
        return (String) get(12);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_teacher_name</code>. 讲师名称
     */
    @Override
    public void setTeacherName(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_teacher_name</code>. 讲师名称
     */
    @Override
    public String getTeacherName() {
        return (String) get(13);
    }

    /**
     * Setter for <code>train.t_research_answer_record.f_questionnaire_question_id</code>. 问题ID
     */
    @Override
    public void setQuestionnaireQuestionId(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>train.t_research_answer_record.f_questionnaire_question_id</code>. 问题ID
     */
    @Override
    public String getQuestionnaireQuestionId() {
        return (String) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row15<String, Long, String, String, String, Integer, Integer, String, String, String, String, String, String, String, String> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row15<String, Long, String, String, String, Integer, Integer, String, String, String, String, String, String, String, String> valuesRow() {
        return (Row15) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.QUESTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.ANSWER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.IS_RIGHT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.SCORE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.IDEA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.CLASS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.COURSE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.COURSE_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field13() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.TEACHER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field14() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.TEACHER_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field15() {
        return ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getResearchRecordId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getQuestionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getAnswer();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getIsRight();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getScore();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getIdea();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getClassId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getCourseId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getCourseName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value13() {
        return getTeacherId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value14() {
        return getTeacherName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value15() {
        return getQuestionnaireQuestionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value3(String value) {
        setResearchRecordId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value4(String value) {
        setQuestionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value5(String value) {
        setAnswer(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value6(Integer value) {
        setIsRight(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value7(Integer value) {
        setScore(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value8(String value) {
        setIdea(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value9(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value10(String value) {
        setClassId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value11(String value) {
        setCourseId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value12(String value) {
        setCourseName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value13(String value) {
        setTeacherId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value14(String value) {
        setTeacherName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord value15(String value) {
        setQuestionnaireQuestionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecordRecord values(String value1, Long value2, String value3, String value4, String value5, Integer value6, Integer value7, String value8, String value9, String value10, String value11, String value12, String value13, String value14, String value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IResearchAnswerRecord from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setResearchRecordId(from.getResearchRecordId());
        setQuestionId(from.getQuestionId());
        setAnswer(from.getAnswer());
        setIsRight(from.getIsRight());
        setScore(from.getScore());
        setIdea(from.getIdea());
        setMemberId(from.getMemberId());
        setClassId(from.getClassId());
        setCourseId(from.getCourseId());
        setCourseName(from.getCourseName());
        setTeacherId(from.getTeacherId());
        setTeacherName(from.getTeacherName());
        setQuestionnaireQuestionId(from.getQuestionnaireQuestionId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IResearchAnswerRecord> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResearchAnswerRecordRecord
     */
    public ResearchAnswerRecordRecord() {
        super(ResearchAnswerRecord.RESEARCH_ANSWER_RECORD);
    }

    /**
     * Create a detached, initialised ResearchAnswerRecordRecord
     */
    public ResearchAnswerRecordRecord(String id, Long createTime, String researchRecordId, String questionId, String answer, Integer isRight, Integer score, String idea, String memberId, String classId, String courseId, String courseName, String teacherId, String teacherName, String questionnaireQuestionId) {
        super(ResearchAnswerRecord.RESEARCH_ANSWER_RECORD);

        set(0, id);
        set(1, createTime);
        set(2, researchRecordId);
        set(3, questionId);
        set(4, answer);
        set(5, isRight);
        set(6, score);
        set(7, idea);
        set(8, memberId);
        set(9, classId);
        set(10, courseId);
        set(11, courseName);
        set(12, teacherId);
        set(13, teacherName);
        set(14, questionnaireQuestionId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.ResearchAnswerRecordEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.ResearchAnswerRecordEntity pojo = (com.zxy.product.train.jooq.tables.pojos.ResearchAnswerRecordEntity)source;
        pojo.into(this);
        return true;
    }
}
