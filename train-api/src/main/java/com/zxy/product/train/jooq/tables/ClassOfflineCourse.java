/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables;


import com.zxy.product.train.jooq.Keys;
import com.zxy.product.train.jooq.Train;
import com.zxy.product.train.jooq.tables.records.ClassOfflineCourseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ClassOfflineCourse extends TableImpl<ClassOfflineCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>train.t_class_offline_course</code>
     */
    public static final ClassOfflineCourse CLASS_OFFLINE_COURSE = new ClassOfflineCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ClassOfflineCourseRecord> getRecordType() {
        return ClassOfflineCourseRecord.class;
    }

    /**
     * The column <code>train.t_class_offline_course.f_id</code>.
     */
    public final TableField<ClassOfflineCourseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>train.t_class_offline_course.f_class_id</code>. 班级ID
     */
    public final TableField<ClassOfflineCourseRecord, String> CLASS_ID = createField("f_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "班级ID");

    /**
     * The column <code>train.t_class_offline_course.f_type</code>. 课程类型（1面授  2观看录像 3直播 4其他）
     */
    public final TableField<ClassOfflineCourseRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER, this, "课程类型（1面授  2观看录像 3直播 4其他）");

    /**
     * The column <code>train.t_class_offline_course.f_name</code>. 课程名称
     */
    public final TableField<ClassOfflineCourseRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "课程名称");

    /**
     * The column <code>train.t_class_offline_course.f_course_date</code>. 上课日期
     */
    public final TableField<ClassOfflineCourseRecord, Long> COURSE_DATE = createField("f_course_date", org.jooq.impl.SQLDataType.BIGINT, this, "上课日期");

    /**
     * The column <code>train.t_class_offline_course.f_start_time</code>. 开始时间
     */
    public final TableField<ClassOfflineCourseRecord, String> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "开始时间");

    /**
     * The column <code>train.t_class_offline_course.f_end_time</code>. 结束时间
     */
    public final TableField<ClassOfflineCourseRecord, String> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.VARCHAR.length(10), this, "结束时间");

    /**
     * The column <code>train.t_class_offline_course.f_classroom_id</code>. 教室ID
     */
    public final TableField<ClassOfflineCourseRecord, String> CLASSROOM_ID = createField("f_classroom_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "教室ID");

    /**
     * The column <code>train.t_class_offline_course.f_teacher_id</code>. 讲师ID
     */
    public final TableField<ClassOfflineCourseRecord, String> TEACHER_ID = createField("f_teacher_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "讲师ID");

    /**
     * The column <code>train.t_class_offline_course.f_teacher_name</code>. 讲师姓名
     */
    public final TableField<ClassOfflineCourseRecord, String> TEACHER_NAME = createField("f_teacher_name", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "讲师姓名");

    /**
     * The column <code>train.t_class_offline_course.f_teacher_organization</code>. 讲师单位
     */
    public final TableField<ClassOfflineCourseRecord, String> TEACHER_ORGANIZATION = createField("f_teacher_organization", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "讲师单位");

    /**
     * The column <code>train.t_class_offline_course.f_teacher_title</code>. 讲师职称
     */
    public final TableField<ClassOfflineCourseRecord, String> TEACHER_TITLE = createField("f_teacher_title", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "讲师职称");

    /**
     * The column <code>train.t_class_offline_course.f_teacher_phone</code>. 联系电话
     */
    public final TableField<ClassOfflineCourseRecord, String> TEACHER_PHONE = createField("f_teacher_phone", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "联系电话");

    /**
     * The column <code>train.t_class_offline_course.f_teacher_type</code>. 讲师类型(0内部  1外部)
     */
    public final TableField<ClassOfflineCourseRecord, Integer> TEACHER_TYPE = createField("f_teacher_type", org.jooq.impl.SQLDataType.INTEGER, this, "讲师类型(0内部  1外部)");

    /**
     * The column <code>train.t_class_offline_course.f_is_relate_online</code>. 是否关联在线课程（0否 1是）
     */
    public final TableField<ClassOfflineCourseRecord, Integer> IS_RELATE_ONLINE = createField("f_is_relate_online", org.jooq.impl.SQLDataType.INTEGER, this, "是否关联在线课程（0否 1是）");

    /**
     * The column <code>train.t_class_offline_course.f_online_course_id</code>. 在线课程ID
     */
    public final TableField<ClassOfflineCourseRecord, String> ONLINE_COURSE_ID = createField("f_online_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "在线课程ID");

    /**
     * The column <code>train.t_class_offline_course.f_create_time</code>. 创建时间
     */
    public final TableField<ClassOfflineCourseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>train.t_class_offline_course.f_create_member</code>. 创建人
     */
    public final TableField<ClassOfflineCourseRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "创建人");

    /**
     * The column <code>train.t_class_offline_course.f_delete_flag</code>. 删除标记（0未删除，1已删除）
     */
    public final TableField<ClassOfflineCourseRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER, this, "删除标记（0未删除，1已删除）");

    /**
     * The column <code>train.t_class_offline_course.f_online_course_status</code>. 关联在线课程的状态 (0未发布 1已发布)
     */
    public final TableField<ClassOfflineCourseRecord, Integer> ONLINE_COURSE_STATUS = createField("f_online_course_status", org.jooq.impl.SQLDataType.INTEGER, this, "关联在线课程的状态 (0未发布 1已发布)");

    /**
     * The column <code>train.t_class_offline_course.f_duration</code>. 课时
     */
    public final TableField<ClassOfflineCourseRecord, Double> DURATION = createField("f_duration", org.jooq.impl.SQLDataType.DOUBLE, this, "课时");

    /**
     * The column <code>train.t_class_offline_course.f_course_satisfy</code>. 课程满意度（保留一位小数）
     */
    public final TableField<ClassOfflineCourseRecord, Double> COURSE_SATISFY = createField("f_course_satisfy", org.jooq.impl.SQLDataType.FLOAT, this, "课程满意度（保留一位小数）");

    /**
     * The column <code>train.t_class_offline_course.f_gensee_url</code>. 直播的播放路径
     */
    public final TableField<ClassOfflineCourseRecord, String> GENSEE_URL = createField("f_gensee_url", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "直播的播放路径");

    /**
     * The column <code>train.t_class_offline_course.f_recommend</code>. 推荐方
     */
    public final TableField<ClassOfflineCourseRecord, Integer> RECOMMEND = createField("f_recommend", org.jooq.impl.SQLDataType.INTEGER, this, "推荐方");

    /**
     * The column <code>train.t_class_offline_course.f_payment_method</code>. 支付方式
     */
    public final TableField<ClassOfflineCourseRecord, Integer> PAYMENT_METHOD = createField("f_payment_method", org.jooq.impl.SQLDataType.INTEGER, this, "支付方式");

    /**
     * The column <code>train.t_class_offline_course.f_remarks</code>. 备注
     */
    public final TableField<ClassOfflineCourseRecord, String> REMARKS = createField("f_remarks", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "备注");

    /**
     * The column <code>train.t_class_offline_course.f_taxrate</code>. 税率
     */
    public final TableField<ClassOfflineCourseRecord, Integer> TAXRATE = createField("f_taxrate", org.jooq.impl.SQLDataType.INTEGER, this, "税率");

    /**
     * The column <code>train.t_class_offline_course.f_lecturer_source</code>. 讲师类别
     */
    public final TableField<ClassOfflineCourseRecord, Integer> LECTURER_SOURCE = createField("f_lecturer_source", org.jooq.impl.SQLDataType.INTEGER, this, "讲师类别");

    /**
     * Create a <code>train.t_class_offline_course</code> table reference
     */
    public ClassOfflineCourse() {
        this("t_class_offline_course", null);
    }

    /**
     * Create an aliased <code>train.t_class_offline_course</code> table reference
     */
    public ClassOfflineCourse(String alias) {
        this(alias, CLASS_OFFLINE_COURSE);
    }

    private ClassOfflineCourse(String alias, Table<ClassOfflineCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private ClassOfflineCourse(String alias, Table<ClassOfflineCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Train.TRAIN_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ClassOfflineCourseRecord> getPrimaryKey() {
        return Keys.KEY_T_CLASS_OFFLINE_COURSE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ClassOfflineCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<ClassOfflineCourseRecord>>asList(Keys.KEY_T_CLASS_OFFLINE_COURSE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassOfflineCourse as(String alias) {
        return new ClassOfflineCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ClassOfflineCourse rename(String name) {
        return new ClassOfflineCourse(name, null);
    }
}
