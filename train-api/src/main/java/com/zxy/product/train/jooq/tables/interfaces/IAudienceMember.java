/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 受众人表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAudienceMember extends Serializable {

    /**
     * Setter for <code>train.t_audience_member.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_audience_member.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_audience_member.f_item_id</code>. 受众项id
     */
    public void setItemId(String value);

    /**
     * Getter for <code>train.t_audience_member.f_item_id</code>. 受众项id
     */
    public String getItemId();

    /**
     * Setter for <code>train.t_audience_member.f_member_id</code>. 受众人id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>train.t_audience_member.f_member_id</code>. 受众人id
     */
    public String getMemberId();

    /**
     * Setter for <code>train.t_audience_member.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_audience_member.f_create_time</code>.
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAudienceMember
     */
    public void from(IAudienceMember from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAudienceMember
     */
    public <E extends IAudienceMember> E into(E into);
}
