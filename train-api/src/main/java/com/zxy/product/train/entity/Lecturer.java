package com.zxy.product.train.entity;

import java.util.List;

import com.zxy.product.train.jooq.tables.pojos.LecturerEntity;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/2/15.
 * 讲师
 */
public class Lecturer extends LecturerEntity {

    private static final long serialVersionUID = 806400029907437729L;

    public static final String URI = "activity/lecturer/info";

    public static final Integer STATUS_YES = 0;//讲师在库（启用）
    public static final Integer STATUS_NO = 1;//讲师退库（禁用）
    
    public static final Integer RELEASE_STATUS_NO = 0;//讲师未发布
    public static final Integer RELEASE_STATUS_YES = 1;//讲师已发布
    
    public static final Integer TYPE_INSIDE = 0;//内部讲师
    public static final Integer TYPE_EXTERNAL = 1;//外部讲师
    
    public static final Integer SOURCES = 0;//讲师来源 0 自建 1 班级
    public static final String classOrg = "96597"; //班级讲师默认值
    private Integer index; // 序号,用于生成excel时产生序号

    private String labelStr;

    private String num;

    private String labelId;//导入

    private String labelIds;
    
    private String sequenceName;//专业序列名称
    
    private String parentId;//上级专业序列ID
    
    private String parentName;//上级专业序列名称
    
    private String level;
    
    private String attribute;
    
    private String organizationCompanyId;

    private String ascriptionOrganizationName;
    private String modifyMemberName;
    private String createMemberName;
    private List<LecturerAdeptCourse> adeptCourseList; //擅长课程List集合
    private boolean thumbsUp;
    
    private long latelyOrgRecordTime;
    
    public long getLatelyOrgRecordTime() {
		return latelyOrgRecordTime;
	}

	public void setLatelyOrgRecordTime(long latelyOrgRecordTime) {
		this.latelyOrgRecordTime = latelyOrgRecordTime;
	}

	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}

	public String getAttribute() {
		return attribute;
	}

	public void setAttribute(String attribute) {
		this.attribute = attribute;
	}

	public String getLabelIds() {
		return labelIds;
	}

	public void setLabelIds(String labelIds) {
		this.labelIds = labelIds;
	}
	
	public String getAscriptionOrganizationName() {
		return ascriptionOrganizationName;
	}

	public void setAscriptionOrganizationName(String ascriptionOrganizationName) {
		this.ascriptionOrganizationName = ascriptionOrganizationName;
	}


	public String getModifyMemberName() {
		return modifyMemberName;
	}

	public void setModifyMemberName(String modifyMemberName) {
		this.modifyMemberName = modifyMemberName;
	}

	public String getCreateMemberName() {
		return createMemberName;
	}

	public void setCreateMemberName(String createMemberName) {
		this.createMemberName = createMemberName;
	}

	public List<LecturerAdeptCourse> getAdeptCourseList() {
		return adeptCourseList;
	}

	public void setAdeptCourseList(List<LecturerAdeptCourse> adeptCourseList) {
		this.adeptCourseList = adeptCourseList;
	}

	public boolean isThumbsUp() {
		return thumbsUp;
	}

	public void setThumbsUp(boolean thumbsUp) {
		this.thumbsUp = thumbsUp;
	}



	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}



	private Double count;//授课数

    private String satisfy;//平均满意度

	public String getSatisfy() {
		return satisfy;
	}

	public void setSatisfy(String satisfy) {
		this.satisfy = satisfy;
	}

	public String getLabelId() {
		return labelId;
	}

	public void setLabelId(String labelId) {
		this.labelId = labelId;
	}

	public Double getCount() {
		return count;
	}

	public void setCount(Double count) {
		this.count = count;
	}

	public String getNum() {
		return num;
	}

	public void setNum(String num) {
		this.num = num;
	}

	public String getLabelStr() {
		return labelStr;
	}

	public void setLabelStr(String labelStr) {
		this.labelStr = labelStr;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getSequenceName() {
		return sequenceName;
	}

	public void setSequenceName(String sequenceName) {
		this.sequenceName = sequenceName;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	private String levlId;

    private String levelName;

	private String organizationName;

    private List<Label> labelList;

    private List<ClassOfflineCourse> offlineCourseList;

    private String memberName;

    private String memberHeadPortrait;

    private Member member;

    private String rootOrgName; //讲师所属机构名称

    private String rootOrgShortName; //讲师所属机构简称

	private Integer organizationLevel; // 部门级别

	public List<ClassOfflineCourse> getOfflineCourseList() {
		return offlineCourseList;
	}

	public void setOfflineCourseList(List<ClassOfflineCourse> offlineCourseList) {
		this.offlineCourseList = offlineCourseList;
	}

	public String getLevlId() {
		return levlId;
	}

	public void setLevlId(String levlId) {
		this.levlId = levlId;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public List<Label> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<Label> labelList) {
        this.labelList = labelList;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberHeadPortrait() {
        return memberHeadPortrait;
    }

    public void setMemberHeadPortrait(String memberHeadPortrait) {
        this.memberHeadPortrait = memberHeadPortrait;
    }

	public String getRootOrgName() {
		return rootOrgName;
	}

	public void setRootOrgName(String rootOrgName) {
		this.rootOrgName = rootOrgName;
	}

	public String getRootOrgShortName() {
		return rootOrgShortName;
	}

	public void setRootOrgShortName(String rootOrgShortName) {
		this.rootOrgShortName = rootOrgShortName;
	}

	public Integer getOrganizationLevel() {
		return organizationLevel;
	}

	public void setOrganizationLevel(Integer organizationLevel) {
		this.organizationLevel = organizationLevel;
	}

	public String getOrganizationCompanyId() {
		return organizationCompanyId;
	}

	public void setOrganizationCompanyId(String organizationCompanyId) {
		this.organizationCompanyId = organizationCompanyId;
	}
	
}
