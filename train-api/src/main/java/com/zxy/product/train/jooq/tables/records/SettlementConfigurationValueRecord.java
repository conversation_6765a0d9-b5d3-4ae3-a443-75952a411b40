/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.records;


import com.zxy.product.train.jooq.tables.SettlementConfigurationValue;
import com.zxy.product.train.jooq.tables.interfaces.ISettlementConfigurationValue;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SettlementConfigurationValueRecord extends UpdatableRecordImpl<SettlementConfigurationValueRecord> implements Record7<String, String, String, Integer, Long, String, String>, ISettlementConfigurationValue {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>train.t_settlement_configuration_value.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration_value.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>train.t_settlement_configuration_value.f_organization_id</code>. 关联组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration_value.f_organization_id</code>. 关联组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>train.t_settlement_configuration_value.f_settlement_id</code>. 结算表ID
     */
    @Override
    public void setSettlementId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration_value.f_settlement_id</code>. 结算表ID
     */
    @Override
    public String getSettlementId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>train.t_settlement_configuration_value.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration_value.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>train.t_settlement_configuration_value.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration_value.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    /**
     * Setter for <code>train.t_settlement_configuration_value.f_create_member</code>. 创建人ID
     */
    @Override
    public void setCreateMember(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration_value.f_create_member</code>. 创建人ID
     */
    @Override
    public String getCreateMember() {
        return (String) get(5);
    }

    /**
     * Setter for <code>train.t_settlement_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    @Override
    public void setPath(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>train.t_settlement_configuration_value.f_path</code>. 当前节点的所有父节点
     */
    @Override
    public String getPath() {
        return (String) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Long, String, String> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Long, String, String> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.SETTLEMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.CREATE_MEMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE.PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getSettlementId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getCreateMember();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord value2(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord value3(String value) {
        setSettlementId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord value4(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord value6(String value) {
        setCreateMember(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord value7(String value) {
        setPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SettlementConfigurationValueRecord values(String value1, String value2, String value3, Integer value4, Long value5, String value6, String value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISettlementConfigurationValue from) {
        setId(from.getId());
        setOrganizationId(from.getOrganizationId());
        setSettlementId(from.getSettlementId());
        setSort(from.getSort());
        setCreateTime(from.getCreateTime());
        setCreateMember(from.getCreateMember());
        setPath(from.getPath());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISettlementConfigurationValue> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SettlementConfigurationValueRecord
     */
    public SettlementConfigurationValueRecord() {
        super(SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE);
    }

    /**
     * Create a detached, initialised SettlementConfigurationValueRecord
     */
    public SettlementConfigurationValueRecord(String id, String organizationId, String settlementId, Integer sort, Long createTime, String createMember, String path) {
        super(SettlementConfigurationValue.SETTLEMENT_CONFIGURATION_VALUE);

        set(0, id);
        set(1, organizationId);
        set(2, settlementId);
        set(3, sort);
        set(4, createTime);
        set(5, createMember);
        set(6, path);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.train.jooq.tables.pojos.SettlementConfigurationValueEntity)) {
            return false;
        }
        com.zxy.product.train.jooq.tables.pojos.SettlementConfigurationValueEntity pojo = (com.zxy.product.train.jooq.tables.pojos.SettlementConfigurationValueEntity)source;
        pojo.into(this);
        return true;
    }
}
