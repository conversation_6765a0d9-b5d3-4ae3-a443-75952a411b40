/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICollectingCourse extends Serializable {

    /**
     * Setter for <code>train.t_collecting_course.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_collecting_course.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_collecting_course.f_name</code>. 集采课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_collecting_course.f_name</code>. 集采课程名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_collecting_course.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>train.t_collecting_course.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICollectingCourse
     */
    public void from(ICollectingCourse from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICollectingCourse
     */
    public <E extends ICollectingCourse> E into(E into);
}
