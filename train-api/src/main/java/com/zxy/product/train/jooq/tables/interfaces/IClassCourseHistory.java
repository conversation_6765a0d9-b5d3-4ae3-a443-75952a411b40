/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.train.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 历史班级课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IClassCourseHistory extends Serializable {

    /**
     * Setter for <code>train.t_class_course_history.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>train.t_class_course_history.f_type</code>. 课程类型:1F,2L,3O,4Q,5Z
     */
    public void setType(Integer value);

    /**
     * Getter for <code>train.t_class_course_history.f_type</code>. 课程类型:1F,2L,3O,4Q,5Z
     */
    public Integer getType();

    /**
     * Setter for <code>train.t_class_course_history.f_class_id</code>. 班级ID
     */
    public void setClassId(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_class_id</code>. 班级ID
     */
    public String getClassId();

    /**
     * Setter for <code>train.t_class_course_history.f_name</code>. 课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_name</code>. 课程名称
     */
    public String getName();

    /**
     * Setter for <code>train.t_class_course_history.f_course_date</code>. 上课日期
     */
    public void setCourseDate(Long value);

    /**
     * Getter for <code>train.t_class_course_history.f_course_date</code>. 上课日期
     */
    public Long getCourseDate();

    /**
     * Setter for <code>train.t_class_course_history.f_start_time</code>. 开始时间
     */
    public void setStartTime(Long value);

    /**
     * Getter for <code>train.t_class_course_history.f_start_time</code>. 开始时间
     */
    public Long getStartTime();

    /**
     * Setter for <code>train.t_class_course_history.f_end_time</code>. 结束时间
     */
    public void setEndTime(Long value);

    /**
     * Getter for <code>train.t_class_course_history.f_end_time</code>. 结束时间
     */
    public Long getEndTime();

    /**
     * Setter for <code>train.t_class_course_history.f_classroom_id</code>. 教室ID
     */
    public void setClassroomId(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_classroom_id</code>. 教室ID
     */
    public String getClassroomId();

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_name</code>. 讲师姓名
     */
    public void setTeacherName(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_name</code>. 讲师姓名
     */
    public String getTeacherName();

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_organization</code>. 讲师单位
     */
    public void setTeacherOrganization(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_organization</code>. 讲师单位
     */
    public String getTeacherOrganization();

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_title</code>. 讲师职称
     */
    public void setTeacherTitle(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_title</code>. 讲师职称
     */
    public String getTeacherTitle();

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_phone</code>. 联系电话
     */
    public void setTeacherPhone(String value);

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_phone</code>. 联系电话
     */
    public String getTeacherPhone();

    /**
     * Setter for <code>train.t_class_course_history.f_teacher_type</code>. 讲师类型:0待议
     */
    public void setTeacherType(Integer value);

    /**
     * Getter for <code>train.t_class_course_history.f_teacher_type</code>. 讲师类型:0待议
     */
    public Integer getTeacherType();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IClassCourseHistory
     */
    public void from(IClassCourseHistory from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IClassCourseHistory
     */
    public <E extends IClassCourseHistory> E into(E into);
}
