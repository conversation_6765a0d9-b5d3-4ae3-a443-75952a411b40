package com.zxy.product.train.async.listener;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.zxy.common.base.message.Message;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.train.api.ClassThemeService;
import com.zxy.product.train.api.QuestionnaireSurveyService;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.*;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.zxy.product.train.util.StringUtils;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.zxy.product.train.jooq.Tables.*;


/**
 * 生成新版满意度问卷.
 *
 * <AUTHOR> 满意度问卷监听
 */
@Service
public class ScheduleSatisfactionQuestionnairCreateListener extends AbstractMessageListener {

  private static final Logger LOGGER = LoggerFactory
      .getLogger(ScheduleSatisfactionQuestionnairCreateListener.class);
  private static final String DATE_FORMAT="yyyy-MM-dd";
  private static final String DATA_START=" 00:00:00";
  private static final String DATA_END=" 23:59:59";
  private CommonDao<QuestionnaireQuestionType> questionnaireQuestionTypeDao;
  private CommonDao<ClassQuestionnaireTotal> classQuestionnaireTotalDao;
  private CommonDao<ClassOfflineCourse> classOfflineCourseDao;
  private CommonDao<ResearchAnswerRecord> researchAnswerRecordDao;
  private QuestionnaireSurveyService questionnaireSurveyService;
  private CommonDao<ResearchQuestionary> researchQuestionaryDao;
  private CommonDao<ResearchRecord> researchRecordDao;
  private CommonDao<ClassEvaluate> classEvaluateDao;
  private CommonDao<ClassInfo> classInfoDao;
  private CommonDao<Trainee> traineeDao;
  private ClassThemeService classThemeService;
  private CommonDao<Project> projectCommonDao;
  private CommonDao<ClassTheme> classThemeCommonDao;

  @Autowired
  private MessageSender sender;

  @Autowired
  public void setQuestionnaireQuestionTypeDao(
      CommonDao<QuestionnaireQuestionType> questionnaireQuestionTypeDao) {
    this.questionnaireQuestionTypeDao = questionnaireQuestionTypeDao;
  }

  @Autowired
  public void setClassQuestionnaireTotalDao(
      CommonDao<ClassQuestionnaireTotal> classQuestionnaireTotalDao) {
    this.classQuestionnaireTotalDao = classQuestionnaireTotalDao;
  }

  @Autowired
  public void setClassOfflineCourseDao(CommonDao<ClassOfflineCourse> classOfflineCourseDao) {
    this.classOfflineCourseDao = classOfflineCourseDao;
  }

  @Autowired
  public void setResearchAnswerRecordDao(CommonDao<ResearchAnswerRecord> researchAnswerRecordDao) {
    this.researchAnswerRecordDao = researchAnswerRecordDao;
  }

  @Autowired
  public void setQuestionnaireSurveyService(QuestionnaireSurveyService questionnaireSurveyService) {
    this.questionnaireSurveyService = questionnaireSurveyService;
  }

  @Autowired
  public void setResearchQuestionaryDao(CommonDao<ResearchQuestionary> researchQuestionaryDao) {
    this.researchQuestionaryDao = researchQuestionaryDao;
  }

  @Autowired
  public void setResearchRecordDao(CommonDao<ResearchRecord> researchRecordDao) {
    this.researchRecordDao = researchRecordDao;
  }

  @Autowired
  public void setClassEvaluateDao(CommonDao<ClassEvaluate> classEvaluateDao) {
    this.classEvaluateDao = classEvaluateDao;
  }

  @Autowired
  public void setTraineeDao(CommonDao<Trainee> traineeDao) {
    this.traineeDao = traineeDao;
  }

  @Autowired
  public void setClassInfoDao(CommonDao<ClassInfo> classInfoDao) {
    this.classInfoDao = classInfoDao;
  }

  @Autowired
  public void setClassThemeService(ClassThemeService classThemeService) {
    this.classThemeService = classThemeService;
  }

  @Autowired
  public void setProjectCommonDao(CommonDao<Project> projectCommonDao) {
    this.projectCommonDao = projectCommonDao;
  }

  @Autowired
  public void setClassThemeCommonDao(CommonDao<ClassTheme> classThemeCommonDao) {
    this.classThemeCommonDao = classThemeCommonDao;
  }

  @Override
  public int[] getTypes() {
    return new int[]{
        com.zxy.product.system.content.MessageTypeContent.SYSTEM_SCHEDULE_MESSAGE,
        MessageTypeContent.SUBMIT_NEW_SATISFACTION_QUESTIONNAIRE,
        MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE,
        MessageTypeContent.CLASS_DELETE_TRAINEE,
            MessageTypeContent.CREATE_NEW_SATISFACTION_QUESTIONNAIRE
    };
  }

  @Override
  protected void onMessage(Message message) {
    String memberId = message.getHeader(MessageHeaderContent.MEMBERID);
    String classId = message.getHeader(MessageHeaderContent.CLASSID);
    String researchQuestionnaireId=message.getHeader(MessageHeaderContent.RESEARCHQUESTIONARYID);
    String themeId=message.getHeader(MessageHeaderContent.THEME_UID);
    switch (message.getType()) {
      case com.zxy.product.system.content.MessageTypeContent.SYSTEM_SCHEDULE_MESSAGE:
        LOGGER
            .info("train/schedule Create Satisfaction Questionnair listener:" + message.getType());
        Calendar cal = Calendar.getInstance();
        int h = cal.get(Calendar.HOUR_OF_DAY);
        int mi = cal.get(Calendar.MINUTE);
        //新增业务逻辑，查询已过期的培训班，重置当前培训班学员的待办消息数量
        if(h==1 && mi<=4){
          handleExpireClass();
        }

        if (h == 0 && mi <= 5) {
          insetResearchQuestionary();
        }

        break;
      case MessageTypeContent.SUBMIT_NEW_SATISFACTION_QUESTIONNAIRE:
        LOGGER
            .info("train/schedule Submit Satisfaction Questionnair listener:" + message.getType());
        String answers = message.getHeader(MessageHeaderContent.ANSWE_STRING);
        submitNewSatisfactionQuestionnaire(classId, memberId, answers,researchQuestionnaireId);
        break;
      case MessageTypeContent.CLASS_ADD_TRAINEE_BY_CODE:
        LOGGER.info("train/schedule Class add Trainee listener:" + message.getType());
        addResearchRecord(classId, memberId);
        break;
      case MessageTypeContent.CLASS_DELETE_TRAINEE:
        LOGGER.info("train/schedule Class Delete Trainee listener:" + message.getType());
        delResearchRecord(classId, memberId);
        break;
        //新增满意度问卷 （党干部培训班满意度问卷是基于周主题生成）
      case MessageTypeContent.CREATE_NEW_SATISFACTION_QUESTIONNAIRE:
        LOGGER.info("train/schedule Class add questionnaire listener:" + message.getType());
        createNewResearchQuestionary(classId,themeId);
        break;
    }
  }

  /**
   * 用户提交新满意度问卷后统计
   *
   * @param classId
   * @param memberId
   */
  private void submitNewSatisfactionQuestionnaire(String classId, String memberId, String answers,String researchquestionnaireId) {

    ResearchRecord researchRecord = researchRecordDao.execute(dao -> {
      try {
        return dao.select(
            Fields.start().add(RESEARCH_RECORD.ID, RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID).end())
            .from(RESEARCH_RECORD)
            .leftJoin(CLASS_EVALUATE)
            .on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID))
            .where(RESEARCH_RECORD.MEMBER_ID.eq(memberId))
            .and(RESEARCH_RECORD.STATUS.eq(ResearchRecord.STATUS_FINISHED))
            .and(CLASS_EVALUATE.CLASS_ID.eq(classId))
            .and(CLASS_EVALUATE.TYPE.eq(ClassEvaluate.TYPE_EVA_STU_NEW))
            .and(CLASS_EVALUATE.DELETE_FLAG.eq(ClassEvaluate.DELETE_FALSE))
            .and(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(researchquestionnaireId))
            .fetchOneInto(ResearchRecord.class);
      } catch (Exception e) {
        return null;
      }
    });
    if (researchRecord != null) {
      //更新学员是否参与满意度问卷状态
      //如果是党干部培训班，走另外一套业务逻辑
      if(existPartyCadreClass(classId)){
        questionnaireSurveyService.savePartyCadre(memberId, classId, researchRecord.getResearchQuestionaryId());
      }else{
        questionnaireSurveyService.save(memberId, classId, researchRecord.getResearchQuestionaryId());

      }
      // 查询用户新满意度问卷的答案
//			List<ResearchAnswerRecord> researchAnswerRecordList = researchAnswerRecordDao.fetch(
//										RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(researchRecord.getId()));
      //处理用户保存的新答案
      List<ResearchAnswerRecord> researchAnswerRecordList = new ArrayList<ResearchAnswerRecord>();
      JSONArray jsonArray = JSONArray.parseArray(answers);
      if (jsonArray == null) {
        return;
      }
      String researchRecordId = researchRecord.getId();
      for (Object object : jsonArray) {
        JSONObject jsonObject = JSONObject.parseObject(object.toString());
        String answer = jsonObject.getString("answer");
        int type = 0;
        try {
          type = jsonObject.getInteger("type");
        } catch (Exception e) {
          type = 0;
        }
        String courseId = jsonObject.getString("courseId");
        ResearchAnswerRecord rar = new ResearchAnswerRecord();
        rar.forInsert();
        rar.setAnswer(answer);
        rar.setIdea(jsonObject.getString("idea"));
        rar.setCourseId(courseId);
        rar.setResearchRecordId(researchRecordId);
        rar.setQuestionnaireQuestionId(jsonObject.getString("questionnaireQuestionId"));
        rar.setType(type + "");
        rar.setMemberId(memberId);
        rar.setResearchRecordId(researchRecordId);
        rar.setClassId(classId);
        researchAnswerRecordList.add(rar);
      }
      newSatisfactionQuestionnaireTotal(researchAnswerRecordList, classId,
          researchRecord.getResearchQuestionaryId(), "0");
    }
  }

  /**
   * 根据传递答案增减答案作答人数
   *
   * @param researchAnswerRecordList
   * @param classId
   * @param questionaryId
   * @param type                     0：增加人数，1：减少人数
   */
  private void newSatisfactionQuestionnaireTotal(
      List<ResearchAnswerRecord> researchAnswerRecordList,
      String classId, String questionaryId, String type) {

    String population = "COURSE_";
    //查询统计表中该培训班中作答人数
    List<ClassQuestionnaireTotal> classQuestionnaireTotalList = classQuestionnaireTotalDao.fetch(
        CLASS_QUESTIONNAIRE_TOTAL.CLASS_ID.eq(classId)
            .and(CLASS_QUESTIONNAIRE_TOTAL.QUESTIONNAIRE_ID.eq(questionaryId)));
    Map<String, ClassQuestionnaireTotal> map = null;
    if (classQuestionnaireTotalList != null) {
      map = classQuestionnaireTotalList.stream().collect(Collectors.toMap(
          c -> {
            String key = ("".equals(c.getCourseId()) || c.getCourseId() == null ? population
                : c.getCourseId()) + c.getQuestionId() + c.getAnswer();
            return key;
          },
          c -> c));
    } else {
      map = new HashMap<String, ClassQuestionnaireTotal>();
    }

    List<String> qqtList = questionnaireQuestionTypeDao.fetch(
        QUESTIONNAIRE_QUESTION_TYPE.LEVEL.eq(QuestionnaireQuestionType.LEVEL_SUBJECTIVE_QUESTIONS)
            .or(QUESTIONNAIRE_QUESTION_TYPE.TYPE
                .eq(QuestionnaireQuestionType.TYPE_CURRICULUM_RECOMMENDATIONS)))
        .stream().map(QuestionnaireQuestionType::getId).collect(Collectors.toList());
    List<ClassQuestionnaireTotal> listInsert = new ArrayList<ClassQuestionnaireTotal>();
    List<ClassQuestionnaireTotal> listUpdate = new ArrayList<ClassQuestionnaireTotal>();
    switch (type) {
      case "0":
        for (ResearchAnswerRecord rar : researchAnswerRecordList) {
          if (qqtList.contains(rar.getQuestionnaireQuestionId())) {
            continue;
          }
          ClassQuestionnaireTotal cqt = null;
          if (map.get(("".equals(rar.getCourseId()) || rar.getCourseId() == null ? population
              : rar.getCourseId()) + rar.getQuestionnaireQuestionId() + rar.getAnswer()) != null) {
            cqt = map.get(("".equals(rar.getCourseId()) || rar.getCourseId() == null ? population
                : rar.getCourseId()) + rar.getQuestionnaireQuestionId() + rar.getAnswer());
            cqt.setNumber(cqt.getNumber() + 1);
            listUpdate.add(cqt);
          } else {
            cqt = new ClassQuestionnaireTotal();
            cqt.forInsert();
            cqt.setClassId(classId);
            cqt.setQuestionnaireId(questionaryId);
            cqt.setCourseId(rar.getCourseId());
            cqt.setQuestionId(rar.getQuestionnaireQuestionId());
            cqt.setAnswer(rar.getAnswer());
            cqt.setNumber(1);
            listInsert.add(cqt);
          }
        }
        if (listInsert.size() > 0) {
          classQuestionnaireTotalDao.insert(listInsert);
        }
        break;

      case "1":
        for (ResearchAnswerRecord rar : researchAnswerRecordList) {
          if (qqtList.contains(rar.getQuestionnaireQuestionId())) {
            continue;
          }
          if (map.get(("".equals(rar.getCourseId()) || rar.getCourseId() == null ? population
              : rar.getCourseId()) + rar.getQuestionnaireQuestionId() + rar.getAnswer()) != null) {
            ClassQuestionnaireTotal cqt = map.get(
                ("".equals(rar.getCourseId()) || rar.getCourseId() == null ? population
                    : rar.getCourseId()) + rar.getQuestionnaireQuestionId() + rar.getAnswer());
            LOGGER.error("删除学员，重新统计课程评价数据，课程id={}，原作答人数={}",cqt.getCourseId(),cqt.getNumber());
            cqt.setNumber(cqt.getNumber() - 1 >= 0 ? cqt.getNumber() - 1 : 0);
            LOGGER.error("删除学员，重新统计课程评价数据，课程id={}，新作答人数={}",cqt.getCourseId(),cqt.getNumber());
            listUpdate.add(cqt);
          }
        }
        break;
      default:
        break;
    }
    if (listUpdate.size() > 0) {
      classQuestionnaireTotalDao.update(listUpdate);
    }
  }


  /**
   * 添加问卷记录
   *
   * @param classId
   * @param memberId
   */
  private void addResearchRecord(String classId, String memberId) {
    LOGGER.error("培训班新增学员，添加问卷记录，班级id={},人员id={}",classId,memberId);
    List<ResearchQuestionary> questionnaireList=researchQuestionaryDao.fetch(
            RESEARCH_QUESTIONARY.CLASS_ID.eq(classId)
//				.and(RESEARCH_QUESTIONARY.STATUS.eq(1))
                    .and(
                            RESEARCH_QUESTIONARY.TYPE.eq(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY))
    );
    LOGGER.error("培训班新增学员，问卷数量，班级id={},人员id={}",classId,memberId,questionnaireList==null?0:questionnaireList.size());
    for(ResearchQuestionary researchQuestionary:questionnaireList){
      if (researchQuestionary!=null) {
        ResearchRecord researchRecord = new ResearchRecord();
        researchRecord.forInsert();
        researchRecord.setCreateTime(System.currentTimeMillis());
        researchRecord.setMemberId(memberId);
        researchRecord.setResearchQuestionaryId(researchQuestionary.getId());
        researchRecordDao.insert(researchRecord);
        LOGGER.error("培训班新增学员，新增问卷，班级id={},人员id={},问卷id={}，问卷名称={}",classId,memberId,researchRecord.getResearchQuestionaryId(),researchQuestionary.getName());
      }
    }
  }

  /**
   * 删除学员问卷记录
   *
   * @param classId
   * @param memberId
   */
  private void delResearchRecord(String classId, String memberId) {
    LOGGER.error("删除学员问卷关联关系，班级id={}，用户id={}",
            classId,memberId);
    List<ResearchQuestionary> questionnaireListList=researchQuestionaryDao.fetch(
            RESEARCH_QUESTIONARY.CLASS_ID.eq(classId)
//				.and(RESEARCH_QUESTIONARY.STATUS.eq(1))
                    .and(
                            RESEARCH_QUESTIONARY.TYPE.eq(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY))
    );
    LOGGER.error("删除学员问卷，班级id={}，用户id={},问卷数量={}",
            classId,memberId,questionnaireListList==null?0:questionnaireListList.size());
    for(ResearchQuestionary researchQuestionary :questionnaireListList){
      if (researchQuestionary!=null) {
        List<QuestionnaireQuestionType> questionnaireQuestionTypeList = questionnaireQuestionTypeDao
                .fetch(QUESTIONNAIRE_QUESTION_TYPE.QUESTIONNAIRE_CODE
                        .eq(QuestionnaireQuestionType.TRAINING_SATISFACTION_ASSESSMENT));
        List<String> qqtIdList = questionnaireQuestionTypeList.stream()
                .map(QuestionnaireQuestionType::getId).collect(Collectors.toList());
        String questionaryId = researchQuestionary.getId();
        //更新学员是否参与满意度问卷状态
//			questionnaireSurveyService.save(memberId, classId, questionaryId);
        Optional<Trainee> record = traineeDao
                .fetchOne(TRAINEE.CLASS_ID.eq(classId), TRAINEE.MEMBER_ID.eq(memberId),
                        TRAINEE.DELETE_FLAG.eq(0), TRAINEE.TYPE.eq(0));
        if (!record.isPresent()) {
          Integer praiseNum = traineeDao
                  .execute(y -> y.select(Fields.start().add(TRAINEE.ID.count()).end()).from(TRAINEE)
                          .where(TRAINEE.CLASS_ID.eq(classId)).and(TRAINEE.COMMIT_QUESTIONARY.eq(1))
                          .and(TRAINEE.TYPE.eq(0))
                          .and(TRAINEE.DELETE_FLAG.eq(0))
                  ).fetchOne(TRAINEE.ID.count());
          Optional<ClassInfo> classInfo = classInfoDao.getOptional(classId);
          if (classInfo.isPresent()) {
            ClassInfo c = classInfo.get();
            c.setSubmitNum(praiseNum);
            classInfoDao.update(c);
          }
        }
        //删除问卷相关数据
        List<String> levelList = questionnaireQuestionTypeList.stream().filter(mapper -> {
          return QuestionnaireQuestionType.LEVEL_OVERALL_ASSESSMENT.equals(mapper.getLevel());
        }).map(QuestionnaireQuestionType::getId).collect(Collectors.toList());
        List<ResearchAnswerRecord> researchAnswerRecordList = researchAnswerRecordDao.fetch(
                RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID.in(qqtIdList)
                        .and(RESEARCH_ANSWER_RECORD.CLASS_ID.eq(classId))
                        .and(RESEARCH_ANSWER_RECORD.MEMBER_ID.eq(memberId)));
        List<ResearchAnswerRecord> levelRarList = researchAnswerRecordList.stream()
                .filter(predicate -> {
                  return levelList.contains(predicate.getQuestionnaireQuestionId());
                }).collect(Collectors.toList());
        //如果是党校进修班 单独处理，因为党校进修班分成了周问卷+总体问卷
        if(existPartyCadreClass(classId)){
          //减少用户作答统计
          newSatisfactionQuestionnaireTotal(researchAnswerRecordList, classId, questionaryId, "1");
        }else{
          if (levelRarList.size() == levelList.size()) {
            //减少用户作答统计
            newSatisfactionQuestionnaireTotal(researchAnswerRecordList, classId, questionaryId, "1");
          }
        }

        //删除用户该问卷记录
//            researchRecordDao.delete(RESEARCH_RECORD.MEMBER_ID.eq(memberId)
//                    .and(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(questionaryId)));
        List<String> recordList=researchRecordDao.execute(dslContext -> {
          return dslContext.select(Fields.start().add(RESEARCH_RECORD.ID).end()).from(RESEARCH_RECORD)
                  .where(RESEARCH_RECORD.MEMBER_ID.eq(memberId)
                          .and(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(questionaryId)))
                  .fetch(RESEARCH_RECORD.ID);
        });
        LOGGER.error("删除学员问卷关联关系，班级id={}，用户id={}，问卷id={}，问卷名称={},记录数量={}",
                classId,memberId,questionaryId,researchQuestionary.getName(),recordList==null?0:recordList.size());
        researchRecordDao.delete(recordList);
//            Stream<Condition> conditionStream=Stream.of(
//                    RESEARCH_RECORD.MEMBER_ID.eq(memberId),
//                    RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(questionaryId)
//            );
//            researchRecordDao.delete(conditionStream);

//            Stream<Condition> conditionStreamAnswer=Stream.of(
//                    RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID.in(qqtIdList),
//                    RESEARCH_ANSWER_RECORD.CLASS_ID.eq(classId),
//                    RESEARCH_ANSWER_RECORD.MEMBER_ID.eq(memberId)
//            );
//            researchAnswerRecordDao.delete(conditionStreamAnswer);

        //删除用户作答记录
        List<String> answerRecordList=researchAnswerRecordDao.execute(dslContext -> {
          return dslContext.select(Fields.start().add(RESEARCH_ANSWER_RECORD.ID).end())
                  .from(RESEARCH_ANSWER_RECORD)
                  .where(RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID.in(qqtIdList)
                          .and(RESEARCH_ANSWER_RECORD.CLASS_ID.eq(classId))
                          .and(RESEARCH_ANSWER_RECORD.MEMBER_ID.eq(memberId))
                          .and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.in(recordList))).fetch(RESEARCH_ANSWER_RECORD.ID);
        });
        LOGGER.error("删除学员问卷回答关联关系，班级id={}，用户id={}，问卷id={}，问卷名称={},回答记录数量={}",
                classId,memberId,questionaryId,researchQuestionary.getName(),answerRecordList==null?0:answerRecordList.size());
        researchAnswerRecordDao.delete(answerRecordList);
        //删除用户作答记录
//            researchAnswerRecordDao.delete(
//                    RESEARCH_ANSWER_RECORD.QUESTIONNAIRE_QUESTION_ID.in(qqtIdList)
//                            .and(RESEARCH_ANSWER_RECORD.CLASS_ID.eq(classId))
//                            .and(RESEARCH_ANSWER_RECORD.MEMBER_ID.eq(memberId)));

          //发消息，更新学员的待办消息
          sender.send(
                  MessageTypeContent.SUBMIT_RESEARCH_QUESTIONARY_NEW,MessageHeaderContent.MEMBERID,
                  memberId);
      }
    }
  }

  /**
   * 新增满意度问卷
   */
  private void insetResearchQuestionary() {
    //当前时间
    long time = System.currentTimeMillis() + 5;
    List<ClassInfo> list = classOfflineCourseDao.execute(dao -> {
      return dao.select(Fields.start()
          .add(CLASS_INFO.ID)
          .add(PROJECT.NAME)
          .add(CLASS_INFO.ARRIVE_DATE)
          .add(CLASS_INFO.RETURN_DATE)
          .add(CLASS_OFFLINE_COURSE.COURSE_DATE.min())
          .add(RESEARCH_QUESTIONARY.ID)
          .add(CLASS_EVALUATE.DELETE_FLAG)
          .add(PROJECT.IS_PARTY_CADRE)
          .add(CLASS_OFFLINE_COURSE.COURSE_DATE.max())
          .end())
          .from(CLASS_INFO)
          .leftJoin(PROJECT).on(PROJECT.ID.eq(CLASS_INFO.PROJECT_ID))
          .leftJoin(CLASS_OFFLINE_COURSE).on(CLASS_OFFLINE_COURSE.CLASS_ID.eq(CLASS_INFO.ID)
              .and(CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(0))
              .and(CLASS_OFFLINE_COURSE.TYPE.in(ClassOfflineCourse.TYPE_FACE,ClassOfflineCourse.TYPE_LIVE,
                      ClassOfflineCourse.TYPE_ONLINE_COURSE, ClassOfflineCourse.TYPE_LIVE_INTERACT)))
          .leftJoin(RESEARCH_QUESTIONARY).on(RESEARCH_QUESTIONARY.CLASS_ID.eq(CLASS_INFO.ID)
              .and(RESEARCH_QUESTIONARY.TYPE
                  .eq(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY)))
          .leftJoin(CLASS_EVALUATE).on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_QUESTIONARY.ID))
          .where(CLASS_INFO.ARRIVE_DATE.ge(ClassInfo.SATISFACTION_TIME))//培训班报到日期在2019.5.12之后
          .and(RESEARCH_QUESTIONARY.ID.isNull()) //只查询无问卷的班级
          .groupBy(CLASS_INFO.ID)
          .fetch(item -> {
            ClassInfo classInfo = new ClassInfo();
            classInfo.setId(item.getValue(CLASS_INFO.ID));
            classInfo.setArriveDate(item.getValue(CLASS_INFO.ARRIVE_DATE));
            classInfo.setReturnDate(item.getValue(CLASS_INFO.RETURN_DATE));
            classInfo.setName(item.getValue(PROJECT.NAME));
            try {
              Long courseDate = item.get(CLASS_OFFLINE_COURSE.COURSE_DATE.min());
              classInfo.setCourseDate(courseDate == null ? 0 : courseDate);
            } catch (Exception e) {
              classInfo.setCourseDate(0);
            }
            try {
              Long courseDateMax = item.get(CLASS_OFFLINE_COURSE.COURSE_DATE.max());
              classInfo.setCourseDateMax(courseDateMax == null ? 0 : courseDateMax);
            } catch (Exception e) {
              classInfo.setCourseDateMax(0);
            }
            classInfo.setQuestionaryId(item.getValue(RESEARCH_QUESTIONARY.ID));
            classInfo.setDeleteFlag(item.getValue(CLASS_EVALUATE.DELETE_FLAG));
            classInfo.setIsPartyCadre(item.getValue(PROJECT.IS_PARTY_CADRE));//是否是党干部培训班 0否 1是
            return classInfo;
          });
    });
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
    List<ResearchQuestionary> insertList = new ArrayList<ResearchQuestionary>();
    List<ClassEvaluate> reList = new ArrayList<ClassEvaluate>();
//        List<String> deleteList = new ArrayList<String>();
//        List<String> deleteClassIdList = new ArrayList<String>();
    Map<String, List<String>> map = new HashMap<>();
    String resourceName = "满意度问卷(学员)";
    for (ClassInfo classInfo : list) {
      LOGGER.info("班级名称={}，班级id={}，班级是否删除={}，courseDate={},questionaryId={}，是否是党校班={}",
              classInfo.getName(), classInfo.getId(),classInfo.getCourseDate(),classInfo.getQuestionaryId(),classInfo.getIsPartyCadre());
      if (!ClassEvaluate.DELETE_TRUE.equals(classInfo.getDeleteFlag())
          && classInfo.getCourseDate() > 0
          && classInfo.getCourseDate() <= time && ("".equals(classInfo.getQuestionaryId())
          || classInfo.getQuestionaryId() == null)) {
        //如果是党校干部培训班
        if(classInfo.getIsPartyCadre()==1){
          //党干部培训班生成问卷的业务逻辑不通，需要生成多个周问卷+整体问卷
          //如果配置了周主题，根据周主题生成周问卷，否则根据自然周生成
          partyCadreHandle(classInfo,insertList,reList,map,time);
        }else{
          //新增问卷详情
          ResearchQuestionary rq = new ResearchQuestionary();
          rq.forInsert();
          rq.setCreateTime(time);
          Long t = classInfo.getArriveDate();
          Long t2 = classInfo.getReturnDate();
          String arrDate = sdf.format(new Date(t));
          String retDate = sdf.format(new Date(t2));
          String detailM = "亲爱的学员:" +
                  "非常感谢您对学院各项工作的理解、支持和帮助，为持续提升培训质量，请留下您对培训的评价及建议。并祝您工作顺利、万事如意!" +
                  "培训项目:" + classInfo.getName() + "计划时间:" + arrDate + "至" + retDate;
          rq.setQuestionaryDetail(detailM);
          rq.setStartTime(t.equals(t2) ? t : t2 - 86400000);
          rq.setEndTime(t.equals(t2) ? t2 + 604800000 : t2 + 518400000);
          rq.setName(resourceName);
          rq.setClassId(classInfo.getId());
          rq.setType(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY);
          map.put(classInfo.getId(), Collections.singletonList(rq.getId()));
          insertList.add(rq);
          //班级新增问卷
          ClassEvaluate ce = new ClassEvaluate();
          ce.forInsert();
          ce.setClassId(classInfo.getId());
          ce.setType(ClassEvaluate.TYPE_EVA_STU_NEW);
          ce.setResourceId(rq.getId());
          ce.setResourceName(resourceName);
          ce.setCreateTime(time);
          ce.setDeleteFlag(ClassEvaluate.DELETE_FALSE);
          ce.setStartTime(rq.getStartTime());
          ce.setEndTime(rq.getEndTime());
          ce.setRelease(ClassEvaluate.RELEASS);
          reList.add(ce);
          //			} else if(ClassEvaluate.DELETE_TRUE != classInfo.getDeleteFlag() && classInfo.getCourseDate() == 0 && classInfo.getQuestionaryId() != null){
//				deleteList.add(classInfo.getQuestionaryId());
//				deleteClassIdList.add(classInfo.getId());
        }

      }
    }
    if (insertList.size() > 0) {
      researchQuestionaryDao.insert(insertList);
      classEvaluateDao.insert(reList);
      List<Trainee> traineeList = traineeDao.fetch(TRAINEE.CLASS_ID.in(map.keySet()));
      List<ResearchRecord> rrList = new ArrayList<ResearchRecord>();
      for (Trainee trainee : traineeList) {
        List<String> rqList=map.get(trainee.getClassId());
        for(String rqId:rqList){
          ResearchRecord rr = new ResearchRecord();
          rr.forInsert();
          rr.setMemberId(trainee.getMemberId());
          rr.setResearchQuestionaryId(rqId);
          rrList.add(rr);
        }
      }
      researchRecordDao.insert(rrList);
    }
    // 保存成功后,发送满意度问卷评估站内信
    insertList.stream().filter(data ->data.getIsEnsemble()==null).forEach(x -> {
      LOGGER.info("发送异步消息发送站内信通知,classId={}", x.getClassId());
      sender.send(com.zxy.product.system.content.MessageTypeContent.SYSTEM_SCHEDULE_MESSAGE,
          MessageHeaderContent.CLASSID, x.getClassId());
      //发送待办消息
      sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID, x.getClassId());
    });

//        if(deleteList.size() > 0){
//        	researchQuestionaryDao.delete(deleteList);
//        	researchRecordDao.delete(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.in(deleteList));
//        	classEvaluateDao.delete(CLASS_EVALUATE.RESOURCE_ID.in(deleteList));
//        	researchAnswerRecordDao.delete(RESEARCH_ANSWER_RECORD.CLASS_ID.in(deleteClassIdList));
//        	//重置班级满意度问卷作答人数
//        	List<ClassInfo> classInfoList = classInfoDao.fetch(CLASS_INFO.ID.in(deleteClassIdList));
//        	classInfoList = classInfoList.stream().map(classInfo -> {
//        		classInfo.setSubmitNum(0);
//        		return classInfo;
//        	}).collect(Collectors.toList());
//        	classInfoDao.update(classInfoList);
//        }
  }

  private void createNewResearchQuestionary(String classId,String themeId){
    ClassInfo classInfo=projectCommonDao.execute(dslContext -> {
      return dslContext.select(Fields.start().add(PROJECT.IS_PARTY_CADRE)
              .add(CLASS_INFO.ARRIVE_DATE).add(CLASS_INFO.RETURN_DATE).end())
              .from(PROJECT)
              .leftJoin(CLASS_INFO)
              .on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
              .where(CLASS_INFO.ID.eq(classId)).fetchOne(record -> {
                ClassInfo info=new ClassInfo();
                info.setIsPartyCadre(record.getValue(PROJECT.IS_PARTY_CADRE));
                info.setArriveDate(record.getValue(CLASS_INFO.ARRIVE_DATE));
                info.setReturnDate(record.getValue(CLASS_INFO.RETURN_DATE));
                return info;
              });
    });
    //是党干部培训班
    if(classInfo!=null&&classInfo.getIsPartyCadre()==1){
      Long t = classInfo.getArriveDate();
      Long t2 = classInfo.getReturnDate();
      Long startTime=t.equals(t2) ? t : t2 - 86400000;
      Long endTime=t.equals(t2) ? t2 + 604800000 : t2 + 518400000;
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
      String arrDate = sdf.format(new Date(t));
      String retDate = sdf.format(new Date(t2));
      String lastThemeId=themeId;
      ClassTheme classTheme=classThemeCommonDao.get(themeId);
      //查询出当前主题按照开始时间正序排 关联的满意度问卷
      List<ResearchQuestionary> questionaryList=researchQuestionaryDao.execute(dslContext -> {
        return dslContext.select(
                Fields.start().add(RESEARCH_QUESTIONARY.ID)
                        .add(RESEARCH_QUESTIONARY.NAME).add(RESEARCH_QUESTIONARY.CLASS_THEME_ID).end())
                .from(RESEARCH_QUESTIONARY)
                .innerJoin(CLASS_THEME).on(CLASS_THEME.ID.eq(RESEARCH_QUESTIONARY.CLASS_THEME_ID))
                .where(CLASS_THEME.START_TIME.gt(classTheme.getStartTime()))
                .orderBy(CLASS_THEME.START_TIME.asc()).fetch(record -> {
                  ResearchQuestionary questionary=new ResearchQuestionary();
                  questionary.setId(record.getValue(RESEARCH_QUESTIONARY.ID));
                  questionary.setName(record.getValue(RESEARCH_QUESTIONARY.NAME));
                  questionary.setClassThemeId(record.getValue(RESEARCH_QUESTIONARY.CLASS_THEME_ID));
                  return questionary;
                });
      });
      //如果后面有数据，对这些满意度问卷，批量更改关联的周主题id
      if(questionaryList!=null&&!questionaryList.isEmpty()){
        List<String> totalThemeIdList=new ArrayList<>();
        totalThemeIdList.add(themeId);
        List<String> backThemeIdList=questionaryList.stream()
                .map(ResearchQuestionary::getClassThemeId).collect(Collectors.toList());
        totalThemeIdList.addAll(backThemeIdList);
        int size=questionaryList.size();
        for(int i=0;i<size;i++){
          ResearchQuestionary researchQuestionary=questionaryList.get(i);
          researchQuestionary.setClassThemeId(totalThemeIdList.get(i));
        }
        //批量更新满意度问卷
        researchQuestionaryDao.update(questionaryList);
        lastThemeId=questionaryList.get(size-1).getClassThemeId();
      }
      int count =classThemeCommonDao.count(CLASS_THEME.CLASS_ID.eq(classId).and(CLASS_THEME.TYPE.eq(ClassTheme.TYPE_OFFLINE)));
      String questionaryName="【第"+count+"周】讲师授课满意度调查";
      ResearchQuestionary rq = new ResearchQuestionary();
      rq.forInsert();
      rq.setCreateTime(System.currentTimeMillis());
      String detailM = "亲爱的学员:" +
              "非常感谢您对学院各项工作的理解、支持和帮助，为持续提升培训质量，请留下您对培训的评价及建议。并祝您工作顺利、万事如意!" +
              "培训项目:" + classInfo.getName() + "计划时间:" + arrDate + "至" + retDate;
      rq.setQuestionaryDetail(detailM);
      rq.setStartTime(startTime);
      rq.setEndTime(endTime);
      rq.setName(questionaryName);
      rq.setClassId(classInfo.getId());
      rq.setType(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY);
      rq.setClassThemeId(lastThemeId);//关联班级周主题id
      rq.setIsEnsemble(0);//周满意度问卷
      researchQuestionaryDao.insert(rq);
      //班级新增问卷
      ClassEvaluate ce = new ClassEvaluate();
      ce.forInsert();
      ce.setClassId(classInfo.getId());
      ce.setType(ClassEvaluate.TYPE_EVA_STU_NEW);
      ce.setResourceId(rq.getId());
      ce.setResourceName(questionaryName);
      ce.setCreateTime(System.currentTimeMillis());
      ce.setDeleteFlag(ClassEvaluate.DELETE_FALSE);
      ce.setStartTime(rq.getStartTime());
      ce.setEndTime(rq.getEndTime());
      ce.setRelease(ClassEvaluate.RELEASS);
      classEvaluateDao.insert(ce);
      //插入学员问卷记录表
      List<Trainee> traineeList = traineeDao.fetch(TRAINEE.CLASS_ID.eq(classId));
      List<ResearchRecord> rrList = new ArrayList<ResearchRecord>();
      for (Trainee trainee : traineeList) {
        ResearchRecord rr = new ResearchRecord();
        rr.forInsert();
        rr.setMemberId(trainee.getMemberId());
        rr.setResearchQuestionaryId(rq.getId());
        rrList.add(rr);
      }
      researchRecordDao.insert(rrList);
    }
  }



  private void partyCadreHandle(ClassInfo classInfo,List<ResearchQuestionary> insertList,
                                List<ClassEvaluate> reList,Map<String, List<String>> map,Long time){
      List<ClassTheme> themeList=classThemeService.findOfflineThemeByClassId(classInfo.getId());
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
    List<String> reqIds=new ArrayList<>();
    Long t = classInfo.getArriveDate();
    Long t2 = classInfo.getReturnDate();
    Long startTime=t.equals(t2) ? t : t2 - 86400000;
    Long endTime=t.equals(t2) ? t2 + 604800000 : t2 + 518400000;
    List<Map<String,String>> questionnaireNameList=new ArrayList<>();
    Map<String,String> themeMap=null;
    String prefix="【第";
    String suffix="周】讲师授课满意度调查";
    String ensembleName="总体教学情况满意度调查";//整体满意度问卷名称
    //如果有周主题，周问卷就按照周主题生成
      if(themeList!=null&&!themeList.isEmpty()){
        int size=themeList.size();
        for(int i=0;i<size;i++){
          StringBuilder sbd=new StringBuilder();
          String resourceName=sbd.append(prefix).append(i+1).append(suffix).toString();
          themeMap=new HashMap<>();
          themeMap.put("name",resourceName);
          themeMap.put("id",themeList.get(i).getId());
          questionnaireNameList.add(themeMap);
        }
      }else{
        //如果没有周主题，就按照自然周生成周问卷
        Map<Integer,List<Long>> resultMap=new HashMap<>();
        naturalWeek(classInfo.getCourseDate(),classInfo.getCourseDateMax(),1,resultMap);
        for(Map.Entry<Integer,List<Long>> weekMap:resultMap.entrySet()) {
          themeMap=new HashMap<>();
          List<Long> valueList = weekMap.getValue();
          ClassTheme classTheme=classThemeService.insertTheme(
                  classInfo.getId(),3,"第"+weekMap.getKey(),
                  Optional.of(weekMap.getKey()),Optional.empty(),valueList.get(0),valueList.get(1));
          StringBuilder sbd=new StringBuilder();
          String resourceName=sbd.append(prefix).append(weekMap.getKey()).append(suffix).toString();
          themeMap.put("name",resourceName);
          themeMap.put("id",classTheme.getId());
          questionnaireNameList.add(themeMap);
        }
      }
      questionnaireNameList.add(new HashMap<String,String>(){{put("name",ensembleName);put("id",null);}});
    for(Map<String,String> themesMap:questionnaireNameList){
      String name=themesMap.get("name");
      String themeId=themesMap.get("id");
      ResearchQuestionary rq = new ResearchQuestionary();
      rq.forInsert();
      rq.setCreateTime(time);
      String arrDate = sdf.format(new Date(t));
      String retDate = sdf.format(new Date(t2));
      String detailM = "亲爱的学员:" +
              "非常感谢您对学院各项工作的理解、支持和帮助，为持续提升培训质量，请留下您对培训的评价及建议。并祝您工作顺利、万事如意!" +
              "培训项目:" + classInfo.getName() + "计划时间:" + arrDate + "至" + retDate;
      rq.setQuestionaryDetail(detailM);
      rq.setStartTime(startTime);
      rq.setEndTime(endTime);
      rq.setName(name);
      rq.setClassId(classInfo.getId());
      rq.setType(ResearchQuestionary.TYPE_NEW_SATISFACTION_QUESTIONARY);
      rq.setClassThemeId(themeId);//关联班级周主题id
      if(!ensembleName.equals(name)){
        rq.setIsEnsemble(0);//标识是周问卷;
      }
      map.put(classInfo.getId(), Collections.singletonList(rq.getId()));
      insertList.add(rq);
      reqIds.add(rq.getId());
      //班级新增问卷
      ClassEvaluate ce = new ClassEvaluate();
      ce.forInsert();
      ce.setClassId(classInfo.getId());
      ce.setType(ClassEvaluate.TYPE_EVA_STU_NEW);
      ce.setResourceId(rq.getId());
      ce.setResourceName(name);
      ce.setCreateTime(time);
      ce.setDeleteFlag(ClassEvaluate.DELETE_FALSE);
      ce.setStartTime(rq.getStartTime());
      ce.setEndTime(rq.getEndTime());
      ce.setRelease(ClassEvaluate.RELEASS);
      reList.add(ce);
    }
    map.put(classInfo.getId(),reqIds);
  }
  //根据自然周对日期分组
  public void naturalWeek(long startTime, long endTime, int index, Map<Integer,List<Long>> resultMap){
    Calendar c = Calendar.getInstance();
    Date date=new Date(startTime);
    c.setTime(date);
    int weekDay=c.get(Calendar.DAY_OF_WEEK);
    List<Long> timeList=new ArrayList<>();
    //如果是周日
    if(weekDay==1){
      timeList.add(startTime);
      timeList.add(startTime);
      c.add(Calendar.DATE,1);
      startTime=c.getTimeInMillis();
      resultMap.put(index,timeList);
    }else{
      //如果是周一到周六
      c.add(Calendar.DATE,8-weekDay);
      //判断当天日期所在周的最后一天 是否大于课程的结束时间
      if(c.getTimeInMillis()<endTime){
        timeList.add(startTime);
        timeList.add(c.getTimeInMillis());
        c.add(Calendar.DATE,1);
        startTime=c.getTimeInMillis();
        resultMap.put(index,timeList);
      }else{
        timeList.add(startTime);
        timeList.add(endTime);
        resultMap.put(index,timeList);
        return;
      }
    }
    index=index+1;
    //递归调用
    naturalWeek(startTime,endTime,index,resultMap);
  }

  /**
   * 判断是否是党干部培训班
   * @param classId
   * @return
   */
  private boolean existPartyCadreClass(String classId){
    ClassInfo classInfo=projectCommonDao.execute(dslContext -> {
      return dslContext.select(Fields.start().add(PROJECT.IS_PARTY_CADRE)
              .add(CLASS_INFO.ARRIVE_DATE).add(CLASS_INFO.RETURN_DATE).end())
              .from(PROJECT)
              .leftJoin(CLASS_INFO)
              .on(CLASS_INFO.PROJECT_ID.eq(PROJECT.ID))
              .where(CLASS_INFO.ID.eq(classId)).fetchOne(record -> {
                ClassInfo info=new ClassInfo();
                info.setIsPartyCadre(record.getValue(PROJECT.IS_PARTY_CADRE));
                info.setArriveDate(record.getValue(CLASS_INFO.ARRIVE_DATE));
                info.setReturnDate(record.getValue(CLASS_INFO.RETURN_DATE));
                return info;
              });
    });
    if(classInfo!=null&&classInfo.getIsPartyCadre()==1){
      return true;
    }else{
      return false;
    }
  }

  /**
   * 每天凌晨获取昨天过期（满意度问卷结束时间）的培训班
   * 对培训班里面的学员待办消息重置
   */
  private void handleExpireClass(){
    //捕获异常，防止出现意外情况，导致后面的满意度问卷生成方法不执行
    Long time=System.currentTimeMillis();
    String dateStr= StringUtils.getDayString(Optional.ofNullable(time),Optional.ofNullable(DATE_FORMAT),-1);
    Long startTime=StringUtils.datetimeString2OptionalLong(
            new StringBuilder().append(dateStr).append(DATA_START).toString());
    Long endTime=StringUtils.datetimeString2OptionalLong(
            new StringBuilder().append(dateStr).append(DATA_END).toString());
    List<String> classIds=researchQuestionaryDao.execute(dslContext -> {
      return dslContext.selectDistinct(Fields.start().add(RESEARCH_QUESTIONARY.CLASS_ID).end())
              .from(RESEARCH_QUESTIONARY)
              .leftJoin(CLASS_EVALUATE)
              .on(CLASS_EVALUATE.RESOURCE_ID.eq(RESEARCH_QUESTIONARY.ID))
              .where(RESEARCH_QUESTIONARY.END_TIME.ge(startTime).and(RESEARCH_QUESTIONARY.END_TIME.le(endTime)))
              .fetch(RESEARCH_QUESTIONARY.CLASS_ID);
    });
    if(classIds==null||classIds.isEmpty()){
      LOGGER.info("当前没有过期的培训班,date={}",System.currentTimeMillis());
      return;
    }
    try{
      for(String classId:classIds){
        //发送待办消息
        sender.send(MessageTypeContent.AGENCY_CLASSID, MessageHeaderContent.CLASSID,classId);
      }
    }catch(Exception e){
      LOGGER.error("批量更新培训班学员待办消息数量出现异常，classIds={}", Joiner.on(",").join(classIds));
    }

  }

}
