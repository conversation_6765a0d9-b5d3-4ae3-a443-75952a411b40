package com.zxy.product.train.async.listener;

import static com.zxy.product.train.jooq.Tables.CLASS_OFFLINE_COURSE;
import static com.zxy.product.train.jooq.Tables.DIMENSION;
import static com.zxy.product.train.jooq.Tables.DIMENSION_QUESTION;
import static com.zxy.product.train.jooq.Tables.QUESTION;
import static com.zxy.product.train.jooq.Tables.QUESTION_ATTR;
import static com.zxy.product.train.jooq.Tables.RESEARCH_ANSWER_RECORD;
import static com.zxy.product.train.jooq.Tables.RESEARCH_QUESTIONARY;
import static com.zxy.product.train.jooq.Tables.RESEARCH_RECORD;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.jooq.Field;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.train.content.MessageHeaderContent;
import com.zxy.product.train.content.MessageTypeContent;
import com.zxy.product.train.entity.ClassOfflineCourse;
import com.zxy.product.train.entity.OfflineCourse;
import com.zxy.product.train.entity.Question;
import com.zxy.product.train.entity.QuestionAttr;
import com.zxy.product.train.entity.ResearchQuestionary;
/**
 * 
 * <AUTHOR>
 * 面授课程更新课程满意度
 *
 */
@Service
public class SaveOfflineCouseSatisfactionListener extends AbstractMessageListener{

	private static final Logger LOGGER = LoggerFactory.getLogger(SaveOfflineCouseSatisfactionListener.class);

    private CommonDao<ResearchQuestionary> researchQuestionaryDao;
    private CommonDao<Question> questionDao;
    private CommonDao<ClassOfflineCourse> offlineCourseDao;
	private CommonDao<OfflineCourse> offlineDao;

	@Autowired
    public void setOfflineCourseDao(CommonDao<ClassOfflineCourse> offlineCourseDao) {
		this.offlineCourseDao = offlineCourseDao;
	}
    @Autowired
	public void setOfflineDao(CommonDao<OfflineCourse> offlineDao) {
		this.offlineDao = offlineDao;
	}
    @Autowired
    public void setResearchQuestionaryDao(CommonDao<ResearchQuestionary> researchQuestionaryDao) {
        this.researchQuestionaryDao = researchQuestionaryDao;
    }
    @Autowired
    public void setQuestionDao(CommonDao<Question> questionDao) {
        this.questionDao = questionDao;
    }

    @Override
    protected void onMessage(Message message) {
        String classId = message.getHeader(MessageHeaderContent.QUESTIONNAIRECLASSID);
        LOGGER.error("train 保存班级线下课程满意度,classId:{}" , classId);
        if (classId != null && classId != " ") {
			List list = offlineCourseDao.fetch(CLASS_OFFLINE_COURSE.CLASS_ID.eq(classId));
			if(list.size()>0){
				findManYiB(classId);
			}
        }
    }

    @Override
    public int[] getTypes() {
            return new int[]{
                    MessageTypeContent.TRAIN_QUESTIONNAIRE_UPDATE};
    }

    private int findManYiB(String classId) {
		DecimalFormat df = new DecimalFormat("0.00");
		java.text.DecimalFormat   df2   =new   java.text.DecimalFormat("#.0");
		Field<Integer> count1 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("0", RESEARCH_ANSWER_RECORD.ID).count()
				.as("count1");

		Field<Integer> count2 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("1", RESEARCH_ANSWER_RECORD.ID).count()
				.as("count2");

		Field<Integer> count3 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("2", RESEARCH_ANSWER_RECORD.ID).count()
				.as("count3");

		Field<Integer> count4 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("3", RESEARCH_ANSWER_RECORD.ID).count()
				.as("count4");
		Field<Integer> count5 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("4", RESEARCH_ANSWER_RECORD.ID).count()
				.as("count5");
		Field<Integer> count7 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("6", RESEARCH_ANSWER_RECORD.ID).count()
				.as("count7");
		Field<Integer> count8 = DSL.choose(RESEARCH_ANSWER_RECORD.ANSWER).when("7", RESEARCH_ANSWER_RECORD.ID).count()
				.as("count8");
		List<QuestionAttr> attr = researchQuestionaryDao
				.execute(x -> x.select(Fields.start().add(QUESTION.ID).add(QUESTION.CONTENT).add(QUESTION.ORDER).add(count1).add(count2).add(count3)
						.add(count4).add(count5).add(count7).add(count8).add(RESEARCH_ANSWER_RECORD.ANSWER.count()).end()))
				.from(RESEARCH_QUESTIONARY).leftJoin(DIMENSION)
				.on(RESEARCH_QUESTIONARY.ID.eq(DIMENSION.RESEARCH_QUESTIONARY_ID))
				.leftJoin(DIMENSION_QUESTION).on(DIMENSION_QUESTION.DIMENSION_ID.eq(DIMENSION.ID)).leftJoin(QUESTION)
				.on(DIMENSION_QUESTION.QUESTION_ID.eq(QUESTION.ID)).leftJoin(QUESTION_ATTR)
				.on(QUESTION_ATTR.QUESTION_ID.eq(QUESTION.ID))
				.leftJoin(RESEARCH_RECORD)
				.on(RESEARCH_RECORD.RESEARCH_QUESTIONARY_ID.eq(RESEARCH_QUESTIONARY.ID))
				.leftJoin(RESEARCH_ANSWER_RECORD.forceIndex("ids_t_research_answer_record_index_f_question_id"))
				.on(RESEARCH_ANSWER_RECORD.ANSWER.eq(QUESTION_ATTR.NAME)
						.and(RESEARCH_ANSWER_RECORD.QUESTION_ID.eq(QUESTION.ID))
						.and(RESEARCH_ANSWER_RECORD.RESEARCH_RECORD_ID.eq(RESEARCH_RECORD.ID)))
				.where(RESEARCH_QUESTIONARY.TYPE.eq(4).and(RESEARCH_QUESTIONARY.CLASS_ID.eq(classId))
						.and(DIMENSION.ORDER.eq(2)))
				.groupBy(QUESTION.ID,QUESTION.CONTENT).orderBy(QUESTION.ORDER).fetch(r -> {
					QuestionAttr questionAttr = new QuestionAttr();
					questionAttr.setQuestionContent(r.getValue(QUESTION.CONTENT));
					questionAttr.setQuestionId(r.getValue(QUESTION.ID));
					questionAttr.setManA(r.getValue(count1));
					questionAttr.setManB(r.getValue(count2));
					questionAttr.setManC(r.getValue(count3));
					questionAttr.setManD(r.getValue(count4));
					questionAttr.setManE(r.getValue(count5));
					questionAttr.setManF(r.getValue(count7));
					Integer count6 = r.getValue(count1) + r.getValue(count2) + r.getValue(count3) + r.getValue(count4) + r.getValue(count5);
					Integer count9 = r.getValue(count1) + r.getValue(count7) + r.getValue(count8);
					questionAttr.setManZ(count6);
					Float a = 0.00f;
					Float b = 0.00f;
					Float c = 0.00f;
					Float d = 0.00f;
					Float e = 0.00f;
					Float f = 0.00f;
					Float z = 0.00f;
					if (r.getValue(count1) != 0 && count6 != 0) {
						a = ((float) r.getValue(count1) / (float) count6) * 100;
						a = (float)(Math.round(a*100))/100;
					}
					questionAttr.setManAF(df.format(a));
					if (r.getValue(count2) != 0 && count6 != 0) {
						b = ((float) r.getValue(count2) / (float) count6) * 100;
						b = (float)(Math.round(b*100))/100;
					}
					questionAttr.setManBF(df.format(b));
					if (r.getValue(count3) != 0 && count6 != 0) {
						c = ((float) r.getValue(count3) / (float) count6) * 100;
						c = (float)(Math.round(c*100))/100;
					}
					questionAttr.setManCF(df.format(c));
					if (r.getValue(count4) != 0 && count6 != 0) {
						d = ((float) r.getValue(count4) / (float) count6) * 100;
						d = (float)(Math.round(d*100))/100;
					}
					questionAttr.setManDF(df.format(d));
					if (r.getValue(count5) != 0 && count6 != 0) {
						e = ((float) r.getValue(count5) / (float) count6) * 100;
						e = (float)(Math.round(e*100))/100;
					}
					questionAttr.setManEF(df.format(e));
					if (r.getValue(count7) != 0 && count9 != 0) {
						f = ((float) r.getValue(count7) / (float) count9) * 100;
						f = (float)(Math.round(f*100))/100;
					}
					questionAttr.setManFF(df.format(f));
					if (count6 != 0) {
						if (r.getValue(count1) != 0 && r.getValue(count2) != 0) {
							z = (((float) (r.getValue(count1) + r.getValue(count2))) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else if (r.getValue(count1) != 0 && r.getValue(count2) == 0) {
							z = ((float) r.getValue(count1) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else if (r.getValue(count1) == 0 && r.getValue(count2) != 0) {
							z = ((float) r.getValue(count2) / (float) count6) * 100;
							z = (float)(Math.round(z*100))/100;
							questionAttr.setManZF(df.format(z));
						} else {
							questionAttr.setManZF(df.format(z));
						}
					} else {
						questionAttr.setManZF(df.format(z));
					}
					return questionAttr;
				});
		
		List<QuestionAttr> attrNew = new ArrayList<QuestionAttr>();
		Float p = 0.00f;
		if (attr != null && attr.size() > 0) {
			for (int i = 0; i < attr.size(); i++) {
				if (attr.get(i).getQuestionContent()!=null) {
					if(!attr.get(i).getQuestionContent().contains("是否愿意推荐")){
						p += Float.parseFloat(attr.get(i).getManAF()) + Float.parseFloat(attr.get(i).getManBF());
					}else{
						attr.get(i-1).setManCMF(df.format(p/2));
						attr.get(i-2).setManCMF(df.format(p/2));
						attr.get(i-1).setManF(attr.get(i).getManF());
						attr.get(i-2).setManF(attr.get(i).getManF());
						attr.get(i-1).setManFF(attr.get(i).getManFF());
						attr.get(i-2).setManFF(attr.get(i).getManFF());
						attrNew.add(attr.get(i-2));
						attrNew.add(attr.get(i-1));
						p = 0.00f;
					}
				} else{
					return 1;
				}
			}
		} else {
			return 1;
		}
		//update(attr);
		StringBuffer cIds = new StringBuffer();
		List<ClassOfflineCourse> off = new ArrayList<>();
		List<OfflineCourse> oCourse = new ArrayList<>();
		if (attrNew.size()>0) {
			for (int i = 0; i < attrNew.size(); i++) {
				cIds.append(attrNew.get(i).getQuestionId()).append(",");
			}
			if(cIds.length() > 1) {
				cIds.deleteCharAt(cIds.length() - 1);
			}
			List<ClassOfflineCourse> offline = offlineCourseDao.execute(
					x -> {
						SelectConditionStep<Record> where = x.select(Fields.start().add(CLASS_OFFLINE_COURSE.fields())
								.add(CLASS_OFFLINE_COURSE.ID,CLASS_OFFLINE_COURSE.COURSE_SATISFY).end())
								.from(CLASS_OFFLINE_COURSE)
								.innerJoin(QUESTION).on(QUESTION.CLASS_OFFLINE_COURSE_ID.eq(CLASS_OFFLINE_COURSE.ID))
								.where(QUESTION.ID.in(cIds.toString().split(",")))
								.and(CLASS_OFFLINE_COURSE.DELETE_FLAG.eq(0));
						List <ClassOfflineCourse> progresses = where.fetchInto(CLASS_OFFLINE_COURSE).into(ClassOfflineCourse.class);
	                    return progresses;
					}

			);
			List<Question> q = questionDao.execute(
					x -> {
						SelectConditionStep<Record> where = x.select(Fields.start().add(QUESTION.fields())
								.add(QUESTION.ID,QUESTION.CLASS_OFFLINE_COURSE_ID).end())
								.from(QUESTION)
								.where(QUESTION.ID.in(cIds.toString().split(",")));
						List <Question> pro = where.fetchInto(QUESTION).into(Question.class);
	                    return pro;
					}

			);
			if (q.size()>0) {
				for (Question question : q) {
					for (QuestionAttr questionAttr : attrNew) {
						if (question.getId().equals(questionAttr.getQuestionId())) {
							Optional<ClassOfflineCourse> classOfflineCourse = offlineCourseDao.getOptional(question.getClassOfflineCourseId());
							classOfflineCourse.ifPresent(x -> {
								if (questionAttr.getManCMF() != null) {
									Double m = Double.parseDouble(df2.format(Double.parseDouble(questionAttr.getManCMF()))) / 10;
									x.setCourseSatisfy((double)(Math.round(m*100))/100);
									off.add(x);
								}
							});
							Optional<OfflineCourse> offlineCourse = offlineDao.getOptional(question.getClassOfflineCourseId());
							offlineCourse.ifPresent(x -> {
								if (questionAttr.getManCMF() != null) {
									Double m = Double.parseDouble(df2.format(Double.parseDouble(questionAttr.getManCMF()))) / 10;
									x.setSatisfiedDegree((double)(Math.round(m*100))/100);
									oCourse.add(x);
								}
							});
						}
					}
				}
			}
		}
		if (off.size()>0) {
			offlineCourseDao.update(off);
		}
		if (oCourse.size()>0) {
			offlineDao.update(oCourse);
		}
		return 0;
	}

}
