package com.zxy.product.examstu.web.controller;

import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;

import com.zxy.product.examstu.api.StrongBaseService;

import com.zxy.product.exam.entity.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;


/**
 * 强基计划-考试功能
 */
@Controller
@RequestMapping("/strong-base")
public class StrongBaseController {

    private static final Logger log = LoggerFactory.getLogger(StrongBaseController.class);

    private StrongBaseService strongBaseService;

    @Autowired
    public void setStrongBaseService(StrongBaseService strongBaseService) {
        this.strongBaseService = strongBaseService;
    }


    /**
     * 学员端-查询子认证考试列表信息
     */
    @RequestMapping(value = "/front-exam-list", method = RequestMethod.GET)
    @JSON("exams.(id,name,startTime,endTime,allowExamTimes,coverId,coverIdPath)")
    @JSON("remainCount,examRegistrationFrequency")
    @Param(name="subId", required = true) // 子认证专区id
    @Param(name="contentId", required = true) // 考试组id
    @Param(name="businessType", type=Integer.class, required = true) // 1:模拟考试。2：认证考试
//    @Permitted()
    public Map<String, Object> findFrontExamList(RequestContext requestContext, Subject<Member> subject) {
        Integer examRegion = subject.examRegion();
        return strongBaseService.findFrontExamList(
                examRegion,
            requestContext.get("subId",String.class),
            requestContext.get("contentId",String.class),
            requestContext.get("businessType",Integer.class),
            subject.getCurrentUserId()
        );
    }


}
