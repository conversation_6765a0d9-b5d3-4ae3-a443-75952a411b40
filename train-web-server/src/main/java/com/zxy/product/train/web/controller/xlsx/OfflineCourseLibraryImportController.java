package com.zxy.product.train.web.controller.xlsx;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.validator.LengthValidator;
import com.zxy.common.office.excel.support.validator.RequiredValidator;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.human.util.BrowserUtil;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.entity.Member;
import com.zxy.product.system.entity.Organization;
import com.zxy.product.train.api.CollectionProgrammeConfigService;
import com.zxy.product.train.api.CourseAttributeService;
import com.zxy.product.train.api.LecturerAdeptCourseService;
import com.zxy.product.train.api.LecturerCourseConfigService;
import com.zxy.product.train.api.LecturerService;
import com.zxy.product.train.api.OfflineCourseLibraryService;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.entity.CollectionProgrammeConfig;
import com.zxy.product.train.entity.CourseAttribute;
import com.zxy.product.train.entity.Lecturer;
import com.zxy.product.train.entity.LecturerAdeptCourse;
import com.zxy.product.train.entity.LecturerCourseConfig;
import com.zxy.product.train.entity.OfflineCourseLibrary;

/**
 * 
 * 面授课程库导入
 *
 */
@Controller
@RequestMapping("/offline-course-library-import")
public class OfflineCourseLibraryImportController extends BaseImportController{
 
	private static final String COURSE_ATTRBUTE_NAME = "集采课程";
	private static final String IS_SHARE = "是";

    private GrantService grantService;
	private LecturerService lecturerServivce;
	private LecturerAdeptCourseService lecturerAdeptCourseService;
	private LecturerCourseConfigService lecturerCourseConfigService;
	private CollectionProgrammeConfigService collectionProgrammeConfigService;
	private OfflineCourseLibraryService offlineCourseLibraryService;

	private CourseAttributeService courseAttributeService;

    @Autowired
    public void setGrantService(GrantService grantService) {
		this.grantService = grantService;
	}
    
    @Autowired
    public void setLecturerfigService(LecturerService lecturerServivce) {
		this.lecturerServivce = lecturerServivce;
    }
    
    @Autowired
    public void setLecturerAdeptCourseService(LecturerAdeptCourseService lecturerAdeptCourseService) {
    	this.lecturerAdeptCourseService = lecturerAdeptCourseService;
    }
    
	@Autowired
	public void setCourseAttributeService(CourseAttributeService courseAttributeService) {
		this.courseAttributeService = courseAttributeService;
	}
	
	@Autowired
	public void setOfflineCourseLibraryService(OfflineCourseLibraryService offlineCourseLibraryService) {
		this.offlineCourseLibraryService = offlineCourseLibraryService;
	}
	
    @Autowired
    public void setCollectionProgrammeConfigService(CollectionProgrammeConfigService collectionProgrammeConfigService) {
		this.collectionProgrammeConfigService = collectionProgrammeConfigService;
	}
    

    @Autowired
    public void setLecturerCourseConfigService(LecturerCourseConfigService lecturerCourseConfigService) {
		this.lecturerCourseConfigService = lecturerCourseConfigService;
    }

	
	/**
     * 时间格式转换
     * @param time
     * @return
     */
    public String dataFormat(Optional<Long> time){
    	//时间戳转化为Sting或Date
    	if(time.isPresent()){
            SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd");
            String d = format.format(new Date(time.get()));
            return d;
    	}
		return "";
    }

    /**
     * 面授课程库导入模板
     * @param context
     * @param subject
     * @throws IOException
     */
    @RequestMapping(value = "/export-template", method = RequestMethod.GET)
    @Param(name = "type", type = String.class, required=true)	//0：内部；1：外部
    public void exportImportTemplate(RequestContext context, Subject<Member> subject) throws IOException {
		String type = context.getOptionalString("type").orElse("");
		String attachmentName = "内部讲师面授课程库导入模版.xlsx";
		String typeName = "讲师联系电话";
		if(String.valueOf(Lecturer.TYPE_EXTERNAL).equals(type)) {
			typeName = "讲师身份证号";
			attachmentName = "外部讲师面授课程库导入模版.xlsx";	
		}
		if (BrowserUtil.isMSBrowser(context.getRequest().getHeader("User-Agent"))) {
			attachmentName = URLEncoder.encode(attachmentName, "UTF-8");
		} else {
			attachmentName = new String(attachmentName.getBytes("UTF-8"), "ISO-8859-1");
		}
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + attachmentName);
        Writer writer = new ExcelWriter();
        writer.sheet("面授课程库", new ArrayList<>())
		        .field("讲师姓名", x -> "")
		        .field(typeName, x -> "")
		        .field("课程名称(必填)", x -> "")
		        .field("课程属性(必填)", x -> "")
                .field("课程所属机构\n（“集采课程”必填,其他课程无需填写）", x -> "")
                .field("课程所属方案\n（“集采课程”必填,其他课程无需填写）", x -> "")
                .field("课程分类(必填)", x -> "")
                .field("是否共享(必填)", x -> "")
                .field("适用对象", x -> "")
                .field("参考时长(格式：x小时x分)", x -> "")
                .field("关键词(关键词间用“，”分隔)", x -> "")
                .field("课程简介", x -> "")
                .field("课程目标", x -> "")
                .field("课程大纲", x -> "")
                .field("备注", x -> "");
         
        List<String> ids = new ArrayList<>();
        ids.add("1");
        List<CollectionProgrammeConfig> programmeConfigList = collectionProgrammeConfigService.findConfigByOrganizationIds(ids);
        
        // 课程分类
	    List<LecturerCourseConfig> courseConfigList = lecturerCourseConfigService.find(1);
	    List<String> courseConfig = courseConfigList.stream().map(LecturerCourseConfig :: getName).collect(Collectors.toList());
	    List<String> programmeConfig = programmeConfigList.stream().map(CollectionProgrammeConfig :: getMechanismName).collect(Collectors.toList());
	     
	    // 课程属性
	    List<CourseAttribute> courseAttributeList = courseAttributeService.findAll().getItems();
	    List<String> courseAttributeSet = courseAttributeList.stream().map(CourseAttribute :: getAttributeName).collect(Collectors.toList());
	    
        responseResult(response, writer, null, programmeConfigList, courseConfig, programmeConfig, courseAttributeSet);
    }
    
    /**
     * 相应结果
     * @param response
     * @param writer
     * @param organizationList
     * @param programmeConfigList
     * @param courseConfig
     * @param programmeConfig
     * @throws IOException
     */
	private void responseResult(HttpServletResponse response, Writer writer, List<Organization> organizationList,
			List<CollectionProgrammeConfig> programmeConfigList, List<String> courseConfig, List<String> programmeConfig, List<String> courseAttributeSet)
			throws IOException {
		
		sheel(writer, organizationList, programmeConfigList);
       
        writer.write(response.getOutputStream(), workbook ->{
        	consumer(programmeConfig, courseConfig, workbook, courseAttributeSet);
        });
	}
    
	/**
	 * 附页
	 * @param writer
	 * @param organizationList
	 * @param programmeConfigList
	 */
	private void sheel(Writer writer, List<Organization> organizationList,
			List<CollectionProgrammeConfig> programmeConfigList) {
        // 课程所属机构 课程所属方案
        writer.sheet("课程所属机构", programmeConfigList)
	        .field("课程所属机构", CollectionProgrammeConfig::getMechanismName)
	        .field("课程所属方案", CollectionProgrammeConfig::getProgrammeName)
	        .field("集采机构联系人", CollectionProgrammeConfig::getMechanismContacts)
	        .field("机构联系人电话", CollectionProgrammeConfig::getMechanismContactsNumber)
 	        .field("机构联系人邮箱", CollectionProgrammeConfig::getMechanismContactsEmail);
 //        	.field("集采方案单价", CollectionProgrammeConfig::getCourseUnitPrice);
	}
    
    private void setSelectionContants(org.apache.poi.ss.usermodel.Sheet sheet,  String[] textlist, int firstRow, int endRow, int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        constraint.setExplicitListValues(textlist);
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation data_validation = helper.createValidation(constraint, regions);
        sheet.addValidationData(data_validation);
    }
   
    /**
     * 面授课程库导入集采课程
     * @param file
     * @param rc
     * @param subject
     * @return
     * @throws IOException
     */
    @RequestMapping("/export-import/{type}")
    @Param(name = "type", type = String.class, required=true)	//0：内部；1：外部
	@JSON("successCount, errorCount, errorFileId")
    @JSON("data.(*)")
    @JSON("errorRow.(row,column,code)")
	@Audit(module = "活动管理", subModule = "讲师管理—面授课程库", action = Audit.Action.IMPORT, fisrtAction = "导入", desc = "导入面授课程")
    public Object exportImport(@RequestParam("file") MultipartFile file,RequestContext rc, Subject<Member> subject) throws IOException {
    	Map<String, Object> map = new HashMap<>();
		String type = rc.getOptionalString("type").orElse("");
		String memberId = subject.getCurrentUserId();
    	String memberorganizationId = subject.getRootOrganizationId();
	    //讲师列表
	    List<String> organizationIds = grantService.findGrantedOrganization(memberId, Lecturer.URI,
                Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
                rc.getOptionalString("organizationId"), Optional.empty(), Optional.empty()).stream()
                .map(a -> a.getId()).collect(Collectors.toList());
	    List<Lecturer> lecturerList = lecturerServivce.findMemberToOrgIds(Optional.ofNullable(Integer.valueOf(type)), Optional.ofNullable(null), Optional.ofNullable(organizationIds));
	    Map<String,Lecturer> mapLecturer;
		if(String.valueOf(Lecturer.TYPE_EXTERNAL).equals(type)) {
			//讲师身份证号Map<身份证号,讲师Info>
 			mapLecturer = lecturerList.stream()
 					.filter(action -> {return Lecturer.TYPE_EXTERNAL == action.getType() &&!"".equals(action.getBankIdentity()) && action.getBankIdentity() != null;})
 					.collect(Collectors.toMap(Lecturer::getBankIdentity, x -> x , (x1,x2) -> x2));
		} else {
			//讲师手机号Map<手机号,讲师Info>
 			mapLecturer = lecturerList.stream()
 					.filter(action -> {return Lecturer.TYPE_INSIDE == action.getType() && !"".equals(action.getMobile()) && action.getMobile() != null;})
 					.collect(Collectors.toMap(Lecturer::getMobile, x -> x , (x1,x2) -> x2));
 		}
		
    	// 集采机构、方案 （不授权限默认 1 ）
        List<String> orgId = new ArrayList<>();
        orgId.add("1");
		List<CollectionProgrammeConfig> collectionConfigList = collectionProgrammeConfigService.findConfigByOrganizationIds(orgId);
		// 集采机构
		List<String> companyConfig = collectionConfigList.stream().map(CollectionProgrammeConfig :: getMechanismName).collect(Collectors.toList());
	    
	    List<String> planConfig = collectionConfigList.stream().map(CollectionProgrammeConfig :: getProgrammeName).collect(Collectors.toList());

	    Map<String, String> companyConfigMap = collectionConfigList.stream().map(cc -> {
		    	cc.setKey(cc.getMechanismName() + "-" + cc.getProgrammeName());
		    	return cc;
	        }).collect(Collectors.toMap(CollectionProgrammeConfig :: getKey, CollectionProgrammeConfig :: getId));

    	// 课程分类
	    List<String> courseConfig = new ArrayList<String>();    
	    List<LecturerCourseConfig> filterList = new ArrayList<>();
	    List<LecturerCourseConfig> courseConfigList = lecturerCourseConfigService.find(LecturerCourseConfig.TYPE_COURSE);
	    courseConfigList.forEach(c -> {
	    	if (!courseConfig.contains(c.getName())) {
	    		courseConfig.add(c.getName());
	    		filterList.add(c);
			}
	    });
//	    Set<String> courseConfig = courseConfigList.stream().map(LecturerCourseConfig :: getName).collect(Collectors.toSet());
	    Map<String, String> courseConfigMap = filterList.stream().collect(Collectors.toMap(LecturerCourseConfig :: getName, LecturerCourseConfig :: getId));
	    
	    // 课程属性
	    List<CourseAttribute> courseAttributeList = courseAttributeService.findAll().getItems();
	    List<String> courseAttributeSet = courseAttributeList.stream().map(CourseAttribute :: getAttributeName).collect(Collectors.toList());
	    Map<String, String> courseAttributeMap = courseAttributeList.stream().collect(Collectors.toMap(CourseAttribute :: getAttributeName, CourseAttribute :: getId));
	    
	    List<String> courseNameList = offlineCourseLibraryService.findCourseNameByAttr(COURSE_ATTRBUTE_NAME);
	    
	    Reader r = ValidatorRowData(courseAttributeMap, companyConfig, planConfig, courseConfig, courseNameList, mapLecturer);
        Reader.Result result;
        try {
            result = r.read(file.getInputStream());
        }catch (IOException e) {
            throw new UnprocessableException(ErrorCode.EXCEL_NOTSUPPORT);
        }
        if(result.getErrorRows().size() == 0 && result.getCorrectRows().size() == 0){
            throw new UnprocessableException(ErrorCode.ImportNullFile);
        }
        if(!result.isCellMatched()){
            throw new UnprocessableException(ErrorCode.InvalidExcelTemplate);
        }

		Long createTime = System.currentTimeMillis();
        List<LecturerAdeptCourse> listAdept = new ArrayList<LecturerAdeptCourse>();
        List<OfflineCourseLibrary> list = result.getCorrectRows().stream().map(row -> {
        	OfflineCourseLibrary c = new OfflineCourseLibrary();
            c.forInsert();
            c.setName(row.get(2, String.class));
            c.setCourseAttributes(courseAttributeMap.get(row.get(3, String.class)));
            if (COURSE_ATTRBUTE_NAME.equals(row.get(3, String.class))) {
                c.setInstitutionId(companyConfigMap.get(row.get(4, String.class) + "-" + row.get(5, String.class)));
                c.setCoursePlan(row.get(5, String.class));
			}
            c.setOrganizationId(memberorganizationId);
            c.setSequence(courseConfigMap.get(row.get(6, String.class)));
            c.setIsShare(IS_SHARE.equals(row.get(7, String.class)) == true ? 0 : 1);
            c.setObj(row.get(8, String.class));
            c.setReferenceTime(replaceTime(row.get(9, String.class)));
            c.setKeyword(row.get(10, String.class));
            c.setSummary(row.get(11, String.class));
            c.setTarget(row.get(12, String.class));
            c.setOutline(row.get(13, String.class));
            c.setRemark(row.get(14, String.class));
            c.setIsUse(OfflineCourseLibrary.SHARE_YES);
            c.setUpdateTime(createTime - row.getIndex());
            c.setCreateMember(memberId);
            c.setUpdateMember(memberId);
			c.setCreateTime(createTime - row.getIndex());
			//擅长讲师
			Lecturer lecturer = mapLecturer.get(row.get(1, String.class));
            if(lecturer != null){
            	LecturerAdeptCourse lecturerAdeptCourse = new LecturerAdeptCourse();
            	lecturerAdeptCourse.forInsert();
            	lecturerAdeptCourse.setLecturerId(lecturer.getId());
            	lecturerAdeptCourse.setCourseId(c.getId());
            	lecturerAdeptCourse.setOrganizationId(memberorganizationId);
            	lecturerAdeptCourse.setCreateMemberId(memberId);
            	lecturerAdeptCourse.setCreateTime(createTime - row.getIndex());
            	listAdept.add(lecturerAdeptCourse);
            }
            return c;
        }).collect(Collectors.toList());
        
        if (list.size() > 0) {
        	offlineCourseLibraryService.batchInsert(list);
        	lecturerAdeptCourseService.batchInsert(listAdept);
		}
        map.put("successCount", list.size());
        map.put("errorCount", result.getErrorRows().size());
        map.put("errorRow", result.getErrorRows().stream()
                .flatMap(e-> e.getErrors().stream())
                .map(error-> ImmutableMap.of("column", error.getColumn(),"row", error.getRow(), "code", error.getCode().getCode()))
                .collect(Collectors.toList()));
        map.put("errorFileId", errorResult(result, null, collectionConfigList, companyConfig, courseConfig, courseAttributeSet,type));
        map.put("data", list);
        return map;
    }
    
    /**
     * 校验元素合法性
     * @param courseAttributeMap
     * @param companyConfig
     * @param planConfig
     * @param memberConfig
     * @param phoneConfig
     * @param emailConfig
     * @param courseConfig
     * @param courseNameList
     * @return
     */
	private Reader ValidatorRowData(Map<String, String> courseAttributeMap, List<String> companyConfig, List<String> planConfig, List<String> courseConfig,
			List<String> courseNameList, Map<String,Lecturer> mapLecturer) {
		List<String> courseList = new ArrayList<String>();
		return new DefaultReader().skipRows(1)
                .setColumn(0, String.class, new LengthValidator(0, 16))
     			.setColumn(1,String.class, new LengthValidator(0, 100).compose((v,vc,previous) -> {
        			if(v == null || v.length() == 0) {
        				return true;
        			} else if (mapLecturer.get(v.trim()) != null) {
        				return true;
					} else {
						vc.error(ErrorCode.LecturerIsNotInternalTrainer);//讲师不存在
						return false;
					}
	            }))
        		.setColumn(2,String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 50)).compose((v,vc,previous) -> {
        			if(v == null || v.length() == 0) {
        				vc.error(ErrorCode.CourseNameRequired);//课程名称
        				return false;
					}
	            	return true;
	            }))
                .setColumn(3,String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 20)).compose((v,vc,previous) -> {
                	if(v == null || v.length() == 0) {
        				vc.error(ErrorCode.CourseArrtMatch);// 课程属性
        				return false;
        			}
                	String courseName = (String)previous[2];
                	if (courseList.contains(courseName + v) || !offlineCourseLibraryService.isNotRepeatByName(courseName, courseAttributeMap.get(v))) {
        				vc.error(ErrorCode.CourseNameMatch);
						return false;
					} else {
						courseList.add(courseName + v);
					}
                    return true;
	            }))
                .setColumn(4,String.class, new LengthValidator(0, 50).compose((v,vc,previous) -> {
                	String courseArrt = (String)previous[3];
                	if (COURSE_ATTRBUTE_NAME.equals(courseArrt)) {
                       	if(v == null || v.length()==0) {
            				vc.error(ErrorCode.CourseCompanyMatch);// 课程所属机构(必填)
            				return false;
            			} else if (!companyConfig.contains(v)) {
            				vc.error(ErrorCode.NotCourseCompanyMatch);
            				return false;
    					}
					}
	            	return true;
	            }))
                .setColumn(5,String.class, new LengthValidator(0, 50).compose((v,vc,previous) -> {
                	String courseArrt = (String)previous[3];
                	if (COURSE_ATTRBUTE_NAME.equals(courseArrt)) {
                    	if(v == null || v.length()==0) {
            				vc.error(ErrorCode.CoursePlanMatch);// 课程所属方案(必填)
            				return false;
            			} else if (!planConfig.contains(v)) {
            				vc.error(ErrorCode.NotCoursePlanMatch);
            				return false;
    					}
                	}
	            	return true;
	            }))
                .setColumn(6,String.class, new RequiredValidator<String>().compose(new LengthValidator(1, 50)).compose((v,vc,previous) -> {
        			if(v == null || v.length() == 0) {
        				vc.error(ErrorCode.CourseTypeMatch);// 课程分类(必填)
        				return false;
        			} else if (!courseConfig.contains(v)) {
        				vc.error(ErrorCode.NotCourseTypeMatch);
        				return false;
					}
	            	return true;
	            }))
                .setColumn(7,String.class, new LengthValidator(0, 2).compose((v,vc,previous) -> {
        			if(v == null || v.length() == 0) {
        				vc.error(ErrorCode.IsShareMatch);// 是否共享
        				return false;
        			} else if ("是".equals(v)) {
        				return true;
					} else if ("否".equals(v)) {
        				return true;
					} else {
						vc.error(ErrorCode.SelectIsShareMatch);
        				return false;
					}
                }))
                .setColumn(8, String.class, new LengthValidator(0, 30))// 适用对象
                .setColumn(9,String.class, new LengthValidator(0, 20).compose((v,vc,previous) -> {
        			if(v != null && v.length() > 0 && !isTime(v)) {
        				vc.error(ErrorCode.CourseTimeMatch);// 参考时长(格式：x小时x分)
        				return false;
        			}
	            	return true;
	            }))
                .setColumn(10, String.class, new LengthValidator(0, 50))// 关键词(关键词间用“，”分隔)
                .setColumn(11, String.class, new LengthValidator(0, 200))// 课程简介
                .setColumn(12, String.class, new LengthValidator(0, 150))// 课程目标
                .setColumn(13, String.class, new LengthValidator(0, 500))// 课程大纲
                .setColumn(14, String.class, new LengthValidator(0, 200));// 备注
	}
   
    /**
     * 错过数据相应
     * @param result
     * @param organizationList
     * @param collectionConfigList
     * @param type 
     * @return
     * @throws IOException
     */
	private String errorResult(Reader.Result result, List<Organization> organizationList, List<CollectionProgrammeConfig> collectionConfigList,
			List<String> companyConfig, List<String> courseConfig, List<String> courseAttributeSet, String type) throws IOException {
		String typeName;
		if(String.valueOf(Lecturer.TYPE_EXTERNAL).equals(type)) {
			typeName = "讲师身份证号";
		} else {
			typeName = "讲师联系电话";
		}
		return createErrorTempFile(result, ()-> {
                    Writer writer = new ExcelWriter();
                    writer.sheet("面授课程库", result.getErrorRows())
                    .field("讲师姓名", x -> getRowValue(x, 0))
                    .field(typeName, x -> getRowValue(x, 1))
                    .field("课程名称(必填)", x -> getRowValue(x, 2))
                    .field("课程属性(必填)", x -> getRowValue(x, 3))
                    .field("课程所属机构\n（“集采课程”必填,其他课程无需填写）", x -> getRowValue(x, 4))
                    .field("课程所属方案\n（“集采课程”必填,其他课程无需填写）", x -> getRowValue(x, 5))
//                    .field("上传单位(必填)", x -> getRowValue(x, 4))
                    .field("课程分类(必填)", x -> getRowValue(x, 6))
                    .field("是否共享(必填)", x -> getRowValue(x, 7))
                    .field("适用对象", x -> getRowValue(x, 8))
                    .field("参考时长(格式：x小时x分)", x -> getRowValue(x, 9))
                    .field("关键词(关键词间用“，”分隔)", x -> getRowValue(x, 10))
                    .field("课程简介", x -> getRowValue(x, 11))
                    .field("课程目标", x -> getRowValue(x, 12))
                    .field("课程大纲", x -> getRowValue(x, 13))
                    .field("备注", x -> getRowValue(x, 14));
      
                    sheel(writer, null, collectionConfigList);                         
                    return writer;
                },
				workbook ->{
		        	consumer(companyConfig, courseConfig, workbook, courseAttributeSet);
		        }			
        );
	}
    
    
	private void consumer(List<String> companyConfig, List<String> courseConfig, Workbook workbook, List<String> courseAttributeSet) {
		org.apache.poi.ss.usermodel.Sheet s = workbook.getSheetAt(0);           
		setSelectionContants(s, StringUtils.toStringArray(courseAttributeSet), 1, 5000, 3, 3);
		if (companyConfig.size() > 0) {
			setSelectionContants(s, StringUtils.toStringArray(new HashSet<String>(companyConfig)), 1, 5000, 4, 4);
		}
		if (courseConfig.size() > 0) {
			setSelectionContants(s, StringUtils.toStringArray(courseConfig), 1, 5000, 6, 6);
		}
		setSelectionContants(s, new String[]{"是", "否"}, 1, 5000, 7, 7);
	}

	private Double replaceTime(String args) {
		if (!StringUtils.isEmpty(args)) {
			String hh = StringUtils.replace(args, "小时", ".");
			String mm = StringUtils.replace(hh, "分", "");
			return Double.valueOf(mm);
		}
    	return null;
	}
    
	
	
    private static boolean isValidPhone(String value) {
        String regExp = "^(0|86|17951)?(13[0-9]|15[*********]|17[678]|18[0-9]|14[57]|19[13589])[0-9]{8}$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(value);
        return m.matches();
    }

    private static boolean isValidEmail(String value) {
        String regExp = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(value);
        return m.matches();
    }

    private static boolean isValidInteger(String value) {
		String regExp = "^[1-9]\\d*$";
		Pattern p = Pattern.compile(regExp);
		Matcher m = p.matcher(value);
		return m.matches();
	}
    
    private static boolean isTimeLong(String value) {
		String regExp = "^[0-9]*(\\.[0-9]{1,"+1+"})?$";
		Pattern p = Pattern.compile(regExp);
		Matcher m = p.matcher(value);
		return m.matches();
	}
    
    private static boolean isCost(String value) {
		String regExp = "^[0-9]*(\\.[0-9]{1,"+2+"})?$";
		Pattern p = Pattern.compile(regExp);
		Matcher m = p.matcher(value);
		return m.matches();
	}
    
    private static boolean isSatisfy(String value) {
		String regExp = "^[0-9]*(\\.[0-9]{1,"+1+"})?$";
		Pattern p = Pattern.compile(regExp);
		Matcher m = p.matcher(value);
		return m.matches();
	}
    
    private static boolean isTime(String value) {
		String regExp = "\\d+小时\\d+分";
		Pattern p = Pattern.compile(regExp);
		Matcher m = p.matcher(value);
		return m.matches();
	}

    private static boolean isNum(String value) {
		float a = Float.parseFloat(value);
		int b = (int)a;
		if (0 <= b && b <= 10) {
			return true;
		}
		return false;
	}
}
