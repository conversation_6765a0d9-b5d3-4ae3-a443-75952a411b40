package com.zxy.product.train.web.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.train.api.*;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.entity.*;
import com.zxy.product.train.web.util.ImportExportUtil;
import org.apache.commons.collections4.ListUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Date;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Created by chun on 2017/3/6.
 */
@Controller
@RequestMapping("/courseSalary")
public class CourseSalaryController {

    private CourseSalaryService courseSalaryService;
    private MemberService memberService;
    private ClassInfoService classInfoService;
    private ClassOfflineCourseService classOfflineCourseService;
    private GroupConfigurationValueService groupConfigurationValueService;
    private OrganizationService organizationService;
    private static Logger logger = LoggerFactory.getLogger(CourseSalaryController.class);


    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }
    @Autowired
    public void setGroupConfigurationValueService(GroupConfigurationValueService groupConfigurationValueService) {
        this.groupConfigurationValueService = groupConfigurationValueService;
    }

    @Autowired
    public void setClassOfflineCourseService(ClassOfflineCourseService classOfflineCourseService) {
        this.classOfflineCourseService = classOfflineCourseService;
    }
    @Autowired
    public void setClassInfoService(ClassInfoService classInfoService) {
        this.classInfoService = classInfoService;
    }


    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setCourseSalaryService(CourseSalaryService courseSalaryService) {
        this.courseSalaryService = courseSalaryService;
    }
    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @JSON("*.*")
    public List<ClassOfflineCourse> find(RequestContext requestContext) {
        Integer flag = 0;
        boolean courseSalaryFlag;
        List<ClassOfflineCourse> list = courseSalaryService.find(flag, requestContext.get("id", String.class));
        CourseSalary courseSalary = courseSalaryService.findSum(requestContext.get("id", String.class));
        ClassInfo classInfo = classInfoService.get(requestContext.get("id", String.class));
        courseSalaryFlag = classInfo.getCourseSalary()==null||classInfo.getCourseSalary().equals(0)?true:false;
        if(list.size()>0){
            list.get(0).getCourseSalary().setSumPaidPay(courseSalary.getSumPaidPay());
            list.get(0).getCourseSalary().setSumPay(courseSalary.getSumPay());
            list.get(0).getCourseSalary().setStatus(courseSalary.getStatus());
            list.get(0).getCourseSalary().setServiceCharge(courseSalary.getServiceCharge());
            list.get(0).getCourseSalary().setCourseSalary(courseSalaryFlag);
        }
        return list;
    }
    @RequestMapping(value = "sum", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*.*")
    public CourseSalary findSum(RequestContext requestContext) {
        CourseSalary list = courseSalaryService.findSum(requestContext.get("classId", String.class));
        return list;
    }
    @RequestMapping(value = "get", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @JSON("*.*")
    public ClassOfflineCourse get(RequestContext requestContext) {
        requestContext.get("id", String.class);
        return courseSalaryService.get(requestContext.get("id", String.class));
    }

    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "id", type = String.class)
    @Param(name = "lecturerId", type = String.class)
    @Param(name = "lecturerCard", type = String.class)
    @Param(name = "lecturerBankName", type = String.class)
    @Param(name = "lecturerBankCard", type = String.class)
    @Param(name = "pay", type = Double.class)
    @Param(name = "lecturerName", type = String.class, required = true)
    @Param(name = "tax", type = Double.class)
    @Param(name = "teacherOrganization", type = String.class)
    @Param(name = "paidPay", type = Double.class)
    @Param(name = "classOfflineCourseId", type = String.class)
    @Param(name = "phone", type = String.class)
    @Param(name = "flag", type = String.class)
    @Param(name = "teacherTitle", type = String.class)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*.*")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.INSERT, fisrtAction = "班级维护", secondAction ="编辑课酬", desc = "操作编辑{0}讲师课酬于班级{1}",params = {"lecturerName"}, ids = {"classId"}, jsons = {"name"}, keys = {"class-info"})
    public CourseSalary insert(RequestContext requestContext, Subject<Member> subject) {
        String lecturerName = requestContext.get("lecturerName", String.class);
        return courseSalaryService.insert(subject.getCurrentUserId(), requestContext.get("classOfflineCourseId", String.class),
                requestContext.getOptional("lecturerId", String.class),
                requestContext.getOptional("lecturerCard", String.class),
                requestContext.getOptional("lecturerBankName", String.class),
                requestContext.getOptional("lecturerBankCard", String.class),
                requestContext.getOptional("pay", Double.class),
                requestContext.getOptional("tax", Double.class),
                requestContext.getOptional("paidPay", Double.class),
                requestContext.get("classId", String.class),
                requestContext.get("phone", String.class), requestContext.getOptional("teacherOrganization", String.class), lecturerName,
                requestContext.getOptional("teacherTitle", String.class)
        );
    }

    /**
     * 修改课程
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "lecturerCard", type = String.class)
    @Param(name = "lecturerBankName", type = String.class)
    @Param(name = "lecturerBankCard", type = String.class)
    @Param(name = "teacherTitle", type = String.class)
    @Param(name = "pay", type = Double.class)
    @Param(name = "tax", type = Double.class)
    @Param(name = "lecturerName", type = String.class, required = true)
    @Param(name = "paidPay", type = Double.class)
    @Param(name = "phone", type = String.class)
    @Param(name = "lecturerName", type = String.class, required = true)
    @Param(name = "teacherOrganization", type = String.class)
    @Param(name = "classOfflineCourseId", type = String.class)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*.*")
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.UPDATE, fisrtAction = "班级维护", secondAction ="编辑课酬", desc = "操作编辑{0}讲师课酬于班级{1}",params = {"lecturerName"}, ids = {"classId"}, jsons = {"name"}, keys = {"class-info"})
    public CourseSalary update(RequestContext requestContext, Subject<Member> subject) {
        String lecturerName = requestContext.get("lecturerName", String.class);
        return courseSalaryService.update(requestContext.get("id", String.class), requestContext.get("classOfflineCourseId", String.class),
                requestContext.getOptional("lecturerCard", String.class),
                requestContext.getOptional("lecturerBankName", String.class),
                requestContext.getOptional("lecturerBankCard", String.class),
                requestContext.getOptional("pay", Double.class),
                requestContext.getOptional("tax", Double.class),
                requestContext.getOptional("paidPay", Double.class), requestContext.get("phone", String.class),
                lecturerName, requestContext.getOptional("teacherTitle", String.class), requestContext.getOptional("teacherOrganization", String.class));
    }

    /**
     * 下载讲师信息
     */
    @RequestMapping(value = "/download-excl-total", method = RequestMethod.GET)
    @Param(name="classId", type=String.class, required = true)
    @Param(name="startDate", type=String.class)
    @Param(name="endDate", type=String.class)
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.EXPORT, fisrtAction = "班级维护", secondAction ="班务管理—导出讲师表", desc = "操作导出讲师表于班级{0}", ids = {"id"}, jsons = {"name"}, keys = {"class-info"})
    public void downloadExclTotal(RequestContext context, Subject<Member> subject) throws IOException {

        String classId = context.get("classId", String.class);
        ClassInfo classInfo = classInfoService.get(classId);
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        String header = "attachment;filename=" + new String(("《" + classInfo.getClassName() + "》" + "日程安排-讲师信息表导出").getBytes("gb2312"), "ISO-8859-1") + ".xls";
        response.setHeader("Content-Disposition",header);
        String name = "";
        String organization = "";
        String code = "";
        if (classInfo != null) {
            code = classInfo.getCode();
            name = classInfo.getClassName();
            organization = classInfo.getOrganization();

        }
        // 第一行
//        List<CourseSalary> list = courseSalaryService.downloadAll(classId);


        List<ClassOfflineCourse> list1 = classOfflineCourseService.findByClassId(classId,
                this.findLong(context.getOptionalString("startDate")),
                this.findLong(context.getOptionalString("endDate")),
                Optional.empty());

        // 查询是否需要课程师资评价
        if (CollectionUtils.isNotEmpty(list1)) {
            List<String> courseIds = list1.stream().map(ClassOfflineCourse::getId).collect(Collectors.toList());
            Map<String, Boolean> map = classOfflineCourseService.haveEvaluate(classId, courseIds, subject.getCurrentUserId());
            list1.forEach(classOfflineCourse-> {
                classOfflineCourse.setHaveEvaluate(map.get(classOfflineCourse.getId()));
            });
        }

        HSSFWorkbook workbook = new HSSFWorkbook();
        OutputStream out = response.getOutputStream();

        this.downloadInsideAuditingAll(response, list1, name, organization, workbook,code,"");

        String date = com.zxy.product.system.util.DateUtil.dateLongToString(System.currentTimeMillis(), com.zxy.product.system.util.DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        ImportExportUtil.putWaterRemarkToExcel(workbook, workbook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
        workbook.write(out);
        out.flush();
        out.close();
    }

    public Optional<Long> findLong(Optional<String> time){
        Optional<Long> timeOp = time.isPresent()?Optional.of(Long.parseLong(time.get())):Optional.empty();
        return timeOp;
    }


//    /**
//     * 下载全部课酬
//     */
//    @RequestMapping(value = "/download-excl-total", method = RequestMethod.GET)
//    @Param(name = "id", type = String.class, required = true)
//    @Param(name = "flag", type = String.class, required = true)
//    @Param(name = "pay", type = Double.class)
//    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.EXPORT, fisrtAction = "班级维护", secondAction ="班务管理—导出讲师课酬信息表", desc = "操作导出讲师课酬信息表于班级{0}", ids = {"id"}, jsons = {"name"}, keys = {"class-info"})
//    public void downloadExclTotal(RequestContext context, Subject<Member> subject) throws IOException {
//
//        String classId = context.get("id", String.class);
//        String flag = context.get("flag", String.class);
//        ClassInfo classInfo = classInfoService.get(classId);
//        HttpServletResponse response = context.getResponse();
//        response.setContentType("application/octet-stream;charset=utf-8");
//        String header = "attachment;filename=" + new String(("《" + classInfo.getClassName() + "》" + "日程安排-课酬信息表导出").getBytes("gb2312"), "ISO-8859-1") + ".xls";
//        if(flag.equals("2")){
//            header = "attachment;filename=" + new String(("《" + classInfo.getClassName() + "》" + "讲师课酬信息表导出").getBytes("gb2312"), "ISO-8859-1") + ".xls";
//        }
//        response.setHeader("Content-Disposition",header);
//        String name = "";
//        String organization = "";
//        String code = "";
//        String organizationId = "";
//        String organizationPath = "";
//        if (classInfo != null) {
//            code = classInfo.getCode();
//            name = classInfo.getClassName();
//            organization = classInfo.getOrganization();
//            organizationId = classInfo.getOrganizationId();
//            organizationPath = organizationService.find(organizationId).getPath();
//        }
//        // 第一行
//        List<CourseSalary> list = courseSalaryService.downloadCourse(classId);
//        // 第一行
//        List<CourseSalary> list1 = courseSalaryService.download(1, classId);
//        //外部讲师
//        List<CourseSalary> list2 = courseSalaryService.download(2, classId);
//        HSSFWorkbook workbook = new HSSFWorkbook();
//        OutputStream out = response.getOutputStream();
//        List<GroupConfigurationValue> grouList = groupConfigurationValueService.findConfigurationAll();
//        if (grouList != null && grouList.size() > 0) {
//            for (int i = 0; i < grouList.size(); i++) {
//                if (organizationPath!=null&&organizationPath.contains(grouList.get(i).getPath())) {
//                    organization = grouList.get(i).getShortName();
//                    break;
//                }
//            }
//        }
//        if(flag.equals("2")){
//            this.downloadBasic(response, list, name, organization, workbook,code,"");
//        }
//        this.downloadInsideAuditing(response, list1, name, organization, workbook,code,"");
//
//        this.downloadExternalAuditing(response, list2,  name, organization, workbook,code,"");
//
//        this.signConfirmInside(response, list1, name, organization, workbook,"");
//        this.signConfirmExternal(response, list2, name, organization, workbook,"");
//        if(flag.equals("2")){
//            this.downloadPay(list2, response,name, workbook,"");
//            this.downloadExternalConfirm(response, list2, name, organization, workbook,"");
//        }
//        String date = com.zxy.product.system.util.DateUtil.dateLongToString(System.currentTimeMillis(), com.zxy.product.system.util.DateUtil.YYYYMMDD);
//        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(subject.getCurrentUserId());
//        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
//
//        for (int i = 0; i < 10; i++) {
//            try {
//                ImportExportUtil.putWaterRemarkToExcel(workbook, workbook.getSheetAt(i), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
//            } catch (Exception ignore) {
//            }
//        }
//
//        workbook.write(out);
//        out.flush();
//        out.close();
//    }

    @RequestMapping(value = "/week-download", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "flag", type = Integer.class, required = true)
    @Param(name = "startTime", type = Long.class)
    @Param(name = "endTime", type = Long.class)
    @Audit(module = "活动管理", subModule = "培训管理－班级管理", action = Audit.Action.EXPORT, fisrtAction = "班级维护", secondAction ="班务管理—导出讲师课酬信息表", desc = "操作导出讲师课酬信息表于班级{0}", ids = {"id"}, jsons = {"name"}, keys = {"class-info"})
    public void weekDownload(RequestContext context) throws IOException {
        String classId = context.get("classId", String.class);
        ClassInfo classInfo = classInfoService.get(classId);
        HttpServletResponse response = context.getResponse();
        String shortName = "（第"+context.get("flag", Integer.class)+"周）";
        response.setContentType("application/octet-stream;charset=utf-8");
        String header = "attachment;filename=" + new String(("《" + classInfo.getClassName() + "》" +shortName+ "讲师课酬信息表导出" ) .getBytes("gb2312"), "ISO-8859-1") + ".xls";
        response.setHeader("Content-Disposition",header);
        String name = "";
        String organization = "";
        String code = "";
        String organizationId = "";
        String organizationPath = "";
        if (classInfo != null) {
            code = classInfo.getCode();
            name = classInfo.getClassName();
            organizationId = classInfo.getOrganizationId();
            organization = classInfo.getOrganization();
            organizationPath = organizationService.find(organizationId).getPath();
        }
        // 第一行
        List<CourseSalary> list = courseSalaryService.downloadWeekCourse(classId,context.getOptional("startTime",Long.class),context.getOptional("endTime",Long.class));
        // 第一行
        List<CourseSalary> list1 = courseSalaryService.downloadWeek(classId,context.getOptional("startTime",Long.class),context.getOptional("endTime",Long.class),1);
        //外部讲师
        List<CourseSalary> list2 = courseSalaryService.downloadWeek(classId,context.getOptional("startTime",Long.class),context.getOptional("endTime",Long.class),2);
        HSSFWorkbook workbook = new HSSFWorkbook();
        OutputStream out = response.getOutputStream();
        List<GroupConfigurationValue> grouList = groupConfigurationValueService.findConfigurationAll();
        if (grouList != null && grouList.size() > 0) {
            for (int i = 0; i < grouList.size(); i++) {
                if (organizationPath!=null&&organizationPath.contains(grouList.get(i).getPath())) {
                    organization = grouList.get(i).getShortName();
                    break;
                }
            }
        }
        this.downloadBasic(response, list, name, organization, workbook,code,shortName);
        this.downloadInsideAuditing(response, list1, name, organization, workbook,code,shortName);
        this.downloadExternalAuditing(response, list2,  name, organization, workbook,code,shortName);
        this.signConfirmInside(response, list1, name, organization, workbook,shortName);
        this.signConfirmExternal(response, list2, name, organization, workbook,shortName);
        this.downloadPay(list2, response,name, workbook,shortName);
        this.downloadExternalConfirm(response, list2, name, organization, workbook,shortName);
        workbook.write(out);
        out.flush();
        out.close();
    }


    /**
     * （导出）代付酬金信息表（仅外部讲师）
     *
     * @param list
     * @param response
     * @param name
     * @param workbook
     * @throws IOException
     */
    public void downloadPay(List<CourseSalary> list, HttpServletResponse response, String name, HSSFWorkbook workbook,String shortName) throws IOException {
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet("外付课酬信息表");
        HSSFRow row0 = sheet.createRow(0);
        Cell cell00 = row0.createCell(0);
        row0.setHeightInPoints(55);
        sheet.setColumnWidth(0, 20 * 256);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
        HSSFCellStyle style = this.font(workbook, 15, 0);
        this.frame(12,row0,style);
        cell00.setCellValue("《" + name + "》"+shortName+"代付讲师课酬信息表");
        String[] num = {"序号", "课程名称", "课时(小时)", "讲课日期", "讲师姓名", "身份证号", "银行账号", "开户行", "讲师实际所得（元）", "管理费用（元）", "增值税（元）", "培训费合计（元）"};
        Object[][] value = new Object[list.size() + 2][12];
        DecimalFormat df = new DecimalFormat("######0.00");
        for (int m = 0; m < num.length; m++) {
            value[1][m] = num[m];
        }
        Double numFlag = list.size()>0&&list.get(0).getServiceCharge()!=null?list.get(0).getServiceCharge():0.00;
        Double sumActualNum = 0.0, sumAdministrationNum = 0.0, sumIncrementNum = 0.0;
        for (int i = 0; i < list.size(); i++) {
            Object num2 = 0.0, num3 = 0.0 , num1 = 0.0 ;
            double totalCost = 0.0;
            CourseSalary courseSalary = list.get(i);
            double time = this.dataTime(list.get(i).getClassOfflineCourse().getEndTime(), list.get(i).getClassOfflineCourse().getStartTime());
            String courseDate = null;
            if (courseSalary.getClassOfflineCourse() != null && courseSalary.getClassOfflineCourse().getCourseDate() != null) {
                courseDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date(courseSalary.getClassOfflineCourse().getCourseDate()));
            }
            value[i + 2][0] = i + 1;
            value[i + 2][1] = courseSalary.getClassOfflineCourse().getName();
            value[i + 2][2] = time;
            value[i + 2][3] = courseDate;
            value[i + 2][4] = courseSalary.getClassOfflineCourse().getTeacherName();
            value[i + 2][5] = courseSalary.getLecturerCard();
            value[i + 2][6] = courseSalary.getLecturerBankCard();
            value[i + 2][7] = courseSalary.getLecturerBankName();
            num1 = courseSalary.getPaidPay()!=null?df.format(courseSalary.getPaidPay()):0.00;
            boolean outsidePaymentFlag = list.get(i).getClassOfflineCourse().getPaymentMethod() != null && list.get(i).getClassOfflineCourse().getPaymentMethod() == 1;
            if(outsidePaymentFlag){
                String taxRate = "0.06";
                if(courseSalary.getClassOfflineCourse().getTaxrate()!=null&&courseSalary.getClassOfflineCourse().getTaxrate().equals(3)){
                    taxRate = "0.03";
                }
                if(courseSalary.getPaidPay()!=null){
                    if (numFlag == 0.095 || numFlag == 0.25 || numFlag == 0.28 || numFlag == 0.20 || numFlag == 0.22) {
                        Double flag = this.Transformation(courseSalary.getPaidPay(),String.valueOf(numFlag));
                        num2 = this.formateDouble(flag);
                        Double num555 = this.Transformation((courseSalary.getPaidPay() + flag), taxRate);
                        num3  = this.formateDouble(num555);
                        sumAdministrationNum += flag;
                        sumIncrementNum += this.Transformation((courseSalary.getPaidPay() + flag), taxRate);
                        totalCost = this.Transformation((courseSalary.getPaidPay() + flag), taxRate)+flag;
                    }else{
                        Double flag = this.Transformation(courseSalary.getPaidPay(),"0.28");
                        num2 = this.formateDouble(flag);
                        Double num555 = this.Transformation((courseSalary.getPaidPay() + flag), taxRate);
                        num3 = this.formateDouble(num555);
                        sumAdministrationNum += flag;
                        sumIncrementNum += this.Transformation((courseSalary.getPaidPay() + flag), taxRate);
                        totalCost = this.Transformation((courseSalary.getPaidPay() + flag), taxRate)+flag;
                    }
                }
                value[i + 2][8] = num1;
                double n = new BigDecimal(num1.toString()).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                sumActualNum += n;
                value[i + 2][9] = num2;
                value[i + 2][10] =num3;
                Double sum = Double.parseDouble(num1.toString())+totalCost;
                value[i + 2][11] =  new BigDecimal(sum.toString()).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
            }else{
                //支付方式除外付以外、
                value[i + 2][8] = 0.00;
                value[i + 2][9] = 0.00;
                value[i + 2][10] = 0.00;
                value[i + 2][11] = 0.00;
            }
        }

        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        this.downloadWriteArray1(workbook, sheet, list.size() + 2, 12, value);
        cell00.setCellStyle(style);
        HSSFRow rowList = sheet.createRow(list.size() + 2);
        HSSFCellStyle styleList = workbook.createCellStyle();
        Font fontList = workbook.createFont();
        fontList.setFontName("宋体");//设置字体名称
        fontList.setFontHeightInPoints((short) 13);//设置字号
        styleList.setBorderBottom(BorderStyle.THIN);//下边框
        styleList.setBorderLeft(BorderStyle.THIN);//左边框
        styleList.setBorderRight(BorderStyle.THIN);//右边框
        styleList.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
        styleList.setAlignment(HorizontalAlignment.CENTER); //水平居中
        styleList.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        styleList.setFont(fontList);//选择需要用到的字体格式

        if (list.size() > 0) {
            Cell cellList = rowList.createCell(0);
            sheet.addMergedRegion(new CellRangeAddress(list.size() + 2, list.size() + 2, 0, 7));
            cellList.setCellValue("合计：");
            rowList.setHeightInPoints(30);
            rowList.createCell(8);
            rowList.createCell(9);
            rowList.createCell(10);
            rowList.createCell(11);
            Cell cell8 = this.stringNumberTwo(rowList,8,sumActualNum);
            Cell cell9 = this.stringNumberTwo(rowList,9,sumAdministrationNum);
            Cell cell10 = this.stringNumberTwo(rowList,10,sumIncrementNum);
            double sunNum = sumActualNum + sumAdministrationNum + sumIncrementNum;
            Cell cellList1 = this.stringNumberTwo(rowList,11,sunNum);
            cell9.setCellStyle(styleList);
            cell10.setCellStyle(styleList);
            cell8.setCellStyle(styleList);
            cellList1.setCellStyle(styleList);
            cellList.setCellStyle(styleList);
            for (int i = 1; i <= 7; i++) {
                rowList.createCell(i).setCellStyle(styleList);
            }
        }
    }
    public Double formateDouble(Object num){
        if(num == null){
            return 0d;
        }
        BigDecimal  numOne = new  BigDecimal(num.toString());
        double   numTwo =  numOne.setScale(2,   BigDecimal.ROUND_HALF_UP).doubleValue();
        return numTwo;
    }
    /**
     * 设置货币金额转换
     */
    public double Transformation(Double num,String num1){
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(num));
        BigDecimal bigDecimalOne = new BigDecimal(num1);
        return bigDecimal.multiply(bigDecimalOne).doubleValue();
    }
    /**
     * (导入)基本信息表
     *
     * @param response
     * @param list
     * @param name
     * @param organization
     * @param workbook
     * @throws IOException
     */
    public void downloadBasic(HttpServletResponse response, List<CourseSalary> list, String name, String organization, HSSFWorkbook workbook,String code,String shortName) throws IOException {
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet("讲师基本信息表");
        HSSFRow row0 = sheet.createRow(0);
        Cell cell00 = row0.createCell(0);
        row0.setHeightInPoints(20);
        sheet.setColumnWidth(0, 20 * 256);
        //合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 16));
        cell00.setCellValue("基本信息表");
        HSSFCellStyle style = this.font(workbook, 18, 0);
        cell00.setCellStyle(style);
        HSSFRow row1 = sheet.createRow(1);
        Cell cell10 = row1.createCell(0);
        row1.setHeightInPoints(15);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 1));
        HSSFCellStyle style1 = this.font(workbook, 14, 0);
        cell10.setCellValue("培训班名称：");
        cell10.setCellStyle(style1);
        Cell cell14 = row1.createCell(2);
        Cell cell112 = row1.createCell(12);
        Cell cell17 = row1.createCell(7);
        Cell cell18 = row1.createCell(8);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 2, 4));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 8, 11));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 12, 16));
        cell14.setCellValue("《" + name + "》"+shortName);
        cell17.setCellValue("编号");
        cell17.setCellStyle(style1);
        cell18.setCellValue(code);
        cell18.setCellStyle(style1);
        cell14.setCellStyle(style1);
        cell112.setCellValue("主办部门：" + organization + "");
        cell112.setCellStyle(style1);
        String[] num = {"序号", "课程名称", "课程开始时间", "课程结束时间", "课时", "讲课日期", "讲师姓名", "联系电话", "讲师类型"
                , "讲师单位", "职务", "身份证号", "银行账号", "开户行（精确到支行）","实付（元）","税金（元）\n（自动计算，不需填写）","酬金（元）\n" +
                "（自动计算，不需填写）"};
        Object[][] value = new Object[list.size() + 3][17];
        DecimalFormat df = new DecimalFormat("######0.00");
        for (int m = 0; m < num.length; m++) {
            value[1][m] = num[m];
        }
        for (int i = 0; i < list.size(); i++) {
            CourseSalary courseSalary = list.get(i);
            double time = this.dataTime(list.get(i).getClassOfflineCourse().getEndTime(), list.get(i).getClassOfflineCourse().getStartTime());
            String courseDate = null;
            if (courseSalary.getClassOfflineCourse() != null && courseSalary.getClassOfflineCourse().getCourseDate() != null) {
                courseDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date(courseSalary.getClassOfflineCourse().getCourseDate()));
            }
            String teacherType = "";
            if (courseSalary != null && courseSalary.getClassOfflineCourse() != null && courseSalary.getClassOfflineCourse().getTeacherType() != null) {
                if (courseSalary.getClassOfflineCourse().getTeacherType() == 0) {
                    teacherType = "内部讲师";
                } else if (courseSalary.getClassOfflineCourse().getTeacherType() == 1) {
                    teacherType = "外部讲师";
                }
            }
            value[i + 3][0] = i + 1;
            value[i + 3][1] = courseSalary.getClassOfflineCourse().getName();
            value[i + 3][2] = list.get(i).getClassOfflineCourse().getStartTime();
            value[i + 3][3] = list.get(i).getClassOfflineCourse().getEndTime();
            value[i + 3][4] = time;
            value[i + 3][5] = courseDate;
            value[i + 3][6] = courseSalary.getClassOfflineCourse().getTeacherName();
            String remarks;
            String teacher;
            teacher = courseSalary.getClassOfflineCourse().getTeacherPhone()!=null?courseSalary.getClassOfflineCourse().getTeacherPhone():"";
            remarks = courseSalary.getClassOfflineCourse().getRemarks()!=null?teacher+"("+courseSalary.getClassOfflineCourse().getRemarks()+")":teacher;
            value[i + 3][7] = remarks;
            value[i + 3][8] = teacherType;
            value[i + 3][9] = courseSalary.getClassOfflineCourse().getTeacherOrganization();
            value[i + 3][10] = courseSalary.getClassOfflineCourse().getTeacherTitle();
            value[i + 3][11] = courseSalary.getLecturerCard();
            value[i + 3][12] = courseSalary.getLecturerBankCard();
            value[i + 3][13] = courseSalary.getLecturerBankName();
            value[i + 3][14] = courseSalary.getPaidPay()!=null?df.format(courseSalary.getPaidPay()):0.00;//excl表中的酬金其实是列表中的实付
            value[i + 3][15] = courseSalary.getTax()!=null?df.format(courseSalary.getTax()):0.00;
            value[i + 3][16] = courseSalary.getPay()!=null?df.format(courseSalary.getPay()):0.00;
        }
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        if (list.size() > 0) {
            String[] paymentList = {"外部讲师", "内部讲师"};
            CellRangeAddressList regions = new CellRangeAddressList(3, list.size() + 2, 8, 8);
            DVConstraint constraint = DVConstraint.createExplicitListConstraint(paymentList);
            HSSFDataValidation dataValidation = new HSSFDataValidation(regions, constraint);
            sheet.addValidationData(dataValidation);
        }
        this.downloadWriteArray(workbook, sheet, list.size() + 3, 17, value);

    }



    /**
     * (导出)外部讲师审核表
     *
     * @param response
     * @param list
     * @param name
     * @param organization
     * @param workbook
     * @throws IOException
     */

    public void downloadExternalAuditing(HttpServletResponse response, List<CourseSalary> list, String name, String organization, HSSFWorkbook workbook,String code,String ShortName) throws IOException {
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet("审核表(外部)");
        // 第一行
        HSSFRow row0 = sheet.createRow(0);
        Cell cell00 = row0.createCell(0);
        row0.setHeightInPoints(55);
        sheet.setColumnWidth(0, 20 * 256);
        // 设置背景色
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 13));
        HSSFCellStyle style = this.font(workbook, 17, 0);
        this.frame(14,row0,style);
        cell00.setCellValue(" 《" + name + "》"+ShortName+" ("+code+")外部讲师课酬审核表  主办部门：" + organization + "");
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        cell00.setCellStyle(style);
        String[] num = {"序号", "课程名称", "课时", "讲课日期", "讲师姓名", "联系电话","讲师来源", "讲师单位", "职务", "实付（元）",
                "税金（元）",
                "酬金（元）", "支付方式", "推荐方"};
        Object[][] value = new Object[list.size() + 2][14];
        Double sumActualNum = 0.0, sumAdministrationNum = 0.0,sumFlag=0.0 ,sumIncrementNum = 0.0,outsideSum = 0.0,contractSum = 0.0,frameSum =0.0;
        Double outsidesumTax = 0.0,contractSumTax = 0.0 ,frameSumTax = 0.0,sumActualNumTax = 0.0,sumAdministrationNumTax = 0.0,sumFlagTax = 0.0 ;
        for (int m = 0; m < num.length; m++) {
            value[1][m] = num[m];
        }
        List<CourseSalary> listTax =  list.stream().filter(r->r.getClassOfflineCourse().getTaxrate()!=null&&r.getClassOfflineCourse().getTaxrate().equals(3)).collect(Collectors.toList());
        List<CourseSalary> primaryList = ListUtils.subtract(list,listTax);
        Map<String, List<CourseSalary>> map = primaryList.stream().filter(x->x.getClassOfflineCourse().getPaymentMethod()!=null)
                .collect(Collectors.groupingBy(x -> {
            return x.getClassOfflineCourse().getPaymentMethod().toString();
        }));
        Map<String, List<CourseSalary>> mapTax = listTax.stream().filter(x->x.getClassOfflineCourse().getPaymentMethod()!=null)
                .collect(Collectors.groupingBy(x -> {
                    return x.getClassOfflineCourse().getPaymentMethod().toString();
                }));
        outsidesumTax = mapTax.get("1")!=null?mapTax.get("1").stream().collect(Collectors.summingDouble(CourseSalary::getPaidPay)):0.00;
        contractSumTax = mapTax.get("2")!=null?mapTax.get("2").stream().collect(Collectors.summingDouble(CourseSalary::getPaidPay)):0.0;
        frameSumTax = mapTax.get("3")!=null?mapTax.get("3").stream().map(CourseSalary::getPaidPay).reduce(0.00,Double::sum):0.0;
        outsideSum =  map.get("1")!=null?map.get("1").stream().collect(Collectors.summingDouble(CourseSalary::getPaidPay)):0.00;
        contractSum = map.get("2")!=null?map.get("2").stream().collect(Collectors.summingDouble(CourseSalary::getPaidPay)):0.0;
        frameSum = map.get("3")!=null?map.get("3").stream().map(CourseSalary::getPaidPay).reduce(0.00,Double::sum):0.0;

        for (int i = 0; i < list.size(); i++) {
            CourseSalary courseSalary = list.get(i);
            double time = this.dataTime(list.get(i).getClassOfflineCourse().getEndTime(), list.get(i).getClassOfflineCourse().getStartTime());
            String courseDate = null;
            if (courseSalary.getClassOfflineCourse() != null && courseSalary.getClassOfflineCourse().getCourseDate() != null) {
                courseDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date(courseSalary.getClassOfflineCourse().getCourseDate()));
            }
            value[i + 2][0] = i + 1;
            value[i + 2][1] = courseSalary.getClassOfflineCourse().getName();
            value[i + 2][2] = time;
            value[i + 2][3] = courseDate;
            value[i + 2][4] = courseSalary.getClassOfflineCourse().getTeacherName();
            String remarks;
            String teacher;
            teacher = courseSalary.getClassOfflineCourse().getTeacherPhone()!=null?courseSalary.getClassOfflineCourse().getTeacherPhone():"";
            remarks = courseSalary.getClassOfflineCourse().getRemarks()!=null?teacher+"("+courseSalary.getClassOfflineCourse().getRemarks()+")":teacher;
            value[i + 2][5] = remarks;
            String lecturerSource = null;
            if(courseSalary.getClassOfflineCourse().getLecturerSource()!=null){
                if (courseSalary.getClassOfflineCourse().getLecturerSource().equals(1)){
                    lecturerSource = "政府部门";
                }else {
                    lecturerSource = courseSalary.getClassOfflineCourse().getLecturerSource()!=2?"其他":"事业单位";
                }
            }else{
                lecturerSource = "—";
            }
            value[i + 2][6] = lecturerSource;
            value[i + 2][7] = courseSalary.getClassOfflineCourse().getTeacherOrganization();
            value[i + 2][8] = courseSalary.getClassOfflineCourse().getTeacherTitle();
            Double num1 = courseSalary.getPaidPay()!=null?courseSalary.getPaidPay():0.00;
            value[i + 2][9] = courseSalary.getPaidPay()!=null?this.formateDouble(num1):0.00;
            sumActualNum+= num1;
            Double num2 = courseSalary.getTax()!=null?courseSalary.getTax():0.00;
            value[i + 2][10] = courseSalary.getTax()!=null?this.formateDouble(num2):0.00;
            Double num3 = courseSalary.getPay()!=null?courseSalary.getPay():0.00;
            value[i + 2][11] = courseSalary.getPay()!=null?this.formateDouble(num3):0.00;
            String payment = null;
            if(courseSalary.getClassOfflineCourse().getPaymentMethod()!=null){
                if (courseSalary.getClassOfflineCourse().getPaymentMethod().equals(1)){
                    payment = "外付";
                }else {
                    payment = courseSalary.getClassOfflineCourse().getPaymentMethod()!=2?"框架合同":"合同";
                }
            }
            value[i + 2][12] = payment;
            String recommend = null;
            if(courseSalary.getClassOfflineCourse().getRecommend()!=null){
                if(courseSalary.getClassOfflineCourse().getRecommend().equals(1)){
                    recommend = "主办方";
                }else if(courseSalary.getClassOfflineCourse().getRecommend().equals(4)){
                    recommend  = "移动党校";
                }else{
                    recommend  = courseSalary.getClassOfflineCourse().getRecommend()!=2?"移动党校--党校教育部":"移动党校--教学部";
                }
            }
            value[i + 2][13] = recommend;
        }
        this.writeArrayToExcel(workbook, sheet, list.size() + 2, 14, value, 2);


        sheet.setForceFormulaRecalculation(true);//公式自动计算
        HSSFCellStyle styleList = workbook.createCellStyle();
        styleList.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
        HSSFFont fontList = workbook.createFont();
        fontList.setFontName("宋体");//设置字体名称
        fontList.setFontHeightInPoints((short) 13);//设置字号
        styleList.setBorderLeft(BorderStyle.THIN);//左边框
        styleList.setBorderBottom(BorderStyle.THIN);//下边框
        styleList.setBorderRight(BorderStyle.THIN);//右边框
        styleList.setAlignment(HorizontalAlignment.CENTER); //水平居中
        styleList.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        styleList.setFont(fontList);//选择需要用到的字体格式

        if (list.size() > 0) {
            HSSFRow rowL1 = sheet.createRow(list.size() + 2);
            rowL1.setHeightInPoints(55);
            sheet.addMergedRegion(new CellRangeAddress(list.size() + 2, list.size() + 2, 0, 8));
            Cell cellList9 = rowL1.createCell(0);
            Cell cell8 = this.stringNumberTwo(rowL1, 9, sumActualNum);
            Double numFlag = list.get(0).getServiceCharge()!=null?list.get(0).getServiceCharge():0.00;
            if(numFlag!=0.00){
                Double flag = this.Transformation(outsideSum,numFlag.toString());
                sumFlag = this.Transformation((outsideSum + flag), "0.06")+flag;
                if(outsidesumTax!=0.0){
                    Double flagTax = this.Transformation(outsidesumTax,numFlag.toString());
                    sumFlagTax = this.Transformation((outsidesumTax + flagTax), "0.03")+flagTax;
                }
                contractSum = this.Transformation((contractSum), "0.06");
                if(contractSumTax!=0.0){
                    contractSumTax = this.Transformation((contractSumTax), "0.03");
                }
                frameSum =this.Transformation((frameSum), "0.06");
                if(frameSumTax!=0.0){
                    frameSumTax =this.Transformation((frameSumTax), "0.03");
                }

                sumAdministrationNum = sumFlag+contractSum+frameSum+sumFlagTax+contractSumTax+frameSumTax;
                sumIncrementNum  = sumActualNum + sumAdministrationNum;
            }else{
                frameSum =this.Transformation((frameSum), "0.06");
                if(frameSumTax!=0.0){
                    frameSumTax =this.Transformation((frameSumTax), "0.03");
                }
                contractSum = this.Transformation((contractSum), "0.06");
                if(contractSumTax!=0.0){
                    contractSumTax = this.Transformation((contractSumTax), "0.03");
                }
                sumAdministrationNum = contractSum+frameSum+frameSumTax+contractSumTax;
                sumIncrementNum  = sumActualNum+sumAdministrationNum;
            }
            Cell cell9 = this.stringNumberTwo(rowL1, 10,sumAdministrationNum);
            Cell cell10 = this.stringNumberTwo(rowL1, 11,sumIncrementNum);
            String[] paymentList = {"外付", "合同", "框架合同"};
            String[] recommendList = {"主办方", "移动党校--教学部","移动党校--党校教育部","移动党校"};
            String[] lectureSourceList ={"政府部门","事业单位","其他"};
            CellRangeAddressList regions = new CellRangeAddressList(2, list.size() + 1, 12, 12);
            CellRangeAddressList recommendRegions = new CellRangeAddressList(2, list.size() + 1, 13, 13);
            CellRangeAddressList lectureSourceRegions = new CellRangeAddressList(2, list.size() + 1, 6, 6);
            DVConstraint constraint = DVConstraint.createExplicitListConstraint(paymentList);
            DVConstraint lectureConstraint = DVConstraint.createExplicitListConstraint(lectureSourceList);
            DVConstraint recommendConstraint = DVConstraint.createExplicitListConstraint(recommendList);
            HSSFDataValidation dataValidation = new HSSFDataValidation(regions, constraint);
            HSSFDataValidation lectureValidation = new HSSFDataValidation(lectureSourceRegions, lectureConstraint);
            HSSFDataValidation validation = new HSSFDataValidation(recommendRegions, recommendConstraint);
            sheet.addValidationData(dataValidation);
            sheet.addValidationData(validation);
            sheet.addValidationData(lectureValidation);
            cell9.setCellStyle(styleList);
            cell10.setCellStyle(styleList);
            cell8.setCellStyle(styleList);
            for (int i = 0; i <= 8; i++) {
                rowL1.createCell(i).setCellStyle(styleList);
            }
            Cell cell12 = rowL1.createCell(12);
            cell12.setCellStyle(styleList);
            Cell cell13 = rowL1.createCell(13);
            cell13.setCellStyle(styleList);

            cellList9.setCellStyle(styleList);
            cellList9.setCellValue("合计:");
        }
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 3, list.size() + 3, 0, 13));
        sheetNum(sheet, list.size() + 4);
        sheetNum(sheet, list.size() + 5);
        HSSFRow rowRow = sheet.createRow(list.size() + 4);
        HSSFRow rowRow1 = sheet.createRow(list.size() + 5);
        Cell cellRow = rowRow.createCell(0);
        Cell cellRow1 = rowRow.createCell(4);
        Cell cellRow2 = rowRow.createCell(7);
        Cell cellRow3 = rowRow.createCell(10);
        int ce = 0;
        HSSFCellStyle styleRow = workbook.createCellStyle();
        HSSFFont fontRow = workbook.createFont();
        fontRow.setFontName("宋体");//设置字体名称
        styleRow.setAlignment(HorizontalAlignment.RIGHT); //右对齐
        fontRow.setFontHeightInPoints((short) 13);//设置字号
        styleRow.setFont(fontRow);
        for (int r = 0; r < 4; r++) {
            Cell cellRow4 = rowRow1.createCell(ce);
            cellRow4.setCellValue("日期：");
            if(r==0){
                ce = ce + 4;
            }else{
                ce = ce + 3;
            }
            cellRow4.setCellStyle(styleRow);
        }
        rowRow.setHeightInPoints(20);
        rowRow1.setHeightInPoints(20);
        cellRow.setCellStyle(styleRow);
        cellRow1.setCellStyle(styleRow);
        cellRow2.setCellStyle(styleRow);
        cellRow3.setCellStyle(styleRow);
        cellRow.setCellValue("主办部门项目负责人：");
        cellRow1.setCellValue("教学支撑部班主任：");
        cellRow2.setCellValue("复核人：");
        cellRow3.setCellValue("部门经理：");
    }
    public HSSFCellStyle font(HSSFWorkbook workbook, int num, int flag) {
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");//设置字体名称
        if (flag == 0) {
            font.setBold(true);
        }
        font.setFontHeightInPoints((short) num);//设置字号
        style.setFont(font);
        return style;
    }

    public Cell stringNumberTwo(HSSFRow rowList, Integer cellNum,double num){
        Cell cellList = rowList.createCell(cellNum);
        String numS = String.valueOf(num);
        cellList.setCellValue(new BigDecimal(numS).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        return cellList;
    }

    /**
     * (导出)支付确认单（仅外部讲师）
     *
     * @param response
     * @param list
     * @param name
     * @param organization
     * @param workbook
     * @throws IOException
     */
    public void downloadExternalConfirm(HttpServletResponse response, List<CourseSalary> list,String name, String organization, HSSFWorkbook workbook,String ShortName) throws IOException {
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet("支付确认单");
        // 第一行
        HSSFRow row0 = sheet.createRow(0);
        Cell cell00 = row0.createCell(0);
        row0.setHeightInPoints(55);
        sheet.setColumnWidth(0, 20 * 256);
        // 设置背景色
        HSSFCellStyle style = workbook.createCellStyle();
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));
        this.frame(9,row0,style);
        Font font = workbook.createFont();
        cell00.setCellValue("《 " + name + "》"+ShortName+"课酬支付确认单 \n          主办部门：" + organization + "");
        font.setFontName("宋体");//设置字体名称
        font.setFontHeightInPoints((short) 14);//设置字号

        font.setBold(true);
        //设置背景色
        style.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        style.setWrapText(true);//设置自动换行
        style.setFont(font);//选择需要用到的字体格式
        cell00.setCellStyle(style);
        String[] num = {"课程名称", "课时", "讲课日期", "讲师姓名", "讲师实际所得（元）", "管理费用（元）", "增值税（元）", "培训费合计（元）"
                , "备注"};
        Object[][] value = new Object[list.size() + 2][9];
        DecimalFormat df = new DecimalFormat("######0.00");
        for (int m = 0; m < num.length; m++) {
            value[1][m] = num[m];
        }
        Double numFlag = list.size()>0&&list.get(0).getServiceCharge()!=null?list.get(0).getServiceCharge():0.00;
        double sumActualNum =0.0;
        for (int i = 0; i < list.size(); i++) {
            Object num2 = 0.0,  num3 = 0.0,num1 = 0.0;
            double totalCost = 0.0;
            CourseSalary courseSalary = list.get(i);
            double time = this.dataTime(list.get(i).getClassOfflineCourse().getEndTime(), list.get(i).getClassOfflineCourse().getStartTime());
            String courseDate = null;
            if (courseSalary.getClassOfflineCourse() != null && courseSalary.getClassOfflineCourse().getCourseDate() != null) {
                courseDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date(courseSalary.getClassOfflineCourse().getCourseDate()));
            }
            value[i + 2][0] = courseSalary.getClassOfflineCourse().getName();
            value[i + 2][1] = time;
            value[i + 2][2] = courseDate;
            value[i + 2][3] = courseSalary.getClassOfflineCourse().getTeacherName();
            boolean methodFlag = list.get(i).getClassOfflineCourse().getPaymentMethod() != null && list.get(i).getClassOfflineCourse().getPaymentMethod() == 1;
            if(methodFlag){
                String taxRate = "0.06";
                if(list.get(i).getClassOfflineCourse().getTaxrate()!=null&&list.get(i).getClassOfflineCourse().getTaxrate().equals(3)){
                    taxRate = "0.03";
                }
                if(courseSalary.getPaidPay()!=null){
                    if (numFlag == 0.095 || numFlag == 0.25 || numFlag == 0.28 || numFlag == 0.20 || numFlag == 0.22) {
                        Double flag = this.Transformation(courseSalary.getPaidPay(),String.valueOf(numFlag));
                        num2 = this.formateDouble(flag);
                        Double num555 = this.Transformation((courseSalary.getPaidPay() + flag), taxRate);
                        num3 = this.formateDouble(num555);
                        totalCost = this.Transformation((courseSalary.getPaidPay() + flag), taxRate)+flag;
                    }else{
                        Double flag = this.Transformation(courseSalary.getPaidPay(),"0.28");
                        num2 = this.formateDouble(flag);
                        Double num555 = this.Transformation((courseSalary.getPaidPay() + flag), taxRate);
                        num3 = this.formateDouble(num555);
                        totalCost = this.Transformation((courseSalary.getPaidPay() + flag), taxRate)+flag;
                    }
                }
                num1 = courseSalary.getPaidPay()!=null?df.format(courseSalary.getPaidPay()):0.00;
                value[i + 2][4] =num1;
                value[i + 2][5] = num2;
                value[i + 2][6] =num3;
                double n = Double.parseDouble(num1.toString())+totalCost;
                sumActualNum+= n;
                value[i + 2][7] = new BigDecimal(String.valueOf(n)).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
            }else{
                value[i + 2][4] =0.00;
                value[i + 2][5] = 0.00;
                value[i + 2][6] = 0.00;
                value[i + 2][7] = 0.00;
                sumActualNum+= 0.00;
            }
            value[i + 2][8] = "";
        }
        this.writeArrayToExcel4(workbook, sheet, list.size() + 2, 9, value, 1);
        HSSFRow rowList = sheet.createRow(list.size() + 2);
        Cell cellList = rowList.createCell(0);
        cellList.setCellValue("培训费用合计（单位：元）");
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 2, list.size() + 2, 0, 8));
        HSSFCellStyle styleList = this.bold(workbook, 1);
        HSSFCellStyle styleListElement = this.bold(workbook, 0);
        HSSFCellStyle styleListVertical = this.bold(workbook, 0);
        HSSFCellStyle styleListSing = this.bold(workbook, 3);
        for (int i = 1; i <= 8; i++) {
            rowList.createCell(i).setCellStyle(styleList);
        }
        cellList.setCellStyle(styleList);
        HSSFRow rowListNum = sheet.createRow(list.size() + 3);
        Integer n = list.size() + 2;
        Cell cell12 = rowListNum.createCell(0);
        HSSFCellStyle styleList8 = this.bold(workbook, 4);
        for (int i = 0; i <= 8; i++) {
            rowListNum.createCell(i).setCellStyle(styleList8);
        }
        if (list.size() > 0) {
            cell12.setCellValue(sumActualNum);
        }
        cell12.setCellStyle(styleList8);
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 3, list.size() + 3, 0, 7));
        Cell cellListNum = rowListNum.createCell(8);
        cellListNum.setCellValue("元");
        cellListNum.setCellStyle(styleListElement);
        HSSFRow rowSign = sheet.createRow(list.size() + 4);
        for (int i = 0; i <= 8; i++) {
            rowSign.createCell(i).setCellStyle(styleList8);
        }
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 4, list.size() + 4, 0, 8));
        Cell cellSign = rowSign.createCell(0);
        cellSign.setCellValue("确认签字");
        cellSign.setCellStyle(styleList);
        HSSFRow rowSignDate = sheet.createRow(list.size() + 5);
        for (int i = 0; i <= 8; i++) {
            rowSignDate.createCell(i).setCellStyle(styleList8);
        }
        HSSFRow rowSignDate1 = sheet.createRow(list.size() + 6);
        for (int i = 0; i <= 8; i++) {
            rowSignDate1.createCell(i).setCellStyle(styleList8);
        }
        HSSFRow rowSignDate2 = sheet.createRow(list.size() + 9);

        sheet.addMergedRegion(new CellRangeAddress(list.size() + 5, list.size() + 6, 0, 2));
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 5, list.size() + 6, 3, 8));
        Cell cellSignDate = rowSignDate.createCell(0);
        Cell cellSignDate1 = rowSignDate.createCell(3);
        rowSignDate.setHeightInPoints(50);
        styleListVertical.setVerticalAlignment(VerticalAlignment.CENTER);
        styleListVertical.setWrapText(true);
        styleListVertical.setAlignment(HorizontalAlignment.CENTER);//水平居中
        styleListElement.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中'
        styleListElement.setWrapText(true);
        cellSignDate.setCellValue("甲方(中国移动通信有限公司北京信息技术培训院)");
        cellSignDate.setCellStyle(styleListElement);
        cellSignDate1.setCellValue("教学支撑部班主任（签字）       复核（签字）                日期                  日期");
        cellSignDate1.setCellStyle(styleListVertical);
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 7, list.size() + 8, 0, 2));
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 7, list.size() + 8, 3, 8));
        HSSFRow rowB = sheet.createRow(list.size() + 7);
        for (int i = 0; i <= 8; i++) {
            rowB.createCell(i).setCellStyle(styleList8);
        }
        HSSFRow rowB1 = sheet.createRow(list.size() + 8);
        for (int i = 0; i <= 8; i++) {
            rowB1.createCell(i).setCellStyle(styleList8);
        }
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 9, list.size() + 11, 0, 8));
        HSSFCellStyle styleRemarks = workbook.createCellStyle();
        Font font1 = workbook.createFont();
        font1.setFontName("宋体");//设置字体名称
        font1.setFontHeightInPoints((short) 12);//设置字号
        styleRemarks.setFont(font1);//选择需要用到的字体格式
        styleRemarks.setWrapText(true);
        for(int i = 0; i <= 16; i++){
            rowSignDate2.createCell(i).setCellStyle(styleRemarks);
        }
        Cell cellRemarks = rowSignDate2.createCell(0);
        Cell cellB = rowB.createCell(0);
        Cell cellBSign = rowB.createCell(3);
        cellB.setCellValue("乙方：_____________________");
        cellB.setCellStyle(styleListElement);
        cellBSign.setCellValue("（负责人签字，并加盖公章）");
        cellBSign.setCellStyle(styleListSing);
        styleListElement.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中'
        cellRemarks.setCellStyle(styleRemarks);
        cellRemarks.setCellValue("备注：“管理费用”是按照供应商入围时报价中所承诺的管理费比率与讲师实际\n所得相乘计算，保留两位小数。");

    }

    public HSSFCellStyle bold(HSSFWorkbook workbook, int flag) {
        HSSFCellStyle styleList = workbook.createCellStyle();
        Font fontList = workbook.createFont();
        fontList.setFontName("宋体");//设置字体名称
        fontList.setBold(true);
        fontList.setFontHeightInPoints((short) 11);//设置字号
        styleList.setBorderBottom(BorderStyle.THIN);//下边框
        styleList.setBorderLeft(BorderStyle.THIN);
        styleList.setBorderRight(BorderStyle.THIN);//右边框
        styleList.setWrapText(true); // 强制换行
        if (flag == 2) {
            styleList.setAlignment(HorizontalAlignment.RIGHT); //右对齐
        } else if (flag == 1) {
            styleList.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
            styleList.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
            styleList.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        } else if (flag == 3) {
            styleList.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
            fontList.setColor(HSSFColor.GREY_25_PERCENT.index);
        } else if(flag == 4){
            styleList.setAlignment(HorizontalAlignment.RIGHT); //右对齐
            styleList.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
        }
        styleList.setFont(fontList);//选择需要用到的字体格式
        return styleList;
    }

    /**
     * 讲师酬金签收确认单(内部)
     *
     * @param response
     * @param list
     * @param name
     * @param organization
     * @param workbook
     * @throws IOException
     */
    public void signConfirmInside(HttpServletResponse response, List<CourseSalary> list, String name, String organization, HSSFWorkbook workbook,String shortName) throws IOException {
        Font font  = workbook.createFont();
        Font font1  = workbook.createFont();
        Font font2  = workbook.createFont();
        Font font3 = workbook.createFont();
        HSSFCellStyle style = this.signConfirmStyle(workbook, 1, 15,font);
        HSSFCellStyle style1 = this.signConfirmStyle(workbook, 0, 13,font1);
        HSSFCellStyle style2 = this.signConfirmStyle(workbook, 2, 13,font2);
        HSSFCellStyle style3 = this.signConfirmStyle(workbook, 3, 13,font3);
        if (list.size() == 0) {
            HSSFSheet sheet = workbook.createSheet("签收确认单(内部讲师)");
            HSSFRow row0 = sheet.createRow(0);
            HSSFRow row14 = sheet.createRow(14);
            HSSFRow row15 = sheet.createRow(15);
            HSSFRow row16 = sheet.createRow(16);
            HSSFRow row17 = sheet.createRow(17);
            row0.setHeightInPoints(30);
            row14.setHeightInPoints(30);
            row15.setHeightInPoints(30);
            row16.setHeightInPoints(30);
            row17.setHeightInPoints(30);
            Cell cell00 = row0.createCell(0);
            sheet.setColumnWidth(0, 20 * 256);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
            cell00.setCellValue("讲师课酬签收确认单");
            cell00.setCellStyle(style);
            HSSFRow row1 = sheet.createRow(1);
            row1.setHeightInPoints(38);
            sheet.setColumnWidth(0, 15 * 256);
            sheet.setColumnWidth(1, 75 * 256);
            String[] num = {"", "班    名：", "讲师姓名：", "课程名称：","讲课学时：", "身份证号：", "银行账号：", "开 户 行：", "单    位：", "职    务：", "课    酬："
                    , "税    金：", "共    计：", "讲师签字：", "经办人签字："};
            Row row[] = new HSSFRow[14];
            Cell cell[] = new HSSFCell[1];
            for (int i = 1; i < 14; i++) {
                row[i] = sheet.createRow(i);
                for (int j = 0; j < 1; j++) {
                    cell[j] = row[i].createCell(0);
                    cell[j].setCellValue(convertString(num[i]));
                    row[i].setHeightInPoints(40);
                    cell[j].setCellStyle(style2);
                }
                if (i == 1) {
                    Cell cell11 = row[1].createCell(1);
                    cell11.setCellValue("《" + name + " 》"+shortName);
                    cell11.setCellStyle(style1);
                }
            }
            Cell cel261 = row16.createCell(1);
            cel261.setCellStyle(style3);
            cel261.setCellValue("年      月      日");
        }
        Map<String, List<CourseSalary>> map = list.stream().collect(Collectors.groupingBy(x -> {
            return x.getClassOfflineCourse().getTeacherName();
        }));
        map.forEach((k, v) -> {
            IntStream.range(0, v.size()).forEach(index -> {
                CourseSalary cs = v.get(index);
                String teacherName = null;
                teacherName = cs.getClassOfflineCourse().getTeacherName();
                if (index > 0) {
                    teacherName = teacherName + index;
                }
                // 生成一个表格
                HSSFSheet sheet = workbook.createSheet("签收确认单(内部)-" + teacherName + "");
                // 第一行
                HSSFRow row0 = sheet.createRow(0);
                HSSFRow row14 = sheet.createRow(14);
                HSSFRow row15 = sheet.createRow(15);
                HSSFRow row16 = sheet.createRow(16);
                HSSFRow row17 = sheet.createRow(17);
                row0.setHeightInPoints(30);
                row14.setHeightInPoints(30);
                row15.setHeightInPoints(30);
                row16.setHeightInPoints(30);
                row17.setHeightInPoints(30);
                Cell cell00 = row0.createCell(0);
                sheet.setColumnWidth(0, 20 * 256);
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
                cell00.setCellValue("讲师课酬签收确认单");
                cell00.setCellStyle(style);
                HSSFRow row1 = sheet.createRow(1);
                row1.setHeightInPoints(38);
                sheet.setColumnWidth(0, 15 * 256);
                sheet.setColumnWidth(1, 75 * 256);
                String[] num = {"", "班    名：", "讲师姓名：","课程名称", "讲课学时：", "身份证号：", "银行账号：", "开 户 行：", "单    位：", "职    务：", "课    酬："
                        , "税    金：", "共   计：", "讲师签字：", "经办人签字："};
                Row row[] = new HSSFRow[15];
                Cell cell[] = new HSSFCell[1];
                for (int i = 1; i < 15; i++) {
                    row[i] = sheet.createRow(i);
                    for (int j = 0; j < 1; j++) {
                        cell[j] = row[i].createCell(0);
                        cell[j].setCellValue(convertString(num[i]));
                        cell[j].setCellStyle(style2);
                        row[i].setHeightInPoints(35);
                    }
                    if (i == 1) {
                        Cell cell11 = row[1].createCell(1);
                        cell11.setCellValue("《" + name + "》"+shortName);
                        cell11.setCellStyle(style1);
                    }
                }
                Cell cell21 = row[2].createCell(1);
                cell21.setCellValue(cs.getClassOfflineCourse().getTeacherName());
                cell21.setCellStyle(style1);
                Cell cell31 = row[3].createCell(1);
                double time = this.dataTime(cs.getClassOfflineCourse().getEndTime(), cs.getClassOfflineCourse().getStartTime());
                cell31.setCellValue(cs.getClassOfflineCourse().getName());
                cell31.setCellStyle(style1);
                Cell cell41 = row[4].createCell(1);
                Cell cell51 = row[5].createCell(1);
                Cell cell61 = row[6].createCell(1);
                Cell cell71 = row[7].createCell(1);
                Cell cell81 = row[8].createCell(1);
                Cell cell91 = row[9].createCell(1);
                Cell cel231 = row[10].createCell(1);
                Cell cel211 = row[11].createCell(1);
                Cell cel221 = row[12].createCell(1);
                Cell cel241 = row[13].createCell(1);
                Cell cel242 = row[14].createCell(1);
                Cell cel261 = row17.createCell(1);
                cell51.setCellValue(cs.getLecturerCard());
                cell61.setCellValue(cs.getLecturerBankCard());
                cell61.setCellStyle(style1);
                cell51.setCellStyle(style1);
                cel261.setCellStyle(style3);
                cel261.setCellValue("年      月      日");
                cell71.setCellValue(cs.getLecturerBankName());
                cell71.setCellStyle(style1);
                cell41.setCellValue(time);
                cell41.setCellStyle(style1);
                cell81.setCellValue(cs.getClassOfflineCourse().getTeacherOrganization());
                cell81.setCellStyle(style1);
                cell91.setCellValue(cs.getClassOfflineCourse().getTeacherTitle());
                cell91.setCellStyle(style1);
                cel231.setCellValue(cs.getPaidPay());
                Double tax = cs.getTax()!=null?cs.getTax():0.00;
                cel211.setCellValue(tax);
                Double pay = cs.getPay()!=null?cs.getPay():0.00;
                cel221.setCellValue(pay);
                cel211.setCellStyle(style1);
                cel221.setCellStyle(style1);
                cel231.setCellStyle(style1);
                cel241.setCellStyle(style1);
                cel242.setCellStyle(style1);
            });
        });
    }
    /**
     * 导出)签收确认单(外部)
     *
     * @param response
     * @param list
     * @param name
     * @param organization
     * @param workbook
     */

    public void signConfirmExternal(HttpServletResponse response, List<CourseSalary> list, String name, String organization, HSSFWorkbook workbook,String shortName) {
        Font font  = workbook.createFont();
        Font font1  = workbook.createFont();
        Font font2  = workbook.createFont();
        Font font3  = workbook.createFont();
        HSSFCellStyle style = this.signConfirmStyle(workbook, 1, 15,font);
        HSSFCellStyle style1 = this.signConfirmStyle(workbook, 0, 13,font1);
        HSSFCellStyle style2 = this.signConfirmStyle(workbook, 2, 13,font2);
        HSSFCellStyle style3 = this.signConfirmStyle(workbook, 3, 13,font3);
        if (list.size() == 0) {
            HSSFSheet sheet = workbook.createSheet("签收确认单(外部讲师)");
            HSSFRow row0 = sheet.createRow(0);
            HSSFRow row12 = sheet.createRow(12);
            HSSFRow row15 = sheet.createRow(15);
            row0.setHeightInPoints(30);
            row12.setHeightInPoints(20);
            row15.setHeightInPoints(25);
            Cell cell00 = row0.createCell(0);
            sheet.setColumnWidth(0, 20 * 256);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
            cell00.setCellValue("讲师课酬签收确认单");
            cell00.setCellStyle(style);
            HSSFRow row1 = sheet.createRow(1);
            row1.setHeightInPoints(38);
            sheet.setColumnWidth(0, 15 * 256);
            sheet.setColumnWidth(1, 75 * 256);
            String[] num = {"", "班    名：", "讲师姓名：", "课程名称：", "讲课学时：", "身份证号：", "银行账号：", "开 户 行：", "单    位：", "职    务：", "课    酬："
                    , "讲师签字：", "经办人签字："};
            Row row[] = new HSSFRow[12];
            Cell cell[] = new HSSFCell[1];
            for (int i = 1; i < 12; i++) {
                row[i] = sheet.createRow(i);
                for (int j = 0; j < 1; j++) {
                    cell[j] = row[i].createCell(0);
                    row[i].setHeightInPoints(35);
                    cell[j].setCellStyle(style2);
                    cell[j].setCellValue(convertString(num[i]));
                }
                if (i == 1) {
                    Cell cell11 = row[1].createCell(1);
                    cell11.setCellValue("《" + name + "》"+shortName);
                    cell11.setCellStyle(style1);
                }
            }
            Cell cel261 = row15.createCell(1);
            cel261.setCellStyle(style3);
            cel261.setCellValue("年      月      日");
        }
        Map<String, List<CourseSalary>> map = list.stream().collect(Collectors.groupingBy(x -> {
            return x.getClassOfflineCourse().getTeacherName();
        }));
        map.forEach((k, v) -> {
            IntStream.range(0, v.size()).forEach(index -> {
                CourseSalary cs = v.get(index);
                String teacherName = null;
                teacherName = cs.getClassOfflineCourse().getTeacherName();
                if (index > 0) {
                    teacherName = teacherName + index;
                }
                HSSFSheet sheet = workbook.createSheet("签收确认单(外部)-" + teacherName);
                HSSFRow row0 = sheet.createRow(0);
                HSSFRow row12 = sheet.createRow(12);
                HSSFRow row16 = sheet.createRow(16);
                row0.setHeightInPoints(30);
                row12.setHeightInPoints(20);
                row16.setHeightInPoints(25);
                Cell cell00 = row0.createCell(0);
                sheet.setColumnWidth(0, 20 * 256);
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
//                HSSFCellStyle style = this.signConfirmStyle(workbook, 1, 15,font);
//                HSSFCellStyle style1 = this.signConfirmStyle(workbook, 0, 13,font1);
//                HSSFCellStyle style2 = this.signConfirmStyle(workbook, 2, 13,font2);
//                HSSFCellStyle style3 = this.signConfirmStyle(workbook, 3, 13,font3);
                cell00.setCellStyle(style);
                cell00.setCellValue("讲师课酬签收确认单");
                HSSFRow row1 = sheet.createRow(1);
                row1.setHeightInPoints(38);
                sheet.setColumnWidth(0, 15 * 256);
                sheet.setColumnWidth(1, 75 * 256);
                String[] num = {"", "班    名：", "讲师姓名：", "课程名称：", "讲课学时：", "身份证号：", "银行账号：", "开 户 行：", "单    位：", "职    务：", "课    酬："
                        , "讲师签字：", "经办人签字："};
                Row row[] = new HSSFRow[13];
                Cell cell[] = new HSSFCell[1];
                for (int i = 1; i < 13; i++) {
                    row[i] = sheet.createRow(i);
                    for (int j = 0; j < 1; j++) {
                        cell[j] = row[i].createCell(0);
                        row[i].setHeightInPoints(35);
                        cell[j].setCellValue(convertString(num[i]));
                        cell[j].setCellStyle(style2);
                    }
                    if (i == 1) {
                        Cell cell11 = row[1].createCell(1);
                        cell11.setCellValue("《" + name + "》"+shortName);
                        cell11.setCellStyle(style1);
                    }
                }
                Cell cell21 = row[2].createCell(1);
                cell21.setCellValue(cs.getClassOfflineCourse().getTeacherName());
                cell21.setCellStyle(style1);
                Cell cell31 = row[3].createCell(1);
                double time = this.dataTime(cs.getClassOfflineCourse().getEndTime(), cs.getClassOfflineCourse().getStartTime());
                cell31.setCellValue(cs.getClassOfflineCourse().getName());
                cell31.setCellStyle(style1);
                Cell cell41 = row[4].createCell(1);
                Cell cell51 = row[5].createCell(1);
                Cell cell61 = row[6].createCell(1);
                Cell cell71 = row[7].createCell(1);
                Cell cell81 = row[8].createCell(1);
                Cell cell91 = row[9].createCell(1);
                Cell cel231 = row[10].createCell(1);
                Cell cel211 = row[11].createCell(1);
                Cell cel212 = row[12].createCell(1);
                Cell cel261 = row16.createCell(1);
                cell51.setCellValue(cs.getLecturerCard());
                cell61.setCellValue(cs.getLecturerBankCard());
                cell61.setCellStyle(style1);
                cell51.setCellStyle(style1);
                cel261.setCellStyle(style3);
                cel261.setCellValue("年      月      日");
                cell71.setCellValue(cs.getLecturerBankName());
                cell71.setCellStyle(style1);
                cell41.setCellValue(time);
                cell41.setCellStyle(style1);
                cel231.setCellValue(cs.getPaidPay());
                cell91.setCellValue(cs.getClassOfflineCourse().getTeacherTitle());
                cell91.setCellStyle(style1);
                cell81.setCellValue(cs.getClassOfflineCourse().getTeacherOrganization());
                cell81.setCellStyle(style1);
                cel211.setCellStyle(style1);
                cel231.setCellStyle(style1);
                cel212.setCellStyle(style1);
            });
        });
    }

    public HSSFCellStyle signConfirmStyle(HSSFWorkbook workbook, int flag, int num,Font font) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);//选择需要用到的字体格式
        font.setFontName("宋体");//设置字体名称
        font.setFontHeightInPoints((short) num);//设置字号
        if (flag == 0) {
            style.setAlignment(HorizontalAlignment.CENTER); //水平居中
            style.setBorderBottom(BorderStyle.THIN);//下边框
        } else if (flag == 1) {
            style.setAlignment(HorizontalAlignment.CENTER); //水平居中
            style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
            font.setBold(true);
        } else if (flag == 2) {
            style.setVerticalAlignment(VerticalAlignment.BOTTOM);//底端对齐
//            style.setBorderBottom(BorderStyle.THIN);//下边框
        } else if (flag == 3) {
            style.setVerticalAlignment(VerticalAlignment.BOTTOM);//底端对齐
            style.setAlignment(HorizontalAlignment.RIGHT); // 居中
        }
        return style;
    }


    /**
     * 讲师表
     * @param response
     * @param list
     * @param name
     * @param organization
     * @param workbook
     * @param code
     * @param shortName
     * @throws IOException
     */
    public void downloadInsideAuditingAll(HttpServletResponse response, List<ClassOfflineCourse> list, String name, String organization, HSSFWorkbook workbook,String code,String shortName) throws IOException {
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet("讲师表");
        // 第一行
        HSSFRow row0 = sheet.createRow(0);
        Cell cell00 = row0.createCell(0);
        Cell cell01 = row0.createCell(2);
        Cell cell02 = row0.createCell(7);
        Cell cell03 = row0.createCell(8);
        Cell cell04 = row0.createCell(10);
        //行高
        row0.setHeightInPoints(35);
        //行宽
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(2, 30 * 256);
        sheet.setColumnWidth(7, 20 * 256);
        sheet.setColumnWidth(8, 30 * 256);
        sheet.setColumnWidth(10, 30 * 256);
        // 设置背景色
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 2, 6));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 8, 9));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 10, 14));
        Font font  = workbook.createFont();
        HSSFCellStyle style = workbook.createCellStyle();
        this.frame(15,row0,style);
        cell00.setCellValue("培训班名称：\t");
        cell01.setCellValue("《" + name + "》");
        cell02.setCellValue("编号：\t");
        cell03.setCellValue(code);
        cell04.setCellValue("主办部门：" + organization + "部");
        font.setFontName("宋体");//设置字体名称
        font.setFontHeightInPoints((short) 14);//设置字号
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        font.setBold(true);
        style.setFont(font);//选择需要用到的字体格式
        cell00.setCellStyle(style);
        cell01.setCellStyle(style);
        cell02.setCellStyle(style);
        cell03.setCellStyle(style);
        cell04.setCellStyle(style);
        String[] num = {"序号", "课程名称", "课时", "讲课日期", "讲师姓名", "联系电话", "讲师类型", "讲师单位", "职务","","","","","",""};
        Object[][] value = new Object[list.size() + 2][16];
        for (int m = 0; m < num.length; m++) {
            value[1][m] = num[m];
        }
        for (int i = 0; i < list.size(); i++) {
            ClassOfflineCourse classOfflineCourse = list.get(i);
            double time = this.dataTime(list.get(i).getEndTime(), list.get(i).getStartTime());
            String courseDate = null;
            if (classOfflineCourse != null && classOfflineCourse.getCourseDate() != null) {
                courseDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date(classOfflineCourse.getCourseDate()));
            }
            value[i + 2][0] = i + 1;
            value[i + 2][1] = classOfflineCourse.getName();
            value[i + 2][2] = time;
            value[i + 2][3] = courseDate;
            value[i + 2][4] = classOfflineCourse.getTeacherName();
            String remarks;
            String teacher;
            teacher = classOfflineCourse.getTeacherPhone() != null ? classOfflineCourse.getTeacherPhone() : "";
            remarks = classOfflineCourse.getRemarks() != null?teacher+"("+classOfflineCourse.getRemarks()+")":teacher;
            value[i + 2][5] = remarks;
            value[i + 2][6] = classOfflineCourse.getTeacherType() != null ? getLecturerType(classOfflineCourse.getTeacherType()) : "";
            value[i + 2][7] = classOfflineCourse.getTeacherOrganization();
            value[i + 2][8] = classOfflineCourse.getTeacherTitle();
            value[i + 2][9] = "";
            value[i + 2][10] = "";
            value[i + 2][11] = "";
            value[i + 2][12] ="";
            value[i + 2][13] = "";
            value[i + 2][14] = "";
            value[i + 2][15] = "";
        }
        this.writeArrayToExcelAll(workbook, sheet, list.size() + 2, 15, value);
    }

    /**
     * 内部讲师审核表
     *
     * @param response
     * @param list
     * @param name
     * @param organization
     * @param workbook
     * @throws IOException
     */
    public void downloadInsideAuditing(HttpServletResponse response, List<CourseSalary> list, String name, String organization, HSSFWorkbook workbook,String code,String shortName) throws IOException {
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet("审核表（内部）");
        // 第一行
        HSSFRow row0 = sheet.createRow(0);
        Cell cell00 = row0.createCell(0);
        row0.setHeightInPoints(55);
        sheet.setColumnWidth(0, 20 * 256);
        // 设置背景色
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
        Font font  = workbook.createFont();
        HSSFCellStyle style = workbook.createCellStyle();
        this.frame(13,row0,style);
        cell00.setCellValue("《"+name+"》"+shortName+"  ("+code+")内部讲师课酬审核表  主办部门：" + organization + "");
        font.setFontName("宋体");//设置字体名称
        font.setFontHeightInPoints((short) 17);//设置字号
        style.setAlignment(HorizontalAlignment.CENTER); //水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        font.setBold(true);
        style.setFont(font);//选择需要用到的字体格式
        cell00.setCellStyle(style);
        // 第二行，空白行 "课时", ，"共计（元）","税金（元）"
        String[] num = {"序号", "课程名称", "课时", "讲课日期", "讲师姓名", "联系电话", "讲师单位", "职务", "身份证号",
                "实付（元）", "税金（元）", "酬金（元）", "推荐方"};
        Object[][] value = new Object[list.size() + 2][13];
        DecimalFormat df = new DecimalFormat("######0.00");
        for (int m = 0; m < num.length; m++) {
            value[1][m] = num[m];
        }
        Double sumActualNum = 0.0, sumAdministrationNum = 0.0, sumIncrementNum = 0.0;
        for (int i = 0; i < list.size(); i++) {
            CourseSalary courseSalary = list.get(i);
            double time = this.dataTime(list.get(i).getClassOfflineCourse().getEndTime(), list.get(i).getClassOfflineCourse().getStartTime());
            String courseDate = null;
            if (courseSalary.getClassOfflineCourse() != null && courseSalary.getClassOfflineCourse().getCourseDate() != null) {
                courseDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date(courseSalary.getClassOfflineCourse().getCourseDate()));
            }
            value[i + 2][0] = i + 1;
            value[i + 2][1] = courseSalary.getClassOfflineCourse().getName();
            value[i + 2][2] = time;
            value[i + 2][3] = courseDate;
            value[i + 2][4] = courseSalary.getClassOfflineCourse().getTeacherName();
            String remarks;
            String teacher;
            teacher = courseSalary.getClassOfflineCourse().getTeacherPhone()!=null?courseSalary.getClassOfflineCourse().getTeacherPhone():"";
            remarks = courseSalary.getClassOfflineCourse().getRemarks()!=null?teacher+"("+courseSalary.getClassOfflineCourse().getRemarks()+")":teacher;
            value[i + 2][5] = remarks;
            value[i + 2][6] = courseSalary.getClassOfflineCourse().getTeacherOrganization();
            value[i + 2][7] = courseSalary.getClassOfflineCourse().getTeacherTitle();
            value[i + 2][8] = courseSalary.getLecturerCard();
            Double num1 = courseSalary.getPaidPay()!=null?courseSalary.getPaidPay():0.00;
            sumActualNum+=num1;
            value[i + 2][9] = courseSalary.getPaidPay()!=null?this.formateDouble(num1):0.00;
            Double num2 = courseSalary.getTax()!=null?courseSalary.getTax():0.00;
            sumAdministrationNum+=num2;
            value[i + 2][10] = courseSalary.getTax()!=null?this.formateDouble(num2):0.00;
            Double num3 = courseSalary.getPay()!=null?courseSalary.getPay():0.00;
            sumIncrementNum+=num3;
            value[i + 2][11] = courseSalary.getPay()!=null?this.formateDouble(num3):0.00;

            String recommend = null;
            if(courseSalary.getClassOfflineCourse().getRecommend()!=null){
                if(courseSalary.getClassOfflineCourse().getRecommend().equals(1)){
                    recommend = "主办方";
                }else if (courseSalary.getClassOfflineCourse().getRecommend().equals(2)){
                    recommend = "移动党校--教学部";
                }else if (courseSalary.getClassOfflineCourse().getRecommend().equals(3)){
                    recommend="移动党校--党校教育部";
                }else if (courseSalary.getClassOfflineCourse().getRecommend().equals(5)){
                    recommend="—";
                }
            }
            value[i + 2][12] = recommend;
        }
        this.writeArrayToExcel(workbook, sheet, list.size() + 2, 13, value, 1);


        sheet.setForceFormulaRecalculation(true);//公式自动计算
        HSSFCellStyle styleList = workbook.createCellStyle();
        styleList.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
        Font fontList = workbook.createFont();
        fontList.setFontName("宋体");//设置字体名称
        fontList.setFontHeightInPoints((short) 13);//设置字号
        styleList.setBorderBottom(BorderStyle.THIN);//下边框
        styleList.setBorderLeft(BorderStyle.THIN);//左边框
        styleList.setBorderRight(BorderStyle.THIN);//右边框
        styleList.setAlignment(HorizontalAlignment.CENTER); //水平居中
        styleList.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        styleList.setFont(fontList);//选择需要用到的字体格式
        if (list.size() > 0) {
            HSSFRow rowList = sheet.createRow(list.size() + 2);
            Cell cellList = rowList.createCell(0);
            sheet.addMergedRegion(new CellRangeAddress(list.size() + 2, list.size() + 2, 0, 8));
            rowList.setHeightInPoints(55);
            cellList.setCellValue("合计：");
            Cell cell9 = rowList.createCell(9);
            Cell cell10 = rowList.createCell(10);
            Cell cell11 = rowList.createCell(11);
            Cell cell12 = rowList.createCell(12);
            cell9.setCellValue(this.formateDouble(sumActualNum));
            cell10.setCellValue(this.formateDouble(sumAdministrationNum));
            cell11.setCellValue(this.formateDouble(sumIncrementNum));
            cell9.setCellStyle(styleList);
            cell10.setCellStyle(styleList);
            cell11.setCellStyle(styleList);
            cell12.setCellStyle(styleList);
            cellList.setCellStyle(styleList);
            for (int i = 1; i <= 8; i++) {
                rowList.createCell(i).setCellStyle(styleList);
            }
        }
        sheet.addMergedRegion(new CellRangeAddress(list.size() + 3, list.size() + 3, 0, 11));
        sheetNum(sheet, list.size() + 4);
        sheetNum(sheet, list.size() + 5);
        HSSFRow rowRow = sheet.createRow(list.size() + 4);
        HSSFRow rowRow1 = sheet.createRow(list.size() + 5);
        Cell cellRow = rowRow.createCell(0);
        Cell cellRow1 = rowRow.createCell(4);
        Cell cellRow2 = rowRow.createCell(7);
        Cell cellRow3 = rowRow.createCell(10);
        int ce = 0;
        HSSFCellStyle styleRow = workbook.createCellStyle();
        Font fontRow = workbook.createFont();
        fontRow.setFontName("宋体");//设置字体名称
        fontRow.setFontHeightInPoints((short) 13);//设置字号
        styleRow.setAlignment(HorizontalAlignment.RIGHT); //水平居中
        styleRow.setFont(fontRow);
        for (int r = 0; r < 4; r++) {
            Cell cellRow4 = rowRow1.createCell(ce);
            if(r==0){
                ce = ce + 4;
            }else{
                ce = ce + 3;
            }
            cellRow4.setCellValue("日期：");
            cellRow4.setCellStyle(styleRow);
        }
        rowRow.setHeightInPoints(20);
        rowRow1.setHeightInPoints(20);
        cellRow.setCellStyle(styleRow);
        cellRow1.setCellStyle(styleRow);
        cellRow2.setCellStyle(styleRow);
        cellRow3.setCellStyle(styleRow);
        cellRow.setCellValue("主办部门项目负责人：");
        cellRow1.setCellValue("教学支撑部班主任：");
        cellRow2.setCellValue("复核人：");
        cellRow3.setCellValue("部门经理：");
    }
    public void sheetNum(HSSFSheet sheet, int a) {
        int b = 0;
        for (int i = 0; i < 2; i++) {
            sheet.addMergedRegion(new CellRangeAddress(a, a, b, b + 1));
            b=b+4;
        }
    }
    public void frame(int num,HSSFRow row0,HSSFCellStyle styleList){
        for(int i = 0;i<num;i++){
            Cell cell00 = row0.createCell(i);
            styleList.setBorderTop(BorderStyle.THIN);//上边框
            if(i==0){
                styleList.setBorderLeft(BorderStyle.THIN);//左边框
            }else if(i==num-1){
                styleList.setBorderRight(BorderStyle.THIN);//右边框
            }
            cell00.setCellStyle(styleList);
        }
    }

    public String getLecturerType(int type){
        if (type == 0){
            return "内部";
        }else {
            return "外部";
        }
    }

    public static void writeArrayToExcel4(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value, Integer flag) {

        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 16 * 256);
        sheet.setColumnWidth(1, 4 * 256);
        sheet.setColumnWidth(2, 12 * 256);
        sheet.setColumnWidth(3, 8 * 256);
        sheet.setColumnWidth(4, 9 * 256);
        sheet.setColumnWidth(5, 9 * 256);
        sheet.setColumnWidth(6, 9 * 256);
        sheet.setColumnWidth(7, 9 * 256);
        sheet.setColumnWidth(8, 8 * 256);
        HSSFCellStyle style1 = wb.createCellStyle();
        HSSFCellStyle style = wb.createCellStyle();
        for (int i = 1; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                Font font1 = wb.createFont();
                row[i].setHeightInPoints(55);
                if (i == 1) {
                    style1.setBorderTop(BorderStyle.THIN);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框
                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setBold(true);
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 11);//设置字号
                    style1.setFont(font1);//选择需要用到的字体格式
                    cell[j].setCellValue(convertString(value[i][j]));
                    style1.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style1);
                } else {
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    style.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 11);//设置字号
                    style.setFont(font1);//选择需要用到的字体格式
                    if (j == 5 || j == 6 || j == 7 || j ==4) {
                        cell[j].setCellValue(Double.parseDouble(value[i][j].toString()));
                        style.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    } else {
                        cell[j].setCellValue(convertString(value[i][j]));
                    }
                    style.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style);
                }
            }
        }
    }


    public final static class YELLOW
            extends HSSFColor
    {
        public final static short   index     = 0xd;
        public final static short   index2    = 0x22;
        public final static short[] triplet   =
                {
                        226, 239, 217
                };
        public final static String  hexString = "FFFF:FFFF:0";

        public short getIndex()
        {
            return index;
        }

        public short [] getTriplet()
        {
            return triplet;
        }

        public String getHexString()
        {
            return hexString;
        }
    }

    public static void writeArrayToExcel(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value, Integer flag) {

        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 5 * 256);
        sheet.setColumnWidth(1, 22 * 256);
        sheet.setColumnWidth(2, 5 * 256);
        sheet.setColumnWidth(3, 18 * 256);
        sheet.setColumnWidth(4, 10 * 256);
        sheet.setColumnWidth(5, 15 * 256);
        sheet.setColumnWidth(6, 15 * 256);
        sheet.setColumnWidth(7, 15 * 256);
        if(flag == 2){
            sheet.setColumnWidth(8, 12 * 256);
        }else{
            sheet.setColumnWidth(8, 23 * 256);
        }
        sheet.setColumnWidth(9, 12 * 256);
        sheet.setColumnWidth(10, 12 * 256);
        sheet.setColumnWidth(11, 12 * 256);
        if (flag == 2) {
            sheet.setColumnWidth(12, 12 * 256);
            sheet.setColumnWidth(13, 12 * 256);
            sheet.setColumnWidth(14, 12 * 256);
        }
        HSSFCellStyle style1 = wb.createCellStyle();
        HSSFCellStyle style = wb.createCellStyle();
        Font font1 = wb.createFont();
        for (int i = 1; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                row[i].setHeightInPoints(55);
                if (i == 1) {
                    style1.setBorderTop(BorderStyle.THIN);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderTop(BorderStyle.THIN);//上边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框
                    style1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 13);//设置字号
                    style1.setFont(font1);//选择需要用到的字体格式
                    cell[j].setCellValue(convertString(value[i][j]));
                    style1.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style1);
                } else {
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    style.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 13);//设置字号
                    style.setFont(font1);//选择需要用到的字体格式
                    if (j == 9 || j == 10 || j == 11) {
                        cell[j].setCellValue(Double.parseDouble(value[i][j].toString()));
                        style.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    }else {
                        cell[j].setCellValue(convertString(value[i][j]));
                    }
                    style.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style);
                }
            }
        }
    }

    public static void writeArrayToExcelAll(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value) {

        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 10 * 256);
        sheet.setColumnWidth(1, 30 * 256);
        sheet.setColumnWidth(2, 10 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(4, 20 * 256);
        sheet.setColumnWidth(5, 25 * 256);
        sheet.setColumnWidth(6, 15 * 256);
        sheet.setColumnWidth(7, 25 * 256);
        sheet.setColumnWidth(8, 20 * 256);
        sheet.setColumnWidth(9, 12 * 256);
        sheet.setColumnWidth(10, 12 * 256);
        sheet.setColumnWidth(11, 12 * 256);
        sheet.setColumnWidth(12, 12 * 256);
        sheet.setColumnWidth(13, 12 * 256);
        sheet.setColumnWidth(14, 12 * 256);
        sheet.setColumnWidth(15, 12 * 256);

        HSSFCellStyle style1 = wb.createCellStyle();
        HSSFCellStyle style = wb.createCellStyle();
        Font font1 = wb.createFont();
        for (int i = 1; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                row[i].setHeightInPoints(35);
                if (i == 1) {
                    style1.setBorderTop(BorderStyle.THIN);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderTop(BorderStyle.THIN);//上边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框
                    style1.setFillForegroundColor(IndexedColors.AQUA.getIndex());
                    style1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 13);//设置字号
                    style1.setFont(font1);//选择需要用到的字体格式
                    cell[j].setCellValue(convertString(value[i][j]));
                    style1.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style1);
                } else {
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    style.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 13);//设置字号
                    style.setFont(font1);//选择需要用到的字体格式
                    cell[j].setCellValue(convertString(value[i][j]));

                    style.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style);
                }
            }
        }
    }

    public static void downloadWriteArray(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value) {
        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 5 * 256);
        sheet.setColumnWidth(1, 18 * 256);
        sheet.setColumnWidth(2, 10 * 256);
        sheet.setColumnWidth(3, 10 * 256);
        sheet.setColumnWidth(4, 5 * 256);
        sheet.setColumnWidth(5, 15 * 256);
        sheet.setColumnWidth(6, 10 * 256);
        sheet.setColumnWidth(7, 10 * 256);
        sheet.setColumnWidth(8, 15 * 256);
        sheet.setColumnWidth(9, 10 * 256);
        sheet.setColumnWidth(10, 15 * 256);
        sheet.setColumnWidth(11, 22 * 256);
        sheet.setColumnWidth(12, 22 * 256);
        sheet.setColumnWidth(13, 20 * 256);
        sheet.setColumnWidth(14, 10 * 256);
        sheet.setColumnWidth(15, 18 * 256);
        sheet.setColumnWidth(16, 18 * 256);
        HSSFCellStyle style1 = wb.createCellStyle();
        HSSFCellStyle style = wb.createCellStyle();
        for (int i = 2; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            Font font1 = wb.createFont();
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                row[i].setHeightInPoints(45);
                if (i == 2) {
                    cell[j].setCellValue(convertString(value[1][j]));
                    style1.setBorderTop(BorderStyle.THIN);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框

                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 10);//设置字号
                    style1.setFont(font1);//选择需要用到的字体格式
                    style1.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style1);
                } else {
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    style.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 10);//设置字号
                    style.setFont(font1);//选择需要用到的字体格式
                    cell[j].setCellValue(convertString(value[i][j]));
                    style.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style);
                }
            }
        }
    }
    public static void downloadWriteArray1(HSSFWorkbook wb, HSSFSheet sheet, int rows, int cells, Object[][] value) {
        Row row[] = new HSSFRow[rows];
        Cell cell[] = new HSSFCell[cells];
        sheet.setColumnWidth(0, 5 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 10 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 10 * 256);
        sheet.setColumnWidth(5, 23 * 256);
        sheet.setColumnWidth(6, 28 * 256);
        sheet.setColumnWidth(7, 30 * 256);
        sheet.setColumnWidth(8, 10 * 256);
        sheet.setColumnWidth(9, 10 * 256);
        sheet.setColumnWidth(10, 10 * 256);
        sheet.setColumnWidth(11, 10 * 256);
        sheet.setColumnWidth(12, 10 * 256);
        HSSFCellStyle style = wb.createCellStyle();
        HSSFCellStyle style1 = wb.createCellStyle();
        Font font1 = wb.createFont();
        for (int i = 1; i < row.length; i++) {
            row[i] = sheet.createRow(i);
            for (int j = 0; j < cell.length; j++) {
                cell[j] = row[i].createCell(j);
                row[i].setHeightInPoints(55);
                if (i == 1) {
                    cell[j].setCellValue(convertString(value[1][j]));
                    style1.setBorderTop(BorderStyle.THIN);
                    style1.setBorderBottom(BorderStyle.THIN);//下边框
                    style1.setBorderLeft(BorderStyle.THIN);//左边框
                    style1.setBorderRight(BorderStyle.THIN);//右边框
                    style1.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style1.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 13);//设置字号
                    style1.setFont(font1);//选择需要用到的字体格式
                    style1.setWrapText(true); // 强制换行
                    cell[j].setCellStyle(style1);
                } else {
                    style.setBorderTop(BorderStyle.THIN);
                    style.setBorderBottom(BorderStyle.THIN);//下边框
                    style.setBorderLeft(BorderStyle.THIN);//左边框
                    style.setBorderRight(BorderStyle.THIN);//右边框
                    style.setAlignment(HorizontalAlignment.CENTER); //水平居中
                    style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
                    font1.setFontName("宋体");//设置字体名称
                    font1.setFontHeightInPoints((short) 13);//设置字号
                    cell[j].setCellValue(convertString(value[i][j]));
                    style.setFont(font1);//选择需要用到的字体格式
                    style.setWrapText(true); // 强制换行
                    if (j == 8 || j == 9 || j == 10 || j == 11) {
                        cell[j].setCellValue(Double.parseDouble(value[i][j].toString()));
                        style.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.00"));
                    } else {
                        cell[j].setCellValue(convertString(value[i][j]));
                    }
                    cell[j].setCellStyle(style);
                }
            }
        }
    }



    public static String convertString(Object value) {
        if (value == null) {
            return "";
        } else {
            return value.toString();
        }
    }

    public Double dataTime(String endTime, String startTime) {
        Double time = 0.0;
        if ((endTime != null && !endTime.equals("")) && startTime != null && !startTime.equals("")) {
            String endS[] = endTime.split(":");
            Double branch = 0.0;
            Integer endB = Integer.parseInt(endS[1]);
            String startS[] = startTime.split(":");
            Integer startB = Integer.parseInt(startS[1]);
            Integer hour = (Integer.parseInt(endS[0]) - Integer.parseInt(startS[0]));
            Integer numTime = 0;
            if (endB > startB) {
                numTime = endB - startB;
                branch = 0.5;
                if (numTime > 30) {
                    branch = 1.0;
                }
            } else if (endB < startB) {
                numTime = startB - endB;
                if (numTime >= 30) {
                    branch = -0.5;
                }
            }
            time = branch + hour;
        }
        return time;
    }

    /**
     * 根据课程ID更新课酬实付
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/updateByCourseId", method = RequestMethod.POST)
    @Param(name = "courseId", type = String.class, required = true)
    @Param(name = "paidPay", type = Double.class)
    @Param(name = "classId", type = String.class)
    @Param(name = "lecturerIdCard", type = String.class)//身份证号
    @Param(name = "lecturerBankName", type = String.class)//开户行
    @Param(name = "lecturerBankCard", type = String.class)//银行账号
    @Param(name = "isUpdatePay", type = Boolean.class, required = true)
    @JSON("id,lecturerCard,lecturerBankName,lecturerBankCard,pay,courseId,paidPay,tax,lectrerName")
    public CourseSalary updateByCourseId(RequestContext requestContext) {
        return courseSalaryService.updatePaidByCourseId(requestContext.getString("courseId"), requestContext.getOptional("paidPay", Double.class),
                requestContext.getOptional("classId", String.class),
                requestContext.getOptionalString("lecturerIdCard"),
                requestContext.getOptionalString("lecturerBankName"),
                requestContext.getOptionalString("lecturerBankCard"),
                requestContext.get("isUpdatePay", Boolean.class));
    }
    @RequestMapping(value = "/changePhone", method = RequestMethod.GET)
    @Param(name = "phone", type = String.class, required = true)
    @Param(name = "id", type = String.class)
    @JSON("*.*")
    public ClassOfflineCourse findBuLecturerPhone(RequestContext requestContext) {
        return courseSalaryService.findBuLecturerPhone(requestContext.get("phone",String.class));
    }
    @RequestMapping(value = "/protect-day", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @JSON("*.*")
    public int findProjectDay(RequestContext requestContext) {
        return classInfoService.findClassAndProjectByClassId(requestContext.get("classId",String.class)).getDays();
    }

    @RequestMapping(value = "/remuneration", method = RequestMethod.GET)
    @Param(name = "classId", type = String.class, required = true)
    @Param(name = "cost", type = Double.class)
    @Param(name = "isOverproof", type = Integer.class)
    @JSON("*.*")
    public List<ClassOfflineCourse> findByRemuneration(RequestContext requestContext){
        Optional<Double> doubleNum = requestContext.getOptional("cost", Double.class);
        //2018-01-24  wangdongyan 确认课酬时提交班级课酬是否超标，对classInfo进行修改
        Optional<Integer> isOverproof = requestContext.getOptional("isOverproof",Integer.class);
        List<ClassOfflineCourse> list = courseSalaryService.find(0,requestContext.get("classId", String.class));

        List<CourseSalary> updateList = new ArrayList<>();
        for(ClassOfflineCourse li:list){
            if(li.getTeacherName()==null&&("").equals(li.getTeacherName())){
                throw new UnprocessableException(ErrorCode.FillOutTheNameOfThe);
            }
            if(li.getTeacherType()!=null){
                if(0==li.getTeacherType()){
                    Double num = li.getCourseSalary().getPaidPay();
                    BigDecimal numO=new BigDecimal(num.toString());
                    if(num<=800){
                        li.getCourseSalary().setPay(num);
                        li.getCourseSalary().setTax(0.00);
                    }else if(800<num&&num<3360){
                        li.getCourseSalary().setPay(num);
                        BigDecimal numT = numO.divide(new BigDecimal("0.8"),10,BigDecimal.ROUND_HALF_UP);
                        BigDecimal numTH = numT.subtract(new BigDecimal("200"));
                        BigDecimal numF = numTH.setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal numFI = numTH.subtract(numO);
                        li.getCourseSalary().setTax(numFI.doubleValue());
                        li.getCourseSalary().setPay(numF.doubleValue());
                    }else if(num <21000 && num>=3360 ){
                        BigDecimal numT = numO.divide(new BigDecimal("0.84"),10,BigDecimal.ROUND_HALF_UP);
                        BigDecimal numTH = numT.setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal numF = numTH.subtract(numO);
                        li.getCourseSalary().setPay(numTH.doubleValue());
                        li.getCourseSalary().setTax(numF.doubleValue());
                    }else if(num <49500 && num>= 21000 ){
                        BigDecimal numT = numO.subtract(new BigDecimal("2000"));
                        BigDecimal numTH = numT.divide(new BigDecimal("0.76"),10,BigDecimal.ROUND_HALF_UP);
                        BigDecimal numFI = numTH.setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal numF = numFI.subtract(numO);
                        li.getCourseSalary().setTax(numF.doubleValue());
                        li.getCourseSalary().setPay(numFI.doubleValue());
                    }else if(num >= 49500){
                        BigDecimal numT = numO.subtract(new BigDecimal("7000"));
                        BigDecimal numTH = numT.divide(new BigDecimal("0.68"),10,BigDecimal.ROUND_HALF_UP);
                        BigDecimal numFI = numTH.setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal numF = numFI.subtract(numO);
                        li.getCourseSalary().setTax(numF.doubleValue());
                        li.getCourseSalary().setPay(numFI.doubleValue());
                    }
                }else {
                    Double num = li.getCourseSalary().getPaidPay();
                    BigDecimal numO = new BigDecimal(num.toString());
                    String taxRate = "0.06";
                    if(li.getTaxrate()!=null&&li.getTaxrate().equals(3)){
                        taxRate = "0.03";
                    }else if (li.getTaxrate() != null && li.getTaxrate().equals(0)){
                        taxRate="0";
                    }
                    if(li.getPaymentMethod()==2 || li.getPaymentMethod()==3){
                        BigDecimal numF = numO.multiply(new BigDecimal(taxRate));
                        BigDecimal numFI  = numO.add(numF);
                        Double numTax = numF.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        Double numPay = numFI.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        li.getCourseSalary().setTax(numTax.doubleValue());
                        li.getCourseSalary().setPay(numPay.doubleValue());
                    } else {
                        if (taxRate.equals("0")){
                            logger.info("当前税率if:{}",taxRate);
                            li.getCourseSalary().setTax(0.0);
                            li.getCourseSalary().setPay(li.getCourseSalary().getPaidPay());
                        }else {
                            logger.info("当前税率else:{}",taxRate);
                            BigDecimal numT = numO.multiply(new BigDecimal(doubleNum.get().toString()));
                            BigDecimal numTH = numT.multiply(new BigDecimal(taxRate));
                            BigDecimal numF = numO.multiply(new BigDecimal(taxRate));
                            BigDecimal numFI  = numTH.add(numF);
                            BigDecimal numS =numT.add(numFI);
                            BigDecimal numSE = numS.add(numO);
                            Double numTax = numS.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            Double numPay = numSE.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            li.getCourseSalary().setTax(numTax);
                            li.getCourseSalary().setPay(numPay);
                        }
                        logger.info("最后的税金:{}",   li.getCourseSalary().getTax());
                        logger.info("最后的酬金:{}",   li.getCourseSalary().getPay());
                    }
                }
                if (doubleNum.isPresent()){
                    li.getCourseSalary().setServiceCharge(doubleNum.get());
                }
                updateList.add(li.getCourseSalary());
            }else{
                throw new UnprocessableException(ErrorCode.FillInTheLecturerType);
            }
        }
        courseSalaryService.updateList(updateList);
        isOverproof.ifPresent(r -> {
            classInfoService.updateIsOverproof(requestContext.get("classId", String.class), r);
        });
        return list;
    }
    @RequestMapping(value = "/update-payment/{id}", method = RequestMethod.PUT)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "payMent", type = Integer.class)
    @Param(name = "recommend", type = Integer.class)
    @Param(name = "taxRate", type = Integer.class)
    @Param(name = "lecturerSource", type = Integer.class)
    @Param(name = "radio", type = Double.class)
    @JSON("id")
    public ClassOfflineCourse updatePayment(RequestContext requestContext) {
        ClassOfflineCourse classOfflineCourse =  courseSalaryService.updatePayment(requestContext.get("id",String.class),
                requestContext.getOptional("payMent",Integer.class),requestContext.getOptional("recommend",Integer.class)
                ,requestContext.getOptional("taxRate",Integer.class),requestContext.getOptional("lecturerSource",Integer.class));
        Optional<Double>  num = requestContext.getOptional("radio",Double.class);
        if(num.isPresent()){
            courseSalaryService.updateServiceCharge(requestContext.get("id",String.class),num.get());
        }
        return classOfflineCourse;
    }

}
