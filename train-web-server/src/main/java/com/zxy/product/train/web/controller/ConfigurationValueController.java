package com.zxy.product.train.web.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.zxy.common.restful.audit.Audit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.train.api.ConfigurationValueService;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.entity.ClassroomConfiguration;
import com.zxy.product.train.entity.ConfigurationValue;
import com.zxy.product.train.entity.Member;
import com.zxy.product.train.util.StringUtils;

/**
*
* <AUTHOR> 通用配置控制层
*
*/
@Controller
@RequestMapping("/configuration-value")
public class ConfigurationValueController {

	private ConfigurationValueService configurationValueService;

	@Autowired
	public void setConfigurationValueService(ConfigurationValueService configurationValueService) {
		this.configurationValueService = configurationValueService;
	}

	/**
	 * 单项配置列表
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(method = RequestMethod.GET)
	@Param(name = "typeId", type = Integer.class, required = true )
	@JSON("id,name,coding,sort,createTime,createMember,typeId,deleteFlag")
	@Permitted
    public List<ConfigurationValue> find(RequestContext requestContext) {
        return configurationValueService.findAll(requestContext.get("typeId", Integer.class));
    }

	/**
	 *	单项配置列表更新
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(method = RequestMethod.POST)
    @Param(name = "typeId", type = Integer.class, required = true)
    @Param(name = "newConfigList", type = String.class, required = true)
    @Param(name = "delConfigList", type = String.class, required = true)
    @JSON("*.*")
	@Audit(module = "活动管理", subModule = "培训管理－配置管理", action = Audit.Action.UPDATE, fisrtAction = "配置", desc = "操作配置{0}",ids = {"typeId"}, jsons = {"configurationName"}, keys = {"configuration"})
	@Permitted
	public int save(RequestContext requestContext, Subject<Member> subject){
        return configurationValueService.Save(
                requestContext.getInteger("typeId"),
                jsonToCollection(requestContext.getString("newConfigList"), ConfigurationValue.class, ErrorCode.SaveConfigurationValueError),
                "{}".equals(requestContext.getString("delConfigList")) ? new ArrayList<ConfigurationValue>() : jsonToCollection(requestContext.getString("delConfigList"), ConfigurationValue.class, ErrorCode.SaveConfigurationValueError),
                Optional.ofNullable(subject.getCurrentUserId()));
    }

	private static <T> List<T> jsonToCollection(String json,Class<T> elementClass,ErrorCode errorCode){
        List<T> list=com.alibaba.fastjson.JSON.parseArray(json ,elementClass);
        if(list==null){
            throw new ValidationException(errorCode);
        }
        return list;
    }

	/**
	 * 维度选择器列表页
	 * @param requestContext
	 * @return
	 */
	@RequestMapping(value="/wei",method=RequestMethod.GET)
	@Permitted
	@Param(name = "page", type = Integer.class, required = true)
	@Param(name = "pageSize", type = Integer.class, required = true)
	@Param(name = "name", type = String.class)
	@Param(name = "createTime", type = String.class)
	@JSON("recordCount")
	@JSON("items.(id,name,type,organizationId,createTime,createMember,mName)")
	public PagedResult<ConfigurationValue> findWeiDu(RequestContext requestContext) {
		Optional<String> str = requestContext.getOptional("createTime",String.class);
		String[] date = null;
		Long stratTime = null;
		Long endTime = null;
		if (str.isPresent()) {
			date = str.get().trim().split("to");
			stratTime = stringTimeToOptionalLong(Optional.of(date[0])).get();
			endTime = stringTimeToOptionalLong(Optional.of(date[1])).get();
		}
		return configurationValueService.findWeiDu(
				requestContext.get("page", Integer.class),
				requestContext.get("pageSize", Integer.class),
				Optional.ofNullable(stratTime),
				Optional.ofNullable(endTime),
				requestContext.getOptional("name",String.class)
				);
	}

	/**
	 *	验证编码
	 * @param requestContext
	 * @param subject
	 * @return
	 */
	@RequestMapping(value="/coding",method = RequestMethod.GET)
	@Permitted
    @Param(name = "typeId", type = Integer.class, required = true)
    @Param(name = "id", type = String.class)
    @Param(name = "coding", type = String.class, required = true)
    @JSON("*.*")
    public int code(RequestContext requestContext, Subject<Member> subject){
         int checkCode = configurationValueService.checkCode(
        		requestContext.getOptional("id", String.class),
        		requestContext.getString("coding"),
        		requestContext.getInteger("typeId"));
         return checkCode;
	}

	/**
	 *
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @JSON("*")
    public int delete(RequestContext rc) {
        return configurationValueService.deleteConfig(rc.get("id", String.class));
    }

	public Optional<Long> stringTimeToOptionalLong(Optional<String> time){
        return time.map(r -> {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = null;
            Long data = null;
            try {
                parse = sdf.parse(r);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if (parse != null) {
                data = parse.getTime();
            }
            return data;
        });
    }

	/**
	 *
	 * @param 餐厅选择器
	 * @param subject
	 * @return
	 */
	@RequestMapping(value="/restaurants", method = RequestMethod.GET)
	@Permitted
    @Param(name = "page", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @Param(name = "name", type = String.class)
    @Param(name = "code", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,name,coding)")
    public PagedResult<ConfigurationValue> findRestaurants(RequestContext requestContext, Subject<Member> subject) {

        return configurationValueService.findRestaurants(requestContext.get("page", Integer.class),
                               requestContext.get("pageSize", Integer.class),
                               requestContext.getOptional("name", String.class),
                               requestContext.getOptional("code", String.class));
    }

	/**
	 *
	 * @param 客房选择器
	 * @param subject
	 * @return
	 */
	@RequestMapping(value="/guestrooms", method = RequestMethod.GET)
	@Permitted
    @Param(name = "page", type = Integer.class , required = true)
    @Param(name = "pageSize", type = Integer.class , required = true)
    @Param(name = "name", type = String.class)
    @Param(name = "code", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,name,coding)")
    public PagedResult<ConfigurationValue> findGuestrooms(RequestContext requestContext, Subject<Member> subject) {

        return configurationValueService.findGuestrooms(requestContext.get("page", Integer.class),
                               requestContext.get("pageSize", Integer.class),
                               requestContext.getOptional("name", String.class),
                               requestContext.getOptional("code", String.class));
    }
}
