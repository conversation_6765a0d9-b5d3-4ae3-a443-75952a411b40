package com.zxy.product.train.web.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.entity.Organization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.annotation.Params;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.entity.Member;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.train.api.LecturerReleaseAuthorityService;
import com.zxy.product.train.api.LecturerService;
import com.zxy.product.train.content.ErrorCode;
import com.zxy.product.train.entity.Lecturer;
import com.zxy.product.train.entity.LecturerReleaseAuthority;
import com.zxy.product.train.entity.OfflineCourseLibrary;

/**
 * 讲师库 lulu
 */
@Controller
@RequestMapping("/lecturer")
public class LecturerController {

    private LecturerService lecturerService;
    private MemberService memberService;
    private GrantService grantService;
    private Cache cache;
	private LecturerReleaseAuthorityService lecturerReleaseAuthorityService;
    private Cache systemCche;
    private com.zxy.product.system.api.permission.OrganizationService sysOrganizationService;
    public static final String CACHE_GRANTED_ORGANIZATION = "cachedGrantedOrganization#";

    @Autowired
    public void setGrantService(GrantService grantService) {
		this.grantService = grantService;
	}

	@Autowired
    public void setMemberService(MemberService memberService) {
		this.memberService = memberService;
	}

	@Autowired
    public void setLecturerService(LecturerService lecturerService) {
        this.lecturerService = lecturerService;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("lecturer");
    }

	@Autowired
	public void setLecturerReleaseAuthorityService(LecturerReleaseAuthorityService lecturerReleaseAuthorityService) {
		this.lecturerReleaseAuthorityService = lecturerReleaseAuthorityService;
	}
    @Autowired
    public void setSystemCacheService(CacheService cacheService) {
        this.systemCche = cacheService.create("system-service", "organization");
    }

    @Autowired
    public void setSysOrganizationService(OrganizationService sysOrganizationService) {
        this.sysOrganizationService = sysOrganizationService;
    }

    /**
	 * 查询当前用户是否拥有讲师发布权限
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/release-authority", method = RequestMethod.GET)
    @Permitted(perms = "activity/lecturer/info")
    @JSON("*")
    public Boolean releaseAuthority(Subject<Member> subject) {
		LecturerReleaseAuthority lecturerReleaseAuthority = lecturerReleaseAuthorityService.find(subject.getCurrentUserId());
		return lecturerReleaseAuthority != null;
    }


	@RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "source")
    @JSON("id,memberId,memberName,organizationName,name,type,sourceType,organizationId,headPortrait,mobile,levelId,levelName,unit,email,jobName,sex,description,adeptCourse,attachmentId,"
    		+ "attachmentName,attachmentPath,bankUser,bankIdentity,bank,bankCard,remark,cooperationType,institutions,linkman,linkmanNo,createTime,coverPath"
    		+ ",workStartYear,certificationYear,sequenceId,sequenceName,parentId,lectureExperience,ascriptionOrganizationId,ascriptionOrganizationName,attributeId"
    		+ ",modifyMemberName,modifyTime,createMemberName")
    @JSON("labelList.(id,name)")
    @JSON("offlineCourseList.(name,className,projectCode,organizationName,address,courseDate,courseSatisfy,lecturerBankCard,teacherTitle,teacherOrganization,paidPayD,payD,taxD,time)")
    public Lecturer get(RequestContext context) {
        return lecturerService.get(context.get("id", String.class),context.getOptionalString("source").orElse(null));
    }

    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "organizationId")
    @Param(name = "name")
    @Param(name = "type", type = Integer.class)
    @Param(name = "offlineCourseName")
    @Param(name = "parentId")
    @Param(name = "sequenceId")
    @Param(name = "label")
    @Param(name = "attributeId")
    @Param(name = "levelId")
    @Param(name = "unit")
    @Param(name = "className")
    @Param(name = "courseType")
    @Param(name = "status", type = Integer.class)
//    @Param(name = "adeptCourse")
    @JSON("recordCount")
    @JSON("items.(id,name,sourceType,unit,jobName,levelName,levelId,type,memberName,organizationName,"
    		+ "email,labelNames,sex,description,adeptCourse,attachmentId,bankUser,bank,releaseStatus,"
    		+ "remark,cooperationType,institutions,linkman,linkmanNo,organizationId,status,attributeId,latelyOrgRecordTime)")
    public PagedResult<Lecturer> findPage(RequestContext context, Subject<Member> subject) {
//    	List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), Lecturer.URI,
//                Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
//                context.getOptionalString("organizationId"), Optional.empty(), Optional.empty()).stream()
//                .map(a -> a.getId()).collect(Collectors.toList());

        List<Organization> organizationList = systemCche
                .get(CACHE_GRANTED_ORGANIZATION + subject.getCurrentUserId() + "#" + Lecturer.URI,
                        List.class);
        if(organizationList==null||organizationList.isEmpty()){
            sysOrganizationService
                    .findMemberGrantedOrganizationNo(subject.getCurrentUserId(), Lecturer.URI, false);

            organizationList = systemCche
                    .get(CACHE_GRANTED_ORGANIZATION + subject.getCurrentUserId() + "#" + Lecturer.URI,
                            List.class);
        }

        List<String> organizationIds = organizationList.stream().map(a -> a.getId())
                .collect(Collectors.toList());

        return lecturerService.find(context.getInteger("page"), context.getInteger("pageSize"),
                subject.getCurrentUserId(),context.getOptionalString("organizationId"),
                context.getOptionalString("name"),
                context.getOptionalInteger("type"),
                context.getOptionalInteger("status"),
                context.getOptionalString("levelId"),
                context.getOptionalString("className"),
                context.getOptionalString("offlineCourseName"),
                context.getOptionalString("parentId"),
                context.getOptionalString("sequenceId"),
                context.getOptionalString("label"),
                context.getOptionalString("attributeId"),
                context.getOptionalString("unit"),
                context.getOptionalString("courseType"),
                Optional.empty(),
                Optional.ofNullable(organizationIds));
    }

	/**
	 * 查询擅长课程
	 */
	@RequestMapping(value = "/selectOfflineCourse", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
	@Param(name = "lecturerId", type = String.class)
    @Param(name = "name", type = String.class)
    @Param(name = "courseAttributes", type = String.class)
	@Param(name = "courseSequence", type = String.class)
	@Param(name = "organizationId", type = String.class)
	@Param(name = "courseList", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id, name, courseAttributes, courseAttributesName, organizationName, sequence, createTime, updateTime, isUse, institutionId)")
    public PagedResult<OfflineCourseLibrary> selectOfflineCourse(RequestContext context, Subject<Member> subject) {
		List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), OfflineCourseLibrary.URI, Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
		        context.getOptional("organizationId",String.class), Optional.empty(), Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());

		return lecturerService.selectOfflineCourse(
				context.get("page", Integer.class),
				context.get("pageSize", Integer.class),
				context.getOptional("name",String.class),
				context.getOptionalString("lecturerId"),
				context.getOptional("courseAttributes",String.class),
				context.getOptional("courseSequence", String.class),
				context.getOptional("courseList", String.class),
				context.getOptional("organizationId", String.class),
				organizationIds
				);
    }

    @RequestMapping(value = "/findPageSelect", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "organizationId")
    @Param(name = "name")
    @Param(name = "type", type = Integer.class)
    @Param(name = "levelId")
    @Param(name = "className")
    @Param(name = "offlineCourseName")
    @Param(name = "adeptCourse")
	@Param(name = "parentId")
	@Param(name = "sequenceId")
	@Param(name = "label")
	@Param(name = "attribute")
	@Param(name = "unit")
	@Param(name = "courseType")
    @Param(name = "courseId")
    @JSON("recordCount")
    @JSON("items.(id,name,sourceType,unit,jobName,levelName,levelId,type,memberName,organizationName"
    		+ "mobile,email,labelNames,sex,description,adeptCourse,attachmentId,bankUser,bankIdentity,bank,bankCard"
    		+ "remark,cooperationType,institutions,linkman,linkmanNo,organizationId,status)")
    public PagedResult<Lecturer> findPageSelect(RequestContext context, Subject<Member> subject) {
    	List<String> organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), Lecturer.URI,
                Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
                context.getOptionalString("organizationId"), Optional.empty(), Optional.empty()).stream()
                .map(a -> a.getId()).collect(Collectors.toList());

        return lecturerService.find(context.getInteger("page"),
        		context.getInteger("pageSize"),
                subject.getCurrentUserId(),
                context.getOptionalString("organizationId"),
                context.getOptionalString("name"),
                context.getOptionalInteger("type"),Optional.of(0),
                context.getOptionalString("levelId"),
                context.getOptionalString("className"),
                context.getOptionalString("offlineCourseName"),
                context.getOptionalString("parentId"),
                context.getOptionalString("sequenceId"),
                context.getOptionalString("label"),
                context.getOptionalString("attributeId"),
                context.getOptionalString("unit"),
                context.getOptionalString("courseType"),
                context.getOptionalString("courseId"),
                Optional.ofNullable(organizationIds));
    }

    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "memberId")
    @Param(name = "headPortrait")
    @Param(name = "headPortraitPath")
    @Param(name = "name", type = String.class, required = true)
    @Param(name = "levelId", type = String.class)
    @Param(name = "labelIds")
    @Param(name = "mobile")
    @Param(name = "sex", type = Integer.class, required = true)
    @Param(name = "organizationId")
    @Param(name = "jobName")
    @Param(name = "email")
    @Param(name = "type", type = Integer.class, required = true)
    @Param(name = "description")
    @Param(name = "adeptCourse")
    @Param(name = "attachmentId")
    @Param(name = "attachmentName")
    @Param(name = "attachmentPath")
    @Param(name = "bankUser")
    @Param(name = "bank")
    @Param(name = "bankIdentity")
    @Param(name = "bankCard")
    @Param(name = "remark")
    @Param(name = "unit")
    @Param(name = "cooperationType", type = Integer.class)
    @Param(name = "institutions")
    @Param(name = "linkman")
    @Param(name = "linkmanNo")
    @Param(name = "sourceType", type = Integer.class)
    @Param(name = "attributeId", type = String.class, required = true)
    @Param(name = "workStartYear")
    @Param(name = "certificationYear")
    @Param(name = "sequenceId", type = String.class, required = true)
    @Param(name = "lectureExperience")
    @Param(name = "ascriptionOrganizationId", type = String.class, required = true)
    @Param(name = "adeptCourseList", type = String.class)//擅长讲师记录
    @Param(name = "unitrecordList", type = String.class)//各单位授课记录
    @Param(name = "otherrecordList", type = String.class)//各学院授课记录
    @Param(name = "organizationName", type = String.class)//讲师部门
    @Param(name = "releaseStatus", type = Integer.class)//讲师发布状态
    @JSON("*")
    @Permitted
    @Audit(module = "活动管理", subModule = "讲师管理—讲师库", action = Audit.Action.INSERT, desc = "新增讲师{0}", params = {"name"})
    public Lecturer save(RequestContext context, Subject<Member> subject) {
    	Optional<String> memberId = context.getOptional("memberId", String.class);
    	if (memberId.isPresent()){
    		if(lecturerService.checkMmeber(memberId.get(), Optional.empty()) != 0) throw new UnprocessableException(ErrorCode.LecturerRepeat);
    	}
    	Integer integer = context.getInteger("type");
    	Optional<String> mobile = context.getOptional("mobile", String.class);
    	if (integer.equals(0)) {
	    	if (mobile.isPresent()){
				if(lecturerService.checkCode(mobile.get(), Optional.empty()) != 0) throw new UnprocessableException(ErrorCode.LecturerMobileRepeat);
			}
    	}
    	Optional<String> bankIdentity = context.getOptionalString("bankIdentity");
    	if (integer.equals(1)) {
    		if (mobile.isPresent()){
    			if(memberService.findByPhone(mobile.get()) != 0) throw new UnprocessableException(ErrorCode.MemberMobileRepeat);
    		}
			if(!bankIdentity.isPresent() || lecturerService.getLecturerDetailByIDCard(bankIdentity.get(), 1) != null)
				throw new UnprocessableException(ErrorCode.MemberCardFormatError);
    	}
    	if(mobile.isPresent() && bankIdentity.isPresent()){
    		if(lecturerService.checkCodeAndIDCard(mobile.get(), bankIdentity.get()) != 0) throw new UnprocessableException(ErrorCode.LecturerMobileRepeat);
    	}
        return lecturerService.insertNew( context.getOptionalInteger("sourceType"),
        		context.getOptionalString("memberId"),
        		context.getOptionalString("headPortrait"),
        		context.getOptionalString("headPortraitPath"),
        		context.getString("name"),
                context.getOptionalString("levelId"),
                context.getOptionalString("labelIds"),
                context.getOptionalString("mobile").orElse(""),
                context.getInteger("sex"),
                context.getOptionalString("organizationId"),
                context.getOptionalString("jobName"),
                context.getOptionalString("email"),
                integer,
                context.getOptionalString("description"),
                context.getOptionalString("adeptCourse"),
                context.getOptionalString("attachmentId"),
                context.getOptionalString("attachmentName"),
                context.getOptionalString("attachmentPath"),
                context.getOptionalString("bankUser"),
                context.getOptionalString("bank"),
                bankIdentity,
                context.getOptionalString("bankCard"),
                context.getOptionalString("remark"),
                context.getOptionalString("unit"),
                context.getOptionalInteger("cooperationType"),
                context.getOptionalString("institutions"),
                context.getOptionalString("linkman"),
                context.getOptionalString("linkmanNo"),
                context.getOptionalString("attributeId"),
                context.getOptionalString("workStartYear"),
                context.getOptionalString("certificationYear"),
                context.getOptionalString("sequenceId"),
                context.getOptionalString("lectureExperience"),
                context.getOptionalString("ascriptionOrganizationId"),
                context.getOptionalString("adeptCourseList"),
                context.getOptionalString("unitrecordList"),
                context.getOptionalString("otherrecordList"),
                context.getOptionalString("organizationName"),
                context.getOptionalInteger("releaseStatus").orElse(Integer.valueOf(Lecturer.RELEASE_STATUS_NO)),
                subject.getCurrentUserId());
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "memberId")
    @Param(name = "headPortrait")
    @Param(name = "headPortraitPath")
    @Param(name = "name")
    @Param(name = "levelId")
    @Param(name = "labelIds")
    @Param(name = "mobile")
    @Param(name = "sex", type = Integer.class, required = true)
    @Param(name = "organizationId")
    @Param(name = "jobName")
    @Param(name = "email")
    @Param(name = "type", type = Integer.class, required = true)
    @Param(name = "description")
    @Param(name = "adeptCourse")
    @Param(name = "attachmentId")
    @Param(name = "attachmentName")
    @Param(name = "attachmentPath")
    @Param(name = "bankUser")
    @Param(name = "bank")
    @Param(name = "bankIdentity")
    @Param(name = "bankCard")
    @Param(name = "remark")
    @Param(name = "unit")
    @Param(name = "cooperationType", type = Integer.class)
    @Param(name = "institutions")
    @Param(name = "linkman")
    @Param(name = "linkmanNo")
    @Param(name = "attributeId", type = String.class, required = true)
    @Param(name = "workStartYear")
    @Param(name = "certificationYear")
    @Param(name = "sequenceId", type = String.class, required = true)
    @Param(name = "lectureExperience")
    @Param(name = "ascriptionOrganizationId", type = String.class, required = true)
    @Param(name = "collegerecordList", type = String.class)//学院授课记录
    @Param(name = "unitrecordList", type = String.class)//各单位授课记录
    @Param(name = "otherrecordList", type = String.class)//其他教学教研授课记录
    @Param(name = "organizationName", type = String.class)//讲师部门
    @JSON("*")
    @Permitted
    @Audit(module = "活动管理", subModule = "讲师管理—讲师库", action = Audit.Action.UPDATE, fisrtAction = "编辑", desc = "编辑信息于讲师{0}", params = {"name"})
    public Lecturer update(RequestContext context, Subject<Member> subject) {
//    	com.zxy.product.human.entity.Organization o = memberService
//				.getCompanyOrganizationWithLevel2ByMemberId(subject.getCurrentUserId());
    	Optional<String> organizationId = context.getOptionalString("organizationId");
//    	String org = null;
//    	if (optionalString.isPresent()) {
//    		org = optionalString.get();
//    	} else {
//    		org = o.getId();
//    	}
    	String id = context.getString("id");
    	Optional<String> memberId = context.getOptional("memberId", String.class);
    	if (memberId.isPresent()){
    		if(lecturerService.checkMmeber(memberId.get(), Optional.of(id)) != 0) throw new UnprocessableException(ErrorCode.LecturerRepeat);
    	}
		Optional<String> mobile = context.getOptional("mobile", String.class);
		Integer type = context.getInteger("type");
		Optional<String> bankIdentity = context.getOptionalString("bankIdentity");
    	if (type.equals(0)) {
	    	if (mobile.isPresent()){
	    		List<Lecturer> lecturerList = lecturerService.getLecturerInsideList(Arrays.asList(mobile.get()));
	    		if (lecturerList.size() == 1) {
	    			if(!id.equals(lecturerList.get(0).getId())) throw new UnprocessableException(ErrorCode.LecturerMobileRepeat);
				} else if (lecturerList.size() > 1){
					throw new UnprocessableException(ErrorCode.LecturerMobileRepeat);
				}
			}
    	}
    	if (type.equals(1)) {
    		if (mobile.isPresent()){
    			if(memberService.findByPhone(mobile.get()) != 0) throw new UnprocessableException(ErrorCode.MemberMobileRepeat);
    		}
    		if(!bankIdentity.isPresent()) throw new UnprocessableException(ErrorCode.MemberCardFormatError);
    	}
        return lecturerService.update(context.getString("id"),
        		context.getOptionalString("memberId"),
        		context.getOptionalString("headPortrait"),
        		context.getOptionalString("headPortraitPath"),
                context.getOptionalString("name"),
                context.getOptionalString("levelId"),
                context.getOptionalString("labelIds"),
                context.getOptionalString("mobile"),
                context.getInteger("sex"),
                type,
                organizationId,
                context.getOptionalString("jobName"),
                context.getOptionalString("email"), context.getOptionalString("description"),
                context.getOptionalString("adeptCourse"),
                context.getOptionalString("attachmentId"),context.getOptionalString("attachmentName"),
                context.getOptionalString("attachmentPath"),
                context.getOptionalString("bankUser"), context.getOptionalString("bank"),
                bankIdentity, context.getOptionalString("bankCard"),
                context.getOptionalString("remark"), context.getOptionalString("unit"),
                context.getOptionalInteger("cooperationType"), context.getOptionalString("institutions"),
                context.getOptionalString("linkman"), context.getOptionalString("linkmanNo"),
                context.getOptionalString("attributeId"),
                context.getOptionalString("workStartYear"),
                context.getOptionalString("certificationYear"),
                context.getOptionalString("sequenceId"),
                context.getOptionalString("lectureExperience"),
                context.getOptionalString("ascriptionOrganizationId"),
                context.getOptionalString("collegerecordList"),
                context.getOptionalString("unitrecordList"),
                context.getOptionalString("otherrecordList"),
                context.getOptionalString("organizationName"),
                subject.getCurrentUserId());
    }


    /**
     * 状态改完名称
     * @param status
     * @return
     */
    public static String approvalStatusToName(Integer status){
    	switch (status) {
			case 0: return "待审核";
			case 1: return "通过";
			case 2: return "拒绝";
			default: return "无状态";
		}
    }

    /**
     * 当前用户是讲师
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-lecturer", method = RequestMethod.GET)
    @Permitted
    @Params
    @JSON("(*)")
    public Integer findLecturer(RequestContext context, Subject<Member> subject){
        return lecturerService.findLecturer(subject.getCurrentUserId());
    }

    /**
	 * 个人中心里面讲师查询
	 * @param context
	 * @param subject
	 * @return
	 */
	@RequestMapping(value = "/front", method = RequestMethod.GET)
    @Param(name = "className", type = String.class)
	@Param(name = "teachDate", type = String.class)
	@Param(name = "stratSatisfiedDegree", type = Double.class)
	@Param(name = "endSatisfiedDegree", type = Double.class)
	@Param(name = "stratCourseDuration", type = Double.class)
	@Param(name = "endCourseDuration", type = Double.class)
	@JSON("id,memberId,memberName,organizationName,name,type,sourceType,"
			+ "count,satisfy,organizationId,headPortrait,mobile,levelId,levelName,unit,email,jobName,sex,description,bankUser,bankIdentity,bank,bankCard,remark,cooperationType,institutions,linkman,linkmanNo,createTime,coverPath")
    @JSON("labelList.(id,name)")
    @JSON("offlineCourseList.(name,className,courseDate,courseSatisfy,time,startTime)")
    public Lecturer findFront(RequestContext context, Subject<Member> subject) {
		Optional<String> str = context.getOptional("teachDate",String.class);
		String[] date = null;
		Long stratTime = null;
		Long endTime = null;
		if (str.isPresent()) {
			date = str.get().trim().split("to");
			stratTime = stringTimeToOptionalLong(Optional.of(date[0])).get();
			endTime = stringTimeToOptionalLong(Optional.of(date[1])).get();
		}
		return lecturerService.findFront(
				subject.getCurrentUserId(),
				context.getOptional("stratCourseDuration",Double.class),
				context.getOptional("endCourseDuration",Double.class),
				context.getOptional("className",String.class),
				Optional.ofNullable(stratTime),
				Optional.ofNullable(endTime),
				context.getOptional("stratSatisfiedDegree",Double.class),
				context.getOptional("endSatisfiedDegree",Double.class)
				);
    }

	public Optional<Long> stringTimeToOptionalLong(Optional<String> time){
        return time.map(r -> {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = null;
            Long data = null;
            try {
                parse = sdf.parse(r);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if (parse != null) {
                data = parse.getTime();
            }
            return data;
        });
    }

	@RequestMapping(value = "/head/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "headPortrait")
    @Param(name = "headPortraitPath")
    @JSON("id,name,headPortrait,coverPath")
    public Lecturer updateHead(RequestContext context, Subject<Member> subject) {
        return lecturerService.updateHead(context.getString("id"),
        		context.getOptionalString("headPortrait"),
        		context.getOptionalString("headPortraitPath"));
    }


	 /**
     *通过人员ID获取讲师信息
     *
     */
    @RequestMapping(value = "/find-lecturer/member", method = RequestMethod.GET)
    @JSON("*")
    public Lecturer findLecturerByUserId(Subject<Member> subject){
        return lecturerService.findLecturerByUserId(subject.getCurrentUserId()).get();
    }

    /**
     *日常安排外部讲师列表
     *
     */
    @RequestMapping(value = "/find-external-lecturer", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "className")
    @Param(name = "offlineCourseName")
    @JSON("recordCount")
    @JSON("items.(*)")
    public PagedResult<Lecturer> findExternalLecturer(RequestContext context){
    	return lecturerService.findExternalLecturer(context.getInteger("page"), context.getInteger("pageSize"),
                context.getOptionalString("name"), context.getOptionalString("className"), context.getOptionalString("offlineCourseName"));
    }

    /**
     *根据手机号匹配内部讲师信息
     *
     */
    @RequestMapping(value = "/get-lecturer-detail", method = RequestMethod.GET)
    @Param(name = "phone", type = String.class, required = true)
    @JSON("id, name, jobName, organizationName,bankUser,bankIdentity,bank,bankCard")
    public Lecturer getLecturerDetailByPhone(RequestContext context, Subject<Member> subject){
        Lecturer lecturer = lecturerService.getLecturerDetailByPhone(context.getString("phone"));
        return lecturer;
    }

    /**
	 * 编辑讲师（在库退库）
	 * @param context
	 * @return
	 */
	@RequestMapping(value = "/put-status/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true, type = String.class)
	@Param(name = "status", required = true, type = Integer.class)
    @JSON("*")
    public Lecturer putStatus(RequestContext context) {
        return lecturerService.putStatus(
        		context.getString("id"),
        		context.getInteger("status")
        		);
    }

    /**
	 * 批量发布讲师
	 * @param context
	 * @return
	 */
	@RequestMapping(value = "/release-status", method = RequestMethod.PUT)
    @Param(name = "ids", required = true, type = String.class)
    @JSON("*")
    @Permitted
    public Integer releaseStatus(RequestContext context,Subject<Member> subject) {
		if(lecturerReleaseAuthorityService.find(subject.getCurrentUserId()) == null)
			throw new UnprocessableException(ErrorCode.NotHaveLecturerReleaseAuthority);
        return lecturerService.releaseStatus(context.getString("ids"), Lecturer.RELEASE_STATUS_YES);
    }

    /**
	 * 批量取消发布讲师
	 * @param context
	 * @return
	 */
	@RequestMapping(value = "/release-cancel-status", method = RequestMethod.PUT)
    @Param(name = "ids", required = true, type = String.class)
    @JSON("*")
    @Permitted
    public Integer releaseCancelStatus(RequestContext context,Subject<Member> subject) {
		if(lecturerReleaseAuthorityService.find(subject.getCurrentUserId()) == null)
			throw new UnprocessableException(ErrorCode.NotHaveLecturerReleaseAuthority);
        return lecturerService.releaseStatus(context.getString("ids"), Lecturer.RELEASE_STATUS_NO);
    }

	/**************************学员端*******************************/

	/**
	 * 查询某用户是否为讲师的基本信息
	 * @param context
	 * @return
	 */
	@RequestMapping(value = "/is-lencturer", method = RequestMethod.GET)
    @Permitted
    @Param(name = "memberId", required = true, type = String.class)
    @JSON("id,name,type,attributeId")
    public Lecturer releaseCancelSwtatus(RequestContext context) {
        return lecturerService.getBasics(context.getOptionalString("memberId").orElse(null));
    }

    @RequestMapping(value = "/stu", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "type", type = Integer.class)
    @Param(name = "courseName")
    @Param(name = "parentId")
    @Param(name = "sequenceId")
    @Param(name = "labelName")
    @Param(name = "labels")
    @Param(name = "attributeId")
    @Param(name = "levelId")
    @Param(name = "unit")
    @Param(name = "courseType")
    @Param(name = "organizationId")
    @Param(name = "moduleHomeConfigId")
    @Param(name = "order", type = Integer.class) //【0：最新，1：最热；2：最赞】
    @JSON("recordCount")
    @JSON("items.(id,memberName,memberId,name,unit,jobName,type,attributeId,parentName,thumbsUp,"
    		+ "thumbsUpNumber,browseNumber,sequenceId,sequenceName,parentId,adeptCourseList,"
    		+ "headPortrait,coverPath,institutions,sex,levelName,organizationName)")
    @JSON("items.adeptCourseList.(id,courseId,courseName)")
    public PagedResult<Lecturer> findStudentPage(RequestContext context, Subject<Member> subject) {
    	List<String> organizationIds = new ArrayList<String>();
    	if (context.getOptionalString("organizationId").isPresent()){
    		organizationIds = grantService.findGrantedOrganization(subject.getCurrentUserId(), Lecturer.URI,
    				Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(),
    				context.getOptionalString("organizationId"), Optional.empty(), Optional.empty()).stream()
    				.map(a -> a.getId()).collect(Collectors.toList());
    	}
        return lecturerService.findStudent(context.getInteger("page"), context.getInteger("pageSize"),
                subject.getCurrentUserId(),
                context.getOptionalString("name"),
                context.getOptionalInteger("type"),
                context.getOptionalString("levelId"),
                context.getOptionalString("courseName"),
                context.getOptionalString("parentId"),
                context.getOptionalString("sequenceId"),
                context.getOptionalString("labelName"),
                context.getOptionalString("labels"),
                context.getOptionalString("attributeId"),
                context.getOptionalString("unit"),
                context.getOptionalString("courseType"),
                organizationIds,
                context.getOptionalInteger("order").orElse(0),
                context.getOptionalString("moduleHomeConfigId"));
    }


	/**
	 * 学员端学员查询讲师信息
	 * @param context
	 * @return
	 */
	@RequestMapping(value = "/teacher/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("id,memberId,memberName,name,type,headPortrait,mobile,levelId,levelName,unit,jobName,sex,description,"
    		+ "attachmentId,attachmentName,attachmentPath,remark,institutions,createTime,coverPath,sequenceId,sequenceName,"
    		+ "parentId,parentName,lectureExperience,attributeId,attribute,labelNames,"
    		+ "organizationName,organizationId,ascriptionOrganizationId,ascriptionOrganizationName,thumbsUp")
    @JSON("labelList.(id,name)")
    @JSON("adeptCourseList.(id,courseId,courseName)")
    public Lecturer getStudentFindId(RequestContext context, Subject<Member> subject) {
        return lecturerService.getStudentFindId(context.get("id", String.class), 0, subject.getCurrentUserId());
    }

	/**
	 * 学员端讲师查看自己的讲师信息
	 * @param context
	 * @return
	 */
    @RequestMapping(value = "/teacher", method = RequestMethod.GET)
    @Param(name = "")
    @Permitted
    @JSON("id,memberId,memberName,name,type,headPortrait,mobile,levelId,levelName,unit,jobName,sex,description,"
    		+ "attachmentId,attachmentName,attachmentPath,remark,institutions,createTime,coverPath,sequenceId,sequenceName,"
    		+ "parentId,parentName,lectureExperience,attributeId,attribute,labelNames,"
    		+ "organizationName,organizationId,ascriptionOrganizationId,ascriptionOrganizationName,thumbsUp")
    @JSON("labelList.(id,name)")
    @JSON("adeptCourseList.(id,courseId,courseName)")
    public Lecturer getTeacherFindId(Subject<Member> subject) {
        return lecturerService.getStudentFindId(subject.getCurrentUserId(), 1, subject.getCurrentUserId());
    }

	/**
	 * 浏览数增加
	 * @param context
	 */
	@RequestMapping(value = "/browse/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id")
    @JSON("*")
    public Integer browse(RequestContext context) {
        lecturerService.addBrowse(context.get("id", String.class));
        return 1;
    }

	/**
	 * 学员端讲师修改领域
	 * @param context
	 */
	@RequestMapping(value = "/up-label/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "labelIds")
    @JSON("id,name,labelNames")
    public Lecturer updateLabel(RequestContext context, Subject<Member> subject) {
        return lecturerService.updateLabel(context.getString("id"),
        		context.getOptionalString("labelIds"));
    }

	/**
	 * 学员端讲师修改简介
	 * @param context
	 */
	@RequestMapping(value = "/up-description/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "description")
    @JSON("id,name,description")
    public Lecturer updateDescription(RequestContext context, Subject<Member> subject) {
        return lecturerService.updateDescription(context.getString("id"),
        		context.getOptionalString("description"));
    }

	/**
	 * 学员端讲师修改授课经历
	 * @param context
	 */
	@RequestMapping(value = "/up-experience/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "experience")
    @JSON("id,name,lecturerExperience")
    public Lecturer updateExperience(RequestContext context, Subject<Member> subject) {
        return lecturerService.updateExperience(context.getString("id"),
        		context.getOptionalString("experience"));
    }


	/**
	 * 金牌讲师
	 * @param context
	 * @return
	 */
	@RequestMapping(value = "/gold-lecturer", method = RequestMethod.GET)
    @Param(name = "")
    @JSON("id,name,coverPath,headPortrait,attributeId,attribute,lectureNumber,type")
	@SuppressWarnings("unchecked")
    public List<Lecturer> goldLecturer(RequestContext context) {
		List<Lecturer> list = cache.get("GOLD_LECTURER_RECORD",List.class);
        if(list == null){
            list = lecturerService.goldLecturer(1,5);
            cache.set("GOLD_LECTURER_RECORD", list, 300);
        }
        return list;
    }

}
