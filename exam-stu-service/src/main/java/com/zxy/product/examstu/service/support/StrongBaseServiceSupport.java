package com.zxy.product.examstu.service.support;


import com.google.common.collect.Maps;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;


import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.examstu.annotation.DataSource;
import com.zxy.product.examstu.api.StrongBaseService;

import com.zxy.product.exam.entity.*;
import com.zxy.product.examstu.service.util.GetTableUtil;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import static com.zxy.product.exam.jooq.Tables.*;



import java.util.*;
import java.util.stream.Collectors;


@Service
public class StrongBaseServiceSupport implements StrongBaseService {

    private CommonDao<SubAuthenticatedExamGroup> examGroupDao;

    private Cache groupExamIdCache;

    private CommonDao<ExamRecord> examRecordDao;

    private GetTableUtil getTableUtil;

    private CommonDao<SignUp> signUpDao;

    @Autowired
    public void setSignUpDao(CommonDao<SignUp> signUpDao) {
        this.signUpDao = signUpDao;
    }

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }


    @Autowired
    public void setCacheService(CacheService cacheService){
        this.groupExamIdCache = cacheService.create("sub-auth-group", "exam-ids");
    }

    @Autowired
    public void setExamGroupDao(CommonDao<SubAuthenticatedExamGroup> examGroupDao) {
        this.examGroupDao = examGroupDao;
    }



    @Override
    @DataSource
    public boolean authType(Integer examRegion, String examId) {
        List<Integer> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(examId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.IS_PUBLISH.eq(1))
                .limit(1)
                .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE));
        return com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(list) && SubAuthenticatedExamGroup.BUSINESS_TYPE_2 == list.get(0);
    }

    @Override
    @DataSource
    public Boolean examAgain(Integer examRegion, String examId, String memberId) {
        // 获取用户最新考试记录
        ExamRecord examRecord = getNewestRecord(examRegion, examId, memberId);
        // 进行中的考试记录直接返回
        if (examRecord != null && examRecord.getStatus() != null && ExamRecord.STATUS_DOING.equals(examRecord.getStatus())) {
            return true;
        }

        // 查询这个认证考试所在考试组的允许考试次数
        List<SubAuthenticatedExamGroup> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(examId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(SubAuthenticatedExamGroup.BUSINESS_TYPE_2))
                .limit(1)
                .fetch(r->{
                    SubAuthenticatedExamGroup examGroup = new SubAuthenticatedExamGroup();
                    examGroup.setExamGroupId(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID));
                    examGroup.setExamTimes(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES));
                    return examGroup;
                }));
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(list) || list.get(0).getExamTimes() == null) {
            return true;
        }
        String groupId = list.get(0).getExamGroupId();
        Integer allowExamTimes = list.get(0).getExamTimes();
        // 查询该考试组中的所有认证考试id
        List<String> examIds = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID.eq(groupId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(SubAuthenticatedExamGroup.BUSINESS_TYPE_2))
                .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID));
        // 查询受众的ids
        List<String> targetIds = groupExamIdCache.get(groupId+"#"+memberId+"#"+SubAuthenticatedExamGroup.BUSINESS_TYPE_2,()->findTargetIds(examIds, memberId), 60*10);
        // 查询已考次数
        int examTimes = findExamTimes(examRegion, targetIds, memberId);
        return allowExamTimes > examTimes;
    }


    @Override
    @DataSource
    public  Map<String, Object> findFrontExamList(Integer examRegion, String subId, String contentId, Integer businessType, String currentUserId) {
        // 查询子认证考试组对应类型的考试信息
        List<Exam> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_REGISTRATION_FREQUENCY)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.ORDER)
                        .add(EXAM.ID)
                        .add(EXAM.NAME)
                        .add(EXAM.START_TIME)
                        .add(EXAM.END_TIME)
                        .add(EXAM.COVER_ID)
                        .add(EXAM.COVER_ID_PATH)
                        .add(EXAM.STATUS)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .leftJoin(EXAM).on(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(EXAM.ID))
                .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(subId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID.eq(contentId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(businessType))
                .fetch(r -> {
                    Exam exam = new Exam();
                    exam.setId(r.getValue(EXAM.ID));
                    exam.setName(r.getValue(EXAM.NAME));
                    exam.setStartTime(r.getValue(EXAM.START_TIME));
                    exam.setEndTime(r.getValue(EXAM.END_TIME));
                    exam.setCoverId(r.getValue(EXAM.COVER_ID));
                    exam.setCoverIdPath(r.getValue(EXAM.COVER_ID_PATH));
                    exam.setStatus(r.getValue(EXAM.STATUS));
                    exam.setAllowExamTimes(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES));
                    exam.setIndex(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.ORDER));
                    exam.setExamRegistrationFrequency(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_REGISTRATION_FREQUENCY));
                    return exam;
                }));
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        // 查询这个人的受众考试targetIds
        List<String> ids = list.stream().map(Exam::getId).distinct().collect(Collectors.toList());
        List<String> targetIds = groupExamIdCache.get(contentId+"#"+currentUserId+"#"+businessType,()->findTargetIds(ids, currentUserId), 60*10);
        if (CollectionUtils.isEmpty(targetIds)) {
            return new HashMap<>();
        }
        List<Exam> exams = list.stream().filter(e -> targetIds.contains(e.getId())).sorted(Comparator.comparing(Exam::getIndex)).collect(Collectors.toList());
        Map<String, Object> map = Maps.newHashMap();
        map.put("exams",exams);
        // 认证考试，设置了考试组的考试次数，需要查询剩余的次数m
        if (SubAuthenticatedExamGroup.BUSINESS_TYPE_2 == businessType && !CollectionUtils.isEmpty(exams)) {
            if (exams.get(0).getAllowExamTimes() != null) {
                // 查询考试组已考次数
                int count = findExamTimes(examRegion, exams.stream().map(Exam::getId).distinct().collect(Collectors.toList()), currentUserId);
                Integer allowExamTimes = exams.get(0).getAllowExamTimes();
                map.put("remainCount", count > allowExamTimes ? 0 : allowExamTimes - count);
            }

            // 查询考试报名次数
            if (exams.get(0).getExamRegistrationFrequency() != null) {
                //设定次数
                Integer examRegistrationFrequency = exams.get(0).getExamRegistrationFrequency();
                //已考次数
                Integer count = findExamRegistrationFrequency(exams.stream().map(Exam::getId).distinct().collect(Collectors.toList()), currentUserId);
                //计算剩余次数
                int i = examRegistrationFrequency - count;
                map.put("examRegistrationFrequency", Math.max(i, 0));
            }
        }
        return map;
    }

    @Override
    @DataSource
    public boolean checkExamGroupSignUp(Integer examRegion, String currentUserId, String examId) {
        // 查询这个认证考试所在考试组的允许报名次数
        List<SubAuthenticatedExamGroup> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_REGISTRATION_FREQUENCY)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(examId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(SubAuthenticatedExamGroup.BUSINESS_TYPE_2))
                .limit(1)
                .fetch(r->{
                    SubAuthenticatedExamGroup examGroup = new SubAuthenticatedExamGroup();
                    examGroup.setExamGroupId(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID));
                    examGroup.setExamRegistrationFrequency(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_REGISTRATION_FREQUENCY));
                    return examGroup;
                }));
        //未设置报名次数
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(list) || list.get(0).getExamRegistrationFrequency() == null) {
            return false;
        }
        String groupId = list.get(0).getExamGroupId();
        Integer examRegistrationFrequency = list.get(0).getExamRegistrationFrequency();
        List<String> examIds = getStrings(groupId);
        List<String> targetIds = getTargetIds(currentUserId, groupId, examIds);
        Integer count = findExamRegistrationFrequency(targetIds, currentUserId);
        return count >= examRegistrationFrequency;
    }

    /**
     * 查询该考试组中的所有认证考试id
     * @param groupId 考试组id
     * @return  认证考试id集合
     */
    private List<String> getStrings(String groupId) {
        return examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID.eq(groupId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(SubAuthenticatedExamGroup.BUSINESS_TYPE_2))
                .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID));
    }

    /**
     * 查询受众的ids
     * @param memberId 受众id
     * @param groupId 考试组id
     * @param examIds 认证考试id集合
     * @return 受众ids
     */
    private List<String> getTargetIds(String memberId, String groupId, List<String> examIds) {
        return groupExamIdCache.get(groupId +"#"+ memberId +"#"+SubAuthenticatedExamGroup.BUSINESS_TYPE_2,()->findTargetIds(examIds, memberId), 60*10);
    }

    /**
     * 查询考试报名次数
     */
    private Integer findExamRegistrationFrequency(List<String> examIds, String currentUserId) {
        return signUpDao.execute(r ->
                r.select(SIGNUP.ID.count())
                        .from(SIGNUP)
                        .where(SIGNUP.EXAM_ID.in(examIds)
                                .and(SIGNUP.MEMBER_ID.eq(currentUserId))
                                .and(SIGNUP.STATUS.in(SignUp.STATUS_APPROVE, SignUp.STATUS_PASSED)))
                        .fetchOne(SIGNUP.ID.count()));
    }


    private int findExamTimes(Integer examRegion, List<String> examIds, String currentUserId) {
        int examTimes = 0;
        for (int i = 0; i < examIds.size(); i++) {
            String examId = examIds.get(i);
            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
            Integer count = examRecordDao.execute(e -> e.select(
                            Fields.start()
                                    .add(DSL.count(examRecordTable.field("f_id", String.class)))
                                    .end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_member_id", String.class).eq(currentUserId)
                            .and(examRecordTable.field("f_exam_id", String.class).eq(examId))
                            .and(examRecordTable.field("f_status", Integer.class).notEqual(1))
                    ).fetchOne(DSL.count(examRecordTable.field("f_id", String.class))));
            examTimes = examTimes + count;
        }
        return examTimes;
    }

    private List<String> findTargetIds(List<String> ids, String currentUserId) {
        return examGroupDao.execute(d -> d.selectDistinct(Fields.start()
                        .add(AUDIENCE_OBJECT.TARGET_ID)
                        .end())
                .from(AUDIENCE_OBJECT)
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(AUDIENCE_OBJECT.TARGET_ID.in(ids))
                .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                .and(AUDIENCE_OBJECT.TYPE.eq(AudienceObject.TYPE_EXAM))
                .fetch(AUDIENCE_OBJECT.TARGET_ID));
    }


    @DataSource
    public ExamRecord getNewestRecord(Integer examRegion, String examId, String memberId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRecord> examRecords = examRecordDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class))
                                    .add(examRecordTable.field("f_create_time", Long.class))
                                    .add(examRecordTable.field("f_start_time", Long.class))
                                    .add(examRecordTable.field("f_end_time", Long.class))
                                    .add(examRecordTable.field("f_submit_time", Long.class))
                                    .add(examRecordTable.field("f_last_submit_time", Long.class))
                                    .add(examRecordTable.field("f_is_reset", Integer.class))
                                    .add(examRecordTable.field("f_status", Integer.class))
                                    .add(examRecordTable.field("f_order_content", String.class))
                                    .add(examRecordTable.field("f_member_id", String.class))
                                    .add(examRecordTable.field("f_exam_id", String.class))
                                    .add(examRecordTable.field("f_paper_instance_id", String.class))
                                    .add(examRecordTable.field("f_exam_times", Integer.class))
                                    .add(examRecordTable.field("f_personal_code", Integer.class))
                                    .add(examRecordTable.field("f_face_status", Integer.class))
                                    .end()
                    )
                    .from(examRecordTable)
                    .where(
                            examRecordTable.field("f_exam_id", String.class).eq(examId),
                            examRecordTable.field("f_member_id", String.class).eq(memberId),
                            examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                    ).orderBy(examRecordTable.field("f_create_time", Long.class).desc()).limit(0, 1).fetch(r -> {
                        ExamRecord examRecord = new ExamRecord();
                        examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                        examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
                        examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                        examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                        examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                        examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                        examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                        examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                        examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                        examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                        examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                        examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                        examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                        examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
                        examRecord.setFaceStatus(r.getValue(examRecordTable.field("f_face_status", Integer.class)));
                        return examRecord;
                    });
        });

        if (examRecords.size() > 0) return examRecords.get(0);

        return null;
    }


}
